/* eslint-disable no-console */
import { NextRequest, NextResponse } from 'next/server';

// const routesBlacklist = ['/dashboard'];

export const middleware = async (
  request: NextRequest,
  response: NextResponse
) => {
  // const { pathname } = request.nextUrl;
  // console.log('MIDDLEWARE');
  // const { headers } = request;
  // console.log('headers', headers);
  // console.log('response test', response);
  // from the response get the cookies
  // console.log('response test', response.headers);
  // console.log('cookies', cookies);
  // return NextResponse.next();
  // const allCookies = request.cookies.getAll();
  // console.log('allCookies', allCookies);
  // const response2 = NextResponse.next();
  // console.log('response2', response2);
};

export const config = {
  matcher: [
    // Skip all internal paths (_next)
    '/((?!_next).*)',
  ],
};
