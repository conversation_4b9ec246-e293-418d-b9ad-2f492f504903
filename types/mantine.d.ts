import type {
  DefaultMantineColor,
  MantineColorsTuple,
  ButtonVariant,
  DEFAULT_THEME,
  MantineTheme,
} from '@mantine/core';

import { ButtonVariantsType } from './common';

import { colors, spacing, shadows, borderRadius } from './baseTheme';

// We want to inform mantine that we have extra variants for The mantine components
// We achieve this by using the mantine.d.ts file and extending the Mantine types
// to add our own custom variants as well.

type ExtendedButtonVariants = ButtonVariant | ButtonVariantsType | 'locked';

declare module '@mantine/core' {
  export interface ButtonProps {
    variant?: ExtendedButtonVariants;
  }
  // Leave this comment to check if we will need to access the theme under tsx files
  // export interface MantineTheme {
  //   MantineTheme;
  //   custom: {
  //     colors: { [key: string]: string };
  //   };
  // }
}
