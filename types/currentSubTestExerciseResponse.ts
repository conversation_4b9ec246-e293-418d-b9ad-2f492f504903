import { SubTestQuestionType } from './common';

export enum ExerciseTypes {
  COMPARE_IMAGES = 'compareImages',
  COMPARE_NUMBERS = 'compareNumbers',
  NUMBER_TO_CALCULATOR = 'numberToCalculator',
  AUDIO_TO_CALCULATOR = 'audioToCalculator',
  IMAGE_TO_CALCULATOR = 'imageToCalculator',
  TEXT_TO_CALCULATOR = 'textToCalculator',
  NUMBER_LINES = 'numberlines',
  SHAPES = 'shapes',
  PROBLEM_WITH_OPERATION = 'problemWOperation',
  SOLVE_SECOND_BASED_ON_FIRST = 'solveSecondBasedOnFirst',
  PATTERNS = 'patterns',
}

export type ExhibitsType<T extends SubTestQuestionType['exhibits'][0]['type']> =
  T;

type BaseSubTestQuestionType = Omit<SubTestQuestionType, 'type'>;

export type BaseExhibit = {
  value: string;
  params: unknown;
};

export type CompareImagesType = {
  type: ExerciseTypes.COMPARE_IMAGES;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'image'>;
  })[];
};

export type CompareNumbersType = {
  type: ExerciseTypes.COMPARE_NUMBERS;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'number'>;
  })[];
};

export type NumberToCalculatorType = {
  type: ExerciseTypes.NUMBER_TO_CALCULATOR;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'number'>;
  })[];
};

export type TextToCalculatorType = {
  type: ExerciseTypes.TEXT_TO_CALCULATOR;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'text'>;
  })[];
};

export type AudioToCalculatorType = {
  type: ExerciseTypes.AUDIO_TO_CALCULATOR;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'audio'>;
  })[];
};

export type ImageToCalculatorType = {
  type: ExerciseTypes.IMAGE_TO_CALCULATOR;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'image'>;
  })[];
};

export type NumberLinesType = {
  type: ExerciseTypes.NUMBER_LINES;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'slider'>;
    params: {
      min: number;
      max: number;
      int: number;
    };
  })[];
};

export type PatternsType = {
  type: ExerciseTypes.PATTERNS;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'text'>;
  })[];
};

export type ProblemWithOperationType = {
  type: ExerciseTypes.PROBLEM_WITH_OPERATION;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'text'> | ExhibitsType<'audio'>;
  })[];
};

export type ShapesType = {
  type: ExerciseTypes.SHAPES;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'image'>;
  })[];
};

export type SolveSecondBasedOnFirstType = {
  type: ExerciseTypes.SOLVE_SECOND_BASED_ON_FIRST;
  exhibits: (BaseExhibit & {
    type: ExhibitsType<'text'>;
  })[];
};

// Discriminative Union based on the Exercise Type to adjust exhibits
export type NarrowedSubTestQuestionType = BaseSubTestQuestionType &
  (
    | CompareImagesType
    | CompareNumbersType
    | NumberToCalculatorType
    | AudioToCalculatorType
    | ImageToCalculatorType
    | TextToCalculatorType
    | NumberLinesType
    | ShapesType
    | ProblemWithOperationType
    | SolveSecondBasedOnFirstType
    | PatternsType
  );
