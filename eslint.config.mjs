// eslint.config.mjs
import js from '@eslint/js';
import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import reactPlugin from 'eslint-plugin-react';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import prettierPlugin from 'eslint-plugin-prettier';
import simpleImportSort from 'eslint-plugin-simple-import-sort';
import checkFile from 'eslint-plugin-check-file';
import sortDestructureKeys from 'eslint-plugin-sort-destructure-keys';
import nextPlugin from '@next/eslint-plugin-next';
import importPlugin from 'eslint-plugin-import';
import 'eslint-plugin-only-warn';

export default [
  js.configs.recommended,
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    ignores: [
      '.*.js',
      'node_modules/',
      'dist/',
      '*.d.ts',
      '**/design-system/*',
      '**/node_modules/**',
      'out/*',
      '.next/*',
      '**/out/*',
      '**/.next/*',
    ],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
        ecmaFeatures: { jsx: true },
      },
      globals: { React: true, JSX: true },
    },
    plugins: {
      '@typescript-eslint': tsPlugin,
      react: reactPlugin,
      'jsx-a11y': jsxA11y,
      prettier: prettierPlugin,
      'simple-import-sort': simpleImportSort,
      'check-file': checkFile,
      'sort-destructure-keys': sortDestructureKeys,
      '@next/next': nextPlugin,
      import: importPlugin,
    },
    settings: {
      react: { version: 'detect' },
      'import/resolver': {
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx'],
        },
        typescript: {
          alwaysTryTypes: true,
          project: './tsconfig.json',
        },
      },
    },
    rules: {
      // TypeScript
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-shadow': ['error'],

      // Import rules
      'import/extensions': [
        'error',
        'ignorePackages',
        { js: 'never', jsx: 'never', ts: 'never', tsx: 'never' },
      ],
      'import/no-cycle': 'off',
      'import/no-extraneous-dependencies': 'off',
      'import/no-relative-packages': 'off',
      'import/prefer-default-export': 'off',
      'import/no-unresolved': ['error', { ignore: ['^@/'] }],

      // Check-file
      'check-file/filename-blocklist': [
        'error',
        { '**/*.style.ts': '*.styles.ts' },
      ],

      // File/variable shadowing
      'no-shadow': 'off',
      'no-restricted-globals': 'off',
      'no-underscore-dangle': ['error', { allow: ['_id'] }],

      // Prettier
      'prettier/prettier': [
        'error',
        { singleQuote: true, trailingComma: 'es5' },
      ],

      // React
      'react/function-component-definition': [
        'error',
        {
          namedComponents: 'arrow-function',
          unnamedComponents: 'arrow-function',
        },
      ],
      'react/jsx-curly-newline': 'off',
      'react/jsx-filename-extension': [
        2,
        { extensions: ['.js', '.jsx', '.ts', '.tsx'] },
      ],
      'react/jsx-one-expression-per-line': 'off',
      'react/jsx-props-no-spreading': 'off',
      'react/prop-types': 'off',
      'react/react-in-jsx-scope': 'off',
      'react/require-default-props': 'off',

      // JSX a11y
      'jsx-a11y/anchor-is-valid': [
        'error',
        {
          aspects: ['invalidHref', 'preferButton'],
          components: ['Link'],
          specialLink: ['hrefLeft', 'hrefRight'],
        },
      ],

      // Next.js
      '@next/next/no-html-link-for-pages': 'error',

      // Sorting
      'simple-import-sort/exports': 'error',
      'simple-import-sort/imports': 'error',
      'sort-destructure-keys/sort-destructure-keys': [
        1,
        { caseSensitive: false },
      ],

      // Padding between statements
      'padding-line-between-statements': [
        'error',
        { blankLine: 'always', next: 'if', prev: '*' },
      ],
    },
  },
];
