.hero {
  background-image: 
    url('../public/images/hero.jpg');
  background-size: cover;
  background-position: center;
  height: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
  font-size: 2rem;
}

.logo {
  position: absolute;
  top: var(--spacing-xl);
  left: 0;
  width: 120px;
}

.title {
  font-size: 2rem;
  font-weight: lighter;
  margin-bottom: 1rem;
  max-width: 12em;
  text-align: start;
  color: #000;
}

.imageContainer {
  flex: 1;
  justify-content: start;
}

.aboutSection {
  background: linear-gradient(to right, #0088cc, #00a8b5);
  position: relative;
  padding: 80px 0;
  color: white;
  min-height: 500px;
  overflow: visible;
}

.aboutContent {
  max-width: 500px;
  position: relative;
  z-index: 2;
}

.aboutTitle {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: normal;
  color: white;
}

.tabletContainer {
  position: relative;
  width: 50%;
  margin-bottom: -120px;
}

.tabletImage {
  width: 120%;
  height: auto;
  max-width: none;
  object-fit: contain;
  position: relative;
  right: -10%;
}

.modalImage {
  width: 200px;
}

.certificateImage {
  width: 180px;
}

.featuresSection {
  background: white;
  padding: 80px 0;
}

.featureBlock {
  margin-bottom: 60px;
  width: 100%;
}

.featureTitle {
  font-size: 1.75rem;
  font-weight: normal;
  color: #333;
  margin-bottom: 1.5rem;
}

.featureText {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  width: 100%;
  font-size: 1rem;
}

.productCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: calc(40% - 1rem);
}

.productsRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 60px;
  width: 100%;
}

.listItem {
  color: #666;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.footer {
  background-color: #071708;
  height: 120px;
  width: 100%;
  margin-top: 60px;
}
