import { zodResolver } from '@hookform/resolvers/zod';
import { Textarea, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import countries from 'i18n-iso-countries';
import React from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import Button from '@/components/Button/Button';
import WebsiteLayout from '@/components/Layouts/WebsiteLayout/WebsiteLayout';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import Hero from '@/components/WebsiteSections/Hero/Hero';
import WhatIsMathPro from '@/components/WebsiteSections/WhatIsMathPro/WhatIsMathPro';
import WhoTrustsMathPro from '@/components/WebsiteSections/WhoTrustsMathPro/WhoTrustsMathPro';
import { SupportedCountriesContext } from '@/context/SupportedBillingCountriesProvider';
import { WEBSITE } from '@/services/website';
import { SchoolInterestFormType } from '@/types/common';
import { SCHOOL_INTEREST_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import styles from './SchoolsPage.module.css';

const SchoolsPage = () => {
  const { i18n, t } = useTranslation();
  const { supportedBillingCountries } = SupportedCountriesContext();

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    reset,
  } = useForm<SchoolInterestFormType>({
    resolver: zodResolver(SCHOOL_INTEREST_FORM_SCHEMA),
    defaultValues: {
      name: '',
      surname: '',
      position: '',
      phoneNumber: '',
      email: '',
      message: '',
      school: {
        name: '',
        address: '',
        city: '',
        postcode: '',
        country: '',
        category: '',
      },
    },
    mode: 'onSubmit',
  });

  const sentSchoolInterestMutation = useMutation({
    mutationFn: WEBSITE.SEND_SCHOOL_INTEREST,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: () => {
      notifications.show({
        message: t('email-sent'),
        color: 'green',
      });

      reset();
    },
  });

  const onSubmit: SubmitHandler<SchoolInterestFormType> = (data) => {
    sentSchoolInterestMutation.mutate(data);
  };

  return (
    <WebsiteLayout>
      <Hero page="schools" />

      <WhatIsMathPro type="schools" />

      <div className={styles.contactWrapper}>
        <div className={styles.contactContainer}>
          <div className={styles.contactDetails}>
            <Text
              transKey="show-your-interest"
              type="h2"
              className={styles.aboutTitle}
              color="white"
            />

            <Text
              type="subTitle1"
              transKey="schools-contact-desc"
              lh={1.4}
              color="white"
            />
          </div>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className={styles.formWrapper}
          >
            <Text type="h4" transKey="school-info" />

            <div className={styles.row}>
              <TextInput
                placeholder={t('school-name')}
                w="100%"
                error={t(errors.school?.name?.message || '')}
                {...register('school.name')}
              />

              <Controller
                name="school.category"
                control={control}
                render={({ field }) => (
                  <SelectDropdown
                    value={field.value || ''}
                    data={[
                      { label: t('primary'), value: 'primary' },
                      {
                        label: t('secondary'),
                        value: 'secondary',
                      },
                    ]}
                    onChange={(v) => {
                      field.onChange(v);
                    }}
                    placeholder={t('type')}
                    clearable
                    error={t(errors.school?.category?.message || '')}
                  />
                )}
              />
            </div>

            <div className={styles.row}>
              <TextInput
                placeholder={t('address')}
                w="100%"
                error={t(errors.school?.address?.message || '')}
                {...register('school.address')}
              />

              <TextInput
                placeholder={t('post-code')}
                w="100%"
                error={t(errors.school?.postcode?.message || '')}
                {...register('school.postcode')}
              />
            </div>

            <div className={styles.row}>
              <Controller
                name="school.country"
                control={control}
                render={({ field }) => (
                  <SelectDropdown
                    value={field.value || ''}
                    data={supportedBillingCountries.map((item) => ({
                      label:
                        countries.getName(
                          item.code.toLowerCase(),
                          i18n.language
                        ) || '',
                      value: item.code.toLowerCase(),
                    }))}
                    onChange={(v) => {
                      field.onChange(v);
                    }}
                    placeholder={t('country')}
                    clearable
                    error={t(errors.school?.country?.message || '')}
                  />
                )}
              />

              <TextInput
                placeholder={t('city')}
                w="100%"
                error={t(errors.school?.city?.message || '')}
                {...register('school.city')}
              />
            </div>

            <Text type="h4" transKey="contact-info" mt={24} />

            <div className={styles.row}>
              <TextInput
                placeholder={t('first-name')}
                w="100%"
                error={t(errors.name?.message || '')}
                {...register('name')}
              />

              <TextInput
                placeholder={t('last-name')}
                w="100%"
                error={t(errors.surname?.message || '')}
                {...register('surname')}
              />
            </div>

            <div className={styles.row}>
              <TextInput
                placeholder={t('school-position')}
                w="100%"
                error={t(errors.position?.message || '')}
                {...register('position')}
              />

              <TextInput
                placeholder={t('phone')}
                w="100%"
                error={t(errors.phoneNumber?.message || '')}
                {...register('phoneNumber')}
                type="number"
              />
            </div>

            <TextInput
              placeholder={t('email')}
              w="100%"
              error={t(errors.email?.message || '')}
              {...register('email')}
            />

            <Textarea
              placeholder={t('message')}
              mb={16}
              autosize
              minRows={3}
              maxRows={5}
              mt={32}
              {...register('message')}
              error={t(errors.message?.message || '')}
            />

            <div>
              <Button
                transKey="submit-capital"
                type="submit"
                onClick={handleSubmit(onSubmit)}
                isLoading={sentSchoolInterestMutation.isPending}
                isDisabled={sentSchoolInterestMutation.isPending}
              />
            </div>
          </form>
        </div>
      </div>
      <WhoTrustsMathPro />
    </WebsiteLayout>
  );
};

export default SchoolsPage;
