.contactWrapper {
  background-color: rgba(7, 23, 8, 1);
  display: flex;
  justify-content: center;
  padding: var(--spacing-4xl) var(--spacing-lg);
}

.contactContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  justify-content: space-between;
  height: 100%;
  transition: all 0.2s ease;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-4xl);
}

.aboutTitle {
  font-size: 42px !important;
}

.contactDetails {
  width: 100%;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.formWrapper {
  width: 100%;
  max-width: 661px;
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  background-color: var(--color-white);
  border-radius: var(--radius-xs);
  padding: var(--spacing-lg);
}

.row {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-md);
}

@media screen and (max-width: 1024px) {
  .contactContainer {
    flex-direction: column;
    margin-top: 0;
  }

  .contactWrapper {
    height: fit-content;
  }

  .formWrapper {
    max-width: 700px;
    margin: 0 auto;
  }

  .contactDetails {
    max-width: 100%;
    margin-bottom: var(--spacing-2xl);
  }
}

@media screen and (max-width: 900px) {
  .contactWrapper {
    padding: var(--spacing-3xl) var(--spacing-lg);
  }

  .contactDetails {
    max-width: 100%;
    margin-bottom: var(--spacing-lg);
  }

  .aboutTitle {
    font-size: 32px !important;
  }
}

@media screen and (max-width: 500px) {
  .row {
    flex-direction: column;
  }

  .formWrapper {
    padding: var(--spacing-md);
  }
}

@media screen and (max-width: 480px) {
  .aboutTitle {
    font-size: 30px !important;
  }
}
