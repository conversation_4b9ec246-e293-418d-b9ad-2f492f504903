import { Elements } from '@stripe/react-stripe-js';
import {
  loadStripe,
  StripeElementLocale,
  StripeElementsOptions,
} from '@stripe/stripe-js';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { appearance } from '@/common/consts';
import { PURCHASES_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import Text from '@/components/Text/Text';
import FirstTimePurchaseForm from '@/pages/certification-purchase/components/FirstTimePurchaseForm/FirstTimePurchaseForm';
import FirstTimePurchaseVerification from '@/pages/certification-purchase/components/FirstTimePurchaseVerification/FirstTimePurchaseVerification';
import PRODUCTS from '@/services/products';
import PURCHASES from '@/services/purchases';
import { ProductDetailsType } from '@/types/common';

import styles from './certification-purchase.module.css';
import FirstTimePurchaseSkeleton from './components/FirstTimePurchaseForm/FirstTimePurchaseSkeleton';
import { PaymentStatusType } from './types';

const publicStripeKey = process.env.NEXT_PUBLIC_STRIPE_KEY || '';

const stripePromise = loadStripe(publicStripeKey);

const CertificationPurchase = (): JSX.Element => {
  const [checkoutStatus, setCheckoutStatus] = useState<
    'idle' | PaymentStatusType
  >('idle');

  const [userEmail, setUserEmail] = useState<string>('');

  const { i18n } = useTranslation();

  const router = useRouter();

  const productId = router.query.productId as string;

  const {
    data: clientSecret,
    error: clientSecretError,
    isFetching: isFetchingClientSecret,
  } = useQuery<string | null | undefined>({
    enabled: router.isReady && Boolean(productId),
    queryFn: () => PURCHASES.CREATE_PAYMENT_INTENT({ productId }),
    queryKey: [QUERY_KEYS.CERTIFICATION_PURCHASE_PAYMENT_INTENT, productId],
  });

  const {
    data: productDetails,
    error: productError,
    isFetching: isFetchingProductDetails,
  } = useQuery<ProductDetailsType | null>({
    enabled: router.isReady && Boolean(productId),
    queryFn: () => PRODUCTS.GET_PRODUCT_DETAILS({ productId }),
    queryKey: [QUERY_KEYS.PRODUCT_BY_ID, productId],
  });

  const isFormLoading = checkoutStatus === 'idle' && !clientSecret;

  const options: StripeElementsOptions = {
    appearance,
    clientSecret: clientSecret || '',
    locale: i18n.language as StripeElementLocale,
    loader: 'never',
  };

  const handlePaymentProcessing = (
    type: PaymentStatusType,
    email?: string,
    errorCode?: string
  ) => {
    if (type === 'success') {
      setUserEmail(email || '');
      setCheckoutStatus('success');
    } else if (type === 'failure') {
      router.push(
        `/certification-purchase/payment-error?errorType=${errorCode}`
      );
    }
  };

  const getFormContent = () => {
    return isFormLoading ? (
      <FirstTimePurchaseSkeleton />
    ) : (
      <Elements stripe={stripePromise} options={options}>
        {productDetails && (
          <FirstTimePurchaseForm
            isFetchingPaymentIntent={
              isFetchingClientSecret || isFetchingProductDetails
            }
            clientSecret={clientSecret || ''}
            handlePaymentProcessing={handlePaymentProcessing}
            productDetails={productDetails}
          />
        )}
      </Elements>
    );
  };

  if (
    productError?.message === PURCHASES_ERRORS.PRODUCT_NOT_FOUND ||
    clientSecretError?.message === PURCHASES_ERRORS.PRODUCT_NOT_FOUND
  ) {
    return (
      <Card
        size="2xl"
        radius="xs"
        borderColor="gray100"
        shadow="xs"
        bg="gray100"
        className={styles.wrapper}
      >
        <Text transKey="product-not-found" type="h3" fw={300} />
      </Card>
    );
  }

  return (
    <BaseLayout isAuthPage={false}>
      <div className={styles.wrapper} id="test">
        <Card
          size="2xl"
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="gray100"
        >
          {checkoutStatus === 'idle' ? getFormContent() : null}

          {checkoutStatus === 'success' && (
            <FirstTimePurchaseVerification
              email={userEmail || ''}
              type="success"
            />
          )}
        </Card>
      </div>
    </BaseLayout>
  );
};

export default CertificationPurchase;
