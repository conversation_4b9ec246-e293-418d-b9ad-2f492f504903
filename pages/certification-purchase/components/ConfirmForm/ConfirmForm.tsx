import { PinInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import AUTH from '@/services/auth';

import s from './ConfirmForm.module.css';

type ConfirmFormProps = {
  handleStepUpdate: (step: number) => void;
  userEmail: string;
};

const ConfirmForm = ({ handleStepUpdate, userEmail }: ConfirmFormProps) => {
  const [code, setCode] = useState('');
  const [isInvalidCode, setIsInvalidCode] = useState(false);

  const { t } = useTranslation();

  const codeVerificationMutation = useMutation({
    mutationFn: AUTH.VERIFY_EMAIL,
    onSuccess: () => {
      handleStepUpdate(3);
    },
    onError: (error) => {
      setIsInvalidCode(true);

      if (error.message === 'AuthError.VerificationCodeExpired') {
        notifications.show({
          title: t('error'),
          message: t('expired-verification-code'),
          color: 'red',
        });
      } else if (error.message === 'AuthError.UsedVerificationCode') {
        notifications.show({
          title: t('error'),
          message: t('code-is-already-used'),
          color: 'red',
        });
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  const resendCodeVerificationMutation = useMutation({
    mutationFn: () => AUTH.RESEND_EMAIL_VERIFICATION_CODE(userEmail),
    onSuccess: () => {
      notifications.show({
        title: t('code-sent'),
        message: t('please-check-your-email'),
        color: 'green',
      });
    },
    onError: (error) => {
      if (error.message === 'AuthError.VerificationEmailRateLimited') {
        notifications.show({
          title: t('code-sent'),
          message: t('please-wait-and-try-in-2-minutes'),
          color: 'red',
        });
      } else if (error.message === 'AuthError.UserIsAlreadyVerified') {
        handleStepUpdate(3);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  return (
    <div className={s.wrapper}>
      <Text
        transKey="verification-code-sent"
        type="body1"
        color="black"
        fw={300}
      />

      {userEmail && (
        <Text
          untranslatedText={userEmail || ''}
          type="body1"
          color="black"
          isBold
        />
      )}

      <div className={s.pinInputWrapper}>
        <PinInput
          length={6}
          onChange={(value) => {
            setCode(value);

            if (value.length < 6 && isInvalidCode) setIsInvalidCode(false);
          }}
          classNames={{
            pinInput: s.pinInput,
            input: s.baseInput,
          }}
          error={isInvalidCode}
        />
      </div>

      <div className={s.resendWrapper}>
        <Text
          untranslatedText={t('didnt-receive-code')}
          type="body2"
          color="black"
          fw={300}
        />

        {resendCodeVerificationMutation.isPending ? (
          <Text
            untranslatedText={t('sending-dots')}
            type="body2"
            color="blue"
            fw={700}
            className={s.sending}
          />
        ) : (
          <Text
            untranslatedText={t('resend')}
            type="body2"
            color="blue"
            fw={700}
            onClick={resendCodeVerificationMutation.mutate}
          />
        )}
      </div>

      <Button
        transKey="verify-capital"
        hasFullWidth
        isLoading={codeVerificationMutation.isPending}
        isDisabled={
          isInvalidCode ||
          code.length !== 6 ||
          resendCodeVerificationMutation.isPending
        }
        onClick={() => codeVerificationMutation.mutate({ code })}
      />
    </div>
  );
};

export default ConfirmForm;
