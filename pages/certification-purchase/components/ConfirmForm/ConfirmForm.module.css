.wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  gap: var(--spacing-lg);
}

.pinInputWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--spacing-md) 0;
}

.pinInput {
  height: 70px;
  width: 70px;
}

.baseInput {
  height: 70px !important;
  width: 70px !important;
}

.resendWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin: var(--spacing-md);
  /* justify-content: flex-end; */

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.sending {
  opacity: 0.7;
  cursor: not-allowed !important;
}
