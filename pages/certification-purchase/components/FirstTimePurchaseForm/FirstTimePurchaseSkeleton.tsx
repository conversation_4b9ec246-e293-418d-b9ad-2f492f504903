import { Skeleton } from '@mantine/core';
import { useRouter } from 'next/router';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { PUBLIC_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Checkbox from '@/components/Checkbox/Checkbox';

import Text from '../../../../components/Text/Text';
import styles from './FirstTimePurchaseForm.module.css';

const FirstTimePurchaseSkeleton = () => {
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <div className={`${styles.wrapper}`}>
      <Text transKey="checkout" type="h3" mb={15} color="black" fw={250} />

      <Text transKey="your-cart" type="body1" mb={15} color="black" fw={300} />

      <Skeleton visible height={147} />

      <div>
        <Text
          transKey="personal-info"
          type="body1"
          mb={15}
          mt={15}
          color="black"
          fw={300}
        />

        <Skeleton visible height={56} mb={20} />

        <Skeleton visible height={56} mb={20} />

        <div className={styles.signInWrapper}>
          <Text
            untranslatedText={t('already-have-an-account')}
            type="body2"
            color="black"
            fw={300}
          />

          <Text
            untranslatedText={t('login')}
            type="body2"
            color="blue"
            fw={700}
            onClick={() => {
              router.push(PUBLIC_ROUTES.LOGIN);
            }}
          />
        </div>

        <Text
          transKey="card-details"
          type="body1"
          mb={15}
          mt={15}
          color="black"
          fw={300}
        />

        <div className={styles.stripeWrapper}>
          <div className={styles.stripeSkeletonWrapper}>
            <Skeleton visible height={56} />

            <div>
              <Skeleton visible height={56} mr={16} />
              <Skeleton visible height={56} />
            </div>

            <Skeleton visible height={56} />
          </div>
        </div>

        <div className={styles.termsWrapper}>
          <Checkbox
            value="agreed"
            variant="outlined"
            isRequired
            isChecked={false}
            isDisabled
            onChange={() => {}}
          />

          <div
            className={`${styles.termsDescription} ${styles.disableInteraction}`}
          >
            <Text
              transKey="i-have-read-and-agreed"
              type="body2"
              color="black"
              fw={300}
            />
            <Text
              transKey="terms-of-service"
              type="body2"
              color="blue"
              fw={700}
            />
            <Text transKey="and" type="body2" color="black" fw={300} />
            <Text
              transKey="privacy-policy"
              type="body2"
              color="blue"
              fw={700}
            />
            <Text transKey="pages" type="body2" color="black" fw={300} />
          </div>
        </div>

        <Button transKey="pay-capital" type="submit" hasFullWidth isDisabled />
      </div>
    </div>
  );
};

export default FirstTimePurchaseSkeleton;
