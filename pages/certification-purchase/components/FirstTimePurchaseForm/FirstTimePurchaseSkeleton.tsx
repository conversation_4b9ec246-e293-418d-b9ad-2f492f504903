import { Skeleton } from '@mantine/core';
import React from 'react';

import Button from '@/components/Button/Button';

import styles from './FirstTimePurchaseForm.module.css';

type FirstTimePurchaseSkeletonType = {
  isReceipt: boolean;
};

const FirstTimePurchaseSkeleton = ({
  isReceipt,
}: FirstTimePurchaseSkeletonType) => {
  return (
    <div
      className={`${styles.wrapper} ${isReceipt ? '' : styles.invoiceWrapper}`}
    >
      <div>
        <div className={styles.stripeWrapper}>
          <div
            className={`${styles.stripeSkeletonWrapper} ${isReceipt ? styles.receiptWrapper : ''}`}
          >
            <Skeleton visible height={56} />

            <div>
              <Skeleton visible height={56} mr={16} />
              <Skeleton visible height={56} />
            </div>

            {isReceipt && <Skeleton visible height={56} />}
          </div>
        </div>

        <Button transKey="pay-capital" type="submit" hasFullWidth isDisabled />
      </div>
    </div>
  );
};

export default FirstTimePurchaseSkeleton;
