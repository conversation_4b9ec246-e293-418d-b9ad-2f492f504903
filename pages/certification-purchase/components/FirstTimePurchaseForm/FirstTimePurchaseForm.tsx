import { zodResolver } from '@hookform/resolvers/zod';
import { Skeleton, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  PaymentElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { paymentElementOptions } from '@/common/consts';
import { USERS_ERRORS } from '@/common/errors';
import { PUBLIC_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import CheckOutCard from '@/components/CertificationCards/CheckOutCard/CheckOutCard';
import Checkbox from '@/components/Checkbox/Checkbox';
import Text from '@/components/Text/Text';
import USERS from '@/services/users';
import { FirstTimePurchaseFormType, ProductDetailsType } from '@/types/common';
import { DEFAULT_CHECKOUT_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import { DEFAULT_CHECKOUT_FORM_VALUES } from '../../consts';
import { HandlePaymentProcessingType, ModalContentType } from '../../types';
import TermsAndPrivacyModal from '../TermsAndPrivacyModal/TermsAndPrivacyModal';
import styles from './FirstTimePurchaseForm.module.css';

type FirstTimePurchaseFormProps = {
  isFetchingPaymentIntent: boolean;
  clientSecret: string;
  handlePaymentProcessing: HandlePaymentProcessingType;
  productDetails: ProductDetailsType;
};

const FirstTimePurchaseForm = ({
  clientSecret,
  handlePaymentProcessing,
  isFetchingPaymentIntent,
  productDetails,
}: FirstTimePurchaseFormProps) => {
  const router = useRouter();

  const [checkboxValue, setCheckboxValue] = useState<string>('');
  const [modalContentType, setModalContentType] =
    useState<ModalContentType>('privacy');
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isPaymentPending, setIsPaymentPending] = useState<boolean>(false);
  const [isStripeLoading, setIsStripeLoading] = useState<boolean>(true);
  const [isStripeFormComplete, setIsStripeFormComplete] =
    useState<boolean>(false);

  const isSkeletonLoading = isFetchingPaymentIntent || isStripeLoading;

  const elements = useElements();
  const stripe = useStripe();

  const { t } = useTranslation();

  const productId = productDetails.stripeId || '';

  useEffect(() => {
    if (elements) {
      const element = elements.getElement('payment');

      element?.on('ready', () => {
        setIsStripeLoading(false);
      });
    }
  }, [elements]);

  const {
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<FirstTimePurchaseFormType>({
    resolver: zodResolver(DEFAULT_CHECKOUT_FORM_SCHEMA),
    defaultValues: DEFAULT_CHECKOUT_FORM_VALUES,
    mode: 'onSubmit',
  });

  const hasUserAgreedToTerms = checkboxValue === 'agreed';

  const isPayButtonEnabled =
    hasUserAgreedToTerms &&
    watch('email').trim().length > 1 &&
    watch('fullName').length > 1 &&
    isStripeFormComplete;

  const handleModalAction = (type: ModalContentType) => {
    setModalContentType(type);
    setIsModalOpen(true);
  };

  const paymentCheckout = async (data: FirstTimePurchaseFormType) => {
    setIsPaymentPending(true);

    try {
      if (elements == null || stripe == null) {
        handlePaymentProcessing('failure', '', 'call_issuer');

        return;
      }

      const { error: submitError } = await elements.submit();

      if (submitError) {
        setIsPaymentPending(false);
        return;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        clientSecret,
        elements,
        redirect: 'if_required',
        confirmParams: {
          payment_method_data: {
            billing_details: {
              name: data.fullName.trim(),
              email: data.email.trim().toLowerCase(),
            },
          },
        },
      });

      if (paymentIntent && paymentIntent?.status === 'succeeded') {
        handlePaymentProcessing('success', data.email);
      } else if (error) {
        handlePaymentProcessing('failure', '', error.decline_code);
      }
    } catch (error) {
      handlePaymentProcessing('failure', '', 'call_issuer');
    }
    setIsPaymentPending(false);
  };

  const checkProductOwnershipMutation = useMutation({
    mutationFn: USERS.CHECK_PRODUCT_OWNERSHIP,
    onError: (error) => {
      if (error.message === USERS_ERRORS.USER_NOT_FOUND) {
        notifications.show({
          title: t('error'),
          message: t('user-not-found'),
          color: 'red',
        });
      } else handlePaymentProcessing('failure', '', 'call_issuer');
    },
    onSuccess: (res) => {
      if (res === false) {
        paymentCheckout({
          email: watch('email'),
          fullName: watch('fullName'),
        });
      } else {
        notifications.show({
          title: t('already_paid_this_product'),
          message: t('please_check_your_inbox'),
          color: 'green',
        });
      }
    },
  });

  const onSubmit: SubmitHandler<FirstTimePurchaseFormType> = async (
    data: FirstTimePurchaseFormType
  ) => {
    checkProductOwnershipMutation.mutate({
      email: data.email,
      productId,
    });
  };

  return (
    <div
      className={`${styles.wrapper} ${isPaymentPending && styles.disableInteraction}`}
    >
      <Text transKey="checkout" type="h3" mb={15} color="black" fw={250} />

      <Text transKey="your-cart" type="body1" mb={15} color="black" fw={300} />

      {isSkeletonLoading ? (
        <Skeleton visible height={147} />
      ) : (
        <CheckOutCard productDetails={productDetails} />
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <Text
          transKey="personal-info"
          type="body1"
          mb={15}
          mt={15}
          color="black"
          fw={300}
        />

        {isSkeletonLoading ? (
          <Skeleton visible height={56} mb={20} />
        ) : (
          <div className={styles.inputWrapper}>
            <TextInput
              {...register('fullName', {
                required: t('full-name-required'),
              })}
              placeholder={t('full-name')}
              variant="primary"
              error={t(errors.fullName?.message || '')}
            />
          </div>
        )}

        {isSkeletonLoading ? (
          <Skeleton visible height={56} mb={20} />
        ) : (
          <div className={styles.inputWrapper}>
            <TextInput
              {...register('email', {
                required: t('email-required'),
              })}
              placeholder={t('email')}
              variant="primary"
              error={t(errors.email?.message || '')}
            />
          </div>
        )}

        <div className={styles.signInWrapper}>
          <Text
            untranslatedText={t('already-have-an-account')}
            type="body2"
            color="black"
            fw={300}
          />

          <Text
            untranslatedText={t('login')}
            type="body2"
            color="blue"
            fw={700}
            onClick={() => {
              router.push(PUBLIC_ROUTES.LOGIN);
            }}
          />
        </div>

        <Text
          transKey="card-details"
          type="body1"
          mb={15}
          mt={15}
          color="black"
          fw={300}
        />

        <div className={styles.stripeWrapper}>
          {isSkeletonLoading ? (
            <div className={styles.stripeSkeletonWrapper}>
              <Skeleton visible height={56} />

              <div>
                <Skeleton visible height={56} mr={16} />
                <Skeleton visible height={56} />
              </div>

              <Skeleton visible height={56} />
            </div>
          ) : null}

          <PaymentElement
            options={paymentElementOptions}
            className={styles[isSkeletonLoading ? 'hide' : 'show']}
            onChange={(e) => e.complete && setIsStripeFormComplete(true)}
          />
        </div>

        <div className={styles.termsWrapper}>
          <Checkbox
            value="agreed"
            onChange={(value) => setCheckboxValue(value)}
            isChecked={hasUserAgreedToTerms}
            variant="outlined"
            isRequired
          />

          <div className={styles.termsDescription}>
            <Text
              transKey="i-have-read-and-agreed"
              type="body2"
              color="black"
              fw={300}
            />
            <Text
              transKey="terms-of-service"
              type="body2"
              color="blue"
              fw={700}
              onClick={() => handleModalAction('terms')}
            />
            <Text transKey="and" type="body2" color="black" fw={300} />
            <Text
              transKey="privacy-policy"
              type="body2"
              color="blue"
              fw={700}
              onClick={() => handleModalAction('privacy')}
            />
            <Text transKey="pages" type="body2" color="black" fw={300} />
          </div>
        </div>

        <Button
          transKey="pay-capital"
          hasFullWidth
          type="submit"
          isDisabled={!isPayButtonEnabled}
          isLoading={
            checkProductOwnershipMutation.isPending || isPaymentPending
          }
        />
      </form>

      <TermsAndPrivacyModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        type={modalContentType}
      />
    </div>
  );
};

export default FirstTimePurchaseForm;
