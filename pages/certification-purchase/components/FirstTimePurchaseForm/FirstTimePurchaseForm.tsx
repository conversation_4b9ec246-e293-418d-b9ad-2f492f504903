/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Skeleton } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  PaymentElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  paymentElementOptions,
  paymentInvoiceElementOptions,
} from '@/common/consts';
import { USERS_ERRORS } from '@/common/errors';
import Button from '@/components/Button/Button';
import USERS from '@/services/users';
import { ProductDetailsType } from '@/types/common';

import { HandlePaymentProcessingType } from '../../types';
import styles from './FirstTimePurchaseForm.module.css';

type FirstTimePurchaseFormProps = {
  isFetchingPaymentIntent: boolean;
  clientSecret: string;
  handlePaymentProcessing: HandlePaymentProcessingType;
  productDetails?: ProductDetailsType;
  userEmail: string;
  userUsername: string;
  paymentMethod?: 'receipt' | 'invoice';
};

const FirstTimePurchaseForm = ({
  clientSecret,
  handlePaymentProcessing,
  isFetchingPaymentIntent,
  paymentMethod = 'receipt',
  productDetails,
  userEmail,
  userUsername,
}: FirstTimePurchaseFormProps) => {
  const [isPaymentPending, setIsPaymentPending] = useState<boolean>(false);
  const [isStripeLoading, setIsStripeLoading] = useState<boolean>(true);
  const [isStripeFormComplete, setIsStripeFormComplete] =
    useState<boolean>(false);

  const isSkeletonLoading = isFetchingPaymentIntent || isStripeLoading;

  const elements = useElements();
  const stripe = useStripe();

  const { t } = useTranslation();

  const productId = productDetails?.stripeId || '';

  const router = useRouter();

  useEffect(() => {
    if (elements) {
      const element = elements.getElement('payment');

      element?.on('ready', () => {
        setIsStripeLoading(false);
      });
    }
  }, [elements]);

  const isPayButtonEnabled = isStripeFormComplete;

  const paymentCheckout = async () => {
    setIsPaymentPending(true);

    try {
      if (elements == null || stripe == null) {
        handlePaymentProcessing('failure', '', 'call_issuer');

        return;
      }

      const { error: submitError } = await elements.submit();

      if (submitError) {
        setIsPaymentPending(false);
        return;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        clientSecret,
        elements,
        redirect: 'if_required',
        confirmParams: {
          payment_method_data: {
            billing_details: {
              name: userUsername.trim(),
              email: userEmail.trim().toLowerCase(),
            },
          },
        },
      });

      if (paymentIntent && paymentIntent?.status === 'succeeded') {
        handlePaymentProcessing('success', userEmail);
      } else if (error) {
        handlePaymentProcessing('failure', '', error.decline_code);
      }
    } catch (error) {
      handlePaymentProcessing('failure', '', 'call_issuer');
    }
    setIsPaymentPending(false);
  };

  const checkProductOwnershipMutation = useMutation({
    mutationFn: USERS.CHECK_PRODUCT_OWNERSHIP,
    onError: (error) => {
      if (error.message === USERS_ERRORS.USER_NOT_FOUND) {
        notifications.show({
          title: t('error'),
          message: t('user-not-found'),
          color: 'red',
        });
      } else handlePaymentProcessing('failure', '', 'call_issuer');
    },
    onSuccess: (res) => {
      if (res === false) {
        paymentCheckout();
      } else {
        notifications.show({
          title: t('already_paid_this_product'),
          message: t('please_check_your_inbox'),
          color: 'green',
        });
        router.push('/dashboard/certification-tests');
      }
    },
  });

  const onSubmit = async () => {
    setIsPaymentPending(true);

    checkProductOwnershipMutation.mutate({
      email: userEmail,
      productId,
    });
  };

  return (
    <div
      className={`${styles.wrapper} ${isPaymentPending && styles.disableInteraction}`}
    >
      <div className={styles.stripeWrapper}>
        {isSkeletonLoading ? (
          <div className={styles.stripeSkeletonWrapper}>
            <Skeleton visible height={56} />

            <div>
              <Skeleton visible height={56} mr={16} />
              <Skeleton visible height={56} />
            </div>

            {paymentMethod === 'receipt' && <Skeleton visible height={56} />}
          </div>
        ) : null}

        <PaymentElement
          options={
            paymentMethod === 'invoice'
              ? paymentInvoiceElementOptions
              : paymentElementOptions
          }
          className={styles[isSkeletonLoading ? 'hide' : 'show']}
          onChange={(e) => e.complete && setIsStripeFormComplete(true)}
        />
      </div>

      <Button
        transKey="pay-capital"
        hasFullWidth
        isDisabled={!isPayButtonEnabled || !clientSecret}
        isLoading={checkProductOwnershipMutation.isPending || isPaymentPending}
        onClick={onSubmit}
      />
    </div>
  );
};

export default FirstTimePurchaseForm;
