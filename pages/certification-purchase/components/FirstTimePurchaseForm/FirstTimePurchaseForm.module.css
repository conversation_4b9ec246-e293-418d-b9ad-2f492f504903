.wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.signInWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.termsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-mdl);
  margin-top: var(--spacing-xs);
}

.termsDescription {
  display: flex;
  align-items: center;
  gap: 0 var(--spacing-xs);
  margin-left: var(--spacing-xs);
  flex-wrap: wrap;

  white-space: nowrap;

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  & p:nth-child(4) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.stripeWrapper {
  height: 230px;
  padding: 1px;

  overflow: hidden;

  @media screen and (max-width: 356px) {
    height: 300px;
  }
}

.stripeSkeletonWrapper {
  height: 220px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;

  margin-bottom: var(--spacing-xl);

  & > div {
    width: 100%;
    display: flex;
    flex-direction: row;
  }
}

.inputWrapper {
  height: 76px;
}

.hide {
  opacity: 0;
}

.show {
  opacity: 1;
}

.disableInteraction {
  pointer-events: none;
}
