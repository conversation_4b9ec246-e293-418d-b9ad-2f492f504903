.wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.stripeWrapper {
  margin-bottom: var(--spacing-lg);
  padding: 1px;

  overflow: hidden;

  @media screen and (max-width: 356px) {
    height: 300px;
  }
}

.stripeSkeletonWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;

  & > div {
    width: 100%;
    display: flex;
    flex-direction: row;
  }
}

.receiptWrapper {
  height: 194px;
  max-height: 194px;
}

.inputWrapper {
  height: 76px;
}

.hide {
  opacity: 0;
}

.show {
  opacity: 1;
}

.disableInteraction {
  pointer-events: none;
}

.disabledStripeForm {
  pointer-events: none;
  opacity: 0.4;
  cursor: not-allowed;
}
