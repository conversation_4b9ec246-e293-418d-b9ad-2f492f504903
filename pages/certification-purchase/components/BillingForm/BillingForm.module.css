.inputWrapper {
  height: 76px;
}

.row {
  display: flex;
  gap: var(--spacing-md);
}

.termsDescription {
  display: flex;
  align-items: center;
  gap: 0 var(--spacing-xs);
  margin-left: var(--spacing-xs);
  flex-wrap: wrap;

  white-space: nowrap;

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  & p:nth-child(4) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.signInWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin: var(--spacing-lg) 0;

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.fullWidth {
  width: 100%;
}

.radioWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.radioContainer {
  margin-bottom: var(--spacing-md);
  display: flex;
  gap: var(--spacing-mdl);
}

.verifyButtonWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.smallInput {
  width: 100%;
}

.buttonWrapper {
  margin-top: var(--spacing-md);
}
