import { Modal } from '@mantine/core';
import React from 'react';

import Text from '@/components/Text/Text';

import { ModalContentType } from '../../types';
import styles from './TermsAndPrivacyModal.module.css';

type TermsAndPrivacyModalProps = {
  type: ModalContentType;
  isOpen: boolean;
  onClose: () => void;
};

const TermsAndPrivacyModal = ({
  isOpen,
  onClose,
  type,
}: TermsAndPrivacyModalProps) => {
  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      centered
      size="xl"
      autoFocus={false}
      trapFocus
      title={
        <Text
          transKey={type === 'privacy' ? 'privacy-policy' : 'terms-of-service'}
          type="h3"
          color="blue"
          fw={700}
        />
      }
    >
      <div className={styles.contentWrapper}>
        <Text untranslatedText={type} type="body1" />
      </div>
    </Modal>
  );
};

export default TermsAndPrivacyModal;
