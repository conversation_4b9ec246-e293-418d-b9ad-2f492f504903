.termsWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-mdl);
  margin-top: var(--spacing-sm);
}

.inputWrapper {
  height: 76px;
}

.row {
  height: 76px;
  display: flex;
  gap: var(--spacing-md);
}

.termsDescription {
  display: flex;
  align-items: center;
  gap: 0 var(--spacing-xs);
  margin-left: var(--spacing-xs);
  flex-wrap: wrap;

  white-space: nowrap;

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  & p:nth-child(4) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.signInWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin: var(--spacing-lg) 0;

  & p:nth-child(2) {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.fullWidth {
  width: 100%;
}
