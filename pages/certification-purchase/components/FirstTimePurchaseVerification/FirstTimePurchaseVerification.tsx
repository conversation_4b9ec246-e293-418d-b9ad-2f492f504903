import { useRouter } from 'next/router';
import React, { useState } from 'react';

import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import styles from './FirstTimePurchaseVerification.module.css';

type FirstTimePurchaseVerificationProps = {
  type: 'success' | 'failure';
  errorType?: string;
};

const FirstTimePurchaseVerification = ({
  errorType,
  type,
}: FirstTimePurchaseVerificationProps) => {
  const isSuccess = type === 'success';
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const router = useRouter();

  return (
    <div className={styles.wrapper}>
      <div className={type === 'failure' ? styles.errorAnimation : ''}>
        <Icon
          name={isSuccess ? 'CheckMarkWholeSvg' : 'FailureCircleSvg'}
          color={isSuccess ? 'green' : 'danger'}
        />
      </div>

      <Text
        transKey={
          isSuccess
            ? 'transaction-completed-capital'
            : 'transaction-failed-capital'
        }
        type="subTitle1"
        mt={20}
        mb={60}
        color={isSuccess ? 'green' : 'danger'}
        fw={700}
        align="center"
      />

      <Text
        transKey={isSuccess ? 'thank-you!' : 'could-not-process-payment'}
        type="h1"
        mb={60}
        color="black"
        fw={250}
        align="center"
      />

      {!isSuccess && errorType && (
        <Text
          transKey={(errorType as TranslationKeysType) || 'call_issuer'}
          type="body1"
          mb={40}
          color="black"
          fw={400}
          align="center"
        />
      )}

      <Text
        transKey={
          isSuccess ? 'you-can-proceed-to-math-pro' : 'contact-customer-service'
        }
        type="body1"
        mb={isSuccess ? 0 : 20}
        color="black"
        fw={300}
        align="center"
      />

      <Text
        untranslatedText={isSuccess ? ' ' : '<EMAIL>'}
        type="body1"
        mt={isSuccess ? 0 : 20}
        mb={20}
        color="black"
        fw={700}
        align="center"
      />

      {isSuccess && (
        <Button
          transKey="proceed-capital"
          isLoading={isButtonLoading}
          onClick={() => {
            setIsButtonLoading(true);
            router.push('/dashboard/certification-tests');
          }}
        />
      )}
    </div>
  );
};

export default FirstTimePurchaseVerification;
