/* Screening tests section */
.productsWrapper {
  background: rgba(34, 149, 183, 1);

  padding: var(--spacing-4xl) var(--spacing-lg);
  display: flex;
  justify-content: center;
}

.productsContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  gap: var(--spacing-5xl);
}

.screeningHeadRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
}

.screeningTestsDetails {
  width: 100%;
  max-width: 525px;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-xl);
}

.productsListWrapper {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.productWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 545px;
  width: 100%;
}

.productTitleWrapper {
  display: flex;
  gap: var(--spacing-lg);
}

.priceWhite {
  border-right: 2px solid rgba(7, 23, 8, 0.5);
  padding-right: var(--spacing-sm);
  height: var(--spacing-md);
  display: flex;
  align-items: center;
}

.priceDark {
  border-right: 2px solid rgba(255, 255, 255, 0.5);
  padding-right: var(--spacing-sm);
  height: var(--spacing-md);
  display: flex;
  align-items: center;
}

.tabletImageWrapper {
  z-index: 1;
  width: 100%;
  max-width: 645px;
  height: auto;
  margin-right: -20px;
}

.tabletImage {
  width: 100%;
  max-width: 640px;
}

/* E learning  section */

.eLearningWrapper {
  background: rgb(7, 23, 8);

  padding: var(--spacing-4xl) var(--spacing-lg);
  display: flex;
  justify-content: center;
}

.eLearningContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  position: relative;
}

.eLearningDetails {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 545px;
  min-width: 425px;
  width: 100%;
  z-index: 1;
}

.handsImage {
  width: 100%;
  max-width: 620px;
  position: absolute;
  bottom: -97px;
  right: 0;
}

/* Ebooks  section */
.eBooksWrapper {
  padding: var(--spacing-5xl) var(--spacing-lg);
  background-color: white;

  display: flex;
  justify-content: center;
}

.eBooksDetails {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 545px;
  min-width: 320px;
  width: 100%;
}

.eBooksContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  gap: var(--spacing-xl);
}

.ebookWrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 545px;
}

.ebookImage {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

/* Common */
.aboutTitle {
  font-size: 42px !important;
}

@media screen and (max-width: 980px) {
  .handsImage {
    right: -20px;
    max-width: 500px;
    height: 300px;
  }
}

@media screen and (max-width: 900px) {
  .productsWrapper {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .eLearningWrapper {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .eBooksWrapper {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .handsImage {
    bottom: -48px;
  }

  .eLearningDetails {
    max-width: 370px;
    min-width: 200px;
  }
}

@media screen and (max-width: 768px) {
  .screeningHeadRow {
    flex-direction: column;
  }

  .screeningTestsDetails {
    max-width: 100%;
    min-width: 100%;
  }

  .tabletImageWrapper {
    margin: 0;
    max-width: 100%;
    height: fit-content;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .productsListWrapper {
    flex-direction: column;
    gap: var(--spacing-3xl);
  }

  .productWrapper {
    max-width: 100%;
  }

  .eBooksContainer {
    flex-direction: column;
    gap: var(--spacing-4xl);
  }

  .eBooksDetails {
    max-width: 100%;
  }

  .ebookWrapper {
    max-width: 100%;
  }

  .eLearningContainer {
    flex-direction: column;
  }

  .handsImage {
    position: relative;
    align-self: center;
    right: auto;
  }

  .eLearningDetails {
    max-width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .aboutTitle {
    font-size: 30px;
  }

  .productsContainer {
    gap: var(--spacing-xl);
  }

  .handsImage {
    height: 200px;
    left: -14px;
  }
}
