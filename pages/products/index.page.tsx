import Image from 'next/image';
import React from 'react';

import { CURRENCY_SYMBOLS } from '@/common/consts';
import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import WebsiteLayout from '@/components/Layouts/WebsiteLayout/WebsiteLayout';
import Text from '@/components/Text/Text';
import Hero from '@/components/WebsiteSections/Hero/Hero';
import { TranslationKeysType } from '@/types/common';

import styles from './ProductsPage.module.css';

const PRODUCTS = [
  {
    title: 'MathPro-S',
    icon: 'STypeLicenseSvg',
    subtitleKey: 's-type-page-description-subtitle',
    descriptionKey: 's-type-page-description',
    price: 99,
  },
  {
    title: 'MathPro-D',
    icon: 'DTypeLicenseSvg',
    subtitleKey: 'd-type-page-description-subtitle',
    descriptionKey: 'd-type-page-description',
    price: 99,
  },
];

const ProductsPage = () => {
  return (
    <WebsiteLayout>
      <Hero page="products" />

      {/* Screening tests section */}
      <div className={styles.productsWrapper}>
        <div className={styles.productsContainer}>
          <div className={styles.screeningHeadRow}>
            <div className={styles.screeningTestsDetails}>
              <Text
                transKey="screening-tests-title"
                type="h2"
                className={styles.aboutTitle}
                fw={400}
                color="white"
                mb={24}
              />

              <Text
                type="subTitle1"
                transKey="screening-tests-desc-first"
                color="white"
                mb={12}
                lh={1.4}
              />

              <Text
                type="subTitle1"
                transKey="screening-tests-desc-second"
                color="white"
                mb={12}
                lh={1.4}
              />

              <Text
                type="subTitle1"
                transKey="screening-tests-desc-third"
                color="white"
                mb={12}
                lh={1.4}
              />

              <Text
                type="subTitle1"
                transKey="screening-tests-desc-fourth"
                color="white"
                lh={1.4}
              />
            </div>

            <div className={styles.tabletImageWrapper}>
              <Image
                src="/images/tablet_test.svg"
                alt="tablet"
                width={645}
                height={460}
                draggable={false}
                className={styles.tabletImage}
              />
            </div>
          </div>

          <div className={styles.productsListWrapper}>
            {PRODUCTS.map((product) => (
              <div key={product.title} className={styles.productWrapper}>
                <div className={styles.productTitleWrapper}>
                  <Icon
                    name={product.icon as keyof typeof Icon}
                    color="white"
                  />

                  <Text
                    untranslatedText={product.title}
                    type="h2"
                    fw={400}
                    color="white"
                    mt={2}
                  />
                </div>

                <div>
                  <Text
                    transKey={product.subtitleKey as TranslationKeysType}
                    color="white"
                    type="subTitle1"
                    fw={700}
                    mb={8}
                    lh={1.4}
                  />

                  <Text
                    transKey={product.descriptionKey as TranslationKeysType}
                    color="white"
                    type="subTitle1"
                    lh={1.4}
                  />
                </div>

                <div>
                  <Button
                    variant="white"
                    type="button"
                    transKey="buy-capital"
                    leftSection={
                      <span className={styles.priceWhite}>
                        <Text
                          untranslatedText={`${CURRENCY_SYMBOLS.eur}${product.price}`}
                          color="black"
                          fw={800}
                          type="subTitle2"
                        />
                      </span>
                    }
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Intervention Program tests section */}
      <div className={styles.eLearningWrapper}>
        <div className={styles.eLearningContainer}>
          <div className={styles.eLearningDetails}>
            <Text
              transKey="e-learning-title"
              type="h2"
              fw={400}
              color="white"
            />

            <Text
              transKey="e-learning-desc"
              color="white"
              mb={20}
              type="subTitle1"
              lh={1.4}
            />

            <div>
              <Button
                variant="primaryRounded"
                type="button"
                transKey="buy-capital"
                leftSection={
                  <span className={styles.priceDark}>
                    <Text
                      untranslatedText={`${CURRENCY_SYMBOLS.eur}${99}`}
                      color="white"
                      fw={800}
                      type="subTitle2"
                    />
                  </span>
                }
              />
            </div>
          </div>

          <Image
            src="/images/raised_hands.webp"
            alt="e-learning"
            width={620}
            height={425}
            draggable={false}
            className={styles.handsImage}
          />
        </div>
      </div>

      {/* Ebooks  section */}
      <div className={styles.eBooksWrapper}>
        <div className={styles.eBooksContainer}>
          <div className={styles.eBooksDetails}>
            <Text transKey="e-books-title" type="h2" fw={400} mt={2} />

            <div>
              <Text
                transKey="e-books-subtitle"
                type="subTitle1"
                fw={700}
                mb={8}
                lh={1.4}
              />

              <Text transKey="e-books-desc" mb={20} type="subTitle1" lh={1.4} />
            </div>

            <div>
              <Button
                variant="primaryRounded"
                type="button"
                transKey="buy-capital"
                leftSection={
                  <span className={styles.priceDark}>
                    <Text
                      untranslatedText={`${CURRENCY_SYMBOLS.eur}${99}`}
                      color="white"
                      fw={800}
                      type="subTitle2"
                    />
                  </span>
                }
              />
            </div>
          </div>

          <div className={styles.ebookWrapper}>
            <Image
              src="/images/e_book_sample.jpg"
              alt="e-learning"
              width={320}
              height={490}
              draggable={false}
              className={styles.ebookImage}
            />
          </div>
        </div>
      </div>
    </WebsiteLayout>
  );
};

export default ProductsPage;
