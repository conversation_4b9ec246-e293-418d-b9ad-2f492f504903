import {
  Box,
  Divider,
  Fieldset,
  Flex,
  Radio as MantineRadio,
  Space,
  Text as MantineText,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useState } from 'react';

import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Checkbox from '@/components/Checkbox/Checkbox';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import { Icons } from '@/components/Icon/Icons';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import Radio from '@/components/Radio/Radio';
import Text from '@/components/Text/Text';

const ComponentsSink = (): JSX.Element => {
  const [activeRadio, setActiveRadio] = useState('');
  const [checkboxValue, setCheckboxValue] = useState('');
  const [opened, { close, open }] = useDisclosure(false);
  const [infoOpened, { close: closeInfo, open: openInfo }] =
    useDisclosure(false);

  const [isActionInProgress, setIsActionInProgress] = useState(false);

  return (
    <div
      style={{
        padding: '120px 78px',
      }}
    >
      <MantineText fw={300} size="28px" c="#000000" mb="lg">
        Buttons
      </MantineText>

      <Flex gap="lg" wrap="wrap">
        <Fieldset legend="locked">
          <Button transKey="add-student" isLocked />
        </Fieldset>

        <Fieldset legend="primary">
          <Button transKey="add-student" variant="primary" size="md" />
        </Fieldset>

        <Fieldset legend="primary loading">
          <Button transKey="add-student" isLoading />
        </Fieldset>

        <Fieldset legend="primary-disabled">
          <Button transKey="add-student" isDisabled />
        </Fieldset>

        <Fieldset legend="primaryOutlined">
          <Button transKey="add-student" variant="primaryOutlined" />
        </Fieldset>

        <Fieldset legend="success">
          <Button transKey="add-student" variant="success" />
        </Fieldset>

        <Fieldset legend="danger">
          <Button transKey="add-student" variant="danger" />
        </Fieldset>

        <Fieldset legend="dangerOutlined">
          <Button transKey="add-student" variant="dangerOutlined" />
        </Fieldset>

        <Fieldset legend="dark">
          <Button transKey="add-student" variant="dark" />
        </Fieldset>
      </Flex>

      <MantineText fw={300} size="28px" c="#000000" mb="lg" mt="xl">
        Close button
      </MantineText>

      <Flex gap="lg" wrap="wrap">
        <Fieldset legend="primary">
          <CloseButton onClick={() => {}} />
        </Fieldset>

        <Fieldset legend="outlined">
          <CloseButton onClick={() => {}} variant="outlined" />
        </Fieldset>
      </Flex>
      <Space mb={32} />

      <MantineText fw={300} size="28px" c="#000000" mb="lg">
        Text
      </MantineText>

      <Flex gap={16} direction="column">
        <Text
          untranslatedText="Lorem ipsum dolor sit amet consectetur adipisicing elit. Facere est deserunt dolorem quas quo voluptatum numquam dolore magnam debitis, incidunt optio architecto!"
          type="h1"
          isBold
          lineClamp={2}
        />

        <Text
          untranslatedText="Lorem ipsum dolor sit amet consectetur adipisicing elit. Facere est deserunt dolorem quas quo voluptatum numquam dolore magnam debitis, incidunt optio architecto!"
          type="h2"
        />

        <Text
          untranslatedText="Lorem ipsum dolor sit amet consectetur adipisicing elit. Facere est deserunt dolorem quas quo voluptatum numquam dolore magnam debitis, incidunt optio architecto!"
          type="h3"
          lineClamp={1}
        />

        <Text untranslatedText="just a paragraph" />
      </Flex>

      <Text untranslatedText="dsd" type="h1" />

      <MantineText fw={300} size="28px" c="#000000" mb="lg">
        Card
      </MantineText>

      <Card shadow="xl" size="3xl" bg="gray100" radius="md">
        Paper is the most basic ui component Use it to create cards, dropdowns,
        modals and other components that require background with shadow
      </Card>

      <Space mb={32} />

      <Card
        size="3xl"
        radius="xs"
        // borderColor="gray100"
        shadow="xs"
        // borderSize={1}
        bg="gray100"
      >
        <Flex gap="xl" direction="column">
          <Text transKey="checkout" type="h3" />

          <Box>
            <Text transKey="your-cart" type="body1" />
          </Box>
        </Flex>
      </Card>
      <Space mb={32} />

      <MantineText fw={300} size="28px" c="#000000" mb="lg" mt="xl">
        Radio button
      </MantineText>

      <Flex gap="lg" wrap="wrap">
        <MantineRadio.Group
          value={activeRadio}
          onChange={setActiveRadio}
          name="favoriteFramework"
          label="Select your favorite framework/library"
        >
          <Radio label="home" value="test" isChecked={activeRadio === 'test'} />

          <Radio
            label="home"
            value="test2"
            isChecked={activeRadio === 'test2'}
          />
        </MantineRadio.Group>
      </Flex>

      <Space mb={32} />

      <MantineText fw={300} size="28px" c="#000000" mb="lg" mt="xl">
        Checkbox
      </MantineText>

      <Flex gap="lg" wrap="wrap">
        <Checkbox
          label="home"
          value="test"
          onChange={(value) => setCheckboxValue(value)}
          isChecked={checkboxValue === 'test'}
          // ask what is the difference between indeterminate and isIndeterminate
          indeterminate
        />
      </Flex>

      <MantineText fw={300} size="28px" c="#000000" mb="lg" mt="xl">
        Icon
      </MantineText>

      <Flex gap="lg" wrap="wrap">
        {Object.keys(Icons).map((key) => (
          <Fieldset
            legend={key}
            key={key}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Icon name={key as keyof typeof Icons} />
          </Fieldset>
        ))}
      </Flex>

      <AlertDialog
        isOpen={opened}
        onConfirmAction={() => {
          setIsActionInProgress(true);

          // set timeout to simulate async action
          setTimeout(() => {
            setIsActionInProgress(false);
            close();
          }, 10000);
        }}
        title="already_paid_this_product"
        description="approve_with_id"
        onCancel={close}
        isActionInProgress={isActionInProgress}
        variant="danger"
      />

      <AlertDialog
        isOpen={infoOpened}
        onConfirmAction={() => {
          setIsActionInProgress(true);

          // set timeout to simulate async action
          setTimeout(() => {
            setIsActionInProgress(false);
            close();
          }, 10000);
        }}
        title="already_paid_this_product"
        description="approve_with_id"
        onCancel={closeInfo}
        isActionInProgress={isActionInProgress}
        variant="info"
      />

      <Divider size="md" mt={40} />

      <Flex gap="lg" wrap="wrap" mt={40}>
        click me to open danger alertDialog
        <Button transKey="assistant" onClick={open} />
        click me to open info alertDialog
        <Button transKey="add-student" onClick={openInfo} />
      </Flex>
    </div>
  );
};

export default ComponentsSink;
