import { Image } from '@mantine/core';
import { useRouter } from 'next/router';
import { JSX } from 'react';

import { CURRENCY_SYMBOLS, IS_STAGING_OR_DEVELOPMENT } from '@/common/consts';
import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import WebsiteLayout from '@/components/Layouts/WebsiteLayout/WebsiteLayout';
import Text from '@/components/Text/Text';
import Hero from '@/components/WebsiteSections/Hero/Hero';
import WhatIsMathPro from '@/components/WebsiteSections/WhatIsMathPro/WhatIsMathPro';
import WhoTrustsMathPro from '@/components/WebsiteSections/WhoTrustsMathPro/WhoTrustsMathPro';
import { TranslationKeysType } from '@/types/common';

import styles from './HomePage.module.css';

const PRODUCTS = [
  {
    transKey: 'short',
    icon: 'STypeLicenseSvg',
    url: '/schools',
    price: 99,
  },
  {
    transKey: 'diagnostic',
    icon: 'DTypeLicenseSvg',
    url: '/schools',
    price: 99,
  },
];

const IndexPage = (): JSX.Element => {
  const router = useRouter();

  return (
    <WebsiteLayout>
      <Hero page="home" />

      <WhatIsMathPro type="home" />

      {/* Who is for section */}
      <div className={styles.whoIsSection}>
        <div className={styles.whoIsContainer}>
          <Text
            transKey="who-is-math-pro-for"
            type="h2"
            className={styles.aboutTitle}
          />

          <div className={styles.whoIsContent}>
            <div className={styles.whoIsRow}>
              <div>
                <Image
                  src="/images/schools.svg"
                  alt="schools"
                  width={150}
                  height={150}
                  draggable={false}
                  className={styles.whoIsImage}
                />
              </div>

              <div className={styles.whoIsDetailsWrapper}>
                <Text type="h3" transKey="schools" fw={400} />

                <div>
                  <Text
                    type="subTitle1"
                    transKey="schools-desc-first-paragraph"
                    fw={700}
                    mb={8}
                    lh={1.4}
                  />

                  <Text
                    type="subTitle1"
                    transKey="schools-desc-second-paragraph"
                    lh={1.4}
                  />
                </div>

                <div className={styles.learnMoreWrapper}>
                  <Button
                    variant="dark"
                    transKey="learn-more-capital"
                    onClick={() => router.push('schools')}
                  />
                </div>
              </div>
            </div>

            <div className={styles.whoIsRow}>
              <div>
                <Image
                  src="/images/independent_teacher.svg"
                  alt="independent teacher"
                  width={150}
                  height={150}
                  draggable={false}
                  className={styles.whoIsImage}
                />
              </div>

              <div className={styles.whoIsDetailsWrapper}>
                <Text type="h3" transKey="independent-teachers" fw={400} />

                <div>
                  <Text
                    type="subTitle1"
                    transKey="independent-teachers-desc-first-paragraph"
                    fw={700}
                    mb={8}
                    lh={1.4}
                  />

                  <Text
                    type="subTitle1"
                    transKey="independent-teachers-desc-second-paragraph"
                    lh={1.4}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Products section */}
      <div className={styles.productsWrapper}>
        <div className={styles.productsContainer}>
          <div className={styles.productsDetails}>
            <Text
              transKey="start-today"
              type="h2"
              className={styles.aboutTitle}
              color="white"
              mb={30}
            />

            <Text transKey="start-today-desc" color="white" type="body2" />
          </div>

          {IS_STAGING_OR_DEVELOPMENT ? (
            <div className={styles.buyProductsButtonsWrapper}>
              {PRODUCTS.map((product) => (
                <Button
                  key={product.transKey}
                  // onClick={() => router.push(product.url)}
                  size="xl"
                  variant="primaryRounded"
                  leftSection={
                    <div className={styles.leftButtonWrapper}>
                      <Icon
                        name={product.icon as keyof typeof Icon}
                        color="white"
                        w={41}
                        h={46}
                      />

                      <div className={styles.buyButtonDetails}>
                        <Text
                          transKey="starter-pack-capital"
                          color="white"
                          fw={800}
                          type="subTitle2"
                          className={styles.starterPack}
                        />

                        <Text
                          transKey={product.transKey as TranslationKeysType}
                          color="white"
                          type="h4"
                          className={styles.productName}
                        />
                      </div>
                    </div>
                  }
                  untranslatedText=""
                  rightSection={
                    <>
                      <span className={styles.priceWhite}>
                        <Text
                          untranslatedText={`${CURRENCY_SYMBOLS.eur}${product.price}`}
                          color="white"
                          fw={800}
                          type="subTitle2"
                        />
                      </span>

                      <Text
                        transKey="buy-capital"
                        fw={800}
                        type="subTitle2"
                        ml={8}
                        color="white"
                      />
                    </>
                  }
                />
              ))}

              <div>
                <Button
                  variant="primaryRounded"
                  transKey="more-capital"
                  size="xl"
                  onClick={() => router.push('products')}
                />
              </div>
            </div>
          ) : (
            <Text
              type="body1"
              transKey="registration-opening-soon"
              fw={400}
              lh={1.5}
              color="white"
              className={styles.registrationOpeningSoon}
            />
          )}
        </div>
      </div>

      <WhoTrustsMathPro />
    </WebsiteLayout>
  );
};

export default IndexPage;
