.problemWrapper {
  padding: var(--spacing-2xl) var(--spacing-lg);
  background: var(--color-turquoise);

  display: flex;
  justify-content: center;
}

.problemContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  gap: var(--spacing-2xl);
}

.problemDetails {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--spacing-lg);
  width: 100%;
  max-width: 500px;
}

.childrenImageWrapper {
  z-index: 1;
  width: 100%;
  height: fit-content;
  min-width: 200px;

  display: flex;
  justify-content: center;
}

.childrenImage {
  width: 100%;
  max-width: 550px;
  height: fit-content;
}

.teamWrapper {
  padding: var(--spacing-5xl) var(--spacing-lg);
  display: flex;
  justify-content: center;
  background-color: white;
}

.teamContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  gap: var(--spacing-4xl);
}

.teamListContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.teamMemberCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.teamMemberImage {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
  object-position: center;
}

/* Common */
.aboutTitle {
  font-size: 42px !important;
}

@media screen and (max-width: 1024px) {
  .teamListContainer {
    grid-template-columns: repeat(auto-fill, minmax(auto, 1fr));
  }

  .teamMemberImage {
    width: 150px;
    height: 150px;
  }
}

@media screen and (max-width: 900px) {
  .problemWrapper {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .teamWrapper {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }

  .teamContainer {
    gap: var(--spacing-2xl);
  }
}

@media screen and (max-width: 750px) {
  .problemContainer {
    flex-direction: column;
    gap: var(--spacing-xl);
  }

  .problemDetails {
    max-width: 100%;
  }

  .childrenImage {
    max-width: 100%;
    margin-right: -24px;
  }

  .teamContainer {
    gap: var(--spacing-2xl);
  }
}

@media screen and (max-width: 480px) {
  .aboutTitle {
    font-size: 30px;
  }
}
