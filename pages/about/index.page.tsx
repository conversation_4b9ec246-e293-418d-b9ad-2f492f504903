import Image from 'next/image';
import React from 'react';

import WebsiteLayout from '@/components/Layouts/WebsiteLayout/WebsiteLayout';
import Text from '@/components/Text/Text';
import Hero from '@/components/WebsiteSections/Hero/Hero';

import styles from './AboutUsPage.module.css';

const TEAM_MEMBERS = [
  {
    name: '<PERSON>',
    role: 'CEO',
    image: 'team1.png',
  },
  {
    name: '<PERSON> blue',
    role: 'CEO',
    image: 'team2.png',
  },
  {
    name: '<PERSON> white',
    role: 'CEO',
    image: 'team3.png',
  },
  {
    name: '<PERSON> red',
    role: 'CEO',
    image: 'team4.png',
  },
];

const AboutPage = () => {
  return (
    <WebsiteLayout>
      <Hero page="about" />

      {/* Problem section */}
      <div className={styles.problemWrapper}>
        <div className={styles.problemContainer}>
          <div className={styles.problemDetails}>
            <Text
              transKey="problem-solves-title"
              type="h2"
              fw={400}
              className={styles.aboutTitle}
              mt={2}
              color="white"
            />

            <Text
              transKey="problem-solves-desc"
              mb={20}
              color="white"
              type="subTitle1"
              lh={1.4}
            />
          </div>

          <div className={styles.childrenImageWrapper}>
            <Image
              src="/images/about_us_kids_no_bg.webp"
              alt="e-learning"
              width={450}
              height={500}
              draggable={false}
              className={styles.childrenImage}
            />
          </div>
        </div>
      </div>

      {/* About us section */}
      <div className={styles.teamWrapper}>
        <div className={styles.teamContainer}>
          <Text
            transKey="meet-the-team"
            type="h2"
            fw={400}
            className={styles.aboutTitle}
            mt={2}
          />

          <div className={styles.teamListContainer}>
            {TEAM_MEMBERS.map((member) => (
              <div key={member.name} className={styles.teamMemberCard}>
                <div>
                  <Image
                    src={`/images/team/${member.image}`}
                    alt={member.name}
                    width={200}
                    height={200}
                    draggable={false}
                    className={styles.teamMemberImage}
                  />
                </div>

                <Text
                  untranslatedText={member.name}
                  type="h3"
                  fw={400}
                  mb={20}
                  mt={30}
                />

                <Text untranslatedText={member.role} type="subTitle" fw={400} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </WebsiteLayout>
  );
};

export default AboutPage;
