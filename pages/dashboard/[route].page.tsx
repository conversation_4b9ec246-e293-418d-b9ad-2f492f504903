import { useRouter } from 'next/router';
import { JSX } from 'react';

import { APP_LAYOUT_ROUTES } from '@/common/routes';
import AppLayout from '@/components/Layouts/AppLayout/AppLayout';
import PageNotFound from '@/components/PageNotFound/PageNotFound';
import { CartProvider } from '@/context/CartProvider';
import { ClassesProvider } from '@/context/ClassesProvider';
import { ExportTestDataProvider } from '@/context/ExportTestDataProvider';
import { GroupsProvider } from '@/context/GroupsProvider';
import { PurchasesProvider } from '@/context/PurchasesProvider';
import { TestsProvider } from '@/context/TestsProvider';
import { UserProvider } from '@/context/UserProvider';
import Admin from '@/features/Admin/Admin';
import Assistant from '@/features/Assistant/Assistant';
import CertificationTests from '@/features/CertificationTests/CertificationTests';
import Docs from '@/features/Docs/Docs';
import Elearning from '@/features/Elearning/Elearning';
import Store from '@/features/Store/Store';
import Students from '@/features/Students/Students';
import UserProfile from '@/features/UserProfile/UserProfile';
import { DashboardRoutesType } from '@/types/common';

const renderSelectedRoute = (selectedRoute: DashboardRoutesType) => {
  switch (selectedRoute) {
    case APP_LAYOUT_ROUTES.CERTIFICATION_TESTS:
      return (
        <ClassesProvider>
          <GroupsProvider>
            <CertificationTests />
          </GroupsProvider>
        </ClassesProvider>
      );

    case APP_LAYOUT_ROUTES.E_LEARNING:
      return <Elearning />;

    case APP_LAYOUT_ROUTES.STUDENTS:
      return (
        <ClassesProvider>
          <GroupsProvider>
            <Students />
          </GroupsProvider>
        </ClassesProvider>
      );

    case APP_LAYOUT_ROUTES.SCHOOL:
      return (
        <ClassesProvider>
          <GroupsProvider>
            <Students />
          </GroupsProvider>
        </ClassesProvider>
      );

    case APP_LAYOUT_ROUTES.STORE:
      return <Store />;

    case APP_LAYOUT_ROUTES.ASSISTANT:
      return <Assistant />;

    case APP_LAYOUT_ROUTES.DOCS:
      return <Docs />;

    case APP_LAYOUT_ROUTES.PROFILE:
      return <UserProfile />;

    case APP_LAYOUT_ROUTES.ADMIN:
      return <Admin />;

    default:
      return null;
  }
};

const renderCorrectPage = (selectedRoute: DashboardRoutesType) => (
  <UserProvider>
    <TestsProvider>
      <PurchasesProvider>
        <CartProvider>
          <ExportTestDataProvider>
            <AppLayout>{renderSelectedRoute(selectedRoute)}</AppLayout>
          </ExportTestDataProvider>
        </CartProvider>
      </PurchasesProvider>
    </TestsProvider>
  </UserProvider>
);

const DashboardRoutes = (): JSX.Element | null => {
  const router = useRouter();
  const selectedRoute = router.query.route as DashboardRoutesType;

  const isRouteSupported =
    Object.values(APP_LAYOUT_ROUTES).includes(selectedRoute);

  if (!router.isReady) return null;

  if (!isRouteSupported) return <PageNotFound />;

  return renderCorrectPage(selectedRoute);
};

export default DashboardRoutes;
