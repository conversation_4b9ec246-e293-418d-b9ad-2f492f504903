import { Box, em } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';

import Card from '@/components/Card/Card';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import Text from '@/components/Text/Text';

import LoginForm from './LoginForm';

const Login = (): JSX.Element => {
  const isMobile = useMediaQuery(`(max-width: ${em(575)})`);

  return (
    <BaseLayout>
      <Box w={isMobile ? '100%' : 500}>
        <Card
          size={isMobile ? 'xl' : '3xl'}
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="white"
        >
          <Text
            transKey="login"
            type={isMobile ? 'h4' : 'h3'}
            mb={40}
            color="black"
            fw={250}
          />

          <LoginForm />
        </Card>
      </Box>
    </BaseLayout>
  );
};

export default Login;
