import { zodResolver } from '@hookform/resolvers/zod';
import { PasswordInput, Popover, Space, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { AUTH_ERRORS } from '@/common/errors';
import { FULL_DASHBOARD_ROUTES, PUBLIC_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import AUTH from '@/services/auth';
import { LoginUserFormValuesType } from '@/types/common';
import { LOGIN_USER_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import c from '../auth.module.css';

const DEFAULT_FORM_VALUES = {
  email: '',
  password: '',
};

const LoginForm = (): JSX.Element => {
  const { t } = useTranslation();
  const router = useRouter();
  const [showErrorAlert, setShowErrorAlert] = useState(false);

  const loginProcessMutation = useMutation({
    mutationFn: AUTH.LOGIN_USER,
    onError: (error) => {
      if (error.message === AUTH_ERRORS.INVALID_USERNAME_OR_PASSWORD) {
        setShowErrorAlert(true);
        return;
      }

      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: () => {
      router.push(FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS);
    },
  });

  const onSubmit = async (values: LoginUserFormValuesType) => {
    const { email, password } = values;

    if (email && password) {
      loginProcessMutation.mutate({ email, password });
    }
  };

  const {
    formState: { errors },
    handleSubmit,
    register,
  } = useForm<LoginUserFormValuesType>({
    resolver: zodResolver(LOGIN_USER_FORM_SCHEMA),
    defaultValues: DEFAULT_FORM_VALUES,
    mode: 'onSubmit',
  });

  return (
    <form className={c['auth-form']} onSubmit={handleSubmit(onSubmit)}>
      <TextInput
        placeholder={t('email')}
        mb={16}
        error={t(errors.email?.message || '')}
        {...register('email')}
      />

      <Popover
        width="target"
        position="bottom"
        withArrow
        shadow="md"
        opened={showErrorAlert}
        closeOnEscape
        onClose={() => setShowErrorAlert(false)}
      >
        <Popover.Target>
          <PasswordInput
            placeholder={t('password')}
            mb={16}
            error={t(errors.password?.message || '')}
            {...register('password')}
            onChange={(e) => {
              register('password').onChange(e);
              setShowErrorAlert(false);
            }}
          />
        </Popover.Target>

        <Popover.Dropdown>
          <Text
            transKey="invalid-username-or-password"
            type="body2"
            color="danger"
          />
        </Popover.Dropdown>
      </Popover>

      <span className={c['action-link']}>
        <Text
          transKey="forgot_your_password"
          type="body3"
          isBold
          color="blue"
          onClick={() => router.push(PUBLIC_ROUTES.FORGOT_PASSWORD)}
        />
      </span>

      <Space mb={12} />

      <Button
        transKey="login"
        type="submit"
        isLoading={loginProcessMutation.isPending}
        onClick={handleSubmit(onSubmit)}
      />
    </form>
  );
};

export default LoginForm;
