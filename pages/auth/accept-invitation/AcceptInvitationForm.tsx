import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Space, Tooltip } from '@mantine/core';
import { notifications, showNotification } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { JSX, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { PASSWORD_REQUIREMENTS } from '@/common/consts';
import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import AUTH from '@/services/auth';
import { AcceptInvitationFormValuesType } from '@/types/common';
import { ACCEPT_INVITATION_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import PasscodeInput from '../../../components/PasscodeInput/PasscodeInput';
import c from '../auth.module.css';

const CompleteRegistrationForm = (): JSX.Element => {
  const { t } = useTranslation();
  const router = useRouter();
  const [arePasswordRequirementsMet, setArePasswordRequirementsMet] =
    useState(false);
  const invitationId = router.query.invitation as string;

  const {
    control,
    formState: { errors },
    handleSubmit,
    watch,
  } = useForm<AcceptInvitationFormValuesType>({
    resolver: zodResolver(ACCEPT_INVITATION_FORM_SCHEMA),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onSubmit',
  });

  const isSubmitButtonDisabled =
    !arePasswordRequirementsMet ||
    watch('newPassword') !== watch('confirmPassword');

  const acceptInvitationProcessMutation = useMutation({
    mutationFn: AUTH.ACCEPT_USER_INVITATION,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: () => {
      router.push(FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS);
    },
  });

  const onSubmit: SubmitHandler<AcceptInvitationFormValuesType> = (data) => {
    if (!invitationId) {
      showNotification({
        title: t('invitation-id-not-found'),
        message: '',
        color: 'red',
      });
    }

    acceptInvitationProcessMutation.mutate({
      password: data.newPassword,
      invitationId,
    });
  };

  return (
    // check on submit might not needed
    <form className={c['auth-form']} onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="newPassword"
        control={control}
        rules={{ required: t('new-password-required') }}
        render={({ field }) => (
          <PasscodeInput
            placeholderText={t('new-password')}
            value={field.value}
            onChange={(v, meetsRequirements) => {
              field.onChange(v);
              setArePasswordRequirementsMet(meetsRequirements);
            }}
            requirements={PASSWORD_REQUIREMENTS}
            error={t(errors.newPassword?.message || '')}
          />
        )}
      />

      <Space mb={32} />

      <Controller
        name="confirmPassword"
        control={control}
        rules={{ required: t('confirm-new-password-required') }}
        render={({ field }) => (
          <PasscodeInput
            placeholderText={t('confirm-new-password')}
            value={field.value}
            type="confirm-password"
            onChange={(v) => {
              field.onChange(v);
            }}
            requirements={[
              {
                re: new RegExp(
                  `^${watch('newPassword')?.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') || '#'}$`
                ),
                label: 'matches-new-password',
              },
            ]}
            error={t(errors.confirmPassword?.message || '')}
          />
        )}
      />

      <Space mb={64} />

      {/* TODO check type submit might not needed */}
      <Tooltip
        label="Please provide correct information on the above fields"
        disabled={!isSubmitButtonDisabled}
      >
        <Box>
          <Button
            transKey="sign-up"
            type="submit"
            hasFullWidth
            isLoading={acceptInvitationProcessMutation.isPending}
            isDisabled={isSubmitButtonDisabled}
          />
        </Box>
      </Tooltip>
    </form>
  );
};

export default CompleteRegistrationForm;
