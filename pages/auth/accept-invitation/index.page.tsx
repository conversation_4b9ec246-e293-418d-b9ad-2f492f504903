import { Box, em } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { JSX } from 'react';

import Card from '@/components/Card/Card';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import Text from '@/components/Text/Text';

import CompleteRegistrationForm from './AcceptInvitationForm';

const AcceptInvitation = (): JSX.Element => {
  const isMobile = useMediaQuery(`(max-width: ${em(575)})`);

  return (
    <BaseLayout>
      <Box w={isMobile ? '100%' : 500}>
        <Card
          size={isMobile ? 'xl' : '3xl'}
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="white"
        >
          <Text
            transKey="complete-invitation"
            type={isMobile ? 'h4' : 'h3'}
            mb={40}
            color="black"
            fw={250}
          />

          <CompleteRegistrationForm />
        </Card>
      </Box>
    </BaseLayout>
  );
};

export default AcceptInvitation;
