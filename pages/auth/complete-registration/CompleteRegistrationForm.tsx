import { zodResolver } from '@hookform/resolvers/zod';
import { Space, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { PASSWORD_REQUIREMENTS } from '@/common/consts';
import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import AUTH from '@/services/auth';
import { CompleteUserRegistrationFormValuesType } from '@/types/common';
import { COMPLETE_USER_REGISTRATION_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import PasscodeInput from '../../../components/PasscodeInput/PasscodeInput';
import c from '../auth.module.css';

const INITIAL_SIGN_UP_FORM_VALUES = {
  email: '',
  temporaryPassword: '',
  newPassword: '',
  confirmPassword: '',
};

const CompleteRegistrationForm = (): JSX.Element => {
  const { t } = useTranslation();
  const router = useRouter();
  const [arePasswordRequirementsMet, setArePasswordRequirementsMet] =
    useState(false);

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<CompleteUserRegistrationFormValuesType>({
    resolver: zodResolver(COMPLETE_USER_REGISTRATION_FORM_SCHEMA),
    defaultValues: INITIAL_SIGN_UP_FORM_VALUES,
    mode: 'onSubmit',
  });

  const isSubmitButtonDisabled =
    !arePasswordRequirementsMet ||
    watch('newPassword') !== watch('confirmPassword') ||
    !watch('email') ||
    !watch('temporaryPassword');

  const signupProcessMutation = useMutation({
    mutationFn: AUTH.COMPLETE_USER_REGISTRATION,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: () => {
      router.push(FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS);
    },
  });

  const onSubmit: SubmitHandler<CompleteUserRegistrationFormValuesType> = (
    data
  ) => {
    signupProcessMutation.mutate(data);
  };

  return (
    // check on submit might not needed
    <form className={c['auth-form']} onSubmit={handleSubmit(onSubmit)}>
      <TextInput
        mb={16}
        placeholder={t('email')}
        error={t(errors.email?.message || '')}
        {...register('email')}
      />

      <TextInput
        placeholder={t('temp-password')}
        mb={16}
        error={t(errors.temporaryPassword?.message || '')}
        {...register('temporaryPassword')}
      />

      <span className={c['action-link']}>
        <Text
          transKey="resend-temporary-password"
          type="body3"
          isBold
          color="blue"
          // onClick={() => console.log('i will resend the password')}
        />
      </span>

      <Controller
        name="newPassword"
        control={control}
        rules={{ required: t('new-password-required') }}
        render={({ field }) => (
          <PasscodeInput
            placeholderText={t('new-password')}
            value={field.value}
            onChange={(v, meetsRequirements) => {
              field.onChange(v);
              setArePasswordRequirementsMet(meetsRequirements);
            }}
            requirements={PASSWORD_REQUIREMENTS}
            error={t(errors.newPassword?.message || '')}
          />
        )}
      />

      <Space mb={16} />

      <Controller
        name="confirmPassword"
        control={control}
        rules={{ required: t('confirm-new-password-required') }}
        render={({ field }) => (
          <PasscodeInput
            placeholderText={t('confirm-new-password')}
            value={field.value}
            type="confirm-password"
            onChange={(v) => {
              field.onChange(v);
            }}
            requirements={[
              {
                re: new RegExp(
                  `^${watch('newPassword')?.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') || '#'}$`
                ),
                label: 'matches-new-password',
              },
            ]}
            error={t(errors.confirmPassword?.message || '')}
          />
        )}
      />

      <Space mb={64} />

      {/* todo check type submit might not needed */}
      <Button
        transKey="sign-up"
        type="submit"
        onClick={handleSubmit(onSubmit)}
        isLoading={signupProcessMutation.isPending}
        isDisabled={isSubmitButtonDisabled}
      />
    </form>
  );
};

export default CompleteRegistrationForm;
