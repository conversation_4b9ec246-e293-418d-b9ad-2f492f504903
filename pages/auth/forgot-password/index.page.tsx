import { Box, em, Flex } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useState } from 'react';

import Card from '@/components/Card/Card';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import Text from '@/components/Text/Text';

import ForgotPasswordForm from './ForgotPasswordForm';

const ResetPassword = (): JSX.Element => {
  const isMobile = useMediaQuery(`(max-width: ${em(575)})`);
  const [isEmailSent, setIsEmailSent] = useState(false);

  const handleEmailSent = () => {
    setIsEmailSent(true);
  };

  return (
    <BaseLayout>
      <Box w={isMobile ? '100%' : 500}>
        <Card
          size={isMobile ? 'xl' : '3xl'}
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="white"
        >
          {isEmailSent ? (
            <Box h="100%" mih={333}>
              <Text
                transKey="email-sent"
                type={isMobile ? 'h4' : 'h3'}
                mb={20}
                color="black"
                fw={250}
              />

              <Flex align="center" h="100%" mih={275}>
                <Text
                  transKey="email-sent-instructions"
                  type="h4"
                  fw={400}
                  mb={40}
                  color="black"
                />
              </Flex>
            </Box>
          ) : (
            <>
              <Text
                transKey="password-reset"
                type={isMobile ? 'h4' : 'h3'}
                mb={20}
                color="black"
                fw={250}
              />
              <Text
                transKey="forgot-password-instructions"
                type="body2"
                mb={40}
                color="black"
              />
              <ForgotPasswordForm handleEmailSent={handleEmailSent} />
            </>
          )}
        </Card>
      </Box>
    </BaseLayout>
  );
};

export default ResetPassword;
