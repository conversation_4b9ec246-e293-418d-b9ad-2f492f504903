/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-console */
import { zodResolver } from '@hookform/resolvers/zod';
import { Space, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import Button from '@/components/Button/Button';
import AUTH from '@/services/auth';

import c from '../auth.module.css';

const DEFAULT_FORM_VALUES = {
  email: '',
};

const FORGOT_PASSWORD_FORM_SCHEMA = z.object({
  email: z.string().toLowerCase().min(1, 'email-required').email({
    message: 'email-invalid',
  }),
});

type ForgotPasswordValuesType = z.infer<typeof FORGOT_PASSWORD_FORM_SCHEMA>;

const ForgotPasswordForm = ({
  handleEmailSent,
}: {
  handleEmailSent: () => void;
}): JSX.Element => {
  const { t } = useTranslation();

  const {
    formState: { errors },
    handleSubmit,
    register,
  } = useForm<ForgotPasswordValuesType>({
    resolver: zodResolver(FORGOT_PASSWORD_FORM_SCHEMA),
    defaultValues: DEFAULT_FORM_VALUES,
    mode: 'onSubmit',
  });

  const forgotPassword = useMutation({
    mutationFn: AUTH.FORGOT_PASSWORD,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: () => {
      handleEmailSent();
    },
  });

  const onSubmit: SubmitHandler<ForgotPasswordValuesType> = (data) => {
    if (data.email) {
      forgotPassword.mutate({ email: data.email });
    }
  };

  return (
    <form className={c['auth-form']} onSubmit={handleSubmit(onSubmit)}>
      <TextInput
        {...register('email')}
        placeholder={t('email')}
        mb={16}
        error={t(`${errors.email?.message || ''}`)}
      />

      <Space mb={64} />

      <Button
        transKey="send-email-capital"
        type="submit"
        onClick={handleSubmit(onSubmit)}
        isLoading={forgotPassword.isPending}
      />
    </form>
  );
};

export default ForgotPasswordForm;
