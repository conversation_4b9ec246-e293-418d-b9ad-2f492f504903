import { Box, em, Flex } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useState } from 'react';

import Card from '@/components/Card/Card';
import BaseLayout from '@/components/Layouts/BaseLayout/BaseLayout';
import Text from '@/components/Text/Text';

import ResetPasswordForm from './ResetPasswordForm';

const ResetPassword = (): JSX.Element => {
  const isMobile = useMediaQuery(`(max-width: ${em(575)})`);

  return (
    <BaseLayout>
      <Box w={isMobile ? '100%' : 500}>
        <Card
          size={isMobile ? 'xl' : '3xl'}
          radius="xs"
          borderColor="gray100"
          shadow="xs"
          bg="white"
        >
          <>
            <Text
              transKey="enter-new-password"
              type={isMobile ? 'h4' : 'h3'}
              mb={40}
              color="black"
              fw={250}
            />

            <ResetPasswordForm />
          </>
        </Card>
      </Box>
    </BaseLayout>
  );
};

export default ResetPassword;
