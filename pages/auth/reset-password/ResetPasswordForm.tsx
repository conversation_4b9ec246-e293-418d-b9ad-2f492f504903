import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Space, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { JSX, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { PASSWORD_REQUIREMENTS } from '@/common/consts';
import { PUBLIC_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import PasscodeInput from '@/components/PasscodeInput/PasscodeInput';
import AUTH from '@/services/auth';

import c from '../auth.module.css';

const DEFAULT_FORM_VALUES = {
  newPassword: '',
  confirmPassword: '',
};

const RESET_PASSWORD_FORM_SCHEMA = z.object({
  newPassword: z.string().min(1, 'new-password-required'),
  confirmPassword: z.string().min(1, 'confirm-new-password-required'),
});

type ResetPasswordValuesType = z.infer<typeof RESET_PASSWORD_FORM_SCHEMA>;

const ResetPasswordForm = (): JSX.Element => {
  const { t } = useTranslation();
  const router = useRouter();
  // get token from url
  const { token } = router.query as { token: string };
  const [arePasswordRequirementsMet, setArePasswordRequirementsMet] =
    useState(false);

  const {
    control,
    formState: { errors },
    handleSubmit,
    watch,
  } = useForm<ResetPasswordValuesType>({
    resolver: zodResolver(RESET_PASSWORD_FORM_SCHEMA),
    defaultValues: DEFAULT_FORM_VALUES,
    mode: 'onSubmit',
  });

  const isSubmitButtonDisabled =
    !arePasswordRequirementsMet ||
    watch('newPassword') !== watch('confirmPassword');

  const resetPassword = useMutation({
    mutationFn: AUTH.RESET_PASSWORD,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: () => router.push(PUBLIC_ROUTES.LOGIN),
  });

  const onSubmit: SubmitHandler<ResetPasswordValuesType> = (data) => {
    if (!isSubmitButtonDisabled && token) {
      resetPassword.mutate({ password: data.newPassword, token });
    }
  };

  return (
    <form className={c['auth-form']} onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="newPassword"
        control={control}
        rules={{ required: t('new-password-required') }}
        render={({ field }) => (
          <PasscodeInput
            placeholderText={t('new-password')}
            value={field.value}
            onChange={(v, meetsRequirements) => {
              field.onChange(v);
              setArePasswordRequirementsMet(meetsRequirements);
            }}
            requirements={PASSWORD_REQUIREMENTS}
            error={t(errors.newPassword?.message || '')}
          />
        )}
      />

      <Space mb={24} />

      <Controller
        name="confirmPassword"
        control={control}
        rules={{ required: t('confirm-new-password-required') }}
        render={({ field }) => (
          <PasscodeInput
            placeholderText={t('confirm-new-password')}
            value={field.value}
            type="confirm-password"
            onChange={(v) => {
              field.onChange(v);
            }}
            requirements={[
              {
                re: new RegExp(
                  `^${watch('newPassword')?.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') || '#'}$`
                ),
                label: 'matches-new-password',
              },
            ]}
            error={t(errors.confirmPassword?.message || '')}
          />
        )}
      />

      <Space mb={48} />

      <Button
        transKey="reset-capital"
        type="submit"
        onClick={handleSubmit(onSubmit)}
        isLoading={resetPassword.isPending}
        isDisabled={isSubmitButtonDisabled}
      />
    </form>
  );
};

export default ResetPasswordForm;
