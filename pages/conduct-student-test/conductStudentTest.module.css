.wrapper {
  height: 100vh;
  background: linear-gradient(136.27deg, #1590c1 9.59%, #3a9ea7 56.18%);
  padding: var(--spacing-xl);
  overflow: hidden;
}

.elementsWrapper {
  max-width: 1040px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
}

@media (max-width: 1040px) {
  .elementsWrapper {
    justify-content: center;
    flex-wrap: wrap;
  }
}

.cardWrapper {
  max-width: 610px;
  max-height: 440px;
  display: flex;

  @media screen and (max-width: 575px) {
    max-height: 500px;
  }
}

.baseWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.promoWrapper {
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

@media (max-width: 1040px) {
  .promoWrapper {
    align-items: center;
  }

  .promoWrapper h2 {
    text-align: center !important;
  }
}

.divider {
  margin: 24px 0;
  width: 100px;
  background: var(--color-white);
  opacity: 0.4;
}

@media (max-width: 1040px) {
  .divider {
    width: 100%;
    margin: 0 24px;
  }
}

@media (max-width: 575px) {
  .divider {
    margin: 16px 0;
  }
}

.pinWrapper {
  height: 100px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 8px;

  @media screen and (max-width: 627px) {
    flex-direction: row;
  }
}

.codeError {
  color: var(--color-error);
}

.pinInput {
  height: 70px;
  width: 70px;

  @media screen and (max-width: 627px) {
    width: 100% !important;
    height: 100% !important;
  }
}

.baseInput {
  height: 70px !important;
  width: 70px !important;

  @media screen and (max-width: 627px) {
    width: 100% !important;
    height: 100% !important;
    max-height: 60px !important;
    max-width: 60px !important;
    padding: 0 !important;
  }
}

.gradeWrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  position: absolute;
  top: -50px;
  right: -35px;
  padding-top: 4pt;
  padding-bottom: 2px;
}

.gradeWrapper h4 {
  font-size: 21px !important;
}

.welcomeWrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.welcomeTitle {
  font-size: 54px;
}
