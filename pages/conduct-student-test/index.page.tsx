import { Box, Divider, Flex, PinInput, Transition } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { AUTH_ERRORS, GLOBAL_ERRORS } from '@/common/errors';
import { getStudentsDisplayName } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import ConductSubTestsModal from '@/features/ConductSubTestsModal/ConductSubTestsModal';
import request from '@/modules/api';
import AUTH from '@/services/auth';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';

import s from './conductStudentTest.module.css';

type WelcomeDetailsType = {
  studentName: string;
  welcomeMessage: string;
  testName?: string;
  testGrade: string;
  schoolGrade: string;
};

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const WELCOME_DETAILS_DEFAULTS = {
  studentName: '',
  welcomeMessage: '',
  testName: '',
  testGrade: '',
  schoolGrade: '',
};

// I can separate the Input button with the Login logic and also the
// actions with the start continue button after the login

const ConductSessionTest = (): JSX.Element => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const router = useRouter();

  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');

  const [sessionId, setSessionId] = useState('');
  const [sessionState, setSessionState] = useState<
    'active' | 'in-progress' | 'scheduled' | 'expired' | 'completed'
  >('active');

  // TODO : CHECK Why those status are not used : SCHEDULED, EXPIRED - and - what to do with them

  const [isValidToStartSession, setIsValidToStartSession] = useState(false);
  const [isValidToRedirect, setIsValidToRedirect] = useState(false);
  const [hasExitedTransition, setHasExitedTransition] = useState(false);

  const [welcomeDetails, setWelcomeDetails] = useState<WelcomeDetailsType>(
    WELCOME_DETAILS_DEFAULTS
  );

  const addSessionIdToURl = (ID: string) => {
    if (router.asPath.includes('session')) return;

    const path = `${router.asPath.includes('?') ? `${router.asPath}&session=${ID}` : `${router.asPath}?session=${ID}`}`;
    router.push(path);
  };

  const startSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.START_TEST_SESSION,
    onSuccess: () => {
      addSessionIdToURl(sessionId);
      setSessionState('in-progress');

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });
    },
  });

  const continueSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.CONTINUE_TEST_SESSION,
  });

  // This gets called two Ways.
  // 1 : If the user use a test session code directly to start a test session.
  // 2 : If the user use a LOGIN code that exists under students table and card view list
  //     And the response of the login is "testing" meaning the student has a test to take and it will always be the latest one assigned
  //     Meaning the user will conduct a test
  const getStudentWelcomeDetailsMutation = useMutation({
    mutationFn: SESSIONS_ACTIONS.GET_STUDENT_WELCOME_DETAILS,
    onSuccess: (res) => {
      if (!res) return;

      setWelcomeDetails({
        studentName: getStudentsDisplayName({
          firstName: res.student.name,
          lastName: res.student?.surname,
          code: res.student?.code,
        }),
        welcomeMessage: res.welcome,
        testName: res.test || '',
        testGrade: res.grade || '',
        schoolGrade: res.student?.grade || '',
      });

      setIsValidToStartSession(true);
    },
  });

  // This gets called ONLY if a the LOGIN code is being used that exists under students table and card view list - as LOGIN (BE - names it "Enter")
  // AND the response of the login is "learning" - meaning the student has either Completed all the TESTS that we assigned or has no TESTS
  const getStudentLoginWelcomeDetailsMutation = useMutation({
    mutationFn: AUTH.GET_STUDENT_LOGIN_WELCOME_DETAILS,
    onSuccess: (res) => {
      if (!res) return;

      setWelcomeDetails({
        studentName: getStudentsDisplayName({
          firstName: res.student.name,
          lastName: res.student?.surname,
          code: res.student?.code,
        }),
        welcomeMessage: res.welcome,
        testGrade: res.grade || '',
        schoolGrade: res.student?.grade || '',
      });

      setIsValidToRedirect(true);
    },
  });

  const loginStudent = useMutation({
    mutationFn: AUTH.LOGIN_STUDENT,
    onSuccess: (res) => {
      const sessionResponseState = res?.session?.state;
      const isSessionScheduledOrExpired =
        sessionResponseState === 'scheduled' ||
        sessionResponseState === 'expired';

      if (
        res?.session?.id &&
        res?.action === 'testing' &&
        !isSessionScheduledOrExpired
      ) {
        setSessionId(res.session.id);

        if (res.session.state !== 'active') {
          setSessionState(res.session.state);
        }

        getStudentWelcomeDetailsMutation.mutate(res.session.id);

        return;
      }

      if (res?.action === 'learning' || isSessionScheduledOrExpired) {
        getStudentLoginWelcomeDetailsMutation.mutate();
      }
    },
    onError: (error) => {
      if (error.message === AUTH_ERRORS.INVALID_SIX_DIGIT_CODE) {
        setCodeError('invalid-six-digit-test-code');
      }
    },
  });

  const redirectUserToMathProResearchPlatform = async () => {
    try {
      if (BASE_API_URL) {
        const response = (await request(
          `${BASE_API_URL}/${BASE_ENDPOINT_PATHS.INTEGRATIONS_MATHPRO_ELEARNING_URL_LINK_LOGIN}`
        )) as any;

        if (response.hasError) {
          throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
        }

        router.push(response?.data?.url);
      }
    } catch (error) {
      notifications.show({
        title: t('Error'),
        message: `${(error as Error)?.message || ''}`,
        color: 'red',
      });
    }
  };

  const hasUserFinishedLoginValidation =
    isValidToRedirect || isValidToStartSession;

  return (
    <Box className={`${s.wrapper} specialBackground`}>
      <div className={`${s.elementsWrapper}`}>
        <div className={s.promoWrapper}>
          <Icon name="LogoFullSvg" color="white" className={s.logo} />

          <Divider className={s.divider} />

          <Text
            transKey="student-login-promo-text"
            color="white"
            type="h2"
            fw={600}
            align="left"
          />
        </div>

        <Card className={s.cardWrapper} size="3xl" radius="xs" shadow="xs">
          {/* 6 DIGIT CODE */}
          <Transition
            mounted={!hasUserFinishedLoginValidation}
            transition="slide-left"
            duration={400}
            timingFunction="ease"
            onExit={() =>
              setTimeout(() => {
                setHasExitedTransition(true);
              }, 400)
            }
          >
            {(styles) => (
              <div style={styles} className={s.baseWrapper}>
                <Text transKey="enter-code" type="h2" color="black" fw={250} />

                <div className={s.pinWrapper}>
                  <PinInput
                    length={6}
                    onChange={(value) => setCode(value)}
                    classNames={{
                      pinInput: s.pinInput,
                      input: s.baseInput,
                    }}
                    error={Boolean(codeError)}
                  />

                  <p className={s.codeError}>{t(codeError)}</p>
                </div>

                <Button
                  hasFullWidth
                  size="lg"
                  transKey="login"
                  isLoading={
                    loginStudent.isPending ||
                    getStudentLoginWelcomeDetailsMutation.isPending ||
                    getStudentWelcomeDetailsMutation.isPending
                  }
                  isDisabled={
                    loginStudent.isPending || !code || code.length < 6
                  }
                  onClick={() => loginStudent.mutate(code)}
                />
              </div>
            )}
          </Transition>

          {/* WELCOME STUDENT START SUBTEST */}
          <Transition
            mounted={hasUserFinishedLoginValidation && hasExitedTransition}
            transition="slide-left"
            duration={400}
            timingFunction="ease"
          >
            {(styles) => (
              <div style={styles} className={s.welcomeWrapper}>
                <div className={s.gradeWrapper}>
                  {welcomeDetails.testGrade && (
                    <Text
                      untranslatedText={welcomeDetails.testGrade}
                      type="h4"
                      color="black"
                      fw={500}
                    />
                  )}

                  {welcomeDetails.schoolGrade && (
                    <Text
                      untranslatedText={welcomeDetails.schoolGrade}
                      type="h4"
                      color="black"
                      fw={500}
                    />
                  )}
                </div>

                {sessionState !== 'completed' && (
                  <Text
                    untranslatedText={welcomeDetails.welcomeMessage}
                    color="black"
                    className={s.welcomeTitle}
                    fw={200}
                    mt={12}
                  />
                )}

                <Text
                  untranslatedText={
                    welcomeDetails?.studentName
                      ? welcomeDetails.studentName
                      : '-'
                  }
                  type="h3"
                  color="black"
                  isBold
                  fw={600}
                  mt={sessionState === 'completed' ? 24 : 0}
                />

                {welcomeDetails.testName && sessionState !== 'completed' && (
                  <Text
                    untranslatedText={welcomeDetails?.testName}
                    type="h4"
                    color="black"
                    mb={12}
                  />
                )}

                {sessionState === 'completed' && (
                  <Flex direction="column" mb={100}>
                    <Text
                      transKey="successfully-completed-test-message"
                      type="h4"
                      color="black"
                      align="center"
                      mb={12}
                    />

                    <Text
                      transKey="close-browser-tab-message"
                      type="h4"
                      color="black"
                      align="center"
                    />
                  </Flex>
                )}

                {sessionState !== 'completed' && (
                  <Button
                    hasFullWidth
                    size="lg"
                    transKey={
                      isValidToStartSession
                        ? `${sessionState === 'in-progress' ? 'continue' : 'start-capital'}`
                        : 'start-practice'
                    }
                    isLoading={startSession.isPending}
                    // isDisabled={sessionState === 'completed'}
                    onClick={() => {
                      if (isValidToRedirect && BASE_API_URL) {
                        redirectUserToMathProResearchPlatform();
                        return;
                      }

                      if (sessionState === 'in-progress') {
                        addSessionIdToURl(sessionId);
                        continueSession.mutate(sessionId);
                      } else if (sessionState === 'active') {
                        startSession.mutate(sessionId);
                      }
                    }}
                  />
                )}
              </div>
            )}
          </Transition>
        </Card>

        <ConductSubTestsModal
          hasCloseButton={false}
          onCompleteTest={() => setSessionState('completed')}
        />
      </div>
    </Box>
  );
};

export default ConductSessionTest;
