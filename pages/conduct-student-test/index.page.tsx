import { Box, Divider, PinInput, Transition } from '@mantine/core';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MdLogout } from 'react-icons/md';

import { ENVIRONMENTS } from '@/common/consts';
import { AUTH_ERRORS, GLOBAL_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import ConductSubTestsModal from '@/features/ConductSubTestsModal/ConductSubTestsModal';
import request from '@/modules/api';
import AUTH from '@/services/auth';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import TESTS_SESSIONS_LISTING from '@/services/tests/sessions-listing';

import s from './conductStudentTest.module.css';

const ConductSessionTest = (): JSX.Element => {
  const queryClient = useQueryClient();
  const router = useRouter();

  const { t } = useTranslation();

  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');

  const [sessionState, setSessionState] = useState<
    'not-started' | 'in-progress' | 'completed'
  >('not-started');
  const [sessionId, setSessionId] = useState('');

  const [isValidToStartSession, setIsValidToStartSession] = useState(false);
  const [hasExitedTransition, setHasExitedTransition] = useState(false);

  const [welcomeDetails, setWelcomeDetails] = useState({
    studentName: '',
    welcomeMessage: '',
    testName: '',
    grade: '',
  });

  const addSessionIdToURl = (ID: string) => {
    if (router.asPath.includes('session')) return;

    const path = `${router.asPath.includes('?') ? `${router.asPath}&session=${ID}` : `${router.asPath}?session=${ID}`}`;
    router.push(path);
  };

  const startSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.START_TEST_SESSION,
    onSuccess: () => {
      addSessionIdToURl(sessionId);
      setSessionState('in-progress');

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });
    },
  });

  const continueSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.CONTINUE_TEST_SESSION,
  });

  const getStudentWelcomeDetailsMutation = useMutation({
    mutationFn: SESSIONS_ACTIONS.GET_STUDENT_WELCOME_DETAILS,
    onSuccess: (res) => {
      if (!res) return;

      setWelcomeDetails({
        studentName: `${res.student.name} ${res.student.surname || ' '}`,
        welcomeMessage: res.welcome,
        testName: res.test || '',
        grade: res.grade || '',
      });

      setIsValidToStartSession(true);
    },
  });

  const getSessionIdFromCodeMutation = useMutation({
    mutationFn: TESTS_SESSIONS_LISTING.GET_SESSION_ID_FROM_SESSION_CODE,
    onSuccess: (res) => {
      getStudentWelcomeDetailsMutation.mutate(res.id);

      if (res.state !== 'active') {
        setSessionState(res.state);
      }
      setSessionId(res.id);
    },
    onError: (error) => {
      if (error.message === AUTH_ERRORS.INVALID_SIX_DIGIT_CODE) {
        setCodeError('invalid-six-digit-test-code');
      }
    },
  });

  const loginStudent = useMutation({
    mutationFn: AUTH.LOGIN_STUDENT,
    onSuccess: () => getSessionIdFromCodeMutation.mutate(code),
    onError: (error) => {
      if (error.message === AUTH_ERRORS.INVALID_SIX_DIGIT_CODE) {
        setCodeError('invalid-six-digit-test-code');
      }
    },
  });

  const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

  const redirect = async () => {
    try {
      const response = await request(
        `${BASE_API_URL}/integrations/mathproresearch/url-link/login`
      );

      if (response.hasError) {
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }

      router.push(response?.data?.url);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Box className={`${s.wrapper} specialBackground`}>
      <div className={`${s.elementsWrapper}`}>
        <div className={s.promoWrapper}>
          <Icon name="LogoFullSvg" color="white" className={s.logo} />

          <Divider className={s.divider} />

          <Text
            transKey="student-login-promo-text"
            color="white"
            type="h2"
            fw={600}
            align="left"
          />
        </div>

        <Card className={s.cardWrapper} size="3xl" radius="xs" shadow="xs">
          {/* 6 DIGIT CODE */}
          <Transition
            mounted={!isValidToStartSession}
            transition="slide-left"
            duration={400}
            timingFunction="ease"
            onExit={() =>
              setTimeout(() => {
                setHasExitedTransition(true);
              }, 400)
            }
          >
            {(styles) => (
              <div style={styles} className={s.baseWrapper}>
                <Text transKey="login" type="h2" color="black" fw={250} />

                <div className={s.pinWrapper}>
                  <PinInput
                    length={6}
                    onChange={(value) => setCode(value)}
                    classNames={{
                      pinInput: s.pinInput,
                      input: s.baseInput,
                    }}
                    error={Boolean(codeError)}
                  />

                  <p className={s.codeError}>{t(codeError)}</p>
                </div>

                <Button
                  hasFullWidth
                  size="lg"
                  transKey="login"
                  isLoading={
                    loginStudent.isPending ||
                    getSessionIdFromCodeMutation.isPending ||
                    getStudentWelcomeDetailsMutation.isPending
                  }
                  isDisabled={
                    loginStudent.isPending || !code || code.length < 6
                  }
                  onClick={() => loginStudent.mutate(code)}
                />
              </div>
            )}
          </Transition>

          {/* WELCOME STUDENT START SUBTEST */}
          <Transition
            mounted={isValidToStartSession && hasExitedTransition}
            transition="slide-left"
            duration={400}
            timingFunction="ease"
          >
            {(styles) => (
              <div style={styles} className={s.welcomeWrapper}>
                {process.env.NEXT_PUBLIC_NODE_ENV !==
                  ENVIRONMENTS.PRODUCTION && (
                  <MdLogout
                    fontSize={28}
                    className={s.redirectIcon}
                    color="var(--color-blue)"
                    onClick={redirect}
                  />
                )}

                <div className={s.gradeWrapper}>
                  <Text
                    untranslatedText={welcomeDetails.grade}
                    type="h3"
                    color="black"
                    fw={500}
                  />
                </div>

                <Text
                  untranslatedText={welcomeDetails.welcomeMessage}
                  color="black"
                  className={s.welcomeTitle}
                  fw={200}
                  mt={12}
                />

                <Text
                  untranslatedText={welcomeDetails.studentName}
                  type="h3"
                  color="black"
                  isBold
                  fw={600}
                />

                <Text
                  untranslatedText={welcomeDetails?.testName}
                  type="h4"
                  color="black"
                  mb={12}
                />

                <Button
                  hasFullWidth
                  size="lg"
                  transKey="start-test"
                  isLoading={startSession.isPending}
                  isDisabled={sessionState === 'completed'}
                  onClick={() => {
                    if (sessionState === 'in-progress') {
                      addSessionIdToURl(sessionId);
                      continueSession.mutate(sessionId);
                    } else if (sessionState === 'not-started') {
                      startSession.mutate(sessionId);
                    }
                  }}
                />
              </div>
            )}
          </Transition>
        </Card>

        <ConductSubTestsModal
          hasCloseButton={false}
          onCompleteTest={() => setSessionState('completed')}
        />
      </div>
    </Box>
  );
};

export default ConductSessionTest;
