.whoIsSection {
  padding: var(--spacing-4xl) var(--spacing-lg);
  display: flex;
  justify-content: center;
  background-color: white;
}

.whoIsContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  gap: var(--spacing-lg);
}

.whoIsContent {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-lg);
}

.whoIsRow {
  display: flex;
  flex-direction: row;
  gap: var(--spacing-lg);
}

.learnMoreWrapper {
  margin-top: var(--spacing-md);
}

.whoIsDetailsWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;
  max-width: 390px;
}

.whoIsImage {
  width: 100%;
  max-width: 150px;
  min-width: 80px;
  height: auto;
}

.productsWrapper {
  background-color: rgba(24, 29, 28, 1);

  position: relative;
  height: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: var(--spacing-4xl) var(--spacing-lg);
}

.productsWrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background: url('../public/images/circles.svg') no-repeat right center;
  background-size: contain;
  z-index: 0;
}

.productsContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  z-index: 1;
}

.productsDetails {
  display: flex;
  flex-direction: column;
  max-width: 525px;
}

.buyProductsButtonsWrapper {
  display: flex;
  flex-direction: row;
  margin-top: var(--spacing-3xl);
  gap: var(--spacing-mdl);
  flex-wrap: wrap;
}

.priceWhite {
  border-right: 2px solid rgba(255, 255, 255, 0.5);
  padding-right: var(--spacing-sm);
  height: var(--spacing-md);
  display: flex;
  align-items: center;
}

.leftButtonWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-md);
  margin-right: var(--spacing-lg);
}

.buyButtonDetails {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.registrationOpeningSoon {
  letter-spacing: 2.5px !important;
  font-style: italic;
  font-size: larger;
}

.aboutTitle {
  font-size: 42px !important;
}

@media screen and (max-width: 1024px) {
  .whoIsContent {
    flex-wrap: wrap;
    gap: var(--spacing-3xl);
  }

  .whoIsRow {
    width: 100%;
  }

  .whoIsDetailsWrapper {
    max-width: 100%;
  }
}

@media screen and (max-width: 900px) {
  .aboutTitle {
    font-size: 32px;
  }

  .whoIsContent {
    flex-wrap: wrap;
    gap: var(--spacing-3xl);
  }

  .whoIsSection {
    padding: var(--spacing-3xl) var(--spacing-lg);
  }

  .whoIsImage {
    max-width: 103px;
    min-width: 80px;
  }
}
@media screen and (max-width: 861px) {
  .buyProductsButtonsWrapper {
    flex-direction: column;
    max-width: fit-content;
  }
}
@media screen and (max-width: 600px) {
  .productsWrapper::after {
    background-size: cover;
  }
}

@media screen and (max-width: 480px) {
  .aboutTitle {
    font-size: 30px;
  }

  .leftButtonWrapper {
    margin-right: var(--spacing-sm);
  }

  .starterPack {
    font-size: small;
  }

  .productName {
    font-size: small;
  }
}
