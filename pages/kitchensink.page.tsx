import { JSX, memo, useRef, useState } from 'react';

// Memoized Input Component to prevent unnecessary re-renders
// const InputField = memo(({ label, name, onChange, type = 'text' }: any) => {
//   console.log(`Rendering ${name}`); // This will help verify optimization
//   return (
//     <div>
//       <label htmlFor={name}>{label}</label>
//       <input id={name} name={name} type={type} onChange={onChange} />
//     </div>
//   );
// });

// const InputField = ({
//   label,
//   name,
//   onChange,
//   type = 'text',
// }: {
//   label: string;
//   name: string;
//   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
//   type?: string;
// }) => {
//   console.log(`Rendering ${name}`); // This will help verify optimization
//   return (
//     <div>
//       <label htmlFor={name}>{label}</label>
//       <input id={name} name={name} type={type} onChange={onChange} />
//     </div>
//   );
// };

// InputField.displayName = 'InputField';

const KitchenSink = (): JSX.Element => {
  // const formData = useRef<{
  //   firstName: string;
  //   lastName: string;
  //   email: string;
  // }>({
  //   firstName: '',
  //   lastName: '',
  //   email: '',
  // });

  const formData = useRef({
    firstName: 'takis',
    lastName: 'test',
    email: '<EMAIL>',
  });

  // const [hasError, setHasError] = useState(false);

  // const [formData, setFormData] = useState({
  //   firstName: 'takis',
  //   lastName: 'test',
  //   email: '<EMAIL>',
  // });

  // Single change handler using input name attribute
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // if (name === 'firstName' && value.trim() === '') {
    //   setHasError(true);
    // } else {
    //   setHasError(false);
    // }

    (formData.current as any)[name] = value;
  };

  // const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const { name, value } = e.target;

  //   // console.log('handleChange', name, value);
  //   setFormData((prev) => ({ ...prev, [name]: value }));
  // };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Submit logic here

    // if (formData.current.firstName.trim() === '') {
    //   setHasError(true);
    // }
  };

  return (
    <div
      style={{
        maxWidth: '700px',
        margin: 'auto',
        marginTop: '24px',
      }}
    >
      <form onSubmit={handleSubmit}>
        {/* <input type="text" name="name" /> */}

        {/* <InputField
          label="First Name"
          name="firstName"
          onChange={handleChange}
        /> */}

        <div>
          <label htmlFor="firstName">First Name</label>

          <input
            id="firstName"
            name="firstName"
            type="text"
            // value={formData.current.firstName}
            onChange={handleChange}
          />

          {/* {hasError && <p>Required {formData.current.firstName}</p>} */}
        </div>

        {/* <InputField label="Last Name" name="lastName" onChange={handleChange} /> */}
        <div>
          <label htmlFor="lastName">Last Name</label>

          <input
            id="lastName"
            name="lastName"
            type="text"
            // value={formData.current.lastName}
            onChange={handleChange}
          />
        </div>

        {/* <InputField
          label="Email"
          name="email"
          onChange={handleChange}
          type="email"
        /> */}

        <div>
          <label htmlFor="email">Email</label>

          <input
            id="email"
            name="email"
            type="email"
            // value={formData.current.email}
            onChange={handleChange}
          />
        </div>

        <button type="submit">Submit</button>
      </form>
    </div>
  );
};

export default KitchenSink;
