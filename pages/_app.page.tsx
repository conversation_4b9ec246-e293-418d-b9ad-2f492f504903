import '@mantine/core/styles.css';
import '@/styles/globals.css';
import '@mantine/notifications/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/dropzone/styles.css';
import '@mantine/nprogress/styles.css';
import '@mantine/tiptap/styles.css';

import { MantineProvider } from '@mantine/core';
import { Notifications, notifications } from '@mantine/notifications';
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import countries from 'i18n-iso-countries';
import { t } from 'i18next';
import type { AppProps } from 'next/app';
import { Manrope } from 'next/font/google';
import Head from 'next/head';
import router from 'next/router';
import { useEffect, useState } from 'react';

import { SUPPORTED_LANGUAGES } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import { PUBLIC_ROUTES } from '@/common/routes';
import BrowserCompatibility from '@/components/BrowserCompatibility/BrowserCompatibility';
import { BrowserCompatibilityProvider } from '@/context/BrowserCompatibilityProvider';
import { SupportedBillingCountriesProvider } from '@/context/SupportedBillingCountriesProvider';
import i18n from '@/i18n';
import { resolver, theme } from '@/styles/theming';

/*
 The font subsets defined by an array of string values with the names of each subset you would like to be preloaded.
 Fonts specified via subsets will have a link preload tag injected into the head.
 When the preload option is true, which is the default. Used in next/font/google.
*/

type ErrorNotificationsType = {
  [key: string]: {
    title: string;
    message: string;
  };
};

const manrope = Manrope({
  subsets: ['latin'],
  preload: true,
});

const errorNotifications: ErrorNotificationsType = {
  unauthorized: {
    title: 'unauthorized',
    message: 'unauthorized-message',
  },
  'server-error': {
    title: 'server-error',
    message: 'something-went-wrong',
  },
} as const;

const showToast = (errorMessage: string, queryKey: string) => {
  const title =
    // eslint-disable-next-line no-nested-ternary
    errorMessage === GLOBAL_ERRORS.ZOD_ERROR
      ? `${t('zod-error')} For query key : ${queryKey}`
      : errorMessage in errorNotifications
        ? t(errorNotifications[errorMessage].title)
        : t(errorNotifications[errorMessage].message);

  const message =
    errorMessage in errorNotifications
      ? t(errorNotifications[errorMessage].message)
      : '';

  notifications.show({
    title,
    message,
    color: 'red',
  });
};

const handleGlobalError = (errorMessage: string, queryKey: string = '') => {
  switch (errorMessage) {
    case GLOBAL_ERRORS.UNAUTHORIZED:
      showToast(GLOBAL_ERRORS.UNAUTHORIZED, queryKey);
      router.push(PUBLIC_ROUTES.LOGIN);
      break;
    case GLOBAL_ERRORS.SERVER_ERROR:
      showToast(GLOBAL_ERRORS.SERVER_ERROR, queryKey);
      // router.push(PUBLIC_ROUTES.LOGIN);
      break;
    case GLOBAL_ERRORS.ZOD_ERROR:
      showToast(GLOBAL_ERRORS.ZOD_ERROR, queryKey);
      break;
    default:

    // redirect to an error page or something
    // router.push(PUBLIC_ROUTES.LOGIN);
  }
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
  queryCache: new QueryCache({
    onError: (error, query) => {
      // console.log('query cache error', error);

      if (error.message) {
        handleGlobalError(error.message, query.queryKey.toString());
      }
    },
  }),
  mutationCache: new MutationCache({
    onError: (error, _, __, mutation) => {
      if (error.message) {
        handleGlobalError(error.message);
      }
    },
  }),
});

const LANGUAGES = Object.values(SUPPORTED_LANGUAGES).map(
  (lang) => lang.shortCode
);

const App = ({ Component, pageProps }: AppProps) => {
  const [isLoading, setIsLoading] = useState(true);

  const loadSupportedLanguagesJsonForCountriesTranslation = async () => {
    await Promise.all(
      LANGUAGES.map(async (lang) => {
        const langData = await import(`i18n-iso-countries/langs/${lang}.json`);
        countries.registerLocale(langData.default || langData);
      })
    );
  };

  useEffect(() => {
    loadSupportedLanguagesJsonForCountriesTranslation();

    i18n().then(() => setIsLoading(false));
  }, []);

  return (
    <>
      <Head>
        <title>MathPro</title>
      </Head>

      <MantineProvider
        theme={{
          ...theme,
          fontFamily: manrope.style.fontFamily,
        }}
        cssVariablesResolver={resolver}
      >
        <BrowserCompatibilityProvider>
          {isLoading ? null : (
            <main className={manrope.className}>
              <Notifications position="top-center" autoClose={5000} />

              <BrowserCompatibility />

              <QueryClientProvider client={queryClient}>
                <ReactQueryDevtools initialIsOpen={false} />
                <SupportedBillingCountriesProvider>
                  {/* <DatesProvider
                settings={{
                  locale: 'en',
                  firstDayOfWeek: 0,
                  weekendDays: [0],
                  timezone: 'GMT',
                }}
              > */}
                  <Component {...pageProps} />
                  {/* </DatesProvider> */}
                </SupportedBillingCountriesProvider>
              </QueryClientProvider>
            </main>
          )}
        </BrowserCompatibilityProvider>
      </MantineProvider>
    </>
  );
};

export default App;
