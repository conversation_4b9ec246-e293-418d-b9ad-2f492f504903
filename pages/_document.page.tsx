import { ColorSchemeScript } from '@mantine/core';
import { Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';

const Document = () => {
  return (
    <Html lang="en">
      <Script
        strategy="beforeInteractive"
        src="https://unpkg.com/css-vh-helper/dist/css-vh-helper.min.js"
      />

      <Head>
        <ColorSchemeScript data-mantine-color-scheme="light" />
        <link rel="preload" href="/images/bamboo.png" as="image" />
      </Head>

      <Main />
      <NextScript />
    </Html>
  );
};

export default Document;
