import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import USERS from '@/services/users';
import {
  DeepOptional,
  UserCertificationsType,
  UserDetailsType,
} from '@/types/common';

export const USER_ROLES: {
  [key: string]: UserDetailsType['roles'][number];
} = {
  TEACHER: 'teacher',
  SCHOOL_TEACHER: 'schoolTeacher',
  SCHOOL_ADMIN: 'schoolAdmin',
  ADMIN: 'admin',
  DEVELOPER: 'developer',
  STUDY_ADMIN: 'studyAdmin',
  RESEARCHER: 'researcher',
} as const;

type DefaultContextDataType = {
  isUserLoading: boolean;
  isUserFetching: boolean;
  user: UserDetailsType;
  userRoles: {
    isIndependentTeacher: boolean;
    isSchoolTeacher: boolean;
    isSchoolAdmin: boolean;
    isAdmin: boolean;
    isDeveloper: boolean;
    isStudyAdmin: boolean;
    isResearcher: boolean;
  };
  isSchoolRole: boolean;
  userCertificates: UserCertificationsType;
  updateUserProfile: (
    updatedProfile: DeepOptional<UserDetailsType['profile']>
  ) => void;
  invalidateUser: () => void;
};

const DEFAULT_USER: UserDetailsType = {
  id: '',
  username: '',
  roles: [],
  profile: {
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    dob: '',
    organisation: '',
    address: {
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postcode: '',
      country: '',
    },
    language: '',
    onboarded: false,
    certificates: [],
  },
};

const defaultContextData: DefaultContextDataType = {
  isUserLoading: true,
  isUserFetching: true,
  user: DEFAULT_USER,
  userRoles: {
    isStudyAdmin: false,
    isIndependentTeacher: false,
    isSchoolTeacher: false,
    isSchoolAdmin: false,
    isAdmin: false,
    isDeveloper: false,
    isResearcher: false,
  },
  isSchoolRole: false,
  userCertificates: {},
  updateUserProfile: () => {},
  invalidateUser: () => {},
};

const getUserCertificates = (
  certificates: UserDetailsType['profile']['certificates']
): UserCertificationsType =>
  certificates.reduce((acc: UserCertificationsType, certificate) => {
    acc[certificate.type] = {
      version: certificate.version,
      isCompleted: Boolean(certificate.progress?.completed),
      isNotBought: !certificate.progress,
      isInProgress: Boolean(certificate.progress),
      type: certificate.type,
    };
    return acc;
  }, {});

const getUserRoles = (supportedRoles: UserDetailsType['roles']) => {
  const userRoles = {
    isAdmin: supportedRoles.includes(USER_ROLES.ADMIN),
    isStudyAdmin: supportedRoles.includes(USER_ROLES.STUDY_ADMIN),
    isIndependentTeacher: supportedRoles.includes(USER_ROLES.TEACHER),
    isSchoolTeacher: supportedRoles.includes(USER_ROLES.SCHOOL_TEACHER),
    isSchoolAdmin: supportedRoles.includes(USER_ROLES.SCHOOL_ADMIN),
    isDeveloper: supportedRoles.includes(USER_ROLES.DEVELOPER),
    isResearcher: supportedRoles.includes(USER_ROLES.RESEARCHER),
  };

  if (
    supportedRoles.includes(USER_ROLES.SCHOOL_ADMIN) &&
    supportedRoles.includes(USER_ROLES.SCHOOL_TEACHER)
  )
    return {
      ...userRoles,
      isSchoolAdmin: true,
      isSchoolTeacher: false,
    };

  return userRoles;
};

const UserContextData = createContext(defaultContextData);

export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const queryClient = useQueryClient();
  const { i18n } = useTranslation();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data, error, isError, isFetching, isLoading } =
    useQuery<UserDetailsType | null>({
      queryFn: USERS.GET_USER_DETAILS,
      queryKey: [QUERY_KEYS.USER],
      staleTime: 1000 * 60 * 60 * 24,
    });

  // This code exists to redirect the user if we want back to the app in case the user visits the login page manually and he is already logged in
  // const setCookie = (name: string, value: string, days: number) => {
  //   let expires = '';

  //   if (days) {
  //     const date = new Date();
  //     date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  //     expires = `; expires=${date.toUTCString()}`;
  //   }
  //   document.cookie = `${name}=${value || ''}${expires}; path=/`;
  // };

  // if (isDashboardPage) {
  //   setCookie('isUserLoggedIn', JSON.stringify(Boolean(data?.id)), 1);
  // }

  // TODO : handle that we didn't get the user data

  const updateUserProfile = useCallback(
    (updatedProfile: DeepOptional<UserDetailsType['profile']>) => {
      queryClient.setQueryData(['user'], (old: UserDetailsType) => {
        return {
          ...old,
          profile: {
            ...old.profile,
            ...updatedProfile,
          },
        };
      });
    },
    [queryClient]
  );

  const invalidateUser = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.USER],
    });
  }, [queryClient]);

  const contextValue = useMemo(
    () => ({
      isUserLoading: isLoading,
      isUserFetching: isFetching,
      user: data || DEFAULT_USER,
      // userRoles: {
      //   isIndependentTeacher: data?.roles.includes(USER_ROLES.TEACHER) || false,
      //   isSchoolTeacher:
      //     data?.roles.includes(USER_ROLES.SCHOOL_TEACHER) || false,
      //   isSchoolAdmin: data?.roles.includes(USER_ROLES.SCHOOL_ADMIN) || false,
      // },
      userRoles: getUserRoles(data?.roles || []),
      userCertificates: data
        ? getUserCertificates(data.profile.certificates)
        : {},
      isSchoolRole:
        data?.roles.includes(USER_ROLES.SCHOOL_ADMIN) ||
        data?.roles.includes(USER_ROLES.SCHOOL_TEACHER) ||
        false,
      updateUserProfile,
      invalidateUser,
    }),
    [isLoading, isFetching, data, updateUserProfile, invalidateUser]
  );

  useEffect(() => {
    if (data?.profile.language && i18n.language !== data.profile.language) {
      i18n.changeLanguage(data.profile.language);
    }
  }, [data?.profile.language, i18n]);

  return (
    <UserContextData.Provider value={contextValue}>
      {children}
    </UserContextData.Provider>
  );
};

export const UserContext = () => {
  return useContext(UserContextData);
};
