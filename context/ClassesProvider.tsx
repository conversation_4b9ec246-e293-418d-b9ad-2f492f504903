/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { createContext, useContext, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import SCHOOL_CLASSES from '@/services/schools/classes';
import { ClassesFetchListType } from '@/types/common';

import { UserContext } from './UserProvider';

type DefaultContextDataType = {
  classesList: ClassesFetchListType['results'];
  isClassesError: boolean;
  areClassesListLoading: boolean;
  classesError: Error | null;
};

const defaultContextData: DefaultContextDataType = {
  classesList: [],
  isClassesError: false,
  areClassesListLoading: true,
  classesError: null,
};

const ClassesContextData = createContext(defaultContextData);

export const ClassesProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const { userRoles } = UserContext();

  const {
    data: classesList,
    error: classesError,
    isError: isClassesError,
    isLoading: areClassesListLoading,
  } = useQuery<ClassesFetchListType | null>({
    queryFn: SCHOOL_CLASSES.GET_CLASSES,
    queryKey: [QUERY_KEYS.CLASSES_LIST],
    staleTime: 1000 * 60 * 60 * 24,
    enabled:
      Boolean(router.isReady) &&
      (userRoles.isSchoolAdmin || userRoles.isSchoolTeacher),
  });

  const contextValue = useMemo(() => {
    return {
      classesList: classesList?.results || [],
      isClassesError,
      areClassesListLoading,
      classesError,
    };
  }, [classesList, isClassesError, areClassesListLoading, classesError]);

  return (
    <ClassesContextData.Provider value={contextValue}>
      {children}
    </ClassesContextData.Provider>
  );
};

export const ClassesContext = () => {
  return useContext(ClassesContextData);
};
