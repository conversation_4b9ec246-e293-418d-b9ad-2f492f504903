/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import SCHOOL_CLASSES from '@/services/schools/classes';
import {
  ClassesListFetchListType,
  SupportedSchoolGradesType,
} from '@/types/common';

import { UserContext } from './UserProvider';

type DefaultContextDataType = {
  classesList: ClassesListFetchListType['results'];
  isClassesError: boolean;
  areClassesListLoading: boolean;
  classesError: Error | null;
  updateGradesList: (selectedGrades: SupportedSchoolGradesType[]) => void;
};

const defaultContextData: DefaultContextDataType = {
  classesList: [],
  isClassesError: false,
  areClassesListLoading: true,
  classesError: null,
  updateGradesList: () => {},
};

const ClassesContextData = createContext(defaultContextData);

export const ClassesProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const { userRoles } = UserContext();
  const [grades, setGrades] = useState<SupportedSchoolGradesType[]>([]);

  const {
    data: classesList,
    error: classesError,
    isError: isClassesError,
    isLoading: areClassesListLoading,
  } = useQuery<ClassesListFetchListType | null>({
    queryFn: () => SCHOOL_CLASSES.GET_CLASSES_LIST({ grades }),
    queryKey: [QUERY_KEYS.CLASSES_LIST, grades],
    staleTime: 1000 * 60 * 60 * 24,
    enabled:
      Boolean(router.isReady) &&
      (userRoles.isSchoolAdmin || userRoles.isSchoolTeacher),
  });

  const updateGradesList = (selectedGrades: SupportedSchoolGradesType[]) => {
    setGrades(selectedGrades);
  };

  const contextValue = useMemo(() => {
    return {
      classesList: classesList?.results || [],
      isClassesError,
      areClassesListLoading,
      classesError,
      updateGradesList,
    };
  }, [classesList, isClassesError, areClassesListLoading, classesError]);

  useEffect(() => {
    // on router change we need to reset the classes list
    setGrades([]);
  }, [router]);

  return (
    <ClassesContextData.Provider value={contextValue}>
      {children}
    </ClassesContextData.Provider>
  );
};

export const ClassesContext = () => {
  return useContext(ClassesContextData);
};
