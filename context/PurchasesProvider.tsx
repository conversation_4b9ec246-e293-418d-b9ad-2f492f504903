/* eslint-disable react-hooks/exhaustive-deps */
import { createContext, useContext, useMemo } from 'react';

import { PaymentResultStatusType } from '@/components/Layouts/AppLayout/CheckoutModal/components/types';
import useCheckout from '@/customHooks/useCheckout';
import { ProductDetailsType } from '@/types/common';

type DefaultContextDataType = {
  isCheckoutModalVisible: boolean;
  onCloseCheckout: () => void;
  openCheckout: (details: ProductDetailsType[] | null) => void;
  productDetails: ProductDetailsType[] | null;
  displayedScreen: 'form' | PaymentResultStatusType;
  handlePaymentProcessing: (
    type: PaymentResultStatusType,
    errorCodeMessage?: string
  ) => void;
  errorCode: string;
};

const defaultContextData: DefaultContextDataType = {
  isCheckoutModalVisible: false,
  onCloseCheckout: () => {},
  openCheckout: () => {},
  productDetails: null,
  displayedScreen: 'form',
  handlePaymentProcessing: () => {},
  errorCode: '',
};

const PurchasesContextData = createContext(defaultContextData);

export const PurchasesProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const {
    displayedScreen,
    errorCode,
    handlePaymentProcessing,
    isCheckoutModalVisible,
    onCloseCheckout,
    openCheckout,
    productDetails,
  } = useCheckout();

  const contextValue = useMemo(() => {
    return {
      isCheckoutModalVisible,
      onCloseCheckout,
      openCheckout,
      productDetails,
      displayedScreen,
      handlePaymentProcessing,
      errorCode,
    };
  }, [isCheckoutModalVisible, displayedScreen, errorCode, productDetails]);

  return (
    <PurchasesContextData.Provider value={contextValue}>
      {children}
    </PurchasesContextData.Provider>
  );
};

export const PurchasesContext = () => {
  return useContext(PurchasesContextData);
};
