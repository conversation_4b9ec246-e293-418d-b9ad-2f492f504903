/* eslint-disable react-hooks/exhaustive-deps */
import { Modal } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DOWNLOAD_SESSIONS } from '@/common/errors';
import Text from '@/components/Text/Text';
import TESTS_SESSIONS_LISTING from '@/services/tests/sessions-listing';
import { ExportTestDataQueryParameters } from '@/types/tests/session-listing';

type DefaultContextDataType = {
  progress: number;
  isExporting: boolean;
  startExport: (exportParams: ExportTestDataQueryParameters) => void;
};

const defaultContextData: DefaultContextDataType = {
  progress: 0,
  isExporting: false,
  startExport: () => {},
};

const ExportTestContextData = createContext(defaultContextData);

export const ExportTestDataProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [isExporting, setIsExporting] = useState(
    defaultContextData.isExporting
  );
  const [activeBullJobId, setActiveBullJobId] = useState<string | null>(null);
  const [hasErrorOccurred, setHasErrorOccurred] = useState(false);
  const [activeProgress, setActiveProgress] = useState<number>(0);
  const [opened, setOpened] = useState(false);
  const [errorLimitAndCount, setErrorLimitAndCount] = useState<{
    count: number;
    limit: number;
  } | null>(null);

  // We inform the BE to initialized the BullJob and start processing the excel
  const startExportMutation = useMutation({
    mutationFn: TESTS_SESSIONS_LISTING.START_EXPORT_TEST_DATA_BULL_JOB,
    onSuccess: (res) => {
      if (res.id && !res.downloadUrl) {
        setActiveBullJobId(res.id);
      } else if (res.id && res.downloadUrl) {
        // Directly download the excel by the link provided in the response due to BE caching. The exact same results have already been generated.
        const link = document.createElement('a');
        link.href = res.downloadUrl;
        link.setAttribute('download', 'tests_data.xlsx');
        document.body.appendChild(link);
        link.click();
        link.remove();

        setIsExporting(false);
        setActiveProgress(0);
      }

      if (
        (res?.progress?.progress || 0) > 0 &&
        res?.progress?.state === 'active'
      ) {
        notifications.show({
          title: t('export-data-in-progress-title-message'),
          message: t('continue-download-export-data-info-message'),
          color: 'blue',
        });
      }
    },
  });

  const downloadExcelMutation = useMutation({
    mutationFn: TESTS_SESSIONS_LISTING.DOWNLOAD_TESTS_DATA_EXCEL,
    onSuccess: async (res) => {
      if (res.status) {
        const link = document.createElement('a');
        link.href = res.status;
        link.setAttribute('download', 'tests_data.xlsx');
        document.body.appendChild(link);
        link.click();
      }

      setIsExporting(false);
      setActiveProgress(0);
    },
  });

  // If the BullJob started successfully and we have a BullJob id we check the server for progress each second
  // The progress will never start automatically if no BullJob id is provided
  const { data: exportProgress, isError: hasExportProgressError } =
    useQuery<any>({
      enabled: Boolean(activeBullJobId),
      queryFn: TESTS_SESSIONS_LISTING.GET_SESSIONS_EXPORT_PROGRESS,
      queryKey: ['active-bull-job'],
      refetchInterval: activeBullJobId ? 2000 : false,
      refetchIntervalInBackground: true,
    });

  // every time we have a progress change we check the status to complete the download when its ready
  useEffect(() => {
    if (exportProgress?.state === 'active') {
      setActiveProgress(exportProgress?.progress || 0);
    } else if (
      typeof exportProgress?.progress === 'number' &&
      exportProgress?.progress === 100 &&
      exportProgress?.state === 'completed'
    ) {
      setActiveBullJobId(null);
      downloadExcelMutation.mutate();
    } else if (exportProgress?.state === 'failed') {
      setHasErrorOccurred(true);
      setActiveBullJobId(null);

      // we reset the query data so the progress and the state will be able to change and trigger the effect again
      // completely clear the active bull job progress state
      queryClient.removeQueries({
        queryKey: ['active-bull-job'],
      });

      notifications.show({
        title: t('Error'),
        message: t('export-data-error'),
        color: 'red',
      });
    }
  }, [exportProgress?.progress, exportProgress?.state]);

  // A generic handler for any error that might occur during the export process
  useEffect(() => {
    const isTooLargeError =
      startExportMutation.error?.message ===
      DOWNLOAD_SESSIONS.SESSION_COUNT_TOO_LARGE;

    if (
      (startExportMutation.isError ||
        downloadExcelMutation.isError ||
        hasExportProgressError) &&
      !isTooLargeError
    ) {
      notifications.show({
        title: t('Error'),
        message: t('export-data-error'),
        color: 'red',
        autoClose: 10000,
      });
    }

    if (isTooLargeError) {
      setOpened(true);
      setErrorLimitAndCount((startExportMutation.error as any)?.cause);
    }

    setIsExporting(false);
    setHasErrorOccurred(false);
    setActiveProgress(0);
  }, [
    startExportMutation.isError,
    downloadExcelMutation.isError,
    hasExportProgressError,
    hasErrorOccurred,
  ]);

  // Leave the automatic progress check for the future
  // useEffect(() => {
  //   startExportMutation.mutate({
  //     testType: 'mathpro-r',
  //   });
  // }, []);

  const contextValue = useMemo(() => {
    return {
      progress: activeProgress,
      isExporting,
      startExport: (exportDataParams: ExportTestDataQueryParameters) => {
        setIsExporting(true);
        startExportMutation.mutate(exportDataParams);
      },
    };
  }, [exportProgress?.progress, isExporting, startExportMutation]);

  return (
    <ExportTestContextData.Provider value={contextValue}>
      <>
        <Modal
          opened={opened}
          onClose={() => setOpened(false)}
          title={t('unable-to-export')}
          c="var(--color-red)"
          centered
          size="lg"
          padding={32}
        >
          <Text
            transKey="export-test-data-too-large"
            transVariables={{
              count: `${errorLimitAndCount?.count}`,
              limit: `${errorLimitAndCount?.limit}`,
            }}
          />
        </Modal>

        {children}
      </>
    </ExportTestContextData.Provider>
  );
};

export const ExportTestDataContext = () => {
  return useContext(ExportTestContextData);
};
