/* eslint-disable react-hooks/exhaustive-deps */
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { createContext, useContext, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import GROUPS from '@/services/students/groups';
import { GroupsFetchListType } from '@/types/common';

import { UserContext } from './UserProvider';

type DefaultContextDataType = {
  groupsList: GroupsFetchListType['results'];
  isGroupsError: boolean;
  areGroupsListLoading: boolean;
  groupsError: Error | null;
};

const defaultContextData: DefaultContextDataType = {
  groupsList: [],
  isGroupsError: false,
  areGroupsListLoading: true,
  groupsError: null,
};

const GroupsContextData = createContext(defaultContextData);

export const GroupsProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();

  const { isSchoolRole } = UserContext();

  const {
    data: groupsList,
    error: groupsError,
    isError: isGroupsError,
    isLoading: areGroupsListLoading,
  } = useQuery<GroupsFetchListType | null>({
    queryFn: GROUPS.GET_GROUPS,
    queryKey: [QUERY_KEYS.GROUPS_LIST],
    staleTime: 10000,
    enabled: Boolean(router.isReady) && !isSchoolRole,
  });

  const contextValue = useMemo(() => {
    return {
      groupsList: groupsList?.results || [],
      isGroupsError,
      areGroupsListLoading,
      groupsError,
    };
  }, [groupsList, isGroupsError, areGroupsListLoading, groupsError]);

  return (
    <GroupsContextData.Provider value={contextValue}>
      {children}
    </GroupsContextData.Provider>
  );
};

export const GroupsContext = () => {
  return useContext(GroupsContextData);
};
