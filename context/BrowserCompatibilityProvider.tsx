import { createContext, useContext, useEffect, useMemo, useState } from 'react';

type OSInfo = {
  os: string;
  osVersion: number | null;
};

type BrowserInfo = {
  browser: string;
  browserVersion: number | null;
};

const minimumCompatibleBrowserVersion = {
  Chrome: 108,
  Edge: 108,
  Firefox: 101,
  Safari: 15,
  Opera: 93,
} as const;

type DefaultContextDataType = {
  isIncompatibleBrowser: boolean;
  browserInfo: BrowserInfo;
  minimumCompatibleBrowserVersion: typeof minimumCompatibleBrowserVersion;
};

const defaultContextData: DefaultContextDataType = {
  isIncompatibleBrowser: false,
  browserInfo: {
    browser: '',
    browserVersion: null,
  },
  minimumCompatibleBrowserVersion,
};

const BrowserCompatibilityContextData = createContext(defaultContextData);

const extractMainVersion = (version: string): number | null => {
  const main = version.split('.')[0];
  const parsed = parseInt(main, 10);
  return isNaN(parsed) ? null : parsed;
};

const getOSInfo = (ua: string): OSInfo => {
  if (/Windows NT/.test(ua)) {
    const match = ua.match(/Windows NT ([\d.]+)/);
    return {
      os: 'Windows',
      osVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  if (/Mac OS X/.test(ua)) {
    const match = ua.match(/Mac OS X ([\d_]+)/);
    const version = match?.[1].replace(/_/g, '.') || '';
    return {
      os: 'macOS',
      osVersion: extractMainVersion(version),
    };
  }

  if (/Android/.test(ua)) {
    const match = ua.match(/Android ([\d.]+)/);
    return {
      os: 'Android',
      osVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  if (/iPhone OS/.test(ua)) {
    const match = ua.match(/iPhone OS ([\d_]+)/);
    const version = match?.[1].replace(/_/g, '.') || '';
    return {
      os: 'iOS',
      osVersion: extractMainVersion(version),
    };
  }

  if (/Linux/.test(ua)) {
    return { os: 'Linux', osVersion: null };
  }

  return { os: 'Unknown', osVersion: null };
};

const getBrowserInfo = (ua: string): BrowserInfo => {
  if (/Edg\/([\d.]+)/.test(ua)) {
    const match = ua.match(/Edg\/([\d.]+)/);
    return {
      browser: 'Edge',
      browserVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  if (/OPR\/([\d.]+)/.test(ua)) {
    const match = ua.match(/OPR\/([\d.]+)/);
    return {
      browser: 'Opera',
      browserVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  if (/Chrome\/([\d.]+)/.test(ua) && !/Edg|OPR/.test(ua)) {
    const match = ua.match(/Chrome\/([\d.]+)/);
    return {
      browser: 'Chrome',
      browserVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  if (/Firefox\/([\d.]+)/.test(ua)) {
    const match = ua.match(/Firefox\/([\d.]+)/);
    return {
      browser: 'Firefox',
      browserVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  if (/Version\/([\d.]+).*Safari/.test(ua)) {
    const match = ua.match(/Version\/([\d.]+)/);
    return {
      browser: 'Safari',
      browserVersion: extractMainVersion(match?.[1] || ''),
    };
  }

  return { browser: 'Unknown', browserVersion: null };
};

const isBrowserIncompatible = ({ browser, browserVersion }: BrowserInfo) => {
  if (!browserVersion) return false;

  switch (browser) {
    case 'Chrome':
      if (browserVersion < minimumCompatibleBrowserVersion.Chrome) {
        return true;
      }
      break;

    case 'Edge':
      if (browserVersion < minimumCompatibleBrowserVersion.Edge) {
        return true;
      }
      break;

    case 'Firefox':
      if (browserVersion < minimumCompatibleBrowserVersion.Firefox) {
        return true;
      }
      break;

    case 'Safari':
      if (browserVersion < minimumCompatibleBrowserVersion.Safari) {
        return true;
      }
      break;

    case 'Opera':
      if (browserVersion < minimumCompatibleBrowserVersion.Opera) {
        return true;
      }
      break;

    default:
      return false;
  }

  return false;
};

export const BrowserCompatibilityProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isIncompatibleBrowser, setIsIncompatibleBrowser] = useState(false);
  const [browserInfo, setBrowserInfo] = useState<BrowserInfo>({
    browser: '',
    browserVersion: null,
  });

  useEffect(() => {
    if (typeof navigator === 'undefined') return;

    const ua = navigator.userAgent;

    const browserInfoDetails = getBrowserInfo(ua);

    const osInfo = getOSInfo(ua);

    setBrowserInfo(browserInfoDetails);

    setIsIncompatibleBrowser(
      isBrowserIncompatible({ ...browserInfoDetails, ...osInfo })
    );
  }, []);
  const contextValue = useMemo(() => {
    return {
      isIncompatibleBrowser,
      browserInfo,
      minimumCompatibleBrowserVersion,
    };
  }, [browserInfo, isIncompatibleBrowser]);

  return (
    <BrowserCompatibilityContextData.Provider value={contextValue}>
      {children}
    </BrowserCompatibilityContextData.Provider>
  );
};

export const BrowserCompatibilityContext = () => {
  return useContext(BrowserCompatibilityContextData);
};
