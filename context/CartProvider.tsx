/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';

type DefaultContextDataType = {
  totalNumberOfItemInCart: number;
  addToCart: (item: any) => void;
  removeFromCart: (itemId: string) => void;
  isCardUpdating: boolean;
};

const defaultContextData: DefaultContextDataType = {
  totalNumberOfItemInCart: 0,
  addToCart: () => {},
  removeFromCart: () => {},
  isCardUpdating: false,
};

const CartContextData = createContext(defaultContextData);

export const CartProvider = ({ children }: { children: React.ReactNode }) => {
  const [cartItems, setCartItems] = useState<any>([]);
  const [isCardUpdating, setIsCardUpdating] = useState(
    defaultContextData.isCardUpdating
  );

  // create a function that will add a new item to the cart

  const cartNotificationUpdate = () => {
    if (isCardUpdating) return;

    setIsCardUpdating(true);
    setTimeout(() => {
      setIsCardUpdating(false);
    }, 2000);
  };

  const addToCart = (item: any) =>
    useCallback(() => {
      setCartItems((prev: any) => [...prev, item]);
      cartNotificationUpdate();
    }, [cartItems]);

  const removeFromCart = (itemId: string) => {
    setCartItems((prev: any) => prev.filter((item: any) => item.id !== itemId));
    cartNotificationUpdate();
  };

  const contextValue = useMemo(() => {
    return {
      totalNumberOfItemInCart: cartItems.length,
      addToCart,
      removeFromCart,
      isCardUpdating,
    };
  }, [addToCart, cartItems.length, isCardUpdating, removeFromCart]);

  return (
    <CartContextData.Provider value={contextValue}>
      {children}
    </CartContextData.Provider>
  );
};

export const CartContext = () => {
  return useContext(CartContextData);
};
