import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useReducer,
  useState,
} from 'react';

import { ProductDetailsType } from '@/types/common';

type DefaultContextDataType = {
  totalNumberOfItemInCart: number;
  addToCart: (item: ProductDetailsType) => void;
  removeFromCart: (itemId: string) => void;
  isCartUpdating: boolean;
  isItemInCart: (stripeId: string) => boolean;
  cartItems: ProductDetailsType[];
  checkoutPrice: number;
};

// Discriminated union for each action based on the type
type CartAction =
  | { type: 'ADD_ITEM'; item: ProductDetailsType }
  | { type: 'REMOVE_ITEM'; productStripeId: string };

const CART_ACTION_OPTIONS = {
  ADD_ITEM: 'ADD_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
} as const;

const defaultContextData: DefaultContextDataType = {
  totalNumberOfItemInCart: 0,
  addToCart: () => {},
  removeFromCart: () => {},
  isCartUpdating: false,
  isItemInCart: () => false,
  cartItems: [],
  checkoutPrice: 0,
};

const CartContextData = createContext(defaultContextData);

const cartReducer = (
  state: ProductDetailsType[],
  action: CartAction
): ProductDetailsType[] => {
  switch (action.type) {
    case CART_ACTION_OPTIONS.ADD_ITEM:
      return [...state, action.item];
    case CART_ACTION_OPTIONS.REMOVE_ITEM:
      return state.filter(
        (product) => product.stripeId !== action.productStripeId
      );
    default:
      return state;
  }
};

export const CartProvider = ({ children }: { children: React.ReactNode }) => {
  const [cartItems, dispatch] = useReducer(cartReducer, []);

  const [isCartUpdating, setIsCartUpdating] = useState(
    defaultContextData.isCartUpdating
  );

  const cartNotificationUpdate = useCallback(() => {
    if (isCartUpdating) return;

    setIsCartUpdating(true);
    setTimeout(() => {
      setIsCartUpdating(false);
    }, 2000);
  }, [isCartUpdating]);

  const addToCart = useCallback(
    (item: ProductDetailsType) => {
      dispatch({ type: CART_ACTION_OPTIONS.ADD_ITEM, item });
      cartNotificationUpdate();
    },
    [cartNotificationUpdate]
  );

  const removeFromCart = useCallback(
    (stripeId: string) => {
      dispatch({
        type: CART_ACTION_OPTIONS.REMOVE_ITEM,
        productStripeId: stripeId,
      });
      cartNotificationUpdate();
    },
    [cartNotificationUpdate]
  );

  const isItemInCart = useCallback(
    (stripeId: string) => {
      return cartItems.some((item) => item.stripeId === stripeId);
    },
    [cartItems]
  );

  const contextValue = useMemo(() => {
    return {
      totalNumberOfItemInCart: cartItems.length,
      addToCart,
      removeFromCart,
      isCartUpdating,
      isItemInCart,
      cartItems,
      checkoutPrice: cartItems.reduce(
        (acc, curr) => acc + (curr.price?.totalAmount || 0),
        0
      ),
    };
  }, [addToCart, cartItems, isCartUpdating, isItemInCart, removeFromCart]);

  return (
    <CartContextData.Provider value={contextValue}>
      {children}
    </CartContextData.Provider>
  );
};

export const CartContext = () => {
  return useContext(CartContextData);
};
