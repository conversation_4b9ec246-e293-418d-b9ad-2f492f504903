import { useQuery } from '@tanstack/react-query';
import { createContext, useContext, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import TESTS_DETAILS from '@/services/tests/details';
import { AllApplicationTestsDetailsType } from '@/types/common';

import { UserContext } from './UserProvider';

type DefaultContextDataType = {
  areTestsLoading: boolean;
  areTestsFetching: boolean;
  allTests: AllApplicationTestsDetailsType;
  totalNumberOfTests: number;
  // onlyAvailableTests: AllApplicationTestsDetailsType;
};

const defaultContextData: DefaultContextDataType = {
  areTestsLoading: true,
  areTestsFetching: true,
  allTests: [],
  totalNumberOfTests: 0,
  // onlyAvailableTests: [],
};

const TestsContextData = createContext(defaultContextData);

export const TestsProvider = ({
  children,
  // isUserLoggedIn,
}: {
  children: React.ReactNode;
  // isUserLoggedIn: boolean;
}) => {
  const { user } = UserContext();

  const isUserLoggedIn = Boolean(user.id);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data, error, isError, isFetching, isLoading } =
    useQuery<AllApplicationTestsDetailsType | null>({
      queryFn: () => TESTS_DETAILS.GET_ALL_APPLICATION_TESTS(),
      queryKey: [QUERY_KEYS.ALL_APPLICATION_TESTS],
      staleTime: 1000 * 60 * 60 * 24,
      enabled: isUserLoggedIn,
    });

  // TODO : handle that we didn't get the tests data

  const contextValue = useMemo(
    () => ({
      areTestsLoading: isLoading,
      areTestsFetching: isFetching,
      allTests: data || [],
      totalNumberOfTests: data ? data.length : 0,
      // onlyAvailableTests: data ? data.filter((test) => test.available) : [],
    }),
    [isLoading, isFetching, data]
  );

  return (
    <TestsContextData.Provider value={contextValue}>
      {children}
    </TestsContextData.Provider>
  );
};

export const TestsContext = () => {
  return useContext(TestsContextData);
};
