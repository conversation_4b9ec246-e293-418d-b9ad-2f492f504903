import { z } from 'zod';

export const STUDENT_GRADE_ENUM = z.enum([
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
  '11',
  '12',
  'K',
  'Alumni',
]);

export const GENDER_ENUM = z.enum(['male', 'female']);

export const ARBITRARY_SKILL_SCHEMA = z.object({
  name: z.string(),
  default: z.number(),
  score: z.number(),
  subskills: z.array(
    z.object({
      name: z.string(),
      default: z.number().optional(),
      score: z.number(),
    })
  ),
});

export const ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE = z.enum([
  'mathpro-d',
  'mathpro-s',
  // 'mathpro-h',
  // 'mathpro-q',
  'mathpro-r',
]);

export const TEACHERS_INVITATION_STATES = z.enum([
  'active',
  'expired',
  'accepted',
  'revoked',
]);

export const TEACHERS_STATUS_STATES = z.enum([
  'active',
  'invited',
  'revoked',
  'inactive',
  'declined',
  'cancelled',
  // We should not accept undefined the TEACHER status should always be defined
  // 'undefined',
]);

export const STUDENT_SCHEMA = z.object({
  id: z.string(),
  firstName: z.string().min(1, 'first-name-required').optional(),
  lastName: z.string().min(1, 'last-name-required').optional(),
  code: z.string().min(1, 'code-required').optional().nullable(),
  dateOfBirth: z.string().optional().nullable(),
  gender: GENDER_ENUM,
  grade: STUDENT_GRADE_ENUM,
  className: z.string().optional(),
  groupName: z.string().optional(),
  residence: z.string().optional().nullable(),
  schoolName: z.string().optional().nullable(),
  anonymous: z.boolean(),
  skills: z.array(ARBITRARY_SKILL_SCHEMA).optional(),
  enter: z.string(),
  extras: z
    .object({
      schoolType: z.string().optional(),
      area: z.string().optional(),
      firstLanguage: z.string().optional(),
      // SES: z.string().optional(),
      neurodiversity: z.string().optional(),
      testDevice: z.string().optional(),
      curriculum: z.string().optional(),
      mathsTeachersScore: z.string().optional(),
      mathsScore: z.string().optional(),
      literatureScore: z.string().optional(),
    })
    .optional(),
});

export const TEST_SESSION_STATE = z.enum([
  'active',
  'in-progress',
  'scheduled',
  'expired',
  'completed',
]);

export const TEST_SESSION_QUESTION_TYPE = z.enum([
  'compareImages',
  'compareNumbers',
  'numberToCalculator',
  'audioToCalculator',
  'imageToCalculator',
  'textToCalculator',
  'numberlines',
  'shapes',
  'problemWOperation',
  'solveSecondBasedOnFirst',
  'patterns',
]);

export const BASIC_PAGINATION_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
});

// ITS THE BASE STUDY SCHEMA FOR THE ADMIN USED FOR THE ADMIN STUDIES DISPLAYED LIST
export const ADMIN_STUDY_SCHEMA = z.object({
  id: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
  administrator: z
    .object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
    })
    .nullable(),
  name: z.string(),
  proctorsCount: z.number(),
  country: z.string(),
  language: z.string(),
  from: z.string().optional().nullable(),
  to: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  sessionsCount: z.number().optional().nullable(),
  statistics: z
    .object({
      fields: z.array(z.string()),
      values: z.any(),
    })
    .optional()
    .nullable(), // received object
});

// ITS THE BASE STUDY SCHEMA FOR THE ADMIN USED FOR THE ADMIN STUDIES DISPLAYED LIST
export const ADMIN_TEST_SCHEMA = z.object({
  id: z.string(),
  // the reason i made this a string its cause the BE might change it to something else from mathpro-r to R. We do not strictly care about the type its just for display here
  type: z.string(),
  name: z.string(),
  short: z.string(),
  version: z.number(),
  research: z.boolean(),
  country: z.string(),
  language: z.string(),
  createdAt: z.string(),
  readiness: z.number(),
  available: z.boolean(),
  data: z.array(
    z.object({
      type: z.string(),
      uploadedAt: z.string().optional(),
    })
  ),
});
