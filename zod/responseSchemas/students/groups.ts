import { z } from 'zod';

import { STUDENT_SCHEMA } from '@/zod/zodConstants';

const GROUP_BASIC_DETAILS_SCHEMA = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  studentsCount: z.number(),
});

export const ADD_GROUP_RESPONSE_SCHEMA = z.object({
  id: z.string(),
});

// Refers to a single response schema from a specific group id that will always return the students
export const GROUP_DETAILS_SCHEMA = GROUP_BASIC_DETAILS_SCHEMA.extend({
  students: z.array(STUDENT_SCHEMA),
});

export const GET_GROUPS_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(GROUP_BASIC_DETAILS_SCHEMA),
});
