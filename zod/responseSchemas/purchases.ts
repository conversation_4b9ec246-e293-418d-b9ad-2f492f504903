import { z } from 'zod';

import { ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE } from '../zodConstants';

export const PAYMENT_INTENT_SCHEMA = z.object({
  clientSecret: z.string(),
});

export const PURCHASES_HISTORY_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  results: z.array(
    z.object({
      product: z.object({
        id: z.string(),
        active: z.boolean(),
        name: z.string(),
        images: z.array(z.string()),
        price: z.object({
          amount: z.number(),
          currency: z.enum(['usd', 'eur', 'gbp']),
        }),
        metadata: z.object({
          type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
          kind: z.string(),
        }),
      }),
      receiptUrl: z.string(),
      purchaseDate: z.string(),
    })
  ),
});

export const VAT_COUNTRIES_LIST_SCHEMA = z.array(
  z.object({
    code: z.string(),
    name: z.string(),
  })
);
