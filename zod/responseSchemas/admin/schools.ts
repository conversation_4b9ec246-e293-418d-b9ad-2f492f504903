import { z } from 'zod';

import { BASIC_PAGINATION_SCHEMA } from '@/zod/zodConstants';

export const SCHOOLS_SCHEMA = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
  })
);

export const SCHOOLS_LIST_SCHEMA = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string(),
  createdAt: z.string(),
  email: z.string().email(),
  phoneNumber: z.string().nullable(),
  teachersCount: z.number(),
  updatedAt: z.string(),
  url: z.string().nullable(),
  type: z.string(),
  profilePicture: z.string().optional().nullable(),
  administrator: z
    .object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
      email: z.string().email().nullable(),
    })
    .nullable(),
  address: z
    .object({
      addressLine1: z.string(),
      addressLine2: z.string().nullable(),
      city: z.string(),
      state: z.string().nullable(),
      postcode: z.string(),
      country: z.string(),
    })
    .nullable(),
});

export const ADMIN_SCHOOLS_LIST_SCHEMA = BASIC_PAGINATION_SCHEMA.extend({
  results: z.array(SCHOOLS_LIST_SCHEMA),
});
