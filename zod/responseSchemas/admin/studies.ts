import { z } from 'zod';

import {
  ADMIN_STUDY_SCHEMA,
  BASIC_PAGINATION_SCHEMA,
  ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
} from '@/zod/zodConstants';

export const STUDIES_SCHEMA = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    locale: z.string(),
  })
);

export const ADMIN_STUDIES_LIST_SCHEMA = BASIC_PAGINATION_SCHEMA.extend({
  results: z.array(ADMIN_STUDY_SCHEMA),
});

export const ADMIN_STUDY_SCHEMA__DETAILS_BY_ID = z.object({
  id: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
  administrator: z
    .object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
    })
    .nullable(),
  name: z.string(),
  proctors: z
    .array(
      z.object({
        id: z.string(),
        firstName: z.string(),
        lastName: z.string(),
        email: z.string().email(),
      })
    )
    .optional(),
  country: z.string().optional(),
  language: z.string().optional(),
  from: z.string().optional().nullable(),
  to: z.string().optional().nullable(),
});

export const ADMIN_STUDY_TESTS_SCHEMA = z.array(
  z.object({
    // type : ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
    id: z.string(),
    type: z.string(),
    name: z.string(),
    short: z.string(),
    version: z.number(),
  })
);
