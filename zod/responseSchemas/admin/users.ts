import { z } from 'zod';

import { BASIC_PAGINATION_SCHEMA } from '@/zod/zodConstants';

export const UNASSIGNED_RESEARCHERS_SCHEMA = BASIC_PAGINATION_SCHEMA.extend({
  results: z.array(
    z.object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
      email: z.string().email(),
    })
  ),
});

export const UNASSIGNED_STUDY_ADMINS_SCHEMA = BASIC_PAGINATION_SCHEMA.extend({
  results: z.array(
    z.object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
      email: z.string().email(),
    })
  ),
});

export const ADMIN_USER_SCHEMA = z.object({
  id: z.string(),
  username: z.string(),
  roles: z.array(z.string().nullable()),
  profile: z
    .object({
      firstName: z.string().optional().nullable(),
      lastName: z.string().optional().nullable(),
      email: z.string().email().optional().nullable(),
      phoneNumber: z.string().optional().nullable(),
      language: z.string().optional().nullable(),
      dob: z.optional(z.string().optional().nullable()),
      organisation: z.optional(z.string().optional().nullable()),
      affiliation: z.string().optional().nullable(),
      address: z
        .object({
          addressLine1: z.string().optional().nullable(),
          addressLine2: z.string().optional().nullable(),
          city: z.string().optional().nullable(),
          state: z.string().optional().nullable(),
          postcode: z.string().optional().nullable(),
          country: z.string().optional().nullable(),
        })
        .optional()
        .nullable(),
      certificates: z
        .array(
          z.object({
            type: z.string(),
            version: z.number(),
            language: z.string(),
            progress: z.object({
              id: z.string(),
              completed: z.string().optional(),
              status: z.string().optional(),
            }),
          })
        )
        .optional()
        .nullable(),
      licenses: z
        .array(
          z.object({
            type: z.string(),
            remaining: z.number(),
          })
        )
        .optional()
        .nullable(),
      onboarded: z.boolean().optional(),
      profilePicture: z.string().optional().nullable(),
    })
    .optional(),
  isArchived: z.boolean().optional(),
  registeredAt: z.string().optional(),
  school: z
    .object({
      id: z.string(),
      name: z.string(),
      code: z.string(),
      email: z.string().email(),
    })
    .optional()
    .nullable(),
});

export const USERS_SCHEMA_ADMIN = BASIC_PAGINATION_SCHEMA.extend({
  results: z.array(ADMIN_USER_SCHEMA),
});
