import { z } from 'zod';

import { ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE } from '../zodConstants';

export const PRODUCT_DETAILS_SCHEMA = z.object({
  stripeId: z.string(),
  kind: z.enum(['certificate', 'test', 'ebook']),
  name: z.string(),
  price: z.object({
    amount: z.number(),
    currency: z.enum(['usd', 'eur', 'gbp']),
  }),
  images: z.array(z.string()),
  type: z.optional(ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE),
  progress: z.optional(
    z.object({
      id: z.string(),
      modules: z.array(
        z.object({
          order: z.number(),
          count: z.number(),
          answered: z.number(),
        })
      ),
    })
  ),
});

export const CERTIFICATIONS_PRODUCTS_SCHEMA = z.array(
  PRODUCT_DETAILS_SCHEMA.extend({
    type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
    language: z.string(),
  })
);
