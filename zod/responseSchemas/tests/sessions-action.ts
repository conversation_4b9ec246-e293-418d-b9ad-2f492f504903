import { z } from 'zod';

import {
  STUDENT_GRADE_ENUM,
  TEST_SESSION_QUESTION_TYPE,
  TEST_SESSION_STATE,
  ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
} from '@/zod/zodConstants';

export const GET_PROGRESS_FROM_TEST_SESSION_BY_ID_SCHEMA = z.object({
  id: z.string(),
  type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
  state: TEST_SESSION_STATE,
  testGrade: STUDENT_GRADE_ENUM,
  studentGrade: STUDENT_GRADE_ENUM,
  language: z.string(),
  title: z.string(),
  mode: z.enum(['practice', 'exercise']),
  info: z.string().optional(),
  studentFullName: z.string(),
  teacherFullName: z.string(),
  hasWatchedIntro: z.boolean(),
  totalSubtestsCount: z.number(),
  currentSubtestsOrder: z.number(),
  displayClock: z.boolean(),
});

export const GET_CURRENT_EXERCISE_FROM_TEST_SESSION_BY_ID_SCHEMA = z.object({
  type: TEST_SESSION_QUESTION_TYPE,
  // This is basically the unique id for the current question
  code: z.string(),
  practice: z.boolean(),
  question: z.string(),
  // Represents the milliseconds that the "eye" remains on screen
  answer: z.string().optional(),
  fixation: z.number(),
  // Represents the milliseconds between the "eye" and the the exhibit
  ISI: z.number(),
  // Represents the milliseconds that the exhibit remains on screen
  disappear: z.number().optional(),
  calculator: z.enum(['standard', 'extended', 'occluded', 'decimals']),
  last: z.boolean().optional(),
  section: z
    .object({
      head: z.string(),
      icon: z.enum(['plus', 'minus', 'times', 'over', 'clock']).optional(),
      audio: z.string().optional(),
      body: z.string().optional(),
    })
    .optional(),
  exhibits: z.array(
    z.object({
      type: z.enum(['image', 'text', 'slider', 'number', 'audio']),
      value: z.string(),
      params: z.object({}).optional().nullable(),
    })
  ),
});

export const GET_INTRO_SCHEMA_FOR_CURRENT_SUB_TEST_SCHEMA = z.object({
  instruction: z.string(),
  canSkipVideo: z.boolean().optional(),
  video: z.string().optional(),
  audio: z.string().optional(),
  image: z.string().optional(),
  sample: GET_CURRENT_EXERCISE_FROM_TEST_SESSION_BY_ID_SCHEMA.extend({
    answer: z
      .object({
        type: z.enum(['index', 'value', 'array']).optional(),
        value: z.string(),
      })
      .optional(),
  }).optional(),
  repeat: z.string().optional(),
});

export const GET_ADJUST_AUDIO_DETAILS_SCHEMA = z.object({
  audioFile: z.string().optional(),
  soundFile: z.string().optional(),
  instructions: z.string(),
  headphones: z.string().optional(),
});

export const GET_TEST_LOCALES_SCHEMA = z.object({
  playButtonTitle: z.string(),
  stopButtonTitle: z.string(),
  nextButtonTitle: z.string(),
  skipButtonTitle: z.string(),
  doneButtonTitle: z.string(),
  startButtonTitle: z.string(),
  subtest: z.string(),
  subject: z.string(),
  grade: z.string(),
  nextQuestion: z.string(),
  timeChallenge: z.string(),
  correct: z.string(),
  incorrect: z.string(),
  hearTheProblem: z.string(),
  congratulations: z.string(),
  exerciseCompleted: z.string(),
  practiceStageLabel: z.string(),
  ifPredicate: z.string().optional(),
  whatIsPredicate: z.string().optional(),
  divisionSymbol: z.string(),
  multiplySymbol: z.string(),
  decimalSymbol: z.string(),
  questionMark: z.string(),
  beginRealTask: z.string(),
  timeCounts: z.string(),
  testCompleted: z.string(),
  subtestCompleted: z.string(),
  welcomeTitle: z.string(),
  ofTitle: z.string(),
  retakeButtonTitle: z.string(),
  subtestRetake: z.string(),
  processing: z.string(),
});

export const GET_STUDENT_WELCOME_DETAILS_SCHEMA = z.object({
  student: z.object({
    name: z.string(),
    surname: z.string().optional(),
    code: z.string().optional(),
    grade: z.string().optional(),
  }),
  // The test grade
  grade: z.string().optional(),
  welcome: z.string(),
  test: z.string(),
});
