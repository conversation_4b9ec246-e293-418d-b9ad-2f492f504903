import { z } from 'zod';

import { TEST_SESSION_STATE } from '@/zod/zodConstants';

export const TEST_SESSIONS_LIST_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(
    z.object({
      id: z.string(),
      student: z.object({
        id: z.string(),
        label: z.string(),
        grade: z.string(),
      }),
      results: z.number().optional(),
      studentGrade: z.string().optional(),
      language: z.string().optional(),
      teacher: z.string(),
      grade: z.string(),
      studentClass: z.string().optional(),
      device: z.enum(['teacher', 'student']),
      started: z.string().optional(),
      scheduled: z.string().optional(),
      state: TEST_SESSION_STATE,
      progress: z.number().optional(),
      analysed: z.boolean(),
      info: z.object({
        type: z.enum(['date', 'action', 'score', 'code', 'text']),
        value: z.string(),
      }),
      updated: z.string(),
      score: z.number().optional(),
      report: z
        .array(
          z.object({
            title: z.string(),
            score: z.number().optional().nullable(),
            level: z.string().optional(),
            code: z.string().optional(),
          })
        )
        .optional()
        .nullable(),
    })
  ),
});

export const TEST_SESSION_EXPORT_SCHEMA = z.object({
  progress: z.number(),
  state: z.enum([
    'completed',
    'failed',
    'active',
    'delayed',
    'prioritized',
    'waiting',
    'waiting-children',
  ]),
  failReason: z.string().optional(),
});

export const TESTS_RESULTS_MATHPRO_SCORE_AVERAGES_SCHEMA = z.object({
  header: z.object({
    // Corresponds to students Count - the total number of students that participated in the test
    students: z.number(),
    duration: z.string(),
    grades: z.array(z.string()).optional(),
    study: z.union([z.string(), z.boolean()]).optional(),
    group: z.string().optional(),
    class: z.string().optional(),
  }),
  groups: z.array(
    z.object({
      group: z.enum(['lowest', 'low', 'average', 'high']),
      // count: z.string(),
      count: z.number(),
    })
  ),
  report: z.array(
    z.object({
      title: z.string(),
      code: z.string(),
      score: z.number().optional(),
      level: z.enum(['lowest', 'low', 'average', 'high']).optional(),
    })
  ),
});
