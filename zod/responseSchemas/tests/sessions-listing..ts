import { z } from 'zod';

import { TEST_SESSION_STATE } from '@/zod/zodConstants';

export const TEST_SESSIONS_LIST_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(
    z.object({
      id: z.string(),
      student: z.object({
        id: z.string(),
        label: z.string(),
        grade: z.string(),
      }),
      results: z.number().optional(),
      language: z.string().optional(),
      teacher: z.string(),
      grade: z.string(),
      device: z.enum(['teacher', 'student']),
      started: z.string().optional(),
      scheduled: z.string().optional(),
      state: TEST_SESSION_STATE,
      progress: z.number().optional(),
      analysed: z.boolean(),
      info: z.object({
        type: z.enum(['date', 'action', 'score', 'code', 'text']),
        value: z.string(),
      }),
      updated: z.string(),
      score: z.number().optional(),
      report: z
        .array(
          z.object({
            title: z.string(),
            score: z.number().optional().nullable(),
            level: z.string().optional(),
          })
        )
        .optional()
        .nullable(),
    })
  ),
});

export const TEST_SESSION_EXPORT_SCHEMA = z.object({
  download: z.string(),
});
