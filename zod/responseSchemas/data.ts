import { z } from 'zod';

export const GET_TEMPLATE_URL_SCHEMA = z.object({
  url: z.string(),
});

export const GET_UPLOAD_URL_SCHEMA = z.object({
  url: z.string(),
  filename: z.string(),
});

export const GET_UPLOAD_PROGRESS_SCHEMA = z.object({
  progress: z.number(),
  state: z.enum([
    'completed',
    'failed',
    'active',
    'delayed',
    'prioritized',
    'waiting',
    'waiting-children',
  ]),
  failReason: z.string().optional(),
  parseError: z
    .object({
      message: z.string(),
      data: z.array(z.string()),
      cause: z.string(),
      row: z.number().optional(),
      column: z.number().optional(),
    })
    .optional(),
});
