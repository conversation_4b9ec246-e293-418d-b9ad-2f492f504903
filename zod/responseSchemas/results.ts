import { z } from 'zod';

export const TEST_SCORES_RESULT_SCHEMA = z.object({
  session: z.object({
    id: z.string(),
    test: z.string(),
    completed: z.string().nullable().optional(),
    started: z.string().nullable(),
    grade: z.string(),
    examiner: z.string(),
    duration: z.string().optional(),
    language: z.string(),
  }),
  student: z.object({
    label: z.string(),
    dob: z.string().nullable(),
    age: z.string().optional(),
    grade: z.string().optional(),
    class: z.string().optional(),
    group: z.string().optional(),
    school: z.string().optional(),
    archived: z.boolean().optional(),
    gender: z.string().optional(),
    hometown: z.string().optional(),
  }),
  results: z.object({
    domains: z.array(
      z.object({
        title: z.string(),
        color: z.string(),
        subjects: z.array(
          z.object({
            order: z.number(),
            title: z.string(),
            // Is the generic english name for the test
            name: z.string(),
            featured: z.boolean(),
            completed: z.boolean(),
            code: z.string().optional(),
            metrics: z
              .array(
                z.object({
                  type: z.string(),
                  score: z.number(),
                  percentile: z.number(),
                  level: z.string(),
                })
              )
              .optional(),
          })
        ),
        total: z
          .object({
            score: z.number(),
            percentile: z.number(),
          })
          .optional(),
      })
    ),
    total: z
      .object({
        score: z.number(),
        percentile: z.number(),
      })
      .optional(),
  }),
});

export const TEST_ERROR_RESULTS_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(
    z.object({
      order: z.number(),
      subtest: z.string(),
      answer: z.string(),
      given: z.string(),
      title: z.string(),
      exhibits: z.array(
        z.object({
          type: z.string(),
          value: z.string(),
          params: z
            .object({
              min: z.number(),
              max: z.number(),
            })
            .optional(),
        })
      ),
    })
  ),
});
