import { z } from 'zod';

import { STUDENT_GRADE_ENUM, STUDENT_SCHEMA } from '@/zod/zodConstants';

const CLASS_BASIC_DETAILS_SCHEMA = z.object({
  id: z.string(),
  name: z.string(),
  grade: STUDENT_GRADE_ENUM,
  studentsCount: z.number(),
});

export const CLASS_DETAILS_SCHEMA = CLASS_BASIC_DETAILS_SCHEMA.extend({
  students: z.array(STUDENT_SCHEMA),
});

export const GET_CLASSES_SCHEMA = z.object({
  limit: z.number(),
  page: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(CLASS_BASIC_DETAILS_SCHEMA),
});

export const ADD_CLASS_RESPONSE_SCHEMA = z.object({
  id: z.string(),
});
