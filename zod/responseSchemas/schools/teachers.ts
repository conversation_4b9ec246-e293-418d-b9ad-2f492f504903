import { z } from 'zod';

import {
  TEACHERS_INVITATION_STATES,
  TEACHERS_STATUS_STATES,
  ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
} from '@/zod/zodConstants';

export const TEACHERS_DETAILS_SCHEMA = z.object({
  id: z.string(),
  userId: z.string(),
  customerId: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  status: TEACHERS_STATUS_STATES,
  affiliation: z.string().nullable().optional(),
  joined: z.string().optional().nullable(),
  classes: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        grade: z.string(),
        studentCount: z.string().optional(),
      })
    )
    .nullable()
    .optional(),
  access: z.string().nullable().optional(),
  invitation: z.object({
    id: z.string(),
    state: TEACHERS_INVITATION_STATES,
    createdAt: z.string(),
    expiresAt: z.string(),
  }),
});

export const TEACHER_SCHEMA = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email().optional(),
  phoneNumber: z.string().nullable().optional(),
  organisation: z.string().nullable().optional(),
  userId: z.string(),
  id: z.string(),
  status: TEACHERS_STATUS_STATES,
  affiliation: z.string().nullable().optional(),
  joined: z.string().optional().nullable(),
  invitation: z.object({
    id: z.string(),
    state: TEACHERS_INVITATION_STATES,
    createdAt: z.string(),
    expiresAt: z.string(),
  }),
  dob: z.string().nullable().optional(),
  address: z
    .object({
      addressLine1: z.string(),
      addressLine2: z.string().nullable(),
      city: z.string(),
      state: z.string().nullable(),
      postcode: z.string(),
      country: z.string(),
    })
    .nullable()
    .optional(),
  certificates: z.array(
    z.object({
      type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
      version: z.number(),
      progress: z
        .object({
          id: z.string(),
          completed: z.optional(z.string().nullable()),
        })
        .nullable()
        .optional(),
      onboarded: z.boolean().optional().nullable(),
      students: z.number().optional().nullable(),
    })
  ),
});

export const GET_TEACHERS_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(TEACHERS_DETAILS_SCHEMA),
});

export const GET_TEACHERS_LIST_SCHEMA = z.object({
  page: z.number(),
  limit: z.number(),
  count: z.number(),
  totalCount: z.number(),
  results: z.array(
    z.object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
    })
  ),
});
