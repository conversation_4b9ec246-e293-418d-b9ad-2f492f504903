import { z } from 'zod';

export const GET_STUDENT_LOGIN_WELCOME_DETAILS_SCHEMA = z.object({
  student: z.object({
    name: z.string(),
    surname: z.string().optional(),
    // Its the student Anonymous code not Login(Enter-code) or session test code
    code: z.string().optional(),
    grade: z.string().optional(),
  }),
  //   The grade of the test
  grade: z.string(),
  welcome: z.string(),
});
