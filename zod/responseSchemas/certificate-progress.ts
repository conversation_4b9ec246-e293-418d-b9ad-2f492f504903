import { z } from 'zod';

import { ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE } from '../zodConstants';

export const CERTIFICATE_PROGRESS_SCHEMA_BY_TYPE = z.object({
  id: z.string(),
  name: z.string(),
  language: z.string(),
  type: ZOD_MATH_PRO_AVAILABLE_PRODUCTS_TYPE,
  version: z.number(),
  description: z.string(),
  startDate: z.string().optional(),
  completed: z.string().optional(),
  modules: z.array(
    z.object({
      order: z.number(),
      count: z.number(),
      answered: z.number(),
      title: z.string(),
      description: z.string(),
      lesson: z
        .object({
          format: z.enum(['Video', 'PDF']),
          url: z.string(),
          source: z.string(),
        })
        .optional(),
      active: z.boolean().nullable(),
      video: z.string().optional(),
      startDate: z.string().optional(),
      completed: z.string().optional(),
    })
  ),
});

export const MODULE_PROGRESS_SCHEMA_BY_CERTIFICATE_TYPE_AND_ORDER = z.object({
  order: z.number(),
  count: z.number(),
  answered: z.number(),
  title: z.string(),
  description: z.string(),
  lesson: z.optional(
    z.object({
      format: z.enum(['Video', 'PDF']),
      source: z.string(),
      url: z.string(),
    })
  ),
  active: z.optional(z.boolean()),
  startDate: z.optional(z.string()),
  completed: z.optional(z.string()),
  questions: z.array(
    z.object({
      order: z.number(),
      type: z.enum(['single', 'multiple']),
      content: z.string(),
      image: z.optional(z.string()),
      selected: z.union([z.array(z.number()), z.number()]).optional(),
      options: z.array(
        z.object({
          order: z.number(),
          content: z.string(),
        })
      ),
    })
  ),
});

export const CERTIFICATE_PROGRESS_MODULE_ANSWER_SCHEMA = z.object({
  correct: z.boolean(),
  results: z.enum([
    'ModuleActive',
    'ModuleFailed',
    'ModuleComplete',
    'CertificateComplete',
  ]),
  score: z.number().optional(),
});
