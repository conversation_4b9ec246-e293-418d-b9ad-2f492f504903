/* eslint-disable no-await-in-loop */
/* eslint-disable no-param-reassign */
/* eslint-disable no-octal-escape */
/* eslint-disable no-plusplus */
/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs').promises;
const { exit } = require('process');
const readline = require('readline');

const red = '\x1b[1;31m';
const green = '\x1b[1;32m';
const yellow = '\x1b[1;33m';
const noColor = '\x1b[0m';

// First language is the reference language, the rest are checked against it
const languages = ['en', 'el', 'sv', 'tr'];

const filePaths = {
  webApp: './translations/',
};

const arrayDifference = (arr1, arr2) => arr1.filter((x) => !arr2.includes(x));

const getKeys = (obj) => {
  const objectKeys = Object.keys(obj);
  const keys = [];

  for (let i = 0; i < objectKeys.length; i++) {
    const key = objectKeys[i];

    if (typeof obj[key] === 'object') {
      keys.push(...getKeys(obj[key]).map((k) => `${key}.${k}`));
    } else {
      keys.push(key);
    }
  }

  return keys;
};

const checkKeyParity = (files, app) => {
  let hasParity = true;

  for (let i = 1; i < files.length; i++) {
    const keys = getKeys(files[0]);
    const otherKeys = getKeys(files[i]);

    const difference = arrayDifference(keys, otherKeys);

    readline.clearLine(process.stdout);
    process.stdout.write('\n');

    if (difference.length > 0) {
      hasParity = false;

      process.stdout.write(
        `${red}X${noColor} Missing keys found in ${yellow}${app}/${languages[i]}${noColor} (compared to ${yellow}${app}/${languages[0]}${noColor}): \n`
      );

      difference.forEach((key) => {
        process.stdout.write(`${red}    ${key}${noColor}\n`);
      });

      process.stdout.write('\n');
    }
  }

  return hasParity;
};

const apps = Object.keys(filePaths);

let allOk = true;

const main = async () => {
  await Promise.all(
    apps.map(async (app) => {
      process.stdout.write(
        `${yellow}❯${noColor} Checking translations for "${app}"...`
      );

      const test = languages.map(async (language) => {
        const path = `${filePaths[app]}${language}.json`;
        const file = await fs.readFile(path);
        return [JSON.parse(file)];
      });

      const result = await Promise.all(test);

      const files = result.map((r) => r[0]);

      const hasParity = checkKeyParity(files, app);

      if (hasParity) {
        process.stdout.write(
          `${green}✓${noColor} Translations for ${yellow}${app}${noColor} are in parity\n`
        );
      } else {
        allOk = false;
      }
    })
  );

  if (allOk) {
    exit(0);
  } else {
    exit(1);
  }
};

main();
