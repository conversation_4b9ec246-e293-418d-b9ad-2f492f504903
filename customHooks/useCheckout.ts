import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import { UserContext } from '@/context/UserProvider';
import { ProductDetailsType } from '@/types/common';

import { PaymentResultStatusType } from '../components/Layouts/AppLayout/CheckoutModal/components/types';

const useCheckout = () => {
  // const [isModalVisible, setIsModalVisible] = useState(false);
  const [productDetails, setProductDetails] = useState<
    ProductDetailsType[] | null
  >(null);

  const [errorCode, setErrorCode] = useState<string>('');

  const queryClient = useQueryClient();

  const { invalidateUser } = UserContext();

  const [displayedScreen, setDisplayedScreen] = useState<
    'form' | PaymentResultStatusType
  >('form');

  const openCheckout = (details: ProductDetailsType[] | null) => {
    setProductDetails(details);
    // TODO : there is no need to have double state to know when to open the modal. Having a product means that the modal should be open
    // setIsModalVisible(true);
  };

  const onCloseCheckout = () => {
    // setIsModalVisible(false);
    setProductDetails(null);
    setDisplayedScreen('form');
  };

  const handlePaymentProcessing = (
    type: PaymentResultStatusType,
    errorCodeMessage?: string
  ) => {
    // TODO : Possible error, each time the handle payment is being use it will always invalidate the user, but also the certification products
    // So if i buy a license that is not related to a certification, it will still invalidate the certification products which is not needed
    // The constant invalidation of the user might be correct cause under the profile we need to show the latest information but maybe we need the purchases list as well
    if (type === 'success') {
      // TODO replace with log polling to confirm the payment is registered in BE
      setTimeout(() => {
        // Having here just to wait for the payment to be registered in BE
        invalidateUser();

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.CERTIFICATION_PRODUCTS],
        });

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.LICENSES_LIST],
        });
      }, 3000);

      setDisplayedScreen('success');
    } else if (type === 'failed') {
      setErrorCode(errorCodeMessage || '');
      setDisplayedScreen('failed');
    }
  };

  return {
    // isCheckoutModalVisible: isModalVisible,
    isCheckoutModalVisible: Boolean((productDetails?.length || 0) > 0),
    openCheckout,
    onCloseCheckout,
    productDetails,
    displayedScreen,
    handlePaymentProcessing,
    errorCode,
  };
};

export default useCheckout;
