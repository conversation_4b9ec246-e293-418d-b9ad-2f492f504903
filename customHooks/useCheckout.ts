import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import { UserContext } from '@/context/UserProvider';
import { ProductDetailsType } from '@/types/common';

import { PaymentResultStatusType } from '../components/Layouts/AppLayout/CheckoutModal/components/types';

const useCheckout = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [productDetails, setProductDetails] = useState<
    ProductDetailsType[] | null
  >(null);

  const [errorCode, setErrorCode] = useState<string>('');

  const queryClient = useQueryClient();

  const { invalidateUser } = UserContext();

  const [displayedScreen, setDisplayedScreen] = useState<
    'form' | PaymentResultStatusType
  >('form');

  const openCheckout = (details: ProductDetailsType[] | null) => {
    setProductDetails(details);
    setIsModalVisible(true);
  };

  const onCloseCheckout = () => {
    setIsModalVisible(false);
    setProductDetails(null);
    setDisplayedScreen('form');
  };

  const handlePaymentProcessing = (
    type: PaymentResultStatusType,
    errorCodeMessage?: string
  ) => {
    if (type === 'success') {
      // TODO replace with log polling to confirm the payment is registered in BE
      setTimeout(() => {
        invalidateUser();

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.CERTIFICATION_PRODUCTS],
        });
      }, 3000);

      setDisplayedScreen('success');
    } else if (type === 'failed') {
      setErrorCode(errorCodeMessage || '');
      setDisplayedScreen('failed');
    }
  };

  return {
    isCheckoutModalVisible: isModalVisible,
    openCheckout,
    onCloseCheckout,
    productDetails,
    displayedScreen,
    handlePaymentProcessing,
    errorCode,
  };
};

export default useCheckout;
