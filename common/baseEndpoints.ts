const BASE_ENDPOINT_PATHS = {
  LOGIN: 'auth/login',
  LOGIN_OTP: 'auth/login-otp',
  LOGOUT: 'auth/logout',
  SIGNUP: 'auth/complete-registration',
  ACCEPT_INVITATION: 'auth/accept-invitation',
  FORGOT_PASSWORD: 'auth/forgot-password',
  CHANGE_PASSWORD: 'auth/change-password',
  RESET_PASSWORD: 'auth/reset-password',
  CREATE_PAYMENT_INTENT: 'purchases/create-payment-intent',
  USER_OWNS_PRODUCT: 'users/owns-product',
  PRODUCTS: 'products',
  PRODUCTS_CERTIFICATES: 'products/certificates',
  USER_DETAILS: 'users/details',
  CERTIFICATES_PROGRESS: 'certificates/progress',
  ALL_TESTS: 'tests/type/all',
  PROFILES: 'profiles',
  PROFILES_COMPLETE_ON_BOARD: 'profiles/complete-onboard',
  PROFILE_IMAGE_URL: 'profiles/upload-profile-pic',
  PURCHASES_HISTORY: 'purchases',
  ALL_SCHOOL_TEACHERS: 'schools/teachers',
  SCHOOLS_STUDENTS: 'schools/students',
  SCHOOLS_STUDENTS_UNCLAIMED: 'schools/students/unclaimed',
  STUDENTS_STUDENT: 'students/student',
  INVITATIONS: 'invitations',
  CREATE_INVOICE: 'purchases/create-invoice',
  SCHOOL_CLASSES: 'schools/classes',
  STUDENTS_GROUPS: 'students/groups',
  STUDENTS_SKILLS_DEFAULTS: 'students/skills/defaults',
  DATA_IMPORT: 'data/import',
  DATA_IMPORT_TEMPLATE: 'data/import/template',
  DATA_IMPORT_UPLOAD_URL: 'data/import/upload-url',
  CLAIM_SCHOOL_STUDENTS: 'schools/students/assign/claim',
  UNCLAIM_SCHOOL_STUDENTS: 'schools/students/assign/unclaim',
  TESTS_SESSIONS_PAGING_TYPE: 'tests/sessions/paging/type',
  TESTS_SESSIONS_STUDY: 'tests/sessions/paging/study',
  TESTS_SESSIONS_SESSION: 'tests/sessions/session',
  TESTS_SESSIONS_CREATE: 'tests/sessions/create',
  TESTS_SESSIONS_GET_CODE: 'tests/sessions/get/code',
  TESTS_SESSIONS_EXPORT: 'tests/sessions/export/queue/type/',
  STUDIES_LIST: 'studies/list',
  RESULTS: 'results/session',
  ADMIN_USERS: 'admin/users',
  ADMIN_STUDIES: 'admin/studies',
  ADMIN_SCHOOLS: 'admin/schools',
} as const;

export default BASE_ENDPOINT_PATHS;
