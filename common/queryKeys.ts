const QUERY_KEYS = {
  // Represents a list of students fetched as an independent teacher
  INDEPENDENT_TEACHER_STUDENT_LIST: 'independent-teacher-students-list',
  // Represents a list of students fetched as a school teacher or school admin
  SCHOOL_STUDENT_LIST: 'school-students-list',
  // Represents a list of students fetched as a school teacher only to populate your dashboard from the the school students
  UNCLAIMED_SCHOOL_STUDENT_LIST: 'unclaimed-school-students-list',
  // Represents a list of subtests thats needed to create a new session under conduct test modal
  SUBTESTS_LIST_BY_TEST_TYPE: 'subtests-by-test-type',
  // Represents A list of supported languages for each test type to conduct a session test under conduct test modal
  TEST_TYPE_SUPPORTED_LANGUAGE_LIST: 'test-type-supported-languages',
  // Represents a list of classes that belongs to a school
  CLASSES_LIST: 'classes-list',
  // Represents a list of groups that belongs to an independent teacher
  GROUPS_LIST: 'groups-list',
  // Represents the user of the whole app
  USER: 'user',
  // Represents a list of certification products that is being used under cart-certifications tab
  CERTIFICATION_PRODUCTS: 'certification-products',
  // Represents a list of created session tests from the conduct test modal
  ALL_CREATED_SESSION_TESTS: 'all-created-session-tests',
  ALL_CREATED_SESSION_TESTS_STUDY: 'all-created-session-tests-study',
  //   ------------
  // Example mathpro-s, mathpro-d etc
  ALL_APPLICATION_TESTS: 'all-application-tests',
  CERTIFICATE_PROGRESS_BY_TEST_TYPE: 'certificate-progress-by-test-type',
  CERTIFICATION_TEST_DETAILS_BY_TEST_TYPE:
    'certification-test-details-by-test-type',
  MODULE_DETAILS: 'module-details',
  CLASS_BY_ID: 'class-by-id',
  GROUP_BY_ID: 'group-by-id',
  SCHOOL_STUDENT_BY_ID: 'school-student',
  INDEPENDENT_TEACHER_STUDENT_BY_ID: 'independent-teacher-student',
  DEFAULT_STUDENT_SKILLS: 'default-student-skills',
  SCHOOL_TEACHERS_LIST: 'school-teachers-list',
  TEACHER_BY_ID: 'teacher-by-id',
  PURCHASE_HISTORY_LIST: 'purchase-history-list',
  CERTIFICATION_PURCHASE_PAYMENT_INTENT:
    'certification-purchase-payment-intent',
  PRODUCT_BY_ID: 'product-by-id',
  CREATE_INVOICE_FOR_PURCHASE: 'create-invoice-for-purchase',
  UPLOAD_PROGRESS_DURING_THE_CREATION_OF_STUDENTS: 'progress-data',
  TEST_SESSION_DETAILS_BY_ID: 'test-session-details-by-id',
  TEST_SESSION_INTRO_BY_ID: 'test-session-intro-by-id',
  TEST_ADJUST_AUDIO_BY_ID: 'test-adjust-audio-by-id',
  TEST_TRANSLATIONS_BY_ID: 'test-translations-by-id',
  TEST_SESSION_CURRENT_QUESTION: 'test-session-current-question',
  STUDIES_LIST: 'studies-list',
  TEST_RESULTS_BY_ID: 'test-results-by-id',
  TEST_RESULTS_ERRORS_BY_ID: 'test-results-errors-by-id',
  ADMIN_USERS: 'admin-users',
  ADMIN_SCHOOLS: 'admin-schools',
  ADMIN_STUDIES: 'admin-studies',
  ADMIN_STUDIES_PAGINATED_LIST: 'admin-studies-paginated-list',
  ADMIN_STUDIES_LANGUAGES: 'admin-studies-languages',
  ADMIN_STUDY_BY_ID: 'admin-study-by-id',
  UNASSIGNED_STUDY_ADMINS: 'unassigned-study-admins',
  ADMIN_STUDY_TESTS: 'admin-study-tests',
  ADMIN_UNASSIGNED_RESEARCHERS: 'admin-unassigned-researchers',
  ADMIN_UNASSIGNED_SCHOOL_ADMINS: 'admin-unassigned-school-admins',
  CREATE_SESSION_TEST_CHECK: 'create-session-test-check',
} as const;

export default QUERY_KEYS;
