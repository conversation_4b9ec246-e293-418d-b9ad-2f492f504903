.root {
  border-radius: var(--radius-2xs);
  padding: 0;
  color: var(--color-white);
  transition: all 0.3s ease-in-out;
}

/* Locked variant */
.root[data-variant='locked'] {
  background-color: var(--color-gray500);
  opacity: 0.5;
}

.root[data-variant='locked']:hover {
  cursor: not-allowed;
}

/* Primary variant */
.root[data-variant='primary'] {
  background-color: var(--color-blue);
}

.root[data-variant='primary']:hover {
  background-color: var(--color-darkerBlue);
}

/* Primary outlined variant */
.root[data-variant='primaryOutlined'] {
  background-color: transparent;
  border: 1px solid var(--color-blue);
}

.root[data-variant='primaryOutlined']:hover {
  border: 1px solid var(--color-darkerBlue);
}

/* Danger variant */
.root[data-variant='danger'] {
  background-color: var(--color-danger);
}

.root[data-variant='danger']:hover {
  background-color: var(--color-darkerDanger);
}

/* Danger outlined variant */
.root[data-variant='dangerOutlined'] {
  background-color: transparent;
  border: 1px solid var(--color-danger);
}

/* Success variant */
.root[data-variant='success'] {
  background-color: var(--color-green);
}

.root[data-variant='success']:hover {
  background-color: var(--color-darkerGreen);
}

/* Dark variant */
.root[data-variant='dark'] {
  background-color: var(--color-black);
}

.root[data-variant='dark']:hover {
  background-color: var(--color-gray800);
}

/* Test variant */
.root[data-variant='test'] {
  background-color: var(--color-yellow);
  color: var(--color-black);
}

.root[data-variant='test']:hover {
  opacity: 0.8;
}

/* Text variant */
.root[data-variant='text'] {
  background-color: var(--color-transparent);
  height: 22px !important;
}

/* White variant */
.root[data-variant='white'] {
  background-color: var(--color-white);
  border-radius: var(--radius-xs);
}

.root[data-variant='white']:hover {
  opacity: 0.8;
}

/* primaryRounded variant */
.root[data-variant='primaryRounded'] {
  background-color: var(--color-blue);
  border-radius: var(--radius-xs);
}

.root[data-variant='primaryRounded']:hover {
  background-color: var(--color-darkerBlue);
}

.root[data-loading='true'] {
  opacity: 0.8;
}

.root:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.root:disabled:hover {
  opacity: 0.4;
}

/* Turquoise variant */

.root[data-variant='turquoisePrimary'] {
  background-color: var(--color-turquoise);
  height: 22px !important;
}

.root[data-variant='turquoisePrimary']:hover {
  opacity: 0.8;
}
