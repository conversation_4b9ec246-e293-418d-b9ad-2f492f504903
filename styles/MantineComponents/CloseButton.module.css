.root {
  width: 46px;
  height: 46px;
  /* border-radius: 5px; */
  border-radius: var(--radius-2xs);

  &[data-variant='primary'] {
    background-color: var(--color-blue);

    &:hover {
      background-color: var(--color-darkerBlue);
    }

    & svg {
      color: var(--color-white);
    }
  }

  &[data-variant='outlined'] {
    background-color: var(--color-white);
    border: 1px solid var(--color-blue);

    &:hover {
      border-color: var(--color-darkerBlue);

      & svg {
        color: var(--color-darkerBlue);
      }
    }

    & svg {
      color: var(--color-blue);
    }
  }
}
