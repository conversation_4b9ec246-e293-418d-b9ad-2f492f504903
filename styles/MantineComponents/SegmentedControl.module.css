.root {
  background-color: transparent;
  border: 1px dashed var(--color-gray400);
  height: 50px !important;
  min-width: fit-content !important;

  &[data-variant='icons'] {
    height: 39px !important;

    .control {
      min-width: 30px;
      max-width: 30px;
      min-height: 30px;
      max-height: 30px;
      padding: 0 -2px;
    }

    .label {
      display: flex;
      justify-content: center;
      align-items: center;

      height: 28px !important;
    }

    .innerLabel {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: -2px;
      margin-bottom: -2px;
    }

    .indicator {
      min-height: 28px !important;
      max-height: 28px !important;
      height: 28px !important;
      width: 30px !important;
      min-width: 30px !important;
      max-width: 30px !important;
    }
  }

  &[data-variant='fit'] {
    .control {
      min-width: fit-content;
    }
  }

  &[data-variant='fit-sm'] {
    height: 42px;

    .control {
      min-width: fit-content;
    }
  }
}

.control {
  min-width: 140px;
}

.label {
  height: 39px !important;

  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray900);

  &[data-active='true'] {
    color: var(--color-white);
  }
}

.indicator {
  box-shadow: none;
  background-color: var(--color-turquoise);
  min-height: 39px;
  width: 100%;
  min-width: 140px;
  max-width: 140px;
  border-radius: 5px;
}

.innerLabel {
}
