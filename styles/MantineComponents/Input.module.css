.input[data-variant='primary'] {
  height: 56px;
  border: 1px solid var(--color-gray300);
  /* box-shadow: 0px 0px 1px var(--color-gray400); */
  padding: 0 var(--spacing-md);
  font-size: 14px;
  font-weight: 400;
  border-radius: 5px;
  transition: background-color 0.6s ease;
  background-color: var(--color-white);
  transition: all 0.3 ease;
}

.input[data-variant='primary']:focus {
  border-color: var(--color-blue);
}

.input[data-variant='primary']:focus-within {
  border-color: var(--color-blue);
}

.input[data-variant='primary'][data-error] {
  border-color: var(--color-error);
}

.input[data-variant='primary'][data-error]:focus {
  border: 1px solid var(--color-red);
}

.input[data-variant='primary'][data-error]:focus-within {
  border: 1px solid var(--color-red);
}

.input[data-variant='primary'][data-disabled] {
  background-color: var(--color-bg3);
}

.input[data-variant='filled'] {
  height: 42px;
  padding: 0 var(--spacing-md);
  padding-right: 60px;
  font-size: 14px;
  font-weight: 400;
  border-radius: 5px;
  width: 387px;
  background-color: var(--color-bg3);
}

.input[data-variant='filled']:focus {
  border-color: var(--color-blue);
}

.input[data-variant='filled']:focus-within {
  border-color: var(--color-blue);
}

.input[data-variant='filled'][data-error] {
  border-color: var(--color-error);
}

.input[data-variant='filled'][data-error]:focus {
  border: 1px solid var(--color-red);
}

.input[data-variant='filled'][data-error]:focus-within {
  border: 1px solid var(--color-red);
}

.input[data-variant='simple'] {
  height: 42px;
  padding: 0 var(--spacing-md);
  padding-right: 60px;
  font-size: 14px;
  font-weight: 400;
  border-radius: 5px;
  width: 387px;
}

.input[data-variant='simple']:focus {
  border-color: var(--color-blue);
}

.input[data-variant='simple']:focus-within {
  border-color: var(--color-blue);
}

.input[data-variant='simple'][data-error] {
  border-color: var(--color-error);
}

.input[data-variant='simple'][data-error]:focus {
  border: 1px solid var(--color-red);
}

.input[data-variant='simple'][data-error]:focus-within {
  border: 1px solid var(--color-red);
}
