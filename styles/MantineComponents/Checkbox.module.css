/* Primary Variant */
.root[data-variant='primary'] {
  border-color: var(--color-turquoise);
}

.root[data-variant='primary'] .input {
  cursor: pointer;
}

.root[data-variant='primary'] .input:checked {
  border-color: var(--color-turquoise);
  background-color: var(--color-turquoise);
}

.root[data-variant='primary'] .input:disabled {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='primary'] .input[data-indeterminate='true'] {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='primary'] .label {
  pointer-events: none;

  font-size: '17px';
  font-weight: 400;
}

/* Outlined Variant */
.root[data-variant='outlined'] {
  border-color: var(--color-turquoise);
}

.root[data-variant='outlined'] .input {
  cursor: pointer;
  background-color: transparent;
}

.root[data-variant='outlined'] .input:checked {
  border-color: var(--color-turquoise);
  background-color: var(--color-turquoise);
}

.root[data-variant='outlined'] .input:disabled {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='outlined'] .input[data-indeterminate='true'] {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='outlined'] .label {
  pointer-events: none;
  font-size: '17px';
  font-weight: 400;
}

/* primaryList Variant */
.root[data-variant='primaryList'] {
  border-color: var(--color-turquoise);
}

.root[data-variant='primaryList'] .input {
  cursor: pointer;
}

.root[data-variant='primaryList'] .input:checked {
  border-color: var(--color-turquoise);
  background-color: var(--color-turquoise);
}

.root[data-variant='primaryList'] .input:disabled {
  background-color: transparent;
  border-color: lightGray;
  opacity: 0.5;
  cursor: not-allowed;
}

.root[data-variant='primaryList'] .input[data-indeterminate='true'] {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='primaryList'] .label {
  pointer-events: none;
}

.root[data-variant='primaryList'] .input:disabled .label {
  color: lightGray !important;
  opacity: 0.5 !important;
}

/* Card Variant */
.root[data-variant='card'] {
  border-color: transparent;
}

.root[data-variant='card'] .input {
  cursor: pointer;
  background-color: transparent;
}

.root[data-variant='card'] .input:checked {
  border-color: var(--color-white);
  background-color: var(--color-turquoise);

  background-color: transparent;
}

.root[data-variant='card'] .input:disabled {
  background-color: transparent;
  border-color: lightGray;
  opacity: 0.4;
  cursor: not-allowed;
}

.root[data-variant='card'] .input[data-indeterminate='true'] {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='card'] .input[data-locked='true'] {
  pointer-events: none;
}

.root[data-variant='card'] .label[data-disabled='true'] {
  opacity: 0.4;
}

.root[data-variant='card'] .label {
  pointer-events: none;

  color: var(--color-white);
  font-size: 17px;
  font-weight: 700;
}
