/* Primary Variant */
.root[data-variant='primary'] {
  border-color: var(--color-turquoise);
}

.root[data-variant='primary'] .input {
  cursor: pointer;
}

.root[data-variant='primary'] .input:checked {
  border-color: var(--color-turquoise);
  background-color: var(--color-turquoise);
}

.root[data-variant='primary'] .input:disabled {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='primary'] .input[data-indeterminate='true'] {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='primary'] .label {
  pointer-events: none;
}

/* Outlined Variant */
.root[data-variant='outlined'] {
  border-color: var(--color-turquoise);
}

.root[data-variant='outlined'] .input {
  cursor: pointer;
  background-color: transparent;
}

.root[data-variant='outlined'] .input:checked {
  border-color: var(--color-turquoise);
  background-color: var(--color-turquoise);
}

.root[data-variant='outlined'] .input:disabled {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='outlined'] .input[data-indeterminate='true'] {
  background-color: transparent;
  border-color: lightGray;
}

.root[data-variant='outlined'] .label {
  pointer-events: none;
}
