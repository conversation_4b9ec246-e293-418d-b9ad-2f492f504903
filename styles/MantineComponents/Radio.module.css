.root[data-variant='outline'] .radio {
  cursor: pointer;
  background-color: transparent;
}

.root[data-variant='outline'] .radio:checked {
  border-color: var(--color-turquoise);
}

.root[data-variant='outline'] .radio:disabled {
  border-color: lightgray;
  opacity: 0.7;
  cursor: not-allowed;
}

.root[data-variant='outline'] .icon {
  color: var(--color-turquoise);
  width: 11px;
  height: 11px;
  transform: translate(-1.6px, -1.7px);
}

.root[data-variant='outline'] .label {
  pointer-events: none;
}
