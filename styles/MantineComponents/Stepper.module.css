.content {
  margin-top: var(--spacing-md);
}

.step {
  .stepLabel {
    color: rgb(128, 130, 130);
  }

  .stepIcon {
    background-color: transparent;
    border: 2px solid var(--color-gray200);
    color: rgb(128, 130, 130);

    min-height: 34px !important;
    min-width: 34px !important;
    max-height: 34px !important;
    max-width: 34px !important;
  }
}

.step[data-progress='true'] {
  .stepLabel {
    color: black;
  }

  .stepIcon {
    background-color: var(--color-white);
    border: 2px solid var(--color-blue);
    color: black;

    min-height: 34px !important;
    min-width: 34px !important;
    max-height: 34px !important;
    max-width: 34px !important;
  }
}

.step[data-completed='true'] {
  .stepLabel {
    color: var(--color-blue);
  }

  .stepIcon {
    background-color: var(--color-blue);
    border: 2px solid var(--color-blue);
    color: var(--color-white);
  }
}

.separator {
  background-color: var(--color-gray200);
}

.separator[data-active='true'] {
  background-color: var(--color-blue);
}
