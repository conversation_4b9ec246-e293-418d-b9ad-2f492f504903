{"env": {"node": true, "browser": true, "es2021": true}, "extends": ["next/core-web-vitals", "next/typescript", "eslint:recommended", "prettier", "plugin:react/recommended", "airbnb", "eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:jsx-a11y/recommended", "plugin:prettier/recommended"], "ignorePatterns": [".*.js", "node_modules/", "dist/", "*.d.ts", "**/design-system/*", "**/node_modules/**", "node_modules/**", "out/*", ".next/*", "**/out/*", "**/.next/*"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["only-warn", "react", "@typescript-eslint", "simple-import-sort", "sort-destructure-keys", "check-file"], "root": true, "globals": {"React": true, "JSX": true}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}, "overrides": [{"files": ["*.js?(x)", "*.ts?(x)"]}], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-shadow": ["error"], "check-file/filename-blocklist": ["error", {"**/*.style.ts": "*.styles.ts"}], "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "import/no-cycle": "off", "import/no-extraneous-dependencies": "off", "import/no-relative-packages": "off", "import/prefer-default-export": "off", "jsx-a11y/anchor-is-valid": ["error", {"aspects": ["<PERSON><PERSON><PERSON><PERSON>", "preferButton"], "components": ["Link"], "specialLink": ["hrefLeft", "hrefRight"]}], "no-restricted-globals": "off", "no-shadow": "off", "no-underscore-dangle": ["error", {"allow": ["_id"]}], "padding-line-between-statements": ["error", {"blankLine": "always", "next": "if", "prev": "*"}], "prettier/prettier": ["error", {"singleQuote": true, "trailingComma": "es5"}], "react/function-component-definition": ["error", {"namedComponents": "arrow-function", "unnamedComponents": "arrow-function"}], "react/jsx-curly-newline": "off", "react/jsx-filename-extension": [2, {"extensions": [".js", ".jsx", ".ts", ".tsx"]}], "react/jsx-one-expression-per-line": "off", "react/jsx-props-no-spreading": "off", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/require-default-props": "off", "simple-import-sort/exports": "error", "simple-import-sort/imports": "error", "sort-destructure-keys/sort-destructure-keys": [1, {"caseSensitive": false}]}}