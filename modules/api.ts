/* eslint-disable @typescript-eslint/no-explicit-any */
import * as Sen<PERSON> from '@sentry/nextjs';
import axios, { AxiosResponse } from 'axios';
import Router from 'next/router';
import { z, ZodType } from 'zod';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { GLOBAL_ERRORS } from '@/common/errors';

import { HTTP_STATUS_CODES } from '../common/consts';
import { PUBLIC_ROUTES } from '../common/routes';

type HTTP_METHODS = 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const IS_DEVELOPMENT = process.env.NEXT_PUBLIC_NODE_ENV === 'development';

const FORWARD_401_ERROR_UNDER_THOSE_PUBLIC_ROUTES: string[] = [
  PUBLIC_ROUTES.LOGIN,
  PUBLIC_ROUTES.COMPLETE_REGISTRATION,
  PUBLIC_ROUTES.STUDENT_LOGIN,
];

const FORWARD_401_ERROR_UNDER_THOSE_PATHS: string[] = [
  BASE_ENDPOINT_PATHS.CHANGE_PASSWORD,
];

const apiClient = axios.create({
  baseURL: `${BASE_API_URL}`,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

const handleResponseInDevelopment = <U extends ZodType>(
  response: AxiosResponse<any, any>,
  responseSchema: U
) => {
  const { data, error, success } = responseSchema.safeParse(response.data);

  if (error) {
    // eslint-disable-next-line no-console
    console.log('error', error);
  }

  if (!success) {
    throw new Error('zod-error', error);
  }

  return {
    data,
    status: response.status,
    hasError: false,
  };
};

const handleUnauthorizedResponse = (requestedApiPath: string) => {
  if (
    !FORWARD_401_ERROR_UNDER_THOSE_PUBLIC_ROUTES.includes(Router.pathname) &&
    !FORWARD_401_ERROR_UNDER_THOSE_PATHS.includes(requestedApiPath)
  ) {
    throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
  }
};

const handleAxiosError = (
  path: string,
  status: number,
  code: string,
  errors: any,
  cause: any
) => {
  if (status === HTTP_STATUS_CODES.UNAUTHORIZED)
    handleUnauthorizedResponse(path);

  if (status === HTTP_STATUS_CODES.SERVER_ERROR)
    throw new Error(GLOBAL_ERRORS.SERVER_ERROR);

  if (code === 'ERR_NETWORK') throw new Error(GLOBAL_ERRORS.REQUEST_TIMEOUT);

  return {
    data: null,
    status,
    hasError: true,
    errors,
    cause,
  };
};

const request = async <U extends ZodType>(
  path: string,
  method: HTTP_METHODS = 'GET',
  params = {},
  responseSchema: U | null = null
): Promise<{
  data: z.infer<U> | null;
  status: number;
  hasError: boolean;
  cause?: string;
  errors?: string[];
}> => {
  try {
    const response = await apiClient.request({
      url: path,
      method,
      ...(params && { data: params }),
    });

    // We do not safe parse in production, Only runs in development
    if (responseSchema && IS_DEVELOPMENT) {
      handleResponseInDevelopment(response, responseSchema);
    }

    return {
      data: response.data,
      status: response.status,
      hasError: false,
    };
  } catch (error: unknown) {
    const isZodError = (error as Error)?.message === 'zod-error';
    const isAxiosError = axios.isAxiosError(error);
    Sentry.captureException(error);

    if (isZodError) {
      throw new Error(GLOBAL_ERRORS.ZOD_ERROR);
    }

    if (isAxiosError) {
      return handleAxiosError(
        path,
        error.response?.status || HTTP_STATUS_CODES.BAD_REQUEST,
        error.code || '',
        error.response?.data.errorMessage,
        error.response?.data.cause
      );
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }
};

export default request;
