import i18next from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';

import { translations } from '@/translations';

const translationsKeys = Object.keys(translations);

type AccType = {
  [key: string]: {
    translations: {
      [key: string]: string;
    };
  };
};

const finalTranslations = Object.entries(translations).reduce(
  (acc: AccType, [key, value]) => {
    acc[key] = {
      translations: value,
    };

    return acc;
  },
  {}
);

const i18n = async () => {
  return i18next
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      debug: false,
      fallbackLng: 'el',
      interpolation: {
        escapeValue: false, // not needed for react as it escapes by default
      },
      ns: ['translations'],
      react: {
        useSuspense: true,
      },
      resources: finalTranslations,
      returnObjects: true,
      supportedLngs: translationsKeys,
    });
};

export default i18n;
