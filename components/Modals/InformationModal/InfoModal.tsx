'use client';

import { Modal } from '@mantine/core';

import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';

import classes from './InfoModal.module.css';

interface InfoModalProps {
  opened: boolean;
  onClose: () => void;
  title: string;
  content: string | React.ReactNode;
  size?: string | number;
}

export interface ModalState {
  opened: boolean;
  title: string;
  content: string | React.ReactNode;
}

export const InfoModal = ({
  content,
  onClose,
  opened,
  size = 'md',
  title,
}: InfoModalProps) => {
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size={size}
      padding="xl"
      centered
      withCloseButton={false}
      className={classes.modal}
      autoFocus={false}
      trapFocus
    >
      <div className={classes.modalHeader}>
        <Text type="h2" untranslatedText={title} />

        <CloseButton onClick={onClose} variant="outlined" />
      </div>

      <div className={classes.modalContent}>
        {typeof content === 'string' ? (
          <Text untranslatedText={content} />
        ) : (
          content
        )}
      </div>
    </Modal>
  );
};
