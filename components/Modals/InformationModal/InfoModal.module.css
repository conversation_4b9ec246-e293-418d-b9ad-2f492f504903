.close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
}

.modal {
  position: relative;
}

.modalHeader {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.closeButton {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s;
}

.closeButton:hover {
  opacity: 0.7;
}

.modalContent {
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .modalHeader {
    padding-right: 2.5rem;
  }

  .closeButton {
    top: 0;
    right: 0;
  }
}
