import { Modal } from '@mantine/core';
import { JSX } from 'react';

import { TranslationKeysType } from '@/types/common';

import Button from '../../Button/Button';
import Text from '../../Text/Text';
import s from './AlertDialog.module.css';

type AlertDialogPropsType = {
  isOpen: boolean;
  title: TranslationKeysType;
  description?: TranslationKeysType;
  cancelLabel?: TranslationKeysType;
  confirmLabel?: TranslationKeysType;
  onCancel?: () => void;
  onConfirmAction: () => void;
  variant?: 'danger' | 'info';
  isActionInProgress?: boolean;
  transDescriptionVariables?: Record<string, string>;
  isDisabled?: boolean;
};

const AlertDialog = ({
  cancelLabel,
  confirmLabel,
  description,
  isActionInProgress = false,
  isDisabled,
  isOpen,
  onCancel,
  onConfirmAction,
  title,
  transDescriptionVariables,
  variant = 'info',
}: AlertDialogPropsType): JSX.Element => {
  return (
    <Modal
      opened={isOpen}
      onClose={() => {}}
      centered
      classNames={{
        body: s.body,
        content: s.content,
      }}
      withCloseButton={false}
      closeOnClickOutside={false}
      autoFocus={false}
      trapFocus
    >
      <div className={s.wrapper}>
        <Text
          transKey={title}
          type="h3"
          color={variant === 'info' ? 'blue' : 'error'}
        />

        {description && (
          <Text
            transKey={description}
            type="h4"
            transVariables={transDescriptionVariables}
          />
        )}

        <div className={s['actions-wrapper']}>
          {onCancel && (
            <Button
              transKey={cancelLabel || 'cancel-capital'}
              onClick={onCancel}
              isDisabled={isActionInProgress}
              variant={
                variant === 'info' ? 'primaryOutlined' : 'dangerOutlined'
              }
            />
          )}

          <Button
            transKey={confirmLabel || 'yes-capital'}
            onClick={onConfirmAction}
            isLoading={isActionInProgress}
            isDisabled={isDisabled}
            variant={variant === 'info' ? 'primary' : 'danger'}
          />
        </div>
      </div>
    </Modal>
  );
};

export default AlertDialog;
