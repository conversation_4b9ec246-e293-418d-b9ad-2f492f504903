import { Modal } from '@mantine/core';

import s from './PrimaryModal.module.css';

type PrimaryModalPropsType = {
  isOpen: boolean;
  content: JSX.Element | null;
};

const PrimaryModal = ({
  content,
  isOpen,
}: PrimaryModalPropsType): JSX.Element => {
  return (
    <Modal
      opened={isOpen}
      onClose={() => {}}
      fullScreen
      withCloseButton={false}
      transitionProps={{
        duration: 400,
        transition: 'pop',
      }}
      classNames={s}
      withOverlay={false}
      autoFocus={false}
      trapFocus
    >
      {content}
    </Modal>
  );
};

export default PrimaryModal;
