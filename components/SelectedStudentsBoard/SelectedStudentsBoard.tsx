import { LoadingOverlay, ScrollArea } from '@mantine/core';
import { JSX } from 'react';

import Card from '../Card/Card';
import DisplayCloseTag from '../DisplayCloseTag/DisplayCloseTag';
import Text from '../Text/Text';
import s from './SelectedStudentsBoard.module.css';

type SelectedStudentsBoardPropsType = {
  isLoading: boolean;
  scrollAreaHeight: number;
  selectedStudents: { id: string; displayedName: string }[];
  onDeleteStudent: (id: string) => void;
};

const SelectedStudentsBoard = ({
  isLoading,
  onDeleteStudent,
  scrollAreaHeight,
  selectedStudents,
}: SelectedStudentsBoardPropsType): JSX.Element => {
  return (
    <Card size="none" className={s.studentsSelectCardWrapper}>
      <LoadingOverlay
        visible={isLoading}
        zIndex={2}
        overlayProps={{ radius: 'sm', blur: 1 }}
      />

      <div className={s.studentsSelectorWrapper}>
        <Text transKey="selected" type="h4" mb={32} />

        <ScrollArea.Autosize
          h={scrollAreaHeight}
          scrollbarSize={4}
          offsetScrollbars
          classNames={{ thumb: 'thumb' }}
        >
          <div className={s.displayedStudents}>
            {selectedStudents.map((student) => (
              <DisplayCloseTag
                key={student.id}
                text={student.displayedName}
                onClose={() => onDeleteStudent(student.id)}
              />
            ))}
          </div>
        </ScrollArea.Autosize>
      </div>
    </Card>
  );
};

export default SelectedStudentsBoard;
