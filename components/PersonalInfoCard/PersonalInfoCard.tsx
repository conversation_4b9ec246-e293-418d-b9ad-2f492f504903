import { JSX } from 'react';

import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';

import s from './PersonalInfoCard.module.css';

type PersonalInfoCardPropsType = {
  dateOfBirth?: string;
  phoneNumber?: string;
  organisation?: string;
  address?: string;
  theming?: 'white' | 'gray';
};

const PersonalInfoCard = ({
  address,
  dateOfBirth,
  organisation,
  phoneNumber,
  theming = 'white',
}: PersonalInfoCardPropsType): JSX.Element => {
  // The reason i don't map over each field each because i want to have control over the fields for future changes

  const arePersonalInfoFieldsEmpty =
    !dateOfBirth && !phoneNumber && !organisation;

  return (
    <Card size="xl" bg={theming === 'white' ? 'white' : 'gray50'} shadow="none">
      <Text transKey="personal-info" type="h3" />

      <div className={s.wrapper}>
        {arePersonalInfoFieldsEmpty && (
          <Text transKey="profile-click-edit" type="body1" />
        )}

        {dateOfBirth && (
          <div className={s['details-wrapper']}>
            <Text transKey="dob-capital" type="subTitle2" />
            <Text untranslatedText={dateOfBirth} type="body2" isBold />
          </div>
        )}

        {phoneNumber && (
          <div className={s['details-wrapper']}>
            <Text transKey="phone-capital" type="subTitle2" />
            <Text untranslatedText={phoneNumber} type="body2" isBold />
          </div>
        )}

        {address && (
          <div className={s['details-wrapper']}>
            <Text transKey="organisation-capital" type="subTitle2" />
            <Text untranslatedText={address} type="body2" isBold />
          </div>
        )}

        {organisation && (
          <div className={s['details-wrapper']}>
            <Text transKey="organisation-capital" type="subTitle2" />
            <Text untranslatedText={organisation} type="body2" isBold />
          </div>
        )}
      </div>
    </Card>
  );
};

export default PersonalInfoCard;
