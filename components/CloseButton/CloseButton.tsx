import { CloseButton as MantineCloseButton } from '@mantine/core';
import React from 'react';

import { CloseButtonVariantsType } from '@/types/common';

type CloseButtonProps = {
  variant?: CloseButtonVariantsType;
  width?: number;
  height?: number;
  onClick: () => void;
};

const CloseButton = ({
  height,
  onClick,
  variant = 'primary',
  width,
}: CloseButtonProps) => {
  return (
    <MantineCloseButton
      variant={variant}
      onClick={onClick}
      iconSize={24}
      {...(width && { w: { width } })}
      {...(height && { h: { height } })}
      autoFocus={false}
    />
  );
};

export default CloseButton;
