import {
  AvailableTypographyElementsType,
  AvailableTypographyTypeType,
} from './types';

export const TEXT_TYPES: {
  [key in AvailableTypographyTypeType]: {
    fontSize: string;
    fontWeight: number;
    component?: AvailableTypographyElementsType;
  };
} = {
  h1: {
    fontSize: '42px',
    fontWeight: 250,
    component: 'h1',
  },
  h2: {
    fontSize: '32px',
    fontWeight: 250,
    component: 'h2',
  },
  h3: {
    fontSize: '28px',
    fontWeight: 300,
    component: 'h3',
  },
  h4: {
    fontSize: '22px',
    fontWeight: 300,
    component: 'h4',
  },
  subTitle: {
    fontSize: '20px',
    fontWeight: 300,
    component: 'h4',
  },
  subTitle1: {
    fontSize: '16px',
    fontWeight: 400,
  },
  subTitle2: {
    fontSize: '15px',
    fontWeight: 300,
  },
  body1: {
    fontSize: '17px',
    fontWeight: 400,
  },
  body2: {
    fontSize: '14px',
    fontWeight: 400,
  },
  body3: {
    fontSize: '12px',
    fontWeight: 400,
  },
  button: {
    fontSize: '13px',
    fontWeight: 800,
  },
  label: {
    fontSize: '10px',
    fontWeight: 500,
    component: 'span',
  },
} as const;

export const TEXT_COLORS = {
  helperText: '#464A49',
  gray: '#747777',
  black: '#181D1C',
  white: '#ffffff',
  blue: '#1590C1',
  turquoise: '#3A9EA7',
  darkBlue: '#023047',
  danger: '#C66058',
  green: '#5F9B4D',
  darkerGreen: '#478833',
};

export const BASE_BOLD_FONT_WEIGHT = 800;
