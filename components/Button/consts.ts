import { theme } from '@/styles/theming';

const { spacing } = theme.other;

// The whole design has a button height of 46 pixes consistent
// Leave the object here for future reference if we need to add more
export const SIZES = {
  default: {
    h: '46px',
    padding: `0 ${spacing.xl}`,
  },
  md: {
    h: '46px',
    padding: `0 ${spacing.lg}`,
  },
  lg: {
    h: '60px',
    padding: `0 ${spacing.xl}`,
  },
};

export const VARIANT_TEXT_COLORS = {
  primaryOutlined: 'blue',
  dangerOutlined: 'danger',
  text: 'blue',
  test: 'black',
} as const;
