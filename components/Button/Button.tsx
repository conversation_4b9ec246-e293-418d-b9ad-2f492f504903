import { Button as MantineButton } from '@mantine/core';
import { FaLock } from 'react-icons/fa';

import Text from '@/components/Text/Text';
import { ButtonVariantsType, TranslationKeysType } from '@/types/common';

import { SIZES, VARIANT_TEXT_COLORS } from './consts';

type ButtonPropsType = {
  transKey?: TranslationKeysType;
  untranslatedText?: string;
  size?: 'md' | 'lg' | 'default';
  type?: 'button' | 'submit';
  isDisabled?: boolean;
  isLoading?: boolean;
  isLocked?: boolean;
  hasFullWidth?: boolean;
  variant?: ButtonVariantsType;
  rightSection?: JSX.Element;
  leftSection?: JSX.Element;
  onClick?: () => void;
  id?: string;
};

const Button = ({
  hasFullWidth = false,
  id,
  isDisabled = false,
  isLoading = false,
  isLocked = false,
  leftSection,
  onClick: onclick,
  rightSection,
  size = 'default',
  transKey,
  type = 'button',
  untranslatedText,
  variant = 'primary',
}: ButtonPropsType): JSX.Element => {
  return (
    <MantineButton
      id={id}
      variant={isLocked ? 'locked' : variant}
      disabled={isDisabled}
      onClick={isDisabled ? () => {} : onclick}
      fullWidth={hasFullWidth}
      mih={SIZES[size].h}
      p={SIZES[size].padding}
      miw="fit-content"
      loading={isLoading}
      type={type}
      rightSection={isLocked ? <FaLock fontSize={12} /> : rightSection}
      leftSection={leftSection}
      loaderProps={{
        color: variant === 'primaryOutlined' ? 'var(--color-blue)' : 'white',
      }}
    >
      <Text
        transKey={transKey}
        untranslatedText={untranslatedText}
        type="button"
        color={
          VARIANT_TEXT_COLORS?.[variant as keyof typeof VARIANT_TEXT_COLORS] ||
          'white'
        }
      />
    </MantineButton>
  );
};

export default Button;
