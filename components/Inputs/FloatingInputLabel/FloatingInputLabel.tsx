import { Box } from '@mantine/core';
import { useState } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import s from './FloatingInputLabel.module.css';

type FloatingInputLabelPropsType = {
  placeholder: string;
  value: string;
  onChangeValue?: (value: string) => void;
  register?: UseFormRegisterReturn;
  type?: 'text' | 'number';
};

const FloatingInputLabel = ({
  onChangeValue,
  placeholder,
  register,
  type = 'text',
  value,
}: FloatingInputLabelPropsType): JSX.Element => {
  const [isFocused, setIsFocused] = useState(false);

  const isValueEmpty = value === '';

  return (
    <Box className={s.wrapper}>
      <span
        className={`${s.label} ${(isFocused || isValueEmpty) && s.labelHidden}`}
      >
        {placeholder}
      </span>

      <Box className={s.inputWrapper}>
        <input
          {...register}
          value={value}
          placeholder={placeholder}
          {...(onChangeValue && {
            onChange: (event) => onChangeValue(event.currentTarget.value),
          })}
          type={type}
          autoComplete="off"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={`${s.input} ${(!isFocused && isValueEmpty && s.inputLabelVisible) || (isFocused && s.inputLabelVisible)}`}
        />
      </Box>
    </Box>
  );
};

export default FloatingInputLabel;
