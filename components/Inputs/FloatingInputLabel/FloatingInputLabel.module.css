.wrapper {
  position: relative;
  height: 56px;
  overflow: hidden;
}

.inputWrapper {
  position: relative;
  width: 100%;
  height: 56px;
  border: 1px solid var(--color-gray300);
  /* box-shadow: 0px 0px 1px var(--color-gray400); */
  border-radius: 5px;
  overflow: hidden;
  transition: border-color 0.2s ease;

  &:focus-within {
    /* border: 2px solid var(--color-gray300); */
    border-color: var(--color-blue);
  }
}

.label {
  position: absolute;
  top: 10px;
  left: 17px;
  pointer-events: 'none';
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 400;
  color: var(--color-gray500);
}

.labelHidden {
  opacity: 0;
}

.inputDisplayedValue {
  position: absolute;
  top: 26px;
  left: 17px;
  z-index: 1;
  font-size: 14px;
  transform: none;
  transition: all 0.4s ease;
  user-select: none;
}

.inputDisplayedValueTransformed {
  height: 100%;
  transform: translate(0px, -8.7px);
  pointer-events: none;
}

.input {
  width: 100%;
  height: 100%;
  padding: 12px var(--spacing-md);
  border: none;
  outline: none;
  font-size: 14px;
  color: var(--color-gray700);
  user-select: none;
  background-color: transparent;
  transform: translate(0px, 10px);
  transition: all 0.4s ease;

  &:focus {
    outline: none;
  }
}

.inputLabelVisible {
  transform: translate(0px, 0px);
}
