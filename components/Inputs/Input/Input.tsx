import { Input as MantineInput } from '@mantine/core';
import { JSX } from 'react';

type InputPropsType = {
  value: string | number;
  type?: 'text ' | 'email' | 'number';
  placeholder?: string;
  onChange: (v: string) => void;
  variant?: 'primary' | 'filled';
  rightSection?: React.ReactNode;
  rightSectionWidth?: number;
  isDisabled?: boolean;
};

const Input = ({
  isDisabled,
  onChange,
  placeholder,
  rightSection,
  rightSectionWidth,
  type,
  value,
  variant = 'primary',
}: InputPropsType): JSX.Element => {
  return (
    <MantineInput
      disabled={isDisabled}
      variant={variant}
      value={value}
      onChange={(e) => onChange(e.currentTarget.value)}
      placeholder={placeholder}
      type={type}
      rightSection={rightSection}
      rightSectionWidth={rightSectionWidth}
    />
  );
};

export default Input;
