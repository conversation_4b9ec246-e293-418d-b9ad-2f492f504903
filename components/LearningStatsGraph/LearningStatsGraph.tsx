import { useTranslation } from 'react-i18next';

import Text from '@/components/Text/Text';
import { TestScoresResultsType } from '@/types/common';

import s from './LearningStatsGraph.module.css';

type LearningStatsGraphProps = {
  domains: TestScoresResultsType['results']['domains'];
  xAxisRange: {
    min: number;
    max: number;
    step: number;
  };
  hasLocalizedTitle: boolean;
};

const SCORE_COLORS: { [key: string]: string } = {
  blue: 'rgba(78, 132, 202, 1)',
  rose: 'rgba(212, 105, 153, 1)',
  pink: 'rgba(161, 99, 197, 1)',
  buff: 'rgba(255, 192, 102, 1)',
  rust: 'rgba(199, 101, 99, 1)',
} as const;

const REACTION_TIME_COLORS: { [key: string]: string } = {
  blue: 'rgb(28 90 171)',
  rose: 'rgb(181 76 123)',
  pink: 'rgb(121 55 160)',
  buff: 'rgb(255 151 71)',
  rust: 'rgb(199 101 99)',
} as const;

const ACCURACY_BREAKPOINT = 70;

const LearningStatsGraph = ({
  domains,
  hasLocalizedTitle,
  xAxisRange,
}: LearningStatsGraphProps): JSX.Element => {
  const { t } = useTranslation();

  const numberOfSteps = Math.floor(xAxisRange.max / xAxisRange.step) + 1;

  return (
    <div className={s.wrapper}>
      <div className={s.yAxis}>
        {domains.map((domain, i) => (
          <div
            className={`${s.entityWrapper} ${domains.length - 1 !== i && s.separationBorder}`}
            key={domain.title}
          >
            <div className={s.textWrapper}>
              <Text
                untranslatedText={domain.title.toUpperCase()}
                type="subTitle2"
                fw={600}
              />
            </div>

            <div className={s.subTestNamesWrapper}>
              {domain.subjects.map((subjectItem) => (
                <div className={s.textWrapper} key={subjectItem.title}>
                  <Text
                    untranslatedText={
                      subjectItem[hasLocalizedTitle ? 'title' : 'name']
                    }
                    type="subTitle1"
                    color={!subjectItem.featured ? 'gray400' : 'black'}
                  />

                  {subjectItem?.metrics?.[1] && (
                    <p
                      className={s.reactionTimeText}
                      style={{
                        color: `${REACTION_TIME_COLORS[domain.color || 'blue']}`,
                      }}
                    >
                      {t('reaction-time')}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className={s.graphWrapper}>
        <div className={s.progressAndSubtestsWrapper}>
          <div className={s.progressBackground} />

          {domains.map((item, i) => (
            <div
              className={`${s.entityWrapper} ${domains.length - 1 !== i && s.separationBorder}`}
              key={item.title}
            >
              <div className={s.categoryPlaceholder} />

              <div className={s.subTestWrapper}>
                {item.subjects.map((subject) => {
                  // ACC = Accuracy
                  const ACC =
                    subject?.metrics?.find((metric) => metric.type === 'ACC') ||
                    null;
                  // CRT = Critical Reaction Time
                  const CRT =
                    subject?.metrics?.find((metric) => metric.type === 'CRT') ||
                    null;

                  const accuracyScore = ACC?.score || 0;
                  const accuracyPercentile = ACC?.percentile || 0;
                  const reactionTimePercentile = CRT?.percentile || 0;

                  return (
                    <div
                      key={subject.title}
                      style={{
                        position: 'relative',
                        transform: CRT
                          ? 'translateY(-1px)'
                          : 'translateY(-2px)',
                      }}
                    >
                      {/*  The first metric is for the Accuracy using the ACC percentile */}
                      <div
                        key={subject.title}
                        className={`${s.progressBar}`}
                        style={
                          {
                            '--to-width': `${accuracyPercentile}%`,
                            width: `${accuracyPercentile}%`,
                            backgroundColor: `${SCORE_COLORS[item.color || 'blue']}`,
                          } as React.CSSProperties
                        }
                      />

                      {/*  The second metric is for the Reaction Time using the CRT percentile, Its BEING DISPLAYED WHEN ACCURACY score IS ABOVE 70 */}
                      {CRT && accuracyScore >= ACCURACY_BREAKPOINT && (
                        <div
                          className={`${s.responseMetricBar}`}
                          style={
                            {
                              '--to-width': `${reactionTimePercentile}%`,
                              width: `${reactionTimePercentile}%`,
                              backgroundColor: `${REACTION_TIME_COLORS[item.color || 'blue']}`,
                            } as React.CSSProperties
                          }
                        />
                      )}

                      {CRT && accuracyScore < ACCURACY_BREAKPOINT && (
                        <Text
                          className={s.accuracyTooLowText}
                          transKey="unavailable-low-accuracy"
                          type="body3"
                          color="gray700"
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        <div className={s.xAxis}>
          <div className={s.line} />

          <div className={s.stepsWrapper}>
            {numberOfSteps > 0 &&
              Array.from({ length: numberOfSteps }, (_, i) => (
                <div key={i} className={s.step}>
                  <Text
                    untranslatedText={`${i * xAxisRange.step}`}
                    type="label"
                    fw={700}
                  />
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LearningStatsGraph;
