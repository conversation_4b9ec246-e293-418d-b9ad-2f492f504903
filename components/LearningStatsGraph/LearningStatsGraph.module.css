.wrapper {
  max-width: 860px;
  width: 100%;
  display: flex;
  gap: var(--spacing-smd);
}

.yAxis {
  display: flex;
  flex-direction: column;
}

.entityWrapper {
  display: flex;
  flex-direction: column;
  /* Keep the same gap and height to maintain the design */
  gap: 16px;
  padding: var(--spacing-mdl) 0;
  z-index: 1;
}

.onlyOneDomain {
  /* gap: 0 !important; */
}

.separationBorder {
  border-bottom: 5px solid var(--color-white);
}

.subTestNamesWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  /* Keep the same gap and height to maintain the design - Needs to be twice the height*/
  gap: var(--spacing-xl);
}

.textWrapper {
  position: relative;
  max-width: 300px;
  /* Keep the same gap and height to maintain the design */
  height: 16px;
  /* text-wrap: nowrap; */
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.graphWrapper {
  min-width: 312px;
  position: relative;
  width: 100%;
}

.progressBackground {
  position: absolute;
  width: 100%;
  height: 100%;

  background: linear-gradient(
    to right,
    rgba(229, 203, 203, 1) 0%,
    rgba(229, 203, 203, 1) 15%,
    rgba(243, 228, 228, 1) 15%,
    rgba(243, 228, 228, 1) 30%,
    rgba(248, 242, 242, 1) 30%,
    rgba(248, 242, 242, 1) 70%,
    rgba(181, 214, 183, 1) 70%,
    rgba(181, 214, 183, 1) 100%
  );
}

.categoryPlaceholder {
  width: 0;
  /* Keep the same gap and height to maintain the design */
  height: 16px;
  opacity: 0;
}

.progressAndSubtestsWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

.subTestWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

@keyframes animateFromLeftToRight {
  from {
    width: 0;
  }
  to {
    width: var(--to-width);
  }
}

.progressBar {
  width: 0;
  height: 16px;
  animation: animateFromLeftToRight 1.5s ease-in-out;
  z-index: 1;
}

.reactionTimeText {
  position: absolute;
  font-size: 12px;
  font-weight: 400;
  top: 18px;
  right: 0;
  height: 8px;
  z-index: 1;
}

.responseMetricBar {
  position: absolute;
  bottom: -16px;
  left: 0;
  width: 0;
  height: 8px;
  animation: animateFromLeftToRight 1.7s ease-in-out;
  z-index: 1;
}

.accuracyTooLowText {
  position: absolute;
  top: 18px;
  left: 8px;
  height: 8px;
  z-index: 1;
}

.xAxis {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-sm) 0;
  background-color: var(--color-white);
}

.line {
  width: 100%;
  height: 1px;
  background-color: black;
  margin-bottom: 10px;
}

.stepsWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.step {
  position: relative;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;

  &::before {
    content: '';
    width: 1px;
    height: 10px;
    background-color: black;
    position: absolute;
    top: -10px;
    left: 50%;
  }
}
