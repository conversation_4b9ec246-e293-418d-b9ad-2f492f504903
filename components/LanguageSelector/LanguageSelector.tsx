/* eslint-disable @typescript-eslint/no-unused-vars */
import { Group, Image, Menu, UnstyledButton } from '@mantine/core';
import { useState } from 'react';
import { FaChevronRight } from 'react-icons/fa';

import s from './LanguageSelector.module.css';

const data = [
  //   { label: 'English', image: '🇧🇫' },
  //   { label: 'German', image: '🇧🇫' },
  //   { label: 'Italian', image: '🇧🇫' },
  { label: 'French', image: 's' },
  { label: 'Polish', image: 's' },
];

// THIS COMPONENT CAN BE COMPLETELY REMOVED was just for testing purposes spyros. I will delete it.

const LanguageSelector = (): JSX.Element => {
  const [opened, setOpened] = useState(false);
  const [selected, setSelected] = useState(data[0]);
  const items = data.map((item) => (
    <Menu.Item
      //   leftSection={<Image src={item.image} width={18} height={18} />}
      leftSection={
        <div
          style={{
            width: 18,
            height: 18,
          }}
        >
          s
        </div>
      }
      onClick={() => setSelected(item)}
      key={item.label}
    >
      {item.label}
    </Menu.Item>
  ));

  return (
    <Menu
      onOpen={() => setOpened(true)}
      onClose={() => setOpened(false)}
      radius="md"
      width="target"
      withinPortal
    >
      <Menu.Target>
        <UnstyledButton
          className={s.control}
          data-expanded={opened || undefined}
        >
          <Group gap="xs">
            {/* <Image src={selected.image} width={22} height={22} /> */}
            <div
              style={{
                width: 22,
                height: 22,
                borderRadius: '50%',
                backgroundColor: 'red',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {selected.image}
            </div>
            <span className={s.label}>{selected.label}</span>
          </Group>
          <FaChevronRight fontSize={12} className={s.icon} />
        </UnstyledButton>
      </Menu.Target>
      <Menu.Dropdown>{items}</Menu.Dropdown>
    </Menu>
  );
};

export default LanguageSelector;
