import { Skeleton, Table, TableData } from '@mantine/core';
import React, { ReactNode } from 'react';

type TableSkeletonProps = {
  numberOfColumns?: number;
  numberOfRows?: number;
};

const TableSkeleton = ({
  numberOfColumns = 7,
  numberOfRows = 18,
}: TableSkeletonProps) => {
  const tableData: TableData = {
    head: Array.from({ length: numberOfColumns }, (_, index) => index).map(
      (index) => <Skeleton height={29} key={index} />
    ),
    body: Array.from({ length: numberOfRows }, (_, index) => index).map(() =>
      Array.from({ length: numberOfColumns }, (_, index) => index).map(
        (index) => <Skeleton height={29} key={index} />
      )
    ),
  };

  return <Table data={tableData} />;
};

export default TableSkeleton;
