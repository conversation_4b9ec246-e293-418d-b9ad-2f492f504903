.selectWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border: 1px solid var(--color-gray200);
  background-color: var(--color-white);
  border-radius: 50%;
  padding: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-black);
  cursor: pointer;

  &:hover {
    background-color: var(--color-turquoise);
    color: var(--color-white);
    border-color: var(--color-turquoise);
  }
}

.itemActive {
  background-color: var(--color-turquoise);
  color: var(--color-white);
  border-color: var(--color-turquoise);
  cursor: default;
  user-select: none;
}

.disabledItem {
  background-color: var(--color-gray100);
  border-color: var(--color-gray400);
  color: var(--color-gray500);
  opacity: 0.8;

  &:hover {
    background-color: var(--color-gray100);
    border-color: var(--color-gray400);
    color: var(--color-gray500);
    cursor: not-allowed;
  }
}
