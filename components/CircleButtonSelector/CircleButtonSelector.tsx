import React from 'react';

import styles from './CircleButtonSelector.module.css';

const TEMPORARY_DISABLED_GRADES = ['7', '8', '9', '10', '11', '12'];

type CircleButtonSelectorProps = {
  options: readonly string[] | string[];
  selectedOption: string | null;
  onSelectOption: (option: string) => void;
};

const CircleButtonSelector = ({
  onSelectOption,
  options,
  selectedOption,
}: CircleButtonSelectorProps) => {
  return (
    options && (
      <div className={`${styles.selectWrapper}`}>
        {options.map((item) => {
          const isDisabled = TEMPORARY_DISABLED_GRADES.includes(item);

          return (
            <button
              key={item}
              className={`${styles.item} ${
                item === selectedOption && styles.itemActive
              } ${isDisabled && styles.disabledItem}`}
              type="button"
              onClick={() => onSelectOption(item)}
              disabled={isDisabled}
            >
              {item}
            </button>
          );
        })}
      </div>
    )
  );
};

export default CircleButtonSelector;
