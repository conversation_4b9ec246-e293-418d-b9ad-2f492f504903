import Link from 'next/link';

import { PRODUCTS } from '@/common/consts';
import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';
import { ProductsType, TranslationKeysType } from '@/types/common';

import Icon from '../Icon/Icon';
import s from './DetailedCertificationsCard.module.css';

type LicensesCardPropsType = {
  completedCertifications: ProductsType[];

  hasEarnMoreButton?: boolean;
  headerTransKey: TranslationKeysType;
};

const DetailedCertificationsCard = ({
  completedCertifications,
  hasEarnMoreButton = true,
  headerTransKey,
}: LicensesCardPropsType): JSX.Element => {
  const { allTests } = TestsContext();

  return (
    <Card size="xl" bg="purple" className="specialBackground" shadow="none">
      <Text transKey={headerTransKey} type="h3" color="white" />

      <div className={s.wrapper}>
        {allTests.map((test, index) => {
          const isCertificationCompleted = completedCertifications.includes(
            test.type
          );

          return (
            <div
              className={`${s['details-wrapper']} ${index % 2 === 0 && s['details-wrapper-reverse']}`}
              key={test.type}
            >
              {isCertificationCompleted ? (
                <Icon
                  name={PRODUCTS[test.type].completedCertificateIcon}
                  color="white"
                />
              ) : (
                <Icon
                  name={PRODUCTS[test.type].certificateIcon}
                  color="white"
                />
              )}
            </div>
          );
        })}
      </div>

      {hasEarnMoreButton && (
        <Link
          href="/dashboard/store?tab=certifications"
          className={s['earn-more-text']}
        >
          <Text
            transKey="earn-more-capital"
            fw={700}
            color="white"
            type="body3"
          />
        </Link>
      )}
    </Card>
  );
};

export default DetailedCertificationsCard;
