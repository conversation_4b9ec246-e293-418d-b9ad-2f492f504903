import { Checkbox as MantineCheckbox } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import baseTheme from '@/styles/baseTheme';
import { TranslationKeysType } from '@/types/common';

type CheckboxProps = {
  label?: TranslationKeysType | string;
  errorMessage?: string;
  indeterminate?: boolean;
  isDisabled?: boolean;
  value: string;
  isChecked: boolean;
  onChange: (value: string) => void;
  variant?: 'primary' | 'outlined' | 'primaryList' | 'card';
  isRequired?: boolean;
  dataLocked?: boolean;
  size?: 'md' | 'lg' | 'xl';
};

const Checkbox = ({
  dataLocked,
  errorMessage,
  indeterminate = false,
  isChecked = false,
  isDisabled = false,
  isRequired = false,
  label,
  onChange,
  size,
  value,
  variant = 'primary',
}: CheckboxProps) => {
  const { t } = useTranslation();

  return (
    <MantineCheckbox
      variant={variant}
      error={t(errorMessage || '')}
      indeterminate={indeterminate}
      disabled={isDisabled}
      value={value}
      size={size || undefined}
      checked={isChecked}
      onChange={(e) => onChange(e.target.value)}
      {...(label && {
        label: t(label),
      })}
      {...(indeterminate && {
        checked: true,
        readOnly: true,
        iconColor: baseTheme.colors.turquoise,
        styles: {
          input: {
            borderBlockColor: baseTheme.colors.turquoise,
          },
        },
      })}
      required={isRequired}
      {...(dataLocked && { 'data-locked': 'true' })}
    />
  );
};

export default Checkbox;
