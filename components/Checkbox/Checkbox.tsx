import { Checkbox as MantineCheckbox } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import baseTheme from '@/styles/baseTheme';
import { TranslationKeysType } from '@/types/common';

import Text from '../Text/Text';

type CheckboxProps = {
  label?: TranslationKeysType;
  errorMessage?: string;
  indeterminate?: boolean;
  isDisabled?: boolean;
  value: string;
  isChecked: boolean;
  onChange: (value: string) => void;
  variant?: 'primary' | 'outlined';
  isRequired?: boolean;
};

const Checkbox = ({
  errorMessage,
  indeterminate = false,
  isChecked = false,
  isDisabled = false,
  isRequired = false,
  label,
  onChange,
  value,
  variant = 'primary',
}: CheckboxProps) => {
  const { t } = useTranslation();

  return (
    <MantineCheckbox
      variant={variant}
      error={t(errorMessage || '')}
      indeterminate={indeterminate}
      disabled={isDisabled}
      value={value}
      checked={isChecked}
      onChange={(e) => onChange(e.target.value)}
      {...(label && { label: <Text transKey={label} /> })}
      {...(indeterminate && {
        checked: true,
        readOnly: true,
        iconColor: baseTheme.colors.turquoise,
        styles: {
          input: {
            borderBlockColor: baseTheme.colors.turquoise,
          },
        },
      })}
      required={isRequired}
    />
  );
};

export default Checkbox;
