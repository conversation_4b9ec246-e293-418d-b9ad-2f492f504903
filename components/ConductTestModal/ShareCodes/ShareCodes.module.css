.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.textWrapper {
  margin-right: var(--spacing-xl);
}

.content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.buttonsWrapper {
  display: flex;
  gap: var(--spacing-mdl);
}

.tableWrapper {
  margin-top: var(--spacing-sm);
  margin-bottom: 40px;
  height: 100%;
  max-height: 400px;
}

.row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.emailInput {
  width: 100%;
  border: none;

  &:focus {
    outline: none;
    border: none;
  }

  &.invalid {
    color: red;
  }
}

.copyButton {
  width: fit-content;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
