/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { CopyButton, Table, TableData, Tooltip } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaKey } from 'react-icons/fa';
import validator from 'validator';

import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import TESTS_SESSIONS_CREATE from '@/services/tests/sessions-create';

import { InvitedStudentSession } from '../ConductTestModal';
import s from './ShareCodes.module.css';

type SharingSession = {
  [key: string]: { email?: string; code: string; studentName: string };
};

type ShareCodesProps = {
  invitedStudentsList: InvitedStudentSession[];
  onCloseModal: () => void;
};

const getCorrectlyFormattedPrintList = (sharingSessionItem: SharingSession) => {
  return Object.values(sharingSessionItem).map((item) => ({
    studentName: item.studentName,
    code: item.code,
    ...(item.email && { email: item.email }),
  }));
};

const getCorrectlyFormattedShareList = (sharingSessionItem: SharingSession) => {
  return Object.entries(sharingSessionItem).map(([studentId, item]) => ({
    studentId,
    code: item.code,
    ...(item.email && { email: item.email }),
  }));
};

const ShareCodes = ({ invitedStudentsList, onCloseModal }: ShareCodesProps) => {
  const { t } = useTranslation();

  const [sharingSession, setSharingSession] = useState(
    invitedStudentsList.reduce((acc, invitedStudentItem) => {
      return {
        ...acc,
        [invitedStudentItem.studentId]: {
          code: invitedStudentItem.sessionCode,
          studentName: invitedStudentItem.studentDisplayedName,
          email: '',
        },
      };
    }, {} as SharingSession)
  );

  const sharingSessionList = Object.values(sharingSession);

  const areAllEmailsValid = sharingSessionList.every((item) =>
    item.email ? validator.isEmail(item.email) : true
  );

  const isAtleastOneEmailProvided = sharingSessionList.some(
    (item) => item.email
  );

  const tableData: TableData = {
    head: [t('student-capital'), t('code-capital'), t('email-capital'), ' '],
    body: Object.entries(sharingSession).map(([studentId, item]) => [
      item.studentName,
      item.code && (
        <CopyButton key={studentId} value={item.code} timeout={2000}>
          {({ copied, copy }) => (
            <Tooltip
              label={copied ? t('copied') : t('copy')}
              withArrow
              position="top"
            >
              <div onClick={copy} className={s.copyButton}>
                <FaKey size={13} color="var(--color-turquoise)" />

                <Text type="body1" color="black" untranslatedText={item.code} />
              </div>
            </Tooltip>
          )}
        </CopyButton>
      ),

      <input
        key={studentId}
        placeholder={t('enter-caretaker-email')}
        value={item.email}
        onChange={(e) => {
          setSharingSession((prev) => ({
            ...prev,
            [studentId]: {
              code: item.code,
              studentName: item.studentName,
              email: e.target.value,
            },
          }));
        }}
        className={`${s.emailInput} ${item.email && !validator.isEmail(item.email) && s.invalid}`}
      />,
    ]),
  };

  const printCodesMutation = useMutation({
    mutationFn: TESTS_SESSIONS_CREATE.PRINT_CREATED_TEST_SESSION_CODES,
    onSuccess: async (res) => {
      if (res) {
        const blob = new Blob([res.data], {
          type: 'application/pdf',
        });

        const url = window.URL.createObjectURL(blob);

        // Create a temporary link element to trigger the download
        const link = document.createElement('a');
        link.href = url;
        link.download = 'codes.pdf';
        link.click();

        // Clean up the object URL after the download
        window.URL.revokeObjectURL(url);

        // onCloseModal();
      }
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onError: (error) => {
      // handle error
    },
  });

  const shareCodesMutation = useMutation({
    mutationFn: TESTS_SESSIONS_CREATE.SHARE_CREATED_TEST_SESSION_CODES,
    onSuccess: async (res) => {
      // onCloseModal();
      notifications.show({
        message: t('email-sent'),
        color: 'green',
      });
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  return (
    <div>
      <div className={s.header}>
        <div className={s.textWrapper}>
          <Text transKey="student-codes" type="h3" />
        </div>

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <Card size="xl" className={s.tableWrapper} overflow="auto">
        <Table data={tableData} />
      </Card>

      <div className={s.footer}>
        <Text transKey="each-code-is-unique" type="body2" />

        <div className={s.buttonsWrapper}>
          <Tooltip
            label={t('insert-valid-email')}
            withArrow
            disabled={areAllEmailsValid}
          >
            <div>
              <Button
                type="button"
                transKey="download-capital"
                isDisabled={!areAllEmailsValid || shareCodesMutation.isPending}
                isLoading={printCodesMutation.isPending}
                onClick={() =>
                  printCodesMutation.mutate(
                    getCorrectlyFormattedPrintList(sharingSession)
                  )
                }
              />
            </div>
          </Tooltip>

          <Tooltip
            label={t('insert-valid-email')}
            withArrow
            disabled={areAllEmailsValid && isAtleastOneEmailProvided}
          >
            <div>
              <Button
                onClick={() =>
                  shareCodesMutation.mutate(
                    getCorrectlyFormattedShareList(sharingSession)
                  )
                }
                type="button"
                transKey="share-email-capital"
                isLoading={shareCodesMutation.isPending}
                isDisabled={
                  !areAllEmailsValid ||
                  !isAtleastOneEmailProvided ||
                  printCodesMutation.isPending
                }
              />
            </div>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default ShareCodes;
