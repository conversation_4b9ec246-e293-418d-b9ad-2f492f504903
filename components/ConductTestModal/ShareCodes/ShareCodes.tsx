import {
  Box,
  CopyButton,
  Flex,
  Table,
  <PERSON>Data,
  Tooltip,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaKey } from 'react-icons/fa';
import validator from 'validator';

import { PRODUCTS } from '@/common/consts';
import { downloadFile } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import TESTS_SESSIONS_CREATE from '@/services/tests/sessions-create';
import { ProductsType } from '@/types/common';

import {
  InvitedStudentSession,
  NewlyCreatedTestSessionResponse,
} from '../ConductTestModal';
import s from './ShareCodes.module.css';

type SharingSession = {
  [key: string]: {
    email?: string;
    code: string;
    studentName: string;
    studentGrade: string;
  };
};

type ShareCodesProps = {
  invitedStudentsList: InvitedStudentSession[];
  onCloseModal: () => void;
  testLanguage: string;
  fullResponseDetails: NewlyCreatedTestSessionResponse | null;
  headerDetails: {
    assignedTest: string;
    assignedTestLevel: string;
  };
};

// const getCorrectlyFormattedPrintList = (sharingSessionItem: SharingSession) => {
//   return Object.values(sharingSessionItem).map((item) => ({
//     studentName: item.studentName,
//     code: item.code,
//     ...(item.email && { email: item.email }),
//     studentGrade: item.studentGrade,
//   }));
// };

const getCorrectlyFormattedShareList = (sharingSessionItem: SharingSession) => {
  return Object.entries(sharingSessionItem).map(([studentId, item]) => ({
    studentId,
    code: item.code,
    ...(item.email && { email: item.email }),
    studentGrade: item.studentGrade,
  }));
};

const ShareCodes = ({
  fullResponseDetails,
  headerDetails,
  invitedStudentsList,
  onCloseModal,
  testLanguage,
}: ShareCodesProps) => {
  const { t } = useTranslation();

  const [sharingSession, setSharingSession] = useState(
    invitedStudentsList.reduce((acc, invitedStudentItem) => {
      return {
        ...acc,
        [invitedStudentItem.studentId]: {
          code: invitedStudentItem.sessionCode,
          studentName: invitedStudentItem.studentDisplayedName,
          email: '',
          studentGrade: invitedStudentItem.studentGrade,
        },
      };
    }, {} as SharingSession)
  );

  const sharingSessionList = Object.values(sharingSession);

  const areAllEmailsValid = sharingSessionList.every((item) =>
    item.email ? validator.isEmail(item.email) : true
  );

  const isAtleastOneEmailProvided = sharingSessionList.some(
    (item) => item.email
  );

  const tableData: TableData = {
    head: [
      t('student-capital'),
      t('grade-capital'),
      t('code-capital'),
      t('email-capital'),
      ' ',
    ],
    body: Object.entries(sharingSession).map(([studentId, item]) => [
      item.studentName,
      item.studentGrade,
      item.code && (
        <CopyButton key={studentId} value={item.code} timeout={2000}>
          {({ copied, copy }) => (
            <Tooltip
              label={copied ? t('copied') : t('copy')}
              withArrow
              position="top"
            >
              <div onClick={copy} className={s.copyButton}>
                <FaKey size={13} color="var(--color-turquoise)" />

                <Text type="body1" color="black" untranslatedText={item.code} />
              </div>
            </Tooltip>
          )}
        </CopyButton>
      ),
      <input
        key={studentId}
        placeholder={t('enter-caretaker-email')}
        value={item.email}
        onChange={(e) => {
          setSharingSession((prev) => ({
            ...prev,
            [studentId]: {
              code: item.code,
              studentName: item.studentName,
              email: e.target.value,
              studentGrade: item.studentGrade,
            },
          }));
        }}
        className={`${s.emailInput} ${item.email && !validator.isEmail(item.email) && s.invalid}`}
      />,
    ]),
  };

  const printCodesMutation = useMutation({
    mutationFn: TESTS_SESSIONS_CREATE.PRINT_CREATED_TEST_SESSION_CODES,
    onSuccess: async (res) => {
      if (res) {
        downloadFile(res.data, 'codes', 'pdf');
        // onCloseModal();
      }
    },

    onError: (error) => {
      // handle error
    },
  });

  const shareCodesMutation = useMutation({
    mutationFn: TESTS_SESSIONS_CREATE.SHARE_CREATED_TEST_SESSION_CODES,
    onSuccess: async (res) => {
      // onCloseModal();
      notifications.show({
        message: t('email-sent'),
        color: 'green',
      });
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  return (
    <div>
      <div className={s.header}>
        <div className={s.textWrapper}>
          <Text transKey="student-codes" type="h3" />
        </div>

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <Flex gap={16} mb={24}>
        <Flex gap={8}>
          <Text transKey={'test-first-letter-capital'} type="body1" fw={700} />

          <Text untranslatedText={':'} type="body1" fw={700} />

          <Text
            untranslatedText={`${PRODUCTS[headerDetails.assignedTest as ProductsType].typeName}`}
            type="body1"
            fw={700}
          />
        </Flex>

        <Text untranslatedText={'|'} type="body1" />

        <Flex gap={8}>
          <Text transKey={'test-level'} type="body1" fw={700} />
          :
          <Text
            untranslatedText={`${headerDetails.assignedTestLevel}`}
            type="body1"
            fw={700}
          />
        </Flex>
      </Flex>

      <Card size="xl" className={s.tableWrapper} overflow="auto">
        <Table data={tableData} />
      </Card>

      <div className={s.footer}>
        {/* <Text transKey="each-code-is-unique" type="body2" /> */}

        <div className={s.buttonsWrapper}>
          <Tooltip
            label={t('insert-valid-email')}
            withArrow
            disabled={areAllEmailsValid}
          >
            <div>
              <Button
                type="button"
                transKey="download-capital"
                isDisabled={!areAllEmailsValid || shareCodesMutation.isPending}
                isLoading={printCodesMutation.isPending}
                onClick={() => {
                  if (fullResponseDetails) {
                    printCodesMutation.mutate(
                      // getCorrectlyFormattedPrintList(sharingSession)
                      fullResponseDetails
                    );
                  }
                }}
              />
            </div>
          </Tooltip>

          <Tooltip
            label={t('insert-valid-email')}
            withArrow
            disabled={areAllEmailsValid && isAtleastOneEmailProvided}
          >
            <div>
              <Button
                onClick={() =>
                  shareCodesMutation.mutate({
                    sharedCodes: getCorrectlyFormattedShareList(sharingSession),
                    language: testLanguage,
                  })
                }
                type="button"
                transKey="share-email-capital"
                isLoading={shareCodesMutation.isPending}
                isDisabled={
                  !areAllEmailsValid ||
                  !isAtleastOneEmailProvided ||
                  printCodesMutation.isPending
                }
              />
            </div>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default ShareCodes;
