.container {
  min-height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.textWrapper {
  margin-right: var(--spacing-xl);
}

.content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.dropDownsWrapper {
  display: flex;
  gap: var(--spacing-md);

  & button {
    width: 190px;
  }
}

.icon {
  margin: 0 var(--spacing-sm);
}

.selectedParticipants {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;

  & > div {
    min-width: 190px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--spacing-xl);
}

.radioGroup {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.buttonsWrapper {
  display: flex;
  gap: var(--spacing-mdl);
}

.radioWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.gradesSelectionWrapper {
  max-width: 220px;
  margin-right: var(--spacing-md);
}

.subTestsWrapper {
  width: 190px;
}

.containedText {
  max-width: 350px;
}
