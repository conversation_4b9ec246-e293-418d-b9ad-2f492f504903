import { Divider } from '@mantine/core';
import { DateInput, TimeInput } from '@mantine/dates';
import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FiClock } from 'react-icons/fi';

import { mantineDateParser } from '@/common/helpers';
import Button from '@/components/Button/Button';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';

import { HandleSelectedDataUpdateType, SelectedDataType } from '../types';
import s from './Schedule.module.css';

type ScheduleProps = {
  onCloseModal: () => void;
  handleSelectedDataUpdate: HandleSelectedDataUpdateType;
  selectedData: SelectedDataType;
  onScheduleButtonClick: () => void;
  isLoading: boolean;
};

const compareDatesIfFromDateIsBeforeToDate = (fromDate: Date, toDate: Date) => {
  const fromDateObj = new Date(fromDate);
  const toDateObj = new Date(toDate);

  if (fromDateObj > toDateObj) {
    return true;
  }

  return false;
};

const compareTimeIfBeforeTimeIsBeforeToTime = (
  fromTime: string,
  toTime: string,
  fromDate: Date,
  toDate: Date
) => {
  const [fromHour, fromMinute] = fromTime.split(':').map(Number);
  const [toHour, toMinute] = toTime.split(':').map(Number);

  const starting = new Date(0, 0, 0, fromHour, fromMinute);
  const ending = new Date(0, 0, 0, toHour, toMinute);

  const fromDateObj = new Date(fromDate);
  const toDateObj = new Date(toDate);

  const isSameDay = fromDateObj.getTime() === toDateObj.getTime();

  if (isSameDay) {
    return ending <= starting;
  }

  return false;
};

const isDateToday = (date: Date) => {
  const today = new Date();

  return today.toDateString() === date?.toDateString();
};

const isSelectedTimeInPast = (time: string) => {
  if (!time) return false;

  const [hour, minute] = time.split(':').map(Number);
  const currentTime = new Date();
  const now = new Date(
    0,
    0,
    0,
    currentTime.getHours(),
    currentTime.getMinutes()
  );
  const selectedTime = new Date(0, 0, 0, hour, minute);

  return selectedTime < now;
};

const Schedule = ({
  handleSelectedDataUpdate,
  isLoading,
  onCloseModal,
  onScheduleButtonClick,
  selectedData,
}: ScheduleProps) => {
  const { t } = useTranslation();

  const fromPickerRef = useRef<HTMLInputElement>(null);
  const toPickerRef = useRef<HTMLInputElement>(null);
  const [isPastTimeOnTodaysTest, setIsPastTimeOnTodaysTest] =
    useState<boolean>(false);

  const hasDateError =
    selectedData.fromDate && selectedData.toDate
      ? compareDatesIfFromDateIsBeforeToDate(
          selectedData.fromDate,
          selectedData.toDate
        )
      : false;

  const hasTimeError =
    selectedData.fromDate && selectedData.toDate
      ? compareTimeIfBeforeTimeIsBeforeToTime(
          selectedData.fromTime,
          selectedData.toTime,
          selectedData.fromDate,
          selectedData.toDate
        )
      : false;

  const handleScheduleButtonClick = () => {
    if (
      isDateToday(selectedData.fromDate || new Date()) &&
      isSelectedTimeInPast(selectedData.fromTime)
    ) {
      setIsPastTimeOnTodaysTest(true);
    } else {
      onScheduleButtonClick();
    }
  };

  return (
    <div>
      <div className={s.header}>
        <div className={s.textWrapper}>
          <Text transKey="schedule-test" type="h3" />
        </div>

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <Text transKey="proceed-to-action" type="h4" mb={50} />

      <div className={s.content}>
        <div className={s.row}>
          <Text transKey="from" type="subTitle2" />
          <Text transKey="to" type="subTitle2" />
        </div>

        <div className={s.row}>
          <DateInput
            placeholder={t('select-date')}
            required
            valueFormat="DD/MM/YYYY"
            highlightToday
            dateParser={mantineDateParser}
            onChange={(v) => {
              if (isPastTimeOnTodaysTest) {
                setIsPastTimeOnTodaysTest(false);
              }

              handleSelectedDataUpdate('fromDate', v);
            }}
            minDate={new Date()}
          />

          <DateInput
            placeholder={t('select-date')}
            required
            valueFormat="DD/MM/YYYY"
            highlightToday
            dateParser={mantineDateParser}
            onChange={(v) => handleSelectedDataUpdate('toDate', v)}
            error={hasDateError ? t('date-error') : false}
            minDate={
              selectedData.fromDate
                ? new Date(selectedData.fromDate)
                : new Date()
            }
          />
        </div>

        <div className={s.row}>
          <TimeInput
            ref={fromPickerRef}
            onClick={() => fromPickerRef?.current?.showPicker()}
            rightSection={<FiClock />}
            onChange={(event) => {
              if (isPastTimeOnTodaysTest) {
                setIsPastTimeOnTodaysTest(false);
              }

              handleSelectedDataUpdate('fromTime', event.currentTarget.value);
            }}
            error={isPastTimeOnTodaysTest ? t('past-time-error') : false}
          />
          <TimeInput
            ref={toPickerRef}
            onClick={() => toPickerRef?.current?.showPicker()}
            rightSection={<FiClock />}
            onChange={(event) =>
              handleSelectedDataUpdate('toTime', event.currentTarget.value)
            }
            error={hasTimeError ? t('time-error') : false}
          />
        </div>
      </div>

      <Divider
        variant="dashed"
        w="100%"
        mb={30}
        mt={50}
        style={{ borderColor: 'var(--color-gray)' }}
      />

      <div className={s.footer}>
        <Button
          onClick={handleScheduleButtonClick}
          type="button"
          transKey="schedule-capital"
          isLoading={isLoading}
          isDisabled={
            hasDateError ||
            hasTimeError ||
            selectedData.fromDate === null ||
            selectedData.toDate === null ||
            selectedData.fromTime === '' ||
            selectedData.toTime === '' ||
            isPastTimeOnTodaysTest
          }
        />
      </div>
    </div>
  );
};

export default Schedule;
