.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.textWrapper {
  margin-right: var(--spacing-xl);
}

.content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.row {
  display: flex;
  gap: var(--spacing-xl);
  overflow: visible;

  & > div {
    flex: 1;
  }

  & > p {
    flex: 1;
  }
}
