import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';

import { getStudentsDisplayName } from '@/common/helpers';
import { ClassesContext } from '@/context/ClassesProvider';
import { GroupsContext } from '@/context/GroupsProvider';
import SCHOOL_CLASSES from '@/services/schools/classes';
import STUDENTS_GROUPS from '@/services/students/groups';
import {
  ClassDetailsType,
  GroupDetailsType,
  SelectedEntityByIdAndDisplayedNameType,
  SingleClassDetailsType,
  SingleGroupDetailsType,
} from '@/types/common';

type SelectParticipantsType = {
  displayedName: string;
  id: string;
};

const filterClassesOrGroupsWithSearchQuery = <
  T extends SingleClassDetailsType | SingleGroupDetailsType,
>(
  classesOrGroups: T[],
  searchQuery: string
): T[] => {
  return classesOrGroups.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
};

const useStudentsByClassOrGroupInfo = (
  initialSelectedParticipants: SelectedEntityByIdAndDisplayedNameType[],
  initialSelectedGroupOrClassId?: string
) => {
  const { classesList } = ClassesContext();
  const { groupsList } = GroupsContext();

  const [searchClassOrGroupQuery, setSearchClassOrGroupQuery] = useState('');

  const [
    cachedStudentsByClassIdOrGroupId,
    setCachedStudentsByClassIdOrGroupId,
  ] = useState<{
    // where key is classId or group id
    [key: string]: SelectParticipantsType[];
  }>({
    ...(initialSelectedGroupOrClassId && {
      [initialSelectedGroupOrClassId]: initialSelectedParticipants,
    }),
  });

  const classes = filterClassesOrGroupsWithSearchQuery(
    classesList,
    searchClassOrGroupQuery
  ).filter((classItem) => classItem.studentsCount > 0);

  const groups = filterClassesOrGroupsWithSearchQuery(
    groupsList,
    searchClassOrGroupQuery
  ).filter((groupItem) => groupItem.studentsCount > 0);

  const getStudentsByClassMutation = useMutation({
    mutationFn: SCHOOL_CLASSES.GET_CLASS_DETAILS,
    onError: (error) => {
      console.log('error', error);
    },
    onSuccess: async (res: ClassDetailsType | null) => {
      //   TODO NEED TO HANDLE CODE ASS WELL as displayed option
      if (res) {
        const newStudents = (res.students || []).map((student) => ({
          displayedName: getStudentsDisplayName({
            code: student.code || '',
            firstName: student.firstName,
            lastName: student.lastName,
          }),
          id: student.id,
        }));

        setCachedStudentsByClassIdOrGroupId((prev) => ({
          ...prev,
          [res.id]: newStudents,
        }));
      }
    },
  });

  const getStudentsByGroupMutation = useMutation({
    mutationFn: STUDENTS_GROUPS.GET_GROUP_DETAILS,
    onError: (error) => {
      console.log('error', error);
    },
    onSuccess: async (res: GroupDetailsType | null) => {
      if (res) {
        setCachedStudentsByClassIdOrGroupId((prev) => ({
          ...prev,
          [res.id]: (res.students || []).map((student) => ({
            displayedName: getStudentsDisplayName({
              code: student.code || '',
              firstName: student.firstName,
              lastName: student.lastName,
            }),
            id: student.id,
          })),
        }));

        // onSubmitSelection()
      }
    },
  });

  const isMutationInProgress =
    getStudentsByClassMutation.isPending ||
    getStudentsByGroupMutation.isPending;

  return {
    getStudentsByClassMutation,
    getStudentsByGroupMutation,
    cachedStudentsByClassIdOrGroupId,
    classes,
    groups,
    onSearchClassOrGroup: (value: string) => setSearchClassOrGroupQuery(value),
    isMutationInProgress,
    searchClassOrGroupQuery,
  };
};

export default useStudentsByClassOrGroupInfo;
