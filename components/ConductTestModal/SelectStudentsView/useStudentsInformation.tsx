import { useDebouncedValue } from '@mantine/hooks';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_STUDENTS from '@/services/schools/students';
import INDEPENDENT_TEACHER_STUDENTS from '@/services/students/students';

type StudentsPaginationType = {
  activePage: number;
  debouncedSearchWord: string;
};

const FETCH_DATA_LIMIT = 10;

const getSchoolStudents = ({
  activePage,
  debouncedSearchWord,
}: StudentsPaginationType) =>
  SCHOOL_STUDENTS.GET_SCHOOL_STUDENTS({
    limit: FETCH_DATA_LIMIT,
    page: activePage,
    query: debouncedSearchWord,
    className: '',
    grades: [],
  });

const getStudentsAsIndependentTeacher = ({
  activePage,
  debouncedSearchWord,
}: StudentsPaginationType) =>
  INDEPENDENT_TEACHER_STUDENTS.GET_STUDENTS({
    limit: FETCH_DATA_LIMIT,
    page: activePage,
    grades: [],
    query: debouncedSearchWord,
    groupName: '',
  });

const useStudentsInformation = () => {
  const { isSchoolRole, userRoles } = UserContext();
  const [activePage, setActivePage] = useState(1);

  const [searchStudentQuery, setSearchStudentQuery] = useState('');
  const [debouncedSearchStudentQuery] = useDebouncedValue(
    searchStudentQuery,
    500
  );

  const {
    data,
    isFetching: areStudentsFetching,
    isLoading: areStudentsLoading,
    isPending: areStudentsPending,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } = useQuery<any | null>({
    queryFn: isSchoolRole
      ? () =>
          getSchoolStudents({
            activePage,
            debouncedSearchWord: debouncedSearchStudentQuery,
          })
      : () =>
          getStudentsAsIndependentTeacher({
            activePage,
            debouncedSearchWord: debouncedSearchStudentQuery,
          }),
    queryKey: [
      `${isSchoolRole ? QUERY_KEYS.SCHOOL_STUDENT_LIST : QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST}`,
      activePage,
      debouncedSearchStudentQuery,
    ],
    // placeholderData: keepPreviousData,
    // staleTime: 1000 * 60 * 60 * 24,
  });

  const students = data?.results || [];

  return {
    students,
    onStudentSearch: (value: string) => setSearchStudentQuery(value),
    onPageChange: (page: number) => setActivePage(page),
    activePage,
    areStudentsLoading,
    areStudentsFetching,
    totalNumberOfPages: Math.ceil((data?.count || 0) / FETCH_DATA_LIMIT),
    totalNumberOfStudents: data?.totalCount || 0,
    searchStudentQuery,
  };
};

export default useStudentsInformation;
