import { Box } from '@mantine/core';
import { JSX, useState } from 'react';
import { BiArrowBack } from 'react-icons/bi';

import { getStudentsDisplayName } from '@/common/helpers';
import Button from '@/components/Button/Button';
import SelectedStudentsBoard from '@/components/SelectedStudentsBoard/SelectedStudentsBoard';
import StudentsSelectorCard from '@/components/StudentsSelectorCard/StudentsSelectorCard';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import { SelectedEntityByIdAndDisplayedNameType } from '@/types/common';

import s from './SelectStudentsView.module.css';
import useStudentsByClassOrGroupInfo from './useStudentsByClassOrGroupInfo';
import useStudentsInformation from './useStudentsInformation';

type SelectStudentsViewPropsType = {
  onBack: () => void;
  initialSelectedParticipants: SelectedEntityByIdAndDisplayedNameType[];
  initialSelectedGroupOrClassId?: string;
  onSubmitSelection: (
    students: SelectedEntityByIdAndDisplayedNameType[]
  ) => void;
  selectedDevice: 'student' | 'teacher' | null;
};

const SelectStudentsView = ({
  initialSelectedGroupOrClassId,
  initialSelectedParticipants,
  onBack,
  onSubmitSelection,
  selectedDevice,
}: SelectStudentsViewPropsType): JSX.Element => {
  const { isSchoolRole } = UserContext();
  const [selectedStudents, setSelectedStudents] = useState(
    initialSelectedParticipants
  );

  const {
    activePage,
    areStudentsFetching,
    areStudentsLoading,
    onPageChange,
    onStudentSearch,
    searchStudentQuery,
    students,
    totalNumberOfPages,
    totalNumberOfStudents,
  } = useStudentsInformation();

  const {
    cachedStudentsByClassIdOrGroupId,
    classes,
    getStudentsByClassMutation,
    getStudentsByGroupMutation,
    groups,
    isMutationInProgress,
    onSearchClassOrGroup,
    searchClassOrGroupQuery,
  } = useStudentsByClassOrGroupInfo(
    initialSelectedParticipants,
    initialSelectedGroupOrClassId
  );

  const onUpdateSelectedStudents = (
    updatedStudentsList: SelectedEntityByIdAndDisplayedNameType[]
  ) => {
    setSelectedStudents(updatedStudentsList);
  };

  return (
    <div className={s.wrapper}>
      <div className={s.header}>
        <Box className={s.arrowAndTextWrapper} onClick={onBack}>
          <BiArrowBack fontSize={24} color="black" className={s.backButton} />

          <Text transKey="add-remove-students" type="h3" fw={250} />
        </Box>

        <Button
          transKey="save-capital"
          isDisabled={areStudentsLoading || isMutationInProgress}
          onClick={() => onSubmitSelection(selectedStudents)}
        />
      </div>

      <div className={s.cardsWrapper}>
        <StudentsSelectorCard
          scrollAreaHeight={410}
          students={students}
          selectedStudents={selectedStudents}
          isLoading={areStudentsFetching}
          tableProps={{
            paginationData: {
              activePage,
              totalNumberOfPages,
              onPageChange: (page) => onPageChange(page),
              totalNumberOfResults: totalNumberOfStudents,
              // TODO : fix this to return the correct array of the students with typescript
              totalNumberOfSearchResults: students.length,
            },
            isGlobalCheckBoxDisabled: selectedDevice === 'teacher',
            areSelectionCheckBoxesDisabled:
              selectedDevice === 'teacher' && selectedStudents.length >= 1,
            onUpdateSelectedStudentList: (updatedStudentsList) =>
              onUpdateSelectedStudents(updatedStudentsList),
          }}
          cardViewProps={{
            studentsByClassOrGroupId: cachedStudentsByClassIdOrGroupId,
            displayedClassesOrGroups: (isSchoolRole ? classes : groups) as any,
            totalNumberOfClassesOrGroups: isSchoolRole
              ? classes.length
              : groups.length,
            onClassOrGroupSelection: async (classId, isChecked) => {
              if (isChecked) {
                // remove all students from the selectedStudents array that exist in the studentsByClassId[classItem.id]
                const studentsToRemove = cachedStudentsByClassIdOrGroupId[
                  classId
                ]?.map((student) => student.id);

                setSelectedStudents((prev) =>
                  prev.filter(
                    (selectedStudent) =>
                      !studentsToRemove?.includes(selectedStudent.id)
                  )
                );
              } else {
                if (cachedStudentsByClassIdOrGroupId[classId]) {
                  const uniqueStudentsToAdd = cachedStudentsByClassIdOrGroupId[
                    classId
                  ].filter(
                    (student) =>
                      !selectedStudents.some(
                        (selectedStudent) => selectedStudent.id === student.id
                      )
                  );

                  setSelectedStudents((prev) => {
                    return [...prev, ...uniqueStudentsToAdd];
                  });

                  return;
                }

                const data2 = isSchoolRole
                  ? await getStudentsByClassMutation.mutateAsync(classId)
                  : await getStudentsByGroupMutation.mutateAsync(classId);

                const newStudents = (data2?.students || []).map((student) => ({
                  displayedName: getStudentsDisplayName({
                    code: student.code || '',
                    firstName: student.firstName,
                    lastName: student.lastName,
                  }),
                  id: student.id,
                }));

                setSelectedStudents((prev) => {
                  const previousStudentIds = prev.map(
                    (prevStudent) => prevStudent.id
                  );

                  const filteredNewStudents = newStudents.filter(
                    (newStudent) => !previousStudentIds.includes(newStudent.id)
                  );

                  return [...prev, ...filteredNewStudents];
                });
              }
            },
          }}
          searchProps={{
            searchStudentQuery,
            searchClassesQuery: searchClassOrGroupQuery,
            onInputChange: (value, view) => {
              if (view === 'cards') {
                onSearchClassOrGroup(value);
              } else {
                onStudentSearch(value);
              }
            },
          }}
        />

        <SelectedStudentsBoard
          scrollAreaHeight={440}
          selectedStudents={selectedStudents}
          isLoading={isMutationInProgress}
          onDeleteStudent={(studentId) =>
            setSelectedStudents((prev) =>
              prev.filter((selectedStudent) => selectedStudent.id !== studentId)
            )
          }
        />
      </div>
    </div>
  );
};

export default SelectStudentsView;
