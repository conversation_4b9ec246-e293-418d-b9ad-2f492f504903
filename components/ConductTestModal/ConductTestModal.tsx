import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

// import { SUPPORTED_SCHOOL_GRADES } from '@/common/consts';
import { combineDateTime } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import { TestsContext } from '@/context/TestsProvider';
import { UserContext } from '@/context/UserProvider';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import TESTS_SESSIONS_CREATE from '@/services/tests/sessions-create';
import {
  ProductsType,
  SelectedEntityByIdAndDisplayedNameType,
} from '@/types/common';

import Card from '../Card/Card';
import PrimaryModal from '../Modals/PrimaryModal/PrimaryModal';
import s from './ConductTestModal.module.css';
import Schedule from './Schedule/Schedule';
import SelectStudentsView from './SelectStudentsView/SelectStudentsView';
import SelectTestScreen from './SelectTestScreen/SelectTestScreen';
import ShareCodes from './ShareCodes/ShareCodes';
import {
  DisplayScreenType,
  HandleSelectedDataUpdateType,
  SelectedDataType,
} from './types';

export type InvitedStudentSession = {
  studentId: string;
  studentDisplayedName: string;
  sessionCode: string;
};

type NewlyCreatedTestSessionResponse = {
  id: string;
  code: string;
  studentId: string;
  student: string;
  state: 'active' | 'in-progress' | 'scheduled' | 'expired' | 'completed';
  scheduled: string;
};

type ConductTestModalProps = {
  selectedStudents: SelectedEntityByIdAndDisplayedNameType[];
  selectedGroupId?: string;
  // selectedClassId?: string;
  isOpen: boolean;
  onCloseModal: () => void;
};

const ConductTestModal = ({
  isOpen,
  onCloseModal,
  // selectedClassId,
  selectedGroupId,
  selectedStudents,
}: ConductTestModalProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { allTests } = TestsContext();
  const {
    invalidateUser,
    userRoles: { isAdmin, isDeveloper, isResearcher },
  } = UserContext();

  const [displayedScreen, setDisplayedScreen] =
    useState<DisplayScreenType>('select_test');

  const TEST_TYPE = (router.query.tab || '') as string;

  const isTestTypeValid = allTests.some((item) => item.type === TEST_TYPE);

  const isUserAdminOrDeveloper = isAdmin || isDeveloper;

  const INITIAL_SELECTED_DATA: SelectedDataType = {
    testType: isTestTypeValid ? (TEST_TYPE as ProductsType) : allTests[0]?.type,
    subTests: [],
    language: '',
    // testLevel: SUPPORTED_SCHOOL_GRADES[0],
    testLevel: null,
    participants: selectedStudents || [],
    selectedDevice: isResearcher ? 'student' : null,
    fromDate: null,
    toDate: null,
    fromTime: '',
    toTime: '',
  };

  const [selectedData, setSelectedData] = useState<SelectedDataType>(
    INITIAL_SELECTED_DATA
  );

  // Its being used for the share screen codes
  const [invitedStudentsList, setInvitedStudentsList] = useState<
    InvitedStudentSession[]
  >([]);

  const handleSelectedDataUpdate: HandleSelectedDataUpdateType = (
    key,
    value
  ) => {
    setSelectedData((prev) => ({ ...prev, [key]: value }));
  };

  const onCloseModalAction = () => {
    onCloseModal();

    setTimeout(() => {
      setSelectedData({
        ...INITIAL_SELECTED_DATA,
        participants: selectedStudents || INITIAL_SELECTED_DATA.participants,
      });
      setDisplayedScreen('select_test');
    }, 400);
  };

  // DUPLICATE FUNCTION UNDER CONDUCT TEST MODULE WHERE IF THIS DEVICE IS SELECTED IMMEDIATELY WILL START THE SESSION AND OPEN THE MODAL
  const addSessionIdToURl = (sessionId: string) => {
    // It has a bug that if for some reason the open will be clicked again session will be writing its self on the url
    const path = `${router.asPath.includes('?') ? `${router.asPath}&session=${sessionId}` : `${router.asPath}?session=${sessionId}`}`;
    router.push(path);
  };

  const startSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.START_TEST_SESSION,
    onSuccess: (res, sessionId) => {
      // So we close the conduct test modal and start the test session by adding the sessionId to the url;
      onCloseModal();
      addSessionIdToURl(sessionId);
    },
    onError: (error) => {},
  });

  const conductTestMutation = useMutation({
    mutationFn: TESTS_SESSIONS_CREATE.NEW_TEST_SESSION,
    onSuccess: async (res: NewlyCreatedTestSessionResponse[] | null) => {
      // if selected device is participants device meaning student for us the modal will continue to next steps to share the codes etc.
      if (res && selectedData.selectedDevice === 'student') {
        setInvitedStudentsList(
          res.map((sessionDetails) => ({
            studentId: sessionDetails.studentId,
            studentDisplayedName: sessionDetails.student,
            sessionCode: sessionDetails.code,
          }))
        );
        setDisplayedScreen('share_codes');
        // we refetch the created session tests to change from active to in progress
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
        });

        // By invalidating the user we fetch the new number of licenses the user has after the usage of licenses depending on the number of students selected to conduct a test.
        invalidateUser();
      } else if (res && selectedData.selectedDevice === 'teacher') {
        // Otherwise the only other option is the CURRENT device. In that case the session with the test will start immediately
        await startSession.mutateAsync(res[0].id);
      }

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });

      // By invalidating the user we fetch the new number of licenses the user has after the usage of licenses depending on the number of students selected to conduct a test.
      invalidateUser();

      setSelectedData(INITIAL_SELECTED_DATA);
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const onConductScheduledTest = () => {
    conductTestMutation.mutate({
      type: selectedData.testType,
      // version: 1,
      students: selectedData.participants.map((participant) => participant.id),
      language: selectedData.language,
      device: selectedData.selectedDevice,
      grade: selectedData.testLevel,
      ...(selectedData.subTests.length > 0
        ? { subtests: selectedData.subTests }
        : {}),
      starting: combineDateTime(
        selectedData.fromDate || new Date(),
        selectedData.fromTime
      ),
      expiring: combineDateTime(
        selectedData.toDate || new Date(),
        selectedData.toTime
      ),
    });
  };

  const MODAL_SCREENS = {
    select_test: (
      <SelectTestScreen
        onCloseModal={onCloseModalAction}
        onSelectParticipantsClick={() => setDisplayedScreen('select_students')}
        selectedData={selectedData}
        handleSelectedDataUpdate={handleSelectedDataUpdate}
        isUserAdminOrDeveloper={isUserAdminOrDeveloper}
        onConductButtonClick={() =>
          conductTestMutation.mutate({
            type: selectedData.testType,
            // version: 1,
            students: selectedData.participants.map((student) => student.id),
            language: selectedData.language,
            device: selectedData.selectedDevice,
            grade: selectedData.testLevel,
            ...(selectedData.subTests.length > 0
              ? { subtests: selectedData.subTests }
              : {}),
          })
        }
        // Enable when schedule feature is wanted
        // onScheduleButtonClick={() => setDisplayedScreen('schedule')}
        isActionButtonLoading={
          conductTestMutation.isPending || startSession.isPending
        }
      />
    ),
    schedule: (
      <Schedule
        onScheduleButtonClick={onConductScheduledTest}
        onCloseModal={onCloseModalAction}
        handleSelectedDataUpdate={handleSelectedDataUpdate}
        selectedData={selectedData}
        isLoading={conductTestMutation.isPending}
      />
    ),
    share_codes: (
      <ShareCodes
        onCloseModal={onCloseModalAction}
        invitedStudentsList={invitedStudentsList}
      />
    ),
    select_students: (
      <SelectStudentsView
        onBack={() => {
          setDisplayedScreen('select_test');
        }}
        initialSelectedGroupOrClassId={selectedGroupId}
        initialSelectedParticipants={selectedData.participants}
        onSubmitSelection={(students) => {
          setSelectedData((prev) => {
            return {
              ...prev,
              participants: students,
            };
          });

          setDisplayedScreen('select_test');
        }}
        selectedDevice={selectedData.selectedDevice}
      />
    ),
  };

  return (
    <PrimaryModal
      isOpen={isOpen}
      content={
        <Card
          size="xl"
          bg="gray50"
          className={`${s.wrapper} ${displayedScreen === 'select_students' && s.addRemoveStudentsWrapper}`}
          hasDynamicHeight={displayedScreen !== 'select_students'}
        >
          {MODAL_SCREENS[displayedScreen]}
        </Card>
      }
    />
  );
};

export default ConductTestModal;
