import { SUPPORTED_SCHOOL_GRADES } from '@/common/consts';
import {
  ProductsType,
  SelectedEntityByIdAndDisplayedNameType,
} from '@/types/common';

export type DisplayScreenType =
  | 'select_test'
  | 'schedule'
  | 'share_codes'
  | 'select_students';

export type SelectedDataType = {
  testType: ProductsType | null;
  subTests: string[];
  language: string | null;
  testLevel: (typeof SUPPORTED_SCHOOL_GRADES)[number] | null;
  participants: SelectedEntityByIdAndDisplayedNameType[];
  selectedDevice: 'teacher' | 'student' | null;
  fromDate: Date | null;
  toDate: Date | null;
  fromTime: string;
  toTime: string;
};

export interface HandleSelectedDataUpdateType {
  (
    key: keyof SelectedDataType,
    value: SelectedDataType[keyof SelectedDataType]
  ): void;
}
