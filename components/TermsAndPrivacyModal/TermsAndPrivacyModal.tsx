import { Modal } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import Text from '@/components/Text/Text';

import { ModalContentType } from '../../pages/certification-purchase/types';
import PRIVACY from './privacy.json';
import TERMS from './terms.json';
import styles from './TermsAndPrivacyModal.module.css';

type TermsAndPrivacyModalProps = {
  type: ModalContentType;
  isOpen: boolean;
  onClose: () => void;
};

type ListItem =
  | string
  | {
      listTitle?: string;
      orderedList?: ListItem[];
      unorderedList?: ListItem[];
    };

type TermsAndPrivacyItem = {
  heading?: string;
  title?: string;
  paragraphs?: string[];
  orderedList?: ListItem[];
  unorderedList?: ListItem[];
  listTitle?: string;
};

const renderList = (items: ListItem[], type: 'ol' | 'ul') => {
  const ListTag = type === 'ol' ? 'ol' : 'ul';

  return (
    <ListTag>
      {items.map((item, i) => {
        if (typeof item === 'string') {
          return (
            <li key={i} className={styles.list}>
              <Text untranslatedText={item} type="body1" />
            </li>
          );
        }

        return (
          <li key={i} className={styles.list}>
            {item.listTitle && (
              <Text
                untranslatedText={item.listTitle}
                className={styles.listTitle}
              />
            )}
            {item.unorderedList && renderList(item.unorderedList, 'ul')}
            {item.orderedList && renderList(item.orderedList, 'ol')}
          </li>
        );
      })}
    </ListTag>
  );
};

const TermsAndPrivacyModal = ({
  isOpen,
  onClose,
  type,
}: TermsAndPrivacyModalProps) => {
  const { i18n } = useTranslation();

  const data = (
    type === 'terms'
      ? TERMS[i18n.language as 'en']
      : PRIVACY[i18n.language as 'en']
  ) as TermsAndPrivacyItem[];

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      centered
      size="xl"
      autoFocus={false}
      trapFocus
      title={
        <Text
          transKey={
            type === 'privacy' ? 'privacy-policy' : 'terms-of-service-title'
          }
          color="blue"
          fw={700}
        />
      }
    >
      <div className={styles.contentWrapper}>
        {Array.isArray(data) &&
          data.length > 0 &&
          data.map((item, index) => (
            <div key={index} className={styles.section}>
              {item.heading && (
                <Text
                  untranslatedText={item.heading}
                  className={styles.heading}
                />
              )}
              {item.title && (
                <Text untranslatedText={item.title} className={styles.title} />
              )}

              {item.paragraphs?.map(
                (paragraph, i) =>
                  paragraph && (
                    <Text
                      key={i}
                      untranslatedText={paragraph}
                      className={styles.paragraph}
                    />
                  )
              )}

              {item.orderedList && renderList(item.orderedList, 'ol')}
              {item.unorderedList && renderList(item.unorderedList, 'ul')}
            </div>
          ))}
      </div>
    </Modal>
  );
};

export default TermsAndPrivacyModal;
