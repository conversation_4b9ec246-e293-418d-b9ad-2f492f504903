.contentWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  min-width: 500px;
}

.content {
  white-space: pre-line;
  overflow: scroll;
  height: 100%;
  padding: var(--spacing-lg) 0;
  background-color: var(--color-white);
}

.heading {
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  font-weight: 800 !important;
  font-size: 22px !important;
}

.title {
  font-weight: 700 !important;
  font-size: 18px !important;
  margin-bottom: var(--spacing-md) !important;
  margin-top: var(--spacing-xl) !important;
}

.paragraph {
  margin-bottom: var(--spacing-md) !important;
}

.list {
  margin-left: var(--spacing-xl) !important;
  margin-bottom: var(--spacing-sm) !important;
}

.listTitle {
  margin-bottom: var(--spacing-sm) !important;
}
