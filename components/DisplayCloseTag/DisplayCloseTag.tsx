import { JSX } from 'react';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import s from './DisplayCloseTag.module.css';

type DisplayClosetagProsType = {
  text: string;
  onClose?: () => void;
};

const DisplayCloseTag = ({
  onClose,
  text,
}: DisplayClosetagProsType): JSX.Element => {
  return (
    <div className={`${s.wrapper} ${onClose && s.hovered}`}>
      <Text untranslatedText={text} type="body2" fw={500} />

      {onClose && <Icon name="CloseSvg" onClick={onClose} w={11} h={11} />}
    </div>
  );
};

export default DisplayCloseTag;
