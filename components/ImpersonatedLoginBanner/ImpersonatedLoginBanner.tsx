import { useTranslation } from 'react-i18next';
import { RiAlertLine } from 'react-icons/ri';

import { BrowserCompatibilityContext } from '@/context/BrowserCompatibilityProvider';
import { UserContext } from '@/context/UserProvider';

import styles from './ImpersonatedLoginBanner.module.css';

const ImpersonatedLoginBanner = () => {
  const { t } = useTranslation();
  const { user } = UserContext();
  const { isIncompatibleBrowser } = BrowserCompatibilityContext();

  const isImpersonated = user?.impersonated || false;

  return isImpersonated ? (
    <div
      className={`${styles.wrapper} ${isIncompatibleBrowser ? styles.incompatible : ''}`}
    >
      <RiAlertLine className={styles.icon} />

      <p className={styles.message}>{t('impersonated-login-message')}</p>
    </div>
  ) : null;
};

export default ImpersonatedLoginBanner;
