import { ActionIcon } from '@mantine/core';
import React from 'react';

import { ColorsType, IconType } from '@/types/common';

import Icon from '../Icon/Icon';
import { SIZES } from './consts';

type IconButtonProps = {
  iconName: IconType;
  onClick?: () => void;
  size?: 'md' | 'default';
  variant?: 'primary' | 'outlined' | 'danger' | 'dangerOutlined';
  isDisabled?: boolean;
  isLoading?: boolean;
  className?: string;
  iconSize?: number;
  color?: ColorsType;
};

const IconButton = ({
  className,
  color = 'white',
  iconName,
  iconSize,
  isDisabled = false,
  isLoading = false,
  onClick,
  size = 'default',
  variant = 'primary',
}: IconButtonProps) => {
  return (
    <ActionIcon
      variant={variant}
      onClick={(e) => {
        e.stopPropagation();
        onClick?.();
      }}
      h={SIZES[size].h}
      p={SIZES[size].padding}
      disabled={isDisabled || isLoading}
      loading={isLoading}
      className={className}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <Icon
        name={iconName}
        color={color}
        {...(iconSize && { w: iconSize, h: iconSize })}
      />
    </ActionIcon>
  );
};

export default IconButton;
