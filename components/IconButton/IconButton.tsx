import { ActionIcon } from '@mantine/core';
import React from 'react';

import { IconType } from '@/types/common';

import Icon from '../Icon/Icon';
import { SIZES } from './consts';

type IconButtonProps = {
  iconName: IconType;
  onClick?: () => void;
  size?: 'md' | 'default';
  variant?: 'primary' | 'outlined';
  isDisabled?: boolean;
  isLoading?: boolean;
  className?: string;
  iconSize?: number;
};

const IconButton = ({
  className,
  iconName,
  iconSize,
  isDisabled = false,
  isLoading = false,
  onClick,
  size = 'default',
  variant = 'primary',
}: IconButtonProps) => {
  return (
    <ActionIcon
      variant={variant}
      onClick={onClick}
      h={SIZES[size].h}
      p={SIZES[size].padding}
      disabled={isDisabled || isLoading}
      loading={isLoading}
      className={className}
    >
      <Icon
        name={iconName}
        color="white"
        onClick={onClick || undefined}
        {...(iconSize && { w: iconSize, h: iconSize })}
      />
    </ActionIcon>
  );
};

export default IconButton;
