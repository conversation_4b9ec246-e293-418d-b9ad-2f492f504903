import { Radio as MantineRadio } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { TranslationKeysType } from '@/types/common';

import Text from '../Text/Text';

type RadioProps = {
  label?: TranslationKeysType;
  isChecked?: boolean;
  isDisabled?: boolean;
  onChange?: (value: string) => void;
  errorMessage?: string;
  value: string | number;
};

const Radio = ({
  errorMessage,
  isChecked,
  isDisabled = false,
  label,
  onChange,
  value,
}: RadioProps) => {
  const { t } = useTranslation();

  return (
    <MantineRadio
      disabled={isDisabled}
      label={
        label ? (
          <Text transKey={label} fw={isChecked ? 600 : 400} type="subTitle2" />
        ) : undefined
      }
      variant="outline"
      error={t(errorMessage || '')}
      value={value}
      checked={isChecked}
      onChange={(e) => onChange?.(e.target.value)}
    />
  );
};

export default Radio;
