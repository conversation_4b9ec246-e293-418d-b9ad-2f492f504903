import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { SUPPORTED_LANGUAGES } from '@/common/consts';

import styles from './LanguageSelector.module.css';

const LanguagePicker: React.FC = () => {
  const { i18n } = useTranslation();

  const [active, setActive] = useState<boolean>(false);

  const pickerRef = useRef<HTMLDivElement | null>(null);

  const currentLanguage = i18n.language;

  const toggleLanguagePicker = () => setActive((prev) => !prev);

  const handleChangeLanguage = (lang: string) => {
    if (lang !== currentLanguage) i18n.changeLanguage(lang);

    setActive(false);
  };

  const AVAILABLE_LANGUAGES = Object.keys(SUPPORTED_LANGUAGES);

  const generateAvailableLanguages = () =>
    AVAILABLE_LANGUAGES.filter((lang) => lang !== currentLanguage).map(
      (lang) => (
        <div
          key={`lang-selector-${lang}`}
          className={styles.container}
          style={{ zIndex: currentLanguage === lang ? 2 : 'auto' }}
        >
          <Image
            className={styles.flagImg}
            src={`/images/flags/${lang}.svg`}
            alt={`${lang} flag`}
            width={30}
            height={20}
            onClick={() => handleChangeLanguage(lang)}
            draggable={false}
          />
        </div>
      )
    );

  // Closes dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node)
      ) {
        setActive(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div
      ref={pickerRef}
      className={`${styles.picker} ${active ? styles.active : ''}`}
      data-direction="down"
      data-active={active}
    >
      <Image
        className={styles.flagImg}
        src={`/images/flags/${currentLanguage}.svg`}
        alt={`${currentLanguage} flag`}
        width={30}
        height={25}
        onClick={toggleLanguagePicker}
        draggable={false}
      />

      {active && AVAILABLE_LANGUAGES.length > 0 && (
        <div className={styles.selector}>{generateAvailableLanguages()}</div>
      )}
    </div>
  );
};

export default LanguagePicker;
