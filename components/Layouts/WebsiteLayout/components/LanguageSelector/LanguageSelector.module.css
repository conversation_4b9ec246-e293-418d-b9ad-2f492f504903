.picker {
  position: relative;
  width: 30px;
  height: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.current {
  position: relative;
  z-index: 1;
  -webkit-tap-highlight-color: transparent;
}

.flagImg {
  height: 100%;
  object-position: center;
  cursor: pointer;
  border-radius: 2px;
  overflow: hidden;
}

.selector {
  position: absolute;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  top: 10px;
  transition: all 0.3s ease;
  pointer-events: none;
  background-color: var(--color-white);

  padding-bottom: 8px;
  border-radius: 5px;
  box-shadow: var(--shadow-layer);
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 0;
  height: 0;
  transition: all 0.3s ease;
  pointer-events: all;
}

/* Not Active */
.picker:not([data-active='true']) .selector {
  width: 0 !important;
  height: 0 !important;
  right: 30px;
}

/* Active */
.picker.active[data-active='true'] .selector .container {
  width: 30px;
  height: 20px;
}

.picker.active[data-active='true'] .selector {
  margin-top: 20px;
}

.picker.active[data-active='true'] .selector .container {
  margin-top: 10px;
}
