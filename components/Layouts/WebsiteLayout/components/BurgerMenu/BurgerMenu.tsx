import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { NAV_BAR_ROUTES } from '@/common/consts';

import styles from './BurgerMenu.module.css';

const BurgerMenu = ({ currentRoute }: { currentRoute: string }) => {
  const [isBurgerVisible, setIsBurgerVisible] = useState(false);
  const { t } = useTranslation();

  const handleBurgerToggle = () => {
    setIsBurgerVisible(!isBurgerVisible);
  };

  // Prevent scrolling when the menu is open
  useEffect(() => {
    if (isBurgerVisible) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isBurgerVisible]);

  // Close menu on window resize
  useEffect(() => {
    const handleResize = () => {
      if (isBurgerVisible) {
        setIsBurgerVisible(false);
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isBurgerVisible]);

  return (
    <>
      <div
        className={`${styles.mobile_links_wrapper} ${isBurgerVisible ? styles.visible : ''}`}
      >
        <button
          type="button"
          className={styles.close_menu}
          onClick={handleBurgerToggle}
          aria-label="close menu"
        />

        {NAV_BAR_ROUTES.map(
          (route) =>
            route.isVisible && (
              <Link
                href={`${route.href}`}
                key={route.transKey}
                className={`${styles.mobile_link} ${currentRoute === route.href ? styles.active : ''}`}
                onClick={handleBurgerToggle}
                id={`mobile-${route.href}`}
              >
                {t(route.transKey)}
              </Link>
            )
        )}
      </div>

      <button
        type="button"
        aria-label="open menu"
        className={styles.burger}
        onClick={handleBurgerToggle}
      >
        <div />
      </button>
    </>
  );
};

export default BurgerMenu;
