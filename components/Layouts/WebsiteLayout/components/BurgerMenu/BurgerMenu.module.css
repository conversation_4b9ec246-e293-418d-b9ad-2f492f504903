.burger {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  width: 35px;
  height: 35px;
  cursor: pointer;
  position: relative;

  @media (min-width: 901px) {
    display: none;
  }
}

/* Middle line */
.burger div {
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--color-gray900);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 6px;
}

/* Top and bottom lines using pseudo-elements */
.burger div::before,
.burger div::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--color-gray900);
  left: 0;
  border-radius: 6px;
}

.burger div::before {
  top: -8px; /* distance above middle line */
}

.burger div::after {
  top: 8px; /* distance below middle line */
}

/* Close button (X) */
.close_menu {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  width: 35px;
  height: 35px;
  position: absolute;
  top: var(--spacing-mdl);
  right: var(--spacing-mdl);
  cursor: pointer;
  position: relative;
  position: absolute;
}

.close_menu::before,
.close_menu::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--color-white);
  top: 50%;
  left: 50%;
  transform-origin: center;
}

.close_menu::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close_menu::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.mobile_links_wrapper {
  transition:
    transform 0.3s ease-in-out,
    opacity 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 11;
  width: 100vw;
  opacity: 0;
  transform: translateX(100%);
  pointer-events: none;
  height: 100vh;
}

.mobile_links_wrapper.visible {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
}

.mobile_link {
  font-size: 1.5rem;
  font-weight: bold;
  margin: var(--spacing-md) 0;
  text-align: center;
  text-decoration: none;
  color: var(--color-white);
}

.active {
  color: var(--color-blue);
}
