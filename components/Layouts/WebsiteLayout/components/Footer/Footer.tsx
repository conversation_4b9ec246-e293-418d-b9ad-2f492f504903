import React, { useState } from 'react';

import Icon from '@/components/Icon/Icon';
import TermsAndPrivacyModal from '@/components/TermsAndPrivacyModal/TermsAndPrivacyModal';
import Text from '@/components/Text/Text';

import styles from './Footer.module.css';

const Footer = () => {
  const [isTermsModalVisible, setIsTermsModalVisible] = useState(false);
  const [isPrivacyModalVisible, setIsPrivacyModalVisible] = useState(false);

  return (
    <div className={styles.wrapper}>
      <div className={styles.container}>
        <div className={styles.rowTop}>
          <Icon name="LogoColorfulSvg" w={134} h={55} />

          <div className={styles.contactWrapper}>
            <Text transKey="contact-us-capital" fw={500} color="turquoise" />

            <a href="mailto:<EMAIL>" className={styles.emailLink}>
              <Text
                untranslatedText="<EMAIL>"
                fw={400}
                color="white"
                type="body2"
              />
            </a>
          </div>
        </div>

        <div className={styles.rowBottom}>
          <Text
            untranslatedText={`© ${new Date().getFullYear()} — Copyright`}
            type="body2"
            color="white"
          />

          <div className={styles.termsWrapper}>
            <Text
              transKey="terms-and-conditions"
              fw={400}
              color="white"
              type="body2"
              onClick={() => setIsTermsModalVisible(true)}
              className={styles.clickable}
            />
            <Text
              transKey="privacy"
              fw={400}
              color="white"
              type="body2"
              onClick={() => setIsPrivacyModalVisible(true)}
              className={styles.clickable}
            />
          </div>

          <Text
            transKey="built-by"
            fw={400}
            color="white"
            type="body2"
            className={styles.clickableSimple}
            onClick={() => window.open('https://noesis.solutions/', '_blank')}
          />
        </div>
      </div>

      <TermsAndPrivacyModal
        isOpen={isTermsModalVisible}
        onClose={() => {
          setIsTermsModalVisible(false);
        }}
        type="terms"
      />

      <TermsAndPrivacyModal
        isOpen={isPrivacyModalVisible}
        onClose={() => {
          setIsPrivacyModalVisible(false);
        }}
        type="privacy"
      />
    </div>
  );
};

export default Footer;
