.wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: fit-content;
  background-color: rgb(7, 23, 8) !important;
  padding: var(--spacing-3xl) var(--spacing-lg);
}

.container {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-5xl);
  justify-content: space-between;
  align-items: center;
}

.rowTop {
  display: flex;
  gap: var(--spacing-md);
  justify-content: space-between;
  width: 100%;
}

.rowBottom {
  display: flex;
  gap: var(--spacing-md);
  justify-content: space-between;
  width: 100%;
}

@media screen and (max-width: 700px) {
  .rowBottom {
    flex-direction: column;
  }

  .rowBottom > :nth-child(2) {
    order: -1; /* put the middle one on top */
    margin-bottom: var(--spacing-2xl);
    flex-direction: column;
  }

  .rowBottom > :nth-child(1) {
    order: 0;
  }

  .rowBottom > :nth-child(3) {
    order: 1;
  }
}

@media screen and (max-width: 375px) {
  .container {
    gap: var(--spacing-2xl);
  }
  .rowTop {
    flex-direction: column;
  }

  .rowTop > :nth-child(2) {
    align-items: flex-start;
    margin-top: var(--spacing-2xl);
  }
}

.contactWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

.termsWrapper {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
  justify-content: space-between;
}

.clickable {
  cursor: pointer;
}

.clickableSimple {
  cursor: pointer;
}

.clickable:hover {
  color: var(--color-turquoise) !important;
}

.emailLink {
  text-decoration: none;

  cursor: pointer;
}
