.wrapper {
  width: 100%;
  transition: all 0.2s ease;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;

  display: flex;
  justify-content: center;
}

.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-mdl);
  width: 100%;
  max-width: 1440px;
  justify-self: center;
}

.scrolled {
  background: var(--color-white);
  box-shadow: var(--shadow-layer);
}

.linksWrapper {
  display: flex;
  gap: var(--spacing-md);

  @media screen and (max-width: 900px) {
    display: none;
  }
}

.link {
  cursor: pointer;
  text-decoration: none;
  color: var(--color-gray700);
  /* font-size: 15px; */

  transition: all 0.2s ease;
}

.link:hover {
  color: var(--color-blue);
}

.active {
  color: var(--color-blue);
}

.rightWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-lg);
  min-width: 90px;
}
