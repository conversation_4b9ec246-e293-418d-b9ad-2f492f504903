import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { NAV_BAR_ROUTES } from '@/common/consts';
import Icon from '@/components/Icon/Icon';

import BurgerMenu from '../BurgerMenu/BurgerMenu';
import LanguageSelector from '../LanguageSelector/LanguageSelector';
import styles from './NavBar.module.css';

const NavBar = () => {
  const [isPageScrolled, setIsPageScrolled] = useState(false);
  const router = useRouter();
  const { t } = useTranslation();

  const currentRoute = router.pathname;

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsPageScrolled(true);
      } else {
        setIsPageScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div
      className={`${styles.wrapper} ${isPageScrolled ? styles.scrolled : ''}`}
    >
      <div className={styles.container}>
        <Icon
          name="LogoColorfulSvg"
          onClick={() => router.push('/')}
          w={90}
          h={40}
        />

        <div className={styles.linksWrapper}>
          {NAV_BAR_ROUTES.map(
            (link) =>
              link.isVisible && (
                <Link
                  href={link.href}
                  key={link.href}
                  className={`${styles.link} ${currentRoute === link.href ? styles.active : ''}`}
                >
                  {t(link.transKey)}
                </Link>
              )
          )}
        </div>

        <div className={styles.rightWrapper}>
          <LanguageSelector />

          <BurgerMenu currentRoute={currentRoute} />
        </div>
      </div>
    </div>
  );
};

export default NavBar;
