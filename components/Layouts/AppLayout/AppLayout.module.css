.wrapper {
  width: 100vw;
  height: 100vh;
  min-height: 680px;
  display: flex;
  transition: padding 0.4s ease-in-out;
  background-color: var(--color-gray50);
  padding: var(--spacing-xl);
  padding-left: 0;
}

.container {
  width: 100%;
  padding: 0 var(--spacing-2xl);
  background-color: var(--color-white);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s ease;

  @media (max-width: 1440px) {
    padding: 0 var(--spacing-xl);
  }

  @media (max-width: 1024px) {
    padding: 0 var(--spacing-lg);
  }
}

.side-menu-wrapper {
  min-width: 200px;
  max-width: 200px;
  padding-left: 16px;
  user-select: none;
  transition: all 0.4s ease-in-out;

  @media (max-width: 1400px) {
    min-width: 106px;
    max-width: 106px;
  }
}

/* Logo wrapper base styles */
.logo-wrapper {
  padding-left: var(--spacing-sm);
  padding-top: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

/* SVG styles */
.logo-wrapper svg {
  width: 95px;
  height: 40px;
}

/* Media query */
@media screen and (max-width: 1400px) {
  .logo-wrapper svg {
    width: 50px;
    height: 25px;
  }
}

.icon {
  width: 95px;
  height: 40px;

  @media screen and (max-width: 1400px) {
    width: 50px;
    height: 40px;
  }
}

.navigation-list {
  height: calc(100% - 160px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.navigation-items {
  display: flex;
  flex-direction: column;
  margin-top: var(--spacing-smd);
  gap: var(--spacing-md);
}

.logout-wrapper {
  padding: 0 var(--spacing-lg);
}

.divider {
  width: 100%;
  height: 1px;
  background-color: var(--color-gray200);
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-mdl);
}

.logout-items {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logout {
  cursor: pointer;

  &:hover {
    color: var(--color-gray600) !important;
  }

  @media screen and (max-width: 1400px) {
    display: none;
  }
}

.logoutIcon {
  color: var(--color-gray500) !important;
  margin-left: -6px;
  cursor: pointer;

  @media screen and (min-width: 1401px) {
    display: none;
  }
}
