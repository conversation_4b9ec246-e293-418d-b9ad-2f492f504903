import { Box } from '@mantine/core';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import { LayoutRouteType } from '../types';
import s from './NavigationItem.module.css';

type NavigationItemProsType = {
  name: string;
  isSelected: boolean;
  onClick: () => void;
  icon: LayoutRouteType['icon'];
};

const NavigationItem = ({
  icon,
  isSelected,
  name,
  onClick,
}: NavigationItemProsType): JSX.Element => {
  return (
    <Box
      className={`${s['navigation-item-wrapper']}  ${isSelected && s['selected-route']} ${isSelected && s['item-no-hover']}`}
      onClick={onClick}
    >
      <div className={s['icon-wrapper']}>
        <Icon
          name={icon.name}
          w={icon?.width}
          h={icon?.height}
          color={isSelected ? 'gray700' : 'gray500'}
          onClick={() => {}}
        />
      </div>

      <Text
        untranslatedText={name}
        type={isSelected ? 'body1' : 'subTitle1'}
        color={isSelected ? 'gray700' : 'gray500'}
        className={s['navigation-item-text']}
      />
    </Box>
  );
};

export default NavigationItem;
