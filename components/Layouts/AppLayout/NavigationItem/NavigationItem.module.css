.navigation-item-wrapper {
  position: relative;
  display: flex;
  gap: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-smd) var(--spacing-md);
  align-items: center;
  height: 56px;
  border-top-left-radius: var(--radius-smd);
  border-bottom-left-radius: var(--radius-smd);
  z-index: 1;
}

.navigation-item-text {
  z-index: 1;
  opacity: 1;

  @media (max-width: 1400px) {
    opacity: 0;
  }
}

.navigation-item-wrapper:hover {
  cursor: pointer;
  z-index: 1;
}

.navigation-item-wrapper:hover p {
  color: var(--color-darkerBlue) !important;
}

.navigation-item-wrapper:hover div {
  color: var(--color-darkerBlue) !important;
}

.item-no-hover {
  pointer-events: none;
}

.icon-wrapper {
  min-width: 32px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes result_box_appearance {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.selected-route {
  position: relative;
  background-color: var(--color-white);
  box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.04);

  animation: result_box_appearance 0.07s linear 1;

  &::before {
    content: '';
    position: absolute;
    top: -40px;
    right: 0px;
    width: 40px;
    height: 40px;
    /* background-color: var(--color-white);
    mask: radial-gradient(circle at 0% 0%, transparent 71.7%, white 0%); */

    background: radial-gradient(
      ellipse at top left,
      var(--color-gray50) 71%,
      70%,
      var(--color-white)
    );
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -40px;
    right: 0px;
    width: 40px;
    height: 40px;
    /* DO NOT DELETE THE COMMENTED CSS MIGHT BE USEFUL IN THE FUTURE */
    /* background-color: var(--color-white); */
    /* mask: radial-gradient(circle at 0% 100%, transparent 71.7%, white 0%); */

    background: radial-gradient(
      ellipse at bottom left,
      var(--color-gray50) 71%,
      71%,
      var(--color-white)
    );
  }
}

.selected-route div {
  color: var(--color-darkerBlue) !important;
}

.selected-route p {
  color: var(--color-darkerBlue) !important;
}
