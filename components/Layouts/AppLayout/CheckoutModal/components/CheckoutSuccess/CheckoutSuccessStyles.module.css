.wrapper {
  display: flex;
  flex-direction: column;
}

.summaryListWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-smd);
  margin-bottom: var(--spacing-xl);
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.itemDetails {
  display: flex;
  align-items: center;
}

.buttonWrapper {
  cursor: pointer;
}

.dividerWrapper {
  margin: var(--spacing-2xl) 0;
  opacity: 0.3;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
}
