import { Box, Divider } from '@mantine/core';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { PRODUCTS } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { PurchasesContext } from '@/context/PurchasesProvider';
import { IconType } from '@/types/common';

import styles from './CheckoutSuccessStyles.module.css';

const CheckoutSuccess = () => {
  const { t } = useTranslation();
  const { onCloseCheckout, productDetails } = PurchasesContext();
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleCloseModal = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.PURCHASE_HISTORY_LIST],
    });
    onCloseCheckout();
  };

  return (
    <Box
      h="fit-content"
      w="100%"
      m="auto"
      maw={785}
      ml={20}
      mr={20}
      mt={20}
      mb={20}
    >
      <Card size="xl" bg="gray50">
        <div className={styles.wrapper}>
          <Text
            transKey="thank-you"
            type="h2"
            fw={250}
            align="center"
            mb={40}
          />
        </div>

        <Text transKey="here-are-your-purchases" type="h4" fw={300} mb={20} />

        <div className={styles.summaryListWrapper}>
          {productDetails?.map((product) => (
            <div className={styles.summaryItem} key={product.stripeId}>
              <div className={styles.itemDetails}>
                <Icon
                  name={
                    PRODUCTS[product.type || 'mathpro-d'][
                      `${product.kind || 'certificate'}Icon` as keyof (typeof PRODUCTS)['mathpro-d']
                    ] as IconType
                  }
                  w={28}
                  h={30}
                />
                <Text
                  type="body1"
                  fw={400}
                  untranslatedText={`${product.name} ${t(product.kind)}`}
                  ml={16}
                />
                *
              </div>

              <div className={styles.buttonWrapper}>
                <Text
                  type="button"
                  fw={800}
                  transKey="go-to-course-capital"
                  color="blue"
                  onClick={() => {
                    handleCloseModal();
                    router.push(
                      `${FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS}?tab=${product.type}`
                    );
                  }}
                />
              </div>
            </div>
          ))}

          <div className={styles.dividerWrapper}>
            <Divider variant="dashed" color="gray" />
          </div>

          <div className={styles.footer}>
            <Text type="body2" transKey="access-purchases" />

            <Button transKey="close-capital" onClick={handleCloseModal} />
          </div>
        </div>
      </Card>
    </Box>
  );
};

export default CheckoutSuccess;
