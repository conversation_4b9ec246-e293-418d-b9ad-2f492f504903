/* eslint-disable react-hooks/exhaustive-deps */
import { Box } from '@mantine/core';
import { Elements } from '@stripe/react-stripe-js';
import {
  loadStripe,
  StripeElementLocale,
  StripeElementsOptions,
} from '@stripe/stripe-js';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { appearance, CURRENCY_SYMBOLS, PRODUCTS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { PurchasesContext } from '@/context/PurchasesProvider';
import PURCHASES from '@/services/purchases';
import { IconType } from '@/types/common';

import PurchaseFormSkeleton from '../CheckoutSkeleton/PurchaseFormSkeleton';
import styles from './CheckoutFormStyles.module.css';
import StripeForm from './StripeForm';

const publicStripeKey = process.env.NEXT_PUBLIC_STRIPE_KEY || '';

const stripePromise = loadStripe(publicStripeKey);

const CheckoutForm = () => {
  const { handlePaymentProcessing, onCloseCheckout, productDetails } =
    PurchasesContext();
  const [isStripeLoading, setIsStripeLoading] = useState<boolean>(true);
  const [isPaymentPending, setIsPaymentPending] = useState<boolean>(false);

  const queryClient = useQueryClient();

  const { i18n, t } = useTranslation();

  const totalPrice =
    productDetails?.reduce((acc, curr) => acc + (curr.price?.amount || 0), 0) ||
    0;

  const productsIds = productDetails?.map((product) => product.stripeId) || [];

  const currencySymbol =
    CURRENCY_SYMBOLS[productDetails?.[0]?.price?.currency || 'eur'];

  const cachedClientSecret = queryClient.getQueryData<string | null>([
    QUERY_KEYS.CREATE_INVOICE_FOR_PURCHASE,
    productsIds,
  ]);

  const {
    data: fetchedClientSecret,
    error,
    isError,
  } = useQuery<string | null | undefined>({
    enabled:
      Boolean(productsIds.length > 0) && cachedClientSecret === undefined,
    queryFn: () => PURCHASES.CREATE_INVOICE({ products: productsIds }),
    queryKey: [QUERY_KEYS.CREATE_INVOICE_FOR_PURCHASE, productsIds],
  });

  const clientSecret = cachedClientSecret || fetchedClientSecret;

  const options: StripeElementsOptions = {
    appearance,
    clientSecret: (clientSecret || '') as string,
    locale: i18n.language as StripeElementLocale,
    loader: 'never',
  };

  const isFormLoading =
    !clientSecret || !productDetails || !stripePromise || isStripeLoading;

  const handlePaymentPending = (value: boolean) => {
    setIsPaymentPending(value);
  };

  useEffect(() => {
    if (isError) {
      handlePaymentProcessing('failed', error?.message || 'call_issuer');
    }
  }, [isError, error]);

  const submitForm = () => {
    const form = document.getElementById('stripe-form') as HTMLFormElement;

    if (form) {
      form.requestSubmit();
    }
  };

  return (
    <Box
      h="fit-content"
      m="auto"
      maw={1222}
      w="100%"
      ml={20}
      mr={20}
      mt={20}
      mb={20}
    >
      <Card size="xl" bg="gray50" className={styles.card}>
        <div className={styles.wrapper}>
          <div className={styles.header}>
            <Text transKey="checkout" type="h3" fw={250} />

            <div className={styles.headerActions}>
              <CloseButton onClick={onCloseCheckout} variant="outlined" />
            </div>
          </div>

          <div className={styles.body}>
            <div className={styles.cardDetails}>
              <Text transKey="card-details" type="h4" fw={300} mb={20} />

              <div className={styles.stripeWrapper}>
                {isFormLoading && <PurchaseFormSkeleton />}

                {clientSecret && stripePromise && (
                  <Elements stripe={stripePromise} options={options}>
                    <StripeForm
                      isLoading={isFormLoading}
                      handleStripeLoaded={() => setIsStripeLoading(false)}
                      clientSecret={clientSecret}
                      handlePaymentPending={handlePaymentPending}
                      handlePaymentProcessing={handlePaymentProcessing}
                    />
                  </Elements>
                )}
              </div>
            </div>

            <div className={styles.summary}>
              <div>
                <Text transKey="summary" type="h4" fw={300} mb={20} />
              </div>

              <div className={styles.summaryDetails}>
                <div className={styles.summaryListWrapper}>
                  {productDetails?.map((product) => (
                    <div className={styles.summaryItem} key={product.stripeId}>
                      <div className={styles.itemDetails}>
                        <Icon
                          name={
                            PRODUCTS[product.type || 'mathpro-d'][
                              `${product.kind || 'certificate'}Icon` as keyof (typeof PRODUCTS)['mathpro-d']
                            ] as IconType
                          }
                          w={28}
                          h={30}
                        />

                        <Text
                          type="body1"
                          fw={400}
                          untranslatedText={`${product.name} ${t(product.kind)}`}
                        />
                      </div>

                      <Text
                        type="h4"
                        fw={300}
                        untranslatedText={`${currencySymbol}${getFormattedPrice(product.price?.amount || 0)}`}
                      />
                    </div>
                  ))}
                </div>

                <div className={styles.summaryFooter}>
                  <div className={styles.totalValueWrapper}>
                    <Text
                      transKey="total-price"
                      transVariables={{
                        totalPrice: `${currencySymbol}${getFormattedPrice(totalPrice)}`,
                      }}
                      type="h3"
                      fw={300}
                      color="blue"
                      mb={8}
                    />
                    <Text transKey="prices-include-vat" type="body3" />
                  </div>

                  <Button
                    transKey="pay-capital"
                    isDisabled={isFormLoading}
                    isLoading={isPaymentPending}
                    onClick={submitForm}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </Box>
  );
};

export default CheckoutForm;
