/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Flex } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { Elements } from '@stripe/react-stripe-js';
import {
  loadStripe,
  StripeElementLocale,
  StripeElementsOptions,
} from '@stripe/stripe-js';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getName } from 'i18n-iso-countries';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { appearance, CURRENCY_SYMBOLS } from '@/common/consts';
import { USERS_ERRORS } from '@/common/errors';
import { getFormattedPrice, submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import BillingDetailsForm from '@/components/BillingDetailsForm/BillingDetailsForm';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { PurchasesContext } from '@/context/PurchasesProvider';
import { UserContext } from '@/context/UserProvider';
import PROFILES from '@/services/profiles';
import PURCHASES from '@/services/purchases';

import PurchaseFormSkeleton from '../CheckoutSkeleton/PurchaseFormSkeleton';
import styles from './CheckoutFormStyles.module.css';
import StripeForm from './StripeForm';

const publicStripeKey = process.env.NEXT_PUBLIC_STRIPE_KEY || '';
const stripePromise = loadStripe(publicStripeKey);

const CheckoutForm = () => {
  const { handlePaymentProcessing, onCloseCheckout, productDetails } =
    PurchasesContext();
  const [isStripeLoading, setIsStripeLoading] = useState<boolean>(true);
  const [isPaymentPending, setIsPaymentPending] = useState<boolean>(false);
  const [isInvalidVat, setIsInvalidVat] = useState(false);
  const [isEditEnabled, setIsEditEnabled] = useState(false);

  const { invalidateUser, user } = UserContext();
  const queryClient = useQueryClient();
  const { i18n, t } = useTranslation();

  /** Billing state logic */
  const hasBillingAddress = Boolean(user.profile?.address?.addressLine1);
  const showBillingDetails = hasBillingAddress && !isEditEnabled;
  const showBillingForm = !hasBillingAddress || isEditEnabled;

  const totalPrice =
    productDetails?.reduce(
      (acc, curr) => acc + (curr.price?.totalAmount || 0),
      0
    ) || 0;

  const productsIds = productDetails?.map((product) => product.stripeId) || [];

  const currencySymbol =
    CURRENCY_SYMBOLS[productDetails?.[0]?.price?.currency || 'eur'];

  const cachedClientSecret = queryClient.getQueryData<string | null>([
    QUERY_KEYS.CREATE_INVOICE_FOR_PURCHASE,
    productsIds,
  ]);

  const {
    data: fetchedClientSecret,
    error,
    isError,
  } = useQuery<string | null | undefined>({
    enabled:
      Boolean(productsIds.length > 0) &&
      cachedClientSecret === undefined &&
      Boolean(showBillingDetails),
    queryFn: () => PURCHASES.CREATE_INVOICE({ products: productsIds }),
    queryKey: [QUERY_KEYS.CREATE_INVOICE_FOR_PURCHASE, productsIds],
  });

  const clientSecret = cachedClientSecret || fetchedClientSecret;

  const options: StripeElementsOptions = {
    appearance,
    clientSecret: (clientSecret || '') as string,
    locale: i18n.language as StripeElementLocale,
    loader: 'never',
  };

  const isFormLoading =
    !clientSecret || !productDetails || !stripePromise || isStripeLoading;

  const handlePaymentPending = (value: boolean) => {
    setIsPaymentPending(value);
  };

  useEffect(() => {
    if (isError) {
      handlePaymentProcessing('failed', error?.message || 'call_issuer');
    }
  }, [isError, error]);

  const submitForm = () => {
    const form = document.getElementById('stripe-form') as HTMLFormElement;

    if (form) {
      form.requestSubmit();
    }
  };

  const updateProfileMutation = useMutation({
    mutationFn: PROFILES.UPDATE_USER_PROFILE,
    onError: (error) => {
      if (error.message === USERS_ERRORS.INVALID_TAX_ID) {
        setIsInvalidVat(true);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
    onSuccess: (res) => {
      if (res) {
        invalidateUser();
        setIsEditEnabled(false);
      }
    },
  });

  return (
    <Box
      h="fit-content"
      m="auto"
      maw={showBillingDetails ? 1222 : 785}
      miw={768}
      w="100%"
      ml={20}
      mr={20}
      mt={20}
      mb={20}
    >
      <Card size="xl" bg="gray50" className={styles.card}>
        <div className={styles.wrapper}>
          <div className={styles.header}>
            <Text transKey="checkout" type="h3" fw={250} />

            <div className={styles.headerActions}>
              <CloseButton onClick={onCloseCheckout} variant="outlined" />
            </div>
          </div>

          {showBillingDetails && (
            <div className={styles.body}>
              <div className={styles.cardDetails}>
                <Flex
                  direction="row"
                  justify="space-between"
                  align="center"
                  mb={20}
                >
                  <Text transKey="billing-address" type="h4" fw={300} />

                  <Button
                    transKey="edit"
                    variant="primaryOutlined"
                    onClick={() => setIsEditEnabled(true)}
                  />
                </Flex>

                <Flex direction="row" mb={40} gap="var(--spacing-4xl)">
                  <Flex
                    direction="column"
                    gap="var(--spacing-md)"
                    justify="space-between"
                  >
                    <Flex direction="column">
                      <Text transKey="address" />
                      <Text
                        untranslatedText={user.profile.address?.addressLine1}
                        fw={300}
                        color="gray600"
                      />
                    </Flex>

                    <Flex direction="column">
                      <Text transKey="post-code" />
                      <Text
                        untranslatedText={user.profile.address?.postcode}
                        fw={300}
                        color="gray600"
                      />
                    </Flex>
                  </Flex>

                  <Flex
                    direction="column"
                    gap="var(--spacing-md)"
                    justify="space-between"
                  >
                    <Flex direction="column">
                      <Text transKey="city" />
                      <Text
                        untranslatedText={user.profile.address?.city}
                        fw={300}
                        color="gray600"
                      />
                    </Flex>

                    <Flex direction="column">
                      <Text transKey="country" />
                      <Text
                        untranslatedText={getName(
                          user.profile.address?.country || '',
                          i18n.language
                        )}
                        fw={300}
                        color="gray600"
                      />
                    </Flex>
                  </Flex>
                </Flex>

                <Flex
                  mb={20}
                  direction="row"
                  justify="space-between"
                  align="center"
                >
                  <Text transKey="card-details" type="h4" fw={300} />

                  <Icon name="StripeLogoSvg" />
                </Flex>

                <div className={styles.stripeWrapper}>
                  {isFormLoading && <PurchaseFormSkeleton />}

                  {clientSecret && stripePromise && showBillingDetails && (
                    <Elements stripe={stripePromise} options={options}>
                      <StripeForm
                        isLoading={isFormLoading}
                        handleStripeLoaded={() => setIsStripeLoading(false)}
                        clientSecret={clientSecret}
                        handlePaymentPending={handlePaymentPending}
                        handlePaymentProcessing={handlePaymentProcessing}
                      />
                    </Elements>
                  )}
                </div>
              </div>

              <div className={styles.summary}>
                <div>
                  <Text transKey="summary" type="h4" fw={300} mb={20} />
                </div>

                <div className={styles.summaryDetails}>
                  <div className={styles.summaryListWrapper}>
                    {productDetails?.map((product) => (
                      <div
                        className={styles.summaryItem}
                        key={product.stripeId}
                      >
                        <div className={styles.itemDetails}>
                          <Text
                            type="body1"
                            fw={400}
                            untranslatedText={`${product.name} ${t(product.kind)}`}
                          />
                        </div>

                        <Text
                          type="h4"
                          fw={300}
                          untranslatedText={`${currencySymbol}${getFormattedPrice(product.price?.totalAmount || 0)}`}
                        />
                      </div>
                    ))}
                  </div>

                  <div className={styles.summaryFooter}>
                    <div className={styles.totalValueWrapper}>
                      <Text
                        transKey="total-price"
                        transVariables={{
                          totalPrice: `${currencySymbol}${getFormattedPrice(totalPrice)}`,
                        }}
                        type="h3"
                        fw={300}
                        color="blue"
                        mb={8}
                      />
                      <Text transKey="prices-include-vat" type="body3" />
                    </div>

                    <Button
                      transKey="pay-capital"
                      isDisabled={isFormLoading}
                      isLoading={isPaymentPending}
                      onClick={submitForm}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {showBillingForm && (
            <Flex direction="column">
              <Text transKey="billing-address" type="h4" fw={300} mb={24} />

              <BillingDetailsForm
                defaultValues={{
                  address: {
                    addressLine1: user?.profile?.address?.addressLine1 || '',
                    addressLine2: user?.profile?.address?.addressLine2 || '',
                    city: user?.profile?.address?.city || '',
                    country:
                      user?.profile?.address?.country?.toLowerCase() || '',
                    postcode: user?.profile?.address?.postcode || '',
                  },
                  vatNumber: user?.vatNumber || '',
                }}
                updateProfileMutation={updateProfileMutation}
                isInvalidVat={isInvalidVat}
                updateInvalidVat={setIsInvalidVat}
              />

              <Flex
                mt="var(--spacing-xl)"
                justify="flex-end"
                gap="var(--spacing-lg)"
              >
                {hasBillingAddress && (
                  <Button
                    transKey="cancel-capital"
                    variant="dangerOutlined"
                    isDisabled={updateProfileMutation.isPending}
                    isLoading={updateProfileMutation.isPending}
                    onClick={() => setIsEditEnabled(false)}
                  />
                )}

                <Button
                  transKey="save-capital"
                  isDisabled={updateProfileMutation.isPending}
                  isLoading={updateProfileMutation.isPending}
                  onClick={() => submitFormById('billing-details-form')}
                />
              </Flex>
            </Flex>
          )}
        </div>
      </Card>
    </Box>
  );
};

export default CheckoutForm;
