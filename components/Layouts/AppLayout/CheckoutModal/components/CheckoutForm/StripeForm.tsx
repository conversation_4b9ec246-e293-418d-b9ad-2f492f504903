/* eslint-disable react-hooks/exhaustive-deps */
import {
  PaymentElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import React, { useEffect } from 'react';

import { paymentElementOptions } from '@/common/consts';
import { UserContext } from '@/context/UserProvider';

import { HandlePaymentProcessingType } from '../types';
import styles from './CheckoutFormStyles.module.css';

type StripeFormProps = {
  isLoading: boolean;
  handleStripeLoaded: () => void;
  clientSecret: string;
  handlePaymentPending: (value: boolean) => void;
  handlePaymentProcessing: HandlePaymentProcessingType;
};

const StripeForm = ({
  clientSecret,
  handlePaymentPending,
  handlePaymentProcessing,
  handleStripeLoaded,
  isLoading,
}: StripeFormProps) => {
  const elements = useElements();
  const stripe = useStripe();

  const { user } = UserContext();

  useEffect(() => {
    if (elements) {
      const element = elements.getElement('payment');

      element?.on('ready', () => {
        handleStripeLoaded();
      });
    }
  }, [elements]);

  const submitForm = async (e: React.FormEvent) => {
    e.preventDefault();

    handlePaymentPending(true);

    if (elements == null || stripe == null) {
      // TODO maybe this needs to be removed, check after testing
      handlePaymentProcessing('failed', 'call_issuer');

      return;
    }

    const { error: submitError } = await elements.submit();

    if (submitError) {
      handlePaymentPending(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmPayment({
      clientSecret,
      elements,
      redirect: 'if_required',
      confirmParams: {
        payment_method_data: {
          billing_details: {
            name: `${user.profile.firstName} ${user.profile.lastName}`,
            email: user.profile.email.trim().toLowerCase(),
          },
        },
      },
    });

    if (paymentIntent && paymentIntent?.status === 'succeeded') {
      handlePaymentProcessing('success');
    } else if (error) {
      handlePaymentProcessing('failed', error.decline_code);
    }

    handlePaymentPending(false);
  };

  return (
    <form id="stripe-form" onSubmit={submitForm}>
      <PaymentElement
        options={paymentElementOptions}
        className={styles[isLoading ? 'hide' : 'show']}
      />
    </form>
  );
};

export default StripeForm;
