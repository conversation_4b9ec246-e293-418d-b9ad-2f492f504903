import { Box } from '@mantine/core';
import React from 'react';

import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { PurchasesContext } from '@/context/PurchasesProvider';
import { TranslationKeysType } from '@/types/common';

import styles from './CheckoutFailedStyles.module.css';

const CheckoutFailed = () => {
  const { errorCode, onCloseCheckout } = PurchasesContext();

  return (
    <Box
      h="fit-content"
      w="100%"
      m="auto"
      maw={785}
      ml={20}
      mr={20}
      mt={20}
      mb={20}
    >
      <Card size="xl" bg="gray50">
        <div className={styles.wrapper}>
          <div className={styles.header}>
            <Text transKey="payment-failed" type="h2" fw={250} align="center" />

            <CloseButton onClick={onCloseCheckout} variant="outlined" />
          </div>

          <div className={styles.content}>
            <div className={styles.errorAnimation}>
              <Icon name="FailureCircleSvg" color="danger" />
            </div>

            <Text
              transKey="transaction-failed-capital"
              type="subTitle1"
              mt={20}
              mb={32}
              color="danger"
              fw={700}
              align="center"
            />

            <Text
              transKey="could-not-process-payment"
              type="h2"
              mb={20}
              color="black"
              fw={250}
              align="center"
            />

            <Text
              transKey={(errorCode as TranslationKeysType) || 'call_issuer'}
              type="body1"
              mb={40}
              color="black"
              fw={400}
              align="center"
            />

            <Text
              transKey="contact-customer-service"
              type="body1"
              mb={20}
              color="black"
              fw={300}
              align="center"
            />

            <Text
              untranslatedText="<EMAIL>"
              type="body1"
              mt={20}
              mb={20}
              color="black"
              fw={700}
              align="center"
            />
          </div>
        </div>
      </Card>
    </Box>
  );
};

export default CheckoutFailed;
