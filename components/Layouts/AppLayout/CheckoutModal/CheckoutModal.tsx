import React from 'react';

import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import { PurchasesContext } from '@/context/PurchasesProvider';

import CheckoutFailed from './components/CheckoutFailed/CheckoutFailed';
import CheckoutForm from './components/CheckoutForm/CheckoutForm';
import CheckoutSuccess from './components/CheckoutSuccess/CheckoutSuccess';

const modalContent = {
  form: <CheckoutForm />,
  success: <CheckoutSuccess />,
  failed: <CheckoutFailed />,
};

const CheckoutModal = () => {
  const { displayedScreen, isCheckoutModalVisible } = PurchasesContext();

  return (
    <PrimaryModal
      isOpen={isCheckoutModalVisible}
      content={modalContent[displayedScreen]}
    />
  );
};

export default CheckoutModal;
