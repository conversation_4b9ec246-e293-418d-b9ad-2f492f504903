.wrapper {
  min-height: 100vh;
  background: linear-gradient(136.27deg, #1590c1 9.59%, #3a9ea7 56.18%);
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 940px) {
    align-items: flex-start;
  }
}

.auth-main-wrapper {
  display: flex;
  justify-content: center;
  padding: 0 var(--spacing-2xl);
  transition: all 0.4s ease-in-out;

  @media (max-width: 940px) {
    padding-top: 10vh;
  }

  @media (max-width: 575px) {
    padding: var(--spacing-lg) var(--spacing-md);
    padding-top: var(--spacing-3xl);
  }
}

.certification-main-wrapper {
  overflow: auto;
  display: flex;
  transition: width 0.4s ease-in-out;
  height: 100%;
}

.certificationContainer {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  padding: var(--spacing-md);

  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-3xl);

  @media screen and (max-width: 1024px) {
    gap: var(--spacing-lg);
  }

  @media screen and (max-width: 940px) {
    flex-wrap: wrap;
    gap: var(--spacing-2xl);
  }
}

.certification-promo {
  margin-top: 10vh;
}

.container {
  height: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-3xl);
  flex-wrap: wrap;
  margin: var(--spacing-md) 0;
}

.auth-wrapper {
  max-width: 945px;
  width: 100%;
  gap: var(--spacing-xl);

  @media (max-width: 940px) {
    flex-direction: column;
    align-items: center;
  }
}

.certification-wrapper {
  width: 1200px;
  flex-wrap: wrap;

  display: flex;

  margin: auto;

  padding: 10px var(--spacing-lg);

  @media (max-width: 1200px) {
    width: 100%;
  }

  @media (max-width: 1075px) {
    gap: var(--spacing-3xl);
    justify-content: space-between;
  }

  @media (max-width: 1024px) {
    gap: var(--spacing-lg);
  }

  @media (max-width: 966px) {
    gap: var(--spacing-3xl);
    justify-content: center;
  }

  @media (max-width: 940px) {
    padding: var(--spacing-2xl) var(--spacing-xl);
  }
}

.promo-wrapper {
  min-width: 315px;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-content: center;
  padding-top: var(--spacing-3xl);

  height: 100%;
  align-self: flex-start;

  @media (max-width: 940px) {
    margin-top: 0;
    min-width: 500px;
    flex-direction: row;
  }

  @media (max-width: 575px) {
    min-width: 100%;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: var(--spacing-lg);
    padding-top: 0;

    & > h4 {
      font-size: 20px;
    }
  }
}

.auth-promo-wrapper {
  padding-top: 0;
}

.divider {
  margin: 24px 0;
  width: 100px;
  background: var(--color-white);
  opacity: 0.4;

  @media (max-width: 940px) {
    width: 1px;
    margin: 0 24px;
  }

  @media (max-width: 575px) {
    margin: 16px 0;
    width: 100%;
  }
}

.content {
  width: 'fit-content';

  @media (max-width: 575px) {
    width: 100%;
  }
}

.logo {
  width: 180px;
}
