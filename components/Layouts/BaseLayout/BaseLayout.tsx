import { Divider, em } from '@mantine/core';
import React from 'react';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import s from './BaseLayout.module.css';

type BaseLayoutProps = {
  isAuthPage?: boolean;
  children: React.ReactNode;
};

const BaseLayout = ({ children, isAuthPage = true }: BaseLayoutProps) => {
  return (
    <div
      className={`${s.wrapper}  ${isAuthPage ? s['auth-main-wrapper'] : s['certification-main-wrapper']} specialBackground`}
    >
      <div className={s.container}>
        <div className={s['promo-wrapper']}>
          <Icon name="LogoFullSvg" color="white" className={s.logo} />

          <Divider className={s.divider} />

          <Text
            transKey="base-layout-first-promo-text"
            color="white"
            type="h2"
            fw={600}
            align="left"
          />

          <Text
            transKey="base-layout-second-promo-text"
            color="white"
            type="h2"
            fw={600}
            align="left"
          />
        </div>

        <div className={s.content}>{children}</div>
      </div>
    </div>
  );
};

export default BaseLayout;
