import { Divider } from '@mantine/core';
import React from 'react';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import s from './BaseLayout.module.css';

type BaseLayoutProps = {
  isAuthPage?: boolean;
  children: React.ReactNode;
};

const BaseLayout = ({ children, isAuthPage = true }: BaseLayoutProps) => {
  return (
    <div
      className={`${s.wrapper}  ${isAuthPage ? s['auth-main-wrapper'] : s['certification-main-wrapper']} specialBackground`}
    >
      <div className={`${isAuthPage ? s.container : s.certificationContainer}`}>
        <div
          className={`${s['promo-wrapper']} ${!isAuthPage && s['certification-promo']}`}
        >
          <Icon name="LogoFullSvg" color="white" className={s.logo} />

          <Divider className={s.divider} />

          <Text
            transKey="base-layout-full-promo-text"
            color="white"
            type="h2"
            fw={600}
            align="left"
          />
        </div>

        <div className={s.content}>{children}</div>
      </div>
    </div>
  );
};

export default BaseLayout;
