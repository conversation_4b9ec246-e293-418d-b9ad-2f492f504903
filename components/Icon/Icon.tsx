import { Box } from '@mantine/core';
import React from 'react';

import baseTheme from '../../styles/baseTheme';
import { Icons } from './Icons/index';

const { colors } = baseTheme;

const iconSizes = {
  sm: 14,
  md: 22,
  lg: 27,
  xl: 34,
  xxl: 188,
};

type IconProps = {
  name: keyof typeof Icons;
  w?: number;
  h?: number;
  color?: keyof typeof colors;
  size?: keyof typeof iconSizes;
  onClick?: () => void;
  hasCursor?: boolean;
  className?: string;
};

const Icon = ({
  className,
  color = 'black',
  h,
  hasCursor,
  name,
  onClick,
  size,
  w,
}: IconProps) => {
  return (
    <Box
      w={w || undefined}
      h={h || undefined}
      c={baseTheme.colors[color]}
      className={className}
      onClick={onClick}
      style={{
        cursor: onClick || hasCursor ? 'pointer' : 'default',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {Icons[name]({
        width: size ? iconSizes[size] : w,
        height: size ? iconSizes[size] : h,
      })}
    </Box>
  );
};

export default Icon;
