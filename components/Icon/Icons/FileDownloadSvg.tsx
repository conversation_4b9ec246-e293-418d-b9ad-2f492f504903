/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const FileDownloadSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 48}
    height={props.height || 61}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 48 61"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M.112 3.084A2.431 2.431 0 0 1 2.536.66h32.36c.243 0 .606.121.849.364l11.029 11.029c.242.242.363.606.363.848v45.335a2.431 2.431 0 0 1-2.424 2.424H2.536a2.431 2.431 0 0 1-2.424-2.424V3.084Zm42.905 8.605h-6.909V4.781l6.909 6.908ZM2.537 3.084h31.147v8.605a2.431 2.431 0 0 0 2.424 2.424h8.605v44.123H2.536V3.084Z"
      clipRule="evenodd"
    />
    <path
      fill="currentColor"
      stroke="currentColor"
      strokeWidth={0.066}
      d="M23.66 23.135c-.616 0-1.115.5-1.115 1.115v14.63l-4.91-4.909a1.115 1.115 0 0 0-1.576 1.578l7.115 7.114a.689.689 0 0 0 .974 0l7.114-7.114a1.115 1.115 0 0 0-1.577-1.578l-4.909 4.91V24.25c0-.617-.5-1.116-1.115-1.116Z"
    />
  </svg>
);
export default FileDownloadSvg;
