/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const ProfilePlaceholderSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 120}
    height={props.height || 121}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 120 121"
  >
    <path
      fill="currentColor"
      d="M60 .34c-33.102 0-60 26.893-60 60 0 33.102 26.893 60.001 60 60.001 33.102 0 60-26.893 60-60 0-33.102-26.893-60-60-60Zm0 5.333c30.222 0 54.668 24.446 54.668 54.668 0 15.783-6.677 29.986-17.353 39.959-.5-15.739-13.46-28.395-29.316-28.395H52.003c-15.852 0-28.811 12.662-29.31 28.395-10.682-9.973-17.36-24.176-17.36-39.959C5.334 30.119 29.779 5.673 60 5.673Zm0 20.11c-11.013 0-20.002 8.982-20.002 20.002 0 11.013 8.983 20.002 20.002 20.002 11.013 0 20.002-8.983 20.002-20.003 0-11.013-8.983-20.002-20.002-20.002Zm0 5.331A14.624 14.624 0 0 1 74.665 45.78 14.624 14.624 0 0 1 60 60.443 14.624 14.624 0 0 1 45.336 45.78 14.624 14.624 0 0 1 60 31.114Zm-7.998 46.118h15.996c13.286 0 24.001 10.715 24.001 24.001v3.437a54.455 54.455 0 0 1-32 10.332 54.454 54.454 0 0 1-32-10.332v-3.437c0-13.286 10.717-24.001 24.003-24.001Z"
    />
  </svg>
);
export default ProfilePlaceholderSvg;
