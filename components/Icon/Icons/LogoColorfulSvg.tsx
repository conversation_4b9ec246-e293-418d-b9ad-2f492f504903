/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const LogoColorfulSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 144}
    height={props.height || 61}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 144 61"
  >
    <path
      fill="#3E97A7"
      d="M29.708.411h.12l.244.328c1.894-.14 3.698.654 5.282 1.614 1.314.812 2.259 2.05 3.265 3.193 1.375-1.985 3.351-3.634 5.72-4.254 3.216-1.036 6.872-.15 9.399 2.05 2.187 1.838 3.567 4.637 3.56 7.509.027 8.476-.002 16.951.015 25.425-.992-.002-1.983-.002-2.972.002.01-7.848-.003-15.7.007-23.548-.022-1.225-.152-2.445-.276-3.66-.53-1.927-1.901-3.625-3.737-4.454-2.118-1.063-4.7-.915-6.783.16-2.045 1.284-3.57 3.528-3.518 6.003-.035 8.494.01 16.987-.023 25.48-1.02.051-2.044.04-3.068.03.01-8.108 0-16.214.008-24.324-.015-1.262.069-2.57-.443-3.757-.88-2.482-3.322-4.202-5.898-4.492-1.183-.067-2.393-.042-3.523.361-2.148.704-3.863 2.63-4.19 4.889C20.24 26.014 17.65 43.076 15 60.129a60.56 60.56 0 0 0-2.672-.007c-.992-3.811-2.09-7.595-3.08-11.406-.547-1.907-1.044-3.826-1.566-5.74-.324-1.184-.642-2.377-1.131-3.502-.382-.873-1.068-1.55-1.728-2.216-1.387-.861-2.991-1.277-4.627-1.132-.003-.846.036-1.703-.128-2.54.039-.152.118-.457.16-.61 1.665.114 3.348.352 4.876 1.061 2.665 1.309 4.458 3.951 5.098 6.805.886 3.465 1.914 6.894 2.736 10.376l.354.138c1.425-8.62 2.635-17.272 4.045-25.89.75-5.313 1.64-10.607 2.404-15.917.229-1.488.585-2.997 1.424-4.269C22.622 2.87 25.18 1.218 27.94.748c.59-.093 1.252.03 1.769-.337ZM122.96 36.063c-.002-11.75.005-23.502-.002-35.252.989.01 1.981.007 2.972.002-.007 4.84-.007 9.68 0 14.519.423-.286.824-.6 1.255-.874l-.027-.147.179-.015c.793-.62 1.728-1.009 2.633-1.427 3.105-1.058 6.724-.595 9.369 1.373.212.128.426.256.642.384l-.024.1.354.35.202-.074-.067.2.359.366.202-.069-.076.2.359.364.185-.074-.025.204c.61.61 1.033 1.365 1.439 2.118.623 1.516.96 3.127.871 4.768.202.805.104 1.642.153 2.463.032 3.546-.007 7.093.02 10.639a74.142 74.142 0 0 0-3.049 0c-.128-1.334-.093-2.672-.032-4.008-.172-3.373-.029-6.756-.093-10.134-.069-.684-.217-1.356-.374-2.025-.052-.123-.16-.372-.214-.495-.916-1.751-2.458-3.26-4.399-3.789l-.023-.128c-.423-.041-.843-.086-1.242-.236-1.181-.073-2.374-.051-3.511.318a8.091 8.091 0 0 0-3.993 3.132c-.61 1.154-1.1 2.42-1.083 3.747.013 4.497.003 8.995.008 13.492-.989 0-1.978-.002-2.968.008ZM72.362 12.793c1.804-.585 3.718-.45 5.543-.051.234.074.463.15.694.231l.706.217c.802.307 1.563.71 2.286 1.17.583.4 1.149.825 1.658 1.315.205.41.667.543 1.053.73.013-1.257.005-2.517.003-3.774.991.02 1.983.015 2.974-.002-.012 7.806-.007 15.613-.002 23.42-.99.01-1.978 0-2.967.015 0-1.277-.01-2.552.007-3.829-.175.017-.526.047-.704.062l.045.25-.16.016c-.61.723-1.383 1.276-2.17 1.788-.65.386-1.329.724-2.023 1.019-.238.071-.475.14-.708.214-2.013.635-4.156.72-6.205.216-.231-.069-.46-.14-.69-.216a46.203 46.203 0 0 0-.713-.214c-.8-.32-1.557-.731-2.285-1.181a16.603 16.603 0 0 1-1.169-.903l-.064-.003a49.791 49.791 0 0 0-.369-.344l.061-.194-.314.054.076-.315-.332.083.088-.201c-.093-.089-.275-.271-.366-.36l-.185.072.052-.296h-.2a68.165 68.165 0 0 0-.797-1.084l-.049-.202c-.482-.615-.772-1.344-1.097-2.045a24.557 24.557 0 0 1-.163-.826l-.184-.027c-.008-.133-.027-.4-.037-.532a9.877 9.877 0 0 1-.01-5.58c.017-.103.052-.31.067-.413.127-.318.243-.64.351-.967.45-1.27 1.206-2.384 2-3.46.19-.157.385-.315.579-.47l-.032-.066c.093-.088.275-.268.367-.357l.066.015c.155-.192.312-.384.472-.573.401-.268.795-.546 1.186-.832.721-.46 1.489-.841 2.266-1.198.278-.057.559-.108.84-.155l.026-.177.53-.04Zm-.155 3.174c-2.554.884-4.598 3.031-5.472 5.575-.073.313-.15.623-.248.928-.2 1.587-.217 3.243.34 4.768.897 2.448 2.91 4.483 5.378 5.342.386.11.775.216 1.151.357 1.186.135 2.384.162 3.568-.023.332-.1.674-.172 1.018-.233l.027-.101c2.544-.775 4.614-2.894 5.485-5.39.118-.367.223-.742.364-1.1.167-1.59.204-3.239-.345-4.764-.334-.9-.82-1.732-1.385-2.507-1.063-1.267-2.426-2.323-4.008-2.852-.384-.113-.765-.216-1.137-.356a15.145 15.145 0 0 0-3.594 0c-.377.13-.763.238-1.142.356Z"
    />
    <path
      fill="#3E97A7"
      d="M36.683 43.608c2.074.047 4.145-.079 6.22-.01 1.564.135 3.186.787 4.14 2.084.192.335.38.672.571 1.006.354 1.007.475 2.092.234 3.142-.332 1.442-1.38 2.579-2.633 3.312-.43.125-.859.26-1.287.391-1.614.293-3.257.143-4.888.17.037 2.185.005 4.37.02 6.557-.79-.005-1.58-.005-2.37-.003 0-5.55.012-11.099-.007-16.65Zm2.403 7.627c1.386-.032 2.77.04 4.154-.032.651.007 1.188-.408 1.71-.743.38-.689.787-1.462.57-2.273-.12-1.189-1.225-2.06-2.381-2.109-1.349-.081-2.7-.017-4.048-.027.005 1.727.008 3.457-.005 5.184ZM51.149 60.26c.002-5.551.01-11.1-.002-16.648 2.344.089 4.711-.214 7.044.104 1.387.31 2.743 1.05 3.505 2.288.684 1.36 1.044 3.021.303 4.436.008.079.025.236.032.317l-.177-.017a7.86 7.86 0 0 1-.66 1.228l-.115-.027-.357.344.03.104c-.613.452-1.285.829-2.02 1.045.022.12.069.364.09.485.606.89 1.103 1.85 1.644 2.78.46.792.906 1.59 1.358 2.384l.175.047-.05.413.176.052.457.674c-.918-.052-1.865.103-2.753-.197-.187-.42-.42-.819-.647-1.22l-.184-.047.059-.423-.19-.02c-.445-.841-.972-1.636-1.402-2.485l-.185-.03c.012-.08.035-.238.044-.319-.452-.551-.777-1.186-1.085-1.823-.934.002-1.867.007-2.8-.003.023 2.188.025 4.375.005 6.562-.765-.012-1.53-.01-2.295-.005Zm2.401-14.217c.01 1.732.003 3.462.003 5.194 1.722-.116 3.5.229 5.176-.293 1.686-1.016 1.686-4.057-.337-4.672-1.567-.455-3.235-.13-4.842-.229ZM69.064 43.935a7.867 7.867 0 0 1 3.824-.236c.81.21 1.557.588 2.276 1.009.767.612 1.417 1.38 1.788 2.298.721 1.508.453 3.213.5 4.827-.05 1.223.118 2.465-.118 3.673-.067.318-.13.638-.2.955-.566 1.725-2.059 2.95-3.734 3.535-.394.067-.793.116-1.186.165-.906.222-1.819-.047-2.714-.17-1.752-.49-3.23-1.89-3.799-3.616-.526-1.457-.229-3.029-.305-4.542.071-1.634-.26-3.36.467-4.894.286-.78.835-1.41 1.42-1.983.234-.157.468-.317.701-.475.352-.197.714-.376 1.08-.546Zm-.787 3.499c-.63 1.023-.47 2.276-.482 3.42.09 1.931-.443 4.003.608 5.767 1.048 1.122 2.637 1.343 4.093 1.163A37.694 37.694 0 0 0 74 57.087c1.375-1.307.972-3.302 1.026-4.997-.07-1.59.27-3.295-.527-4.764-1.523-1.82-4.815-1.872-6.222.109Z"
    />
    <path
      fill="#F46036"
      d="M103.496 12.11c1.07-.002 2.138-.002 3.208-.005.01 3.457.003 6.916.005 10.376 3.465-.015 6.929.005 10.393-.013.002 1.09.007 2.18-.005 3.273-3.459-.015-6.923-.081-10.383.034-.017 3.43.005 6.86-.01 10.29a99.227 99.227 0 0 0-3.2.002c-.018-3.437-.003-6.874-.01-10.309-3.455-.086-6.914-.022-10.368-.034a39.76 39.76 0 0 1-.013-3.25c3.46.01 6.921-.01 10.381.012.007-3.46-.003-6.919.002-10.376Z"
    />
  </svg>
);
export default LogoColorfulSvg;
