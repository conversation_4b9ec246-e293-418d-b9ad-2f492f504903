/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const PersonSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 56}
    height={props.height || 56}
    viewBox="0 0 56 56"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M28 0C12.5526 0 0 12.5499 0 28C0 43.4474 12.5499 56 28 56C43.4474 56 56 43.4501 56 28C56 12.5526 43.4501 0 28 0ZM28 2.4884C42.1035 2.4884 53.5117 13.8966 53.5117 28.0001C53.5117 35.3656 50.3959 41.9933 45.4137 46.6475C45.18 39.3027 39.1322 33.3967 31.7328 33.3967H24.2678C16.8707 33.3967 10.8229 39.3054 10.5897 46.6475C5.60497 41.9935 2.48901 35.3656 2.48901 28.0001C2.48901 13.8966 13.8965 2.4884 28 2.4884ZM28 11.8726C22.8604 11.8726 18.6655 16.0646 18.6655 21.207C18.6655 26.3466 22.8576 30.5415 28 30.5415C33.1396 30.5415 37.3345 26.3495 37.3345 21.207C37.3345 16.0675 33.1425 11.8726 28 11.8726ZM28 14.361C31.7956 14.361 34.8434 17.4112 34.8434 21.2044C34.8434 25 31.7933 28.0478 28 28.0478C24.2044 28.0478 21.1566 24.9977 21.1566 21.2044C21.1566 17.4088 24.2067 14.361 28 14.361ZM24.2674 35.8824H31.7324C37.9324 35.8824 42.9329 40.8829 42.9329 47.083V48.6868C38.7357 51.7186 33.5803 53.5088 27.9995 53.5088C22.4187 53.5088 17.2633 51.7186 13.0662 48.6868V47.083C13.0662 40.8829 18.0673 35.8824 24.2674 35.8824Z"
      fill="currentColor"
    />
  </svg>
);
export default PersonSvg;
