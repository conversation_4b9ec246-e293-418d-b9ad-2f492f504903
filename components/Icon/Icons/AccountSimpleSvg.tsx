/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const AccountSimpleSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 30}
    height={props.height || 30}
    fill="none"
    viewBox="0 0 30 30"
  >
    <g clipPath="url(#clip0_1888_3554)">
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M19.683 14.164a4.686 4.686 0 11-9.371 0 4.686 4.686 0 019.37 0zm2 0a6.686 6.686 0 11-13.372 0 6.686 6.686 0 0113.372 0zm.492 14.48H7.825c.264-2.462 2.996-4.991 7.173-4.991h.004c4.176 0 6.91 2.529 7.173 4.991zm-16.37.39c0-4.07 4.123-7.381 9.193-7.381h.004c5.069 0 9.194 3.31 9.194 7.38v.895c0 .396-.401.716-.893.716H6.697c-.494 0-.893-.322-.893-.716v-.894z"
        clipRule="evenodd"
      />
    </g>
    <rect
      width="28"
      height="28"
      x="1"
      y="1"
      stroke="currentColor"
      strokeWidth="2"
      rx="14"
    />
    <defs>
      <clipPath id="clip0_1888_3554">
        <rect width="30" height="30" fill="#fff" rx="15" />
      </clipPath>
    </defs>
  </svg>
);
export default AccountSimpleSvg;
