/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const CartAddProductSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 29}
    height={props.height || 24}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 29 24"
  >
    <path
      fill="currentColor"
      d="M27.837 4.26a.954.954 0 0 0-.767-.367c-.288 0-.728.108-.876.552l-.38 1.379-2.514 9.624H10.704L6.872.736C6.749.306 6.382 0 5.922 0H1.936c-.552 0-.98.43-.98.98 0 .553.428.982.98.982h3.249l.797 3.095v.062l3.004 11.555c.122.43.49.736.95.736h14.13c.429 0 .827-.307.95-.736L28.02 5.088a1.02 1.02 0 0 0-.184-.827Z"
    />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M8.987 20.996a3.009 3.009 0 0 1 3.004-3.004c1.655 0 3.004 1.38 3.004 3.004A3.009 3.009 0 0 1 11.99 24a3.009 3.009 0 0 1-3.004-3.004Zm1.931.03c0 .583.49 1.074 1.073 1.074.582 0 1.042-.49 1.073-1.073 0-.582-.49-1.073-1.073-1.073-.582 0-1.073.49-1.073 1.073ZM19.01 20.996a3.009 3.009 0 0 1 3.004-3.004c1.686 0 3.034 1.38 3.004 3.004A3.009 3.009 0 0 1 22.014 24a3.009 3.009 0 0 1-3.004-3.004Zm1.931.03c0 .583.49 1.074 1.073 1.074.613 0 1.073-.49 1.073-1.073 0-.582-.49-1.073-1.073-1.073s-1.073.49-1.073 1.073Z"
      clipRule="evenodd"
    />
    <path
      fill="currentColor"
      d="M16.04 1.92a.92.92 0 1 1 1.84 0v7.368a.92.92 0 1 1-1.84 0V1.92Z"
    />
    <path
      fill="currentColor"
      d="M20.643 4.684a.92.92 0 1 1 0 1.84h-7.368a.92.92 0 1 1 0-1.84h7.368Z"
    />
  </svg>
);
export default CartAddProductSvg;
