/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const MenuDotsSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 18}
    height={props.height || 18}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 18 18"
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.385}
      d="M3.461 9.692a.692.692 0 1 0 0-1.384.692.692 0 0 0 0 1.384ZM9 9.692a.692.692 0 1 0 0-1.384.692.692 0 0 0 0 1.384ZM14.539 9.692a.692.692 0 1 0 0-1.384.692.692 0 0 0 0 1.384Z"
    />
  </svg>
);
export default MenuDotsSvg;
