/* eslint-disable react/destructuring-assignment */
import * as React from 'react';

const RTypeLicenseSvg = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 43}
    height={props.height || 46}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 43 46"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M32.958 3.993h3.07a6.026 6.026 0 0 1 6.018 6.02V39.98a6.028 6.028 0 0 1-6.02 6.02H6.843a6.028 6.028 0 0 1-6.02-6.02V10.014a6.028 6.028 0 0 1 6.02-6.02h3.07C10.159 1.753 12.039 0 14.342 0h14.19c2.303 0 4.184 1.755 4.427 3.993ZM14.34 2.78c-.935 0-1.698.762-1.698 1.698v1.812c0 .935.763 1.698 1.698 1.698h14.19a1.7 1.7 0 0 0 1.699-1.698V4.477a1.7 1.7 0 0 0-1.698-1.698H14.34ZM36.027 43.22a3.245 3.245 0 0 0 3.242-3.241l-.002-29.966a3.245 3.245 0 0 0-3.241-3.242h-3.07c-.243 2.24-2.123 3.993-4.426 3.993H14.34c-2.304 0-4.184-1.755-4.428-3.993H6.843a3.245 3.245 0 0 0-3.241 3.242V39.98a3.245 3.245 0 0 0 3.241 3.242h29.184Zm-19.383-23.45V33.03h2.22V28.4h3.15l2.245 4.63h2.513l-2.448-5.023a3.265 3.265 0 0 0 1.417-1.127c.546-.78.82-1.71.82-2.79 0-.73-.123-1.393-.369-1.988a3.498 3.498 0 0 0-1.114-1.492c-.497-.393-1.13-.648-1.896-.764-.172-.03-.36-.05-.562-.055a8.517 8.517 0 0 0-.497-.019h-5.479Zm5.387 6.556h-3.168v-4.475h3.168c.122 0 .26.006.414.018.153.007.294.028.423.065.369.092.657.255.866.488.215.233.365.5.451.8.092.296.138.584.138.866 0 .283-.046.571-.138.866a1.971 1.971 0 0 1-.451.792c-.209.233-.497.395-.866.488-.128.036-.27.06-.423.073a5.234 5.234 0 0 1-.414.019Z"
      clipRule="evenodd"
    />
  </svg>
);
export default RTypeLicenseSvg;
