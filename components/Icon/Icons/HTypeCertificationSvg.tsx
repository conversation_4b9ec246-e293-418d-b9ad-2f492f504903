/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const HTypeCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 41}
    height={props.height || 53}
    viewBox="0 0 49 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M34.4121 46.2028C34.7883 46.2086 35.1609 46.1278 35.5009 45.9666C36.7799 45.3502 37.0795 43.7716 37.3445 42.3832C37.4712 41.7207 37.6786 40.6145 37.9552 40.3956C38.007 40.3553 38.2547 40.2228 39.2975 40.2228H40.0119H40.7839C42.0225 40.2228 43.2727 40.0903 44.0274 39.1512C44.9434 37.999 44.4998 36.4262 44.108 35.0378C43.9467 34.4617 43.6414 33.3671 43.7681 33.0848C43.8949 32.8025 44.9722 32.3013 45.6059 32.019C46.9828 31.4083 48.4 30.7746 48.7054 29.3977C49.0107 28.0208 47.9276 26.7706 46.977 25.6933C46.5622 25.2266 45.7845 24.3567 45.7845 24.0341C45.7845 23.7115 46.5392 22.8185 46.9367 22.3403C47.87 21.2457 48.9243 20.0071 48.5902 18.6186C48.256 17.2302 46.7524 16.608 45.4273 16.0434C44.7936 15.7784 43.7393 15.329 43.5838 15.0237C43.4282 14.7184 43.7278 13.6353 43.8833 13.0419C44.2405 11.6477 44.6496 10.0692 43.7163 8.9515C42.9904 8.13342 41.8381 8.00092 40.8242 8.00092C40.4843 8.00092 40.1328 8.00092 39.7757 8.02972C39.4185 8.05853 39.1247 8.02972 38.8136 8.02972C37.9321 8.02972 37.6613 7.90298 37.6037 7.85113C37.3157 7.63221 37.091 6.56064 36.9412 5.85202C36.6531 4.50392 36.3248 2.94842 35.0458 2.37231C34.722 2.2255 34.3701 2.1508 34.0146 2.15339C33.0121 2.15339 31.9924 2.68918 31.0073 3.20191C30.4139 3.57276 29.7579 3.8324 29.0715 3.96814C28.7201 3.89325 28.0518 3.03484 27.6024 2.46449C26.7268 1.32955 25.7416 0.0678711 24.3071 0.0678711C22.8726 0.0678711 21.9105 1.37564 21.0578 2.5221C20.6545 3.06365 19.9747 3.96814 19.6348 4.0488H19.5715C18.8915 3.91247 18.2391 3.66322 17.6415 3.31137C16.7399 2.76702 15.7283 2.43047 14.6803 2.32623C14.3079 2.3222 13.9395 2.40298 13.603 2.56243C12.3182 3.17887 12.0244 4.75741 11.7594 6.14584C11.6327 6.80837 11.4253 7.9145 11.1487 8.13342C11.0969 8.17375 10.8491 8.30625 9.80638 8.30625H9.092H8.32001C7.08138 8.30625 5.83122 8.43876 5.07651 9.37782C4.1605 10.53 4.6041 12.1028 4.99585 13.4913C5.15717 14.0674 5.4625 15.162 5.33576 15.4443C5.20902 15.7266 4.14321 16.199 3.50949 16.4928C2.13258 17.1035 0.715353 17.7372 0.410014 19.1141C0.104676 20.491 1.18776 21.7412 2.13835 22.8185C2.55315 23.2851 3.3309 24.1551 3.3309 24.4777C3.3309 24.8003 2.57619 25.6933 2.17867 26.1714C1.24537 27.2661 0.191093 28.5047 0.525237 29.8931C0.859381 31.2815 2.36303 31.9038 3.68808 32.4626C4.32181 32.7276 5.37609 33.177 5.53164 33.4823C5.68719 33.7876 5.38761 34.8707 5.23206 35.4641C4.87487 36.8583 4.46583 38.4368 5.39913 39.5545C6.11927 40.3956 7.27149 40.5281 8.28545 40.5281C8.62535 40.5281 8.97678 40.5281 9.33397 40.5281C9.69116 40.5281 9.98497 40.5281 10.2961 40.5281C11.1775 40.5281 11.4483 40.6549 11.5059 40.7067C11.794 40.9256 12.0186 41.9972 12.1684 42.7058C12.4565 44.0251 12.7849 45.5806 14.0638 46.1567C14.3894 46.3044 14.7434 46.3791 15.1008 46.3756C16.1033 46.3756 17.123 45.8399 18.1081 45.3271C18.7015 44.9563 19.3574 44.6966 20.0439 44.5609C20.3953 44.6358 21.0636 45.4942 21.513 46.0645C22.3829 47.1995 23.368 48.4612 24.7795 48.4612C26.2083 48.4612 27.1761 47.1476 28.0288 46.0069C28.432 45.4654 29.1119 44.5609 29.4518 44.4802H29.5151C30.2028 44.6122 30.8631 44.8596 31.4681 45.2119C32.3641 45.7559 33.3696 46.0943 34.4121 46.2028ZM28.9563 42.2334C27.8041 42.5042 26.986 43.5815 26.2083 44.6243C25.8453 45.1082 25.0906 46.1164 24.7795 46.151C24.4684 46.1855 23.6964 45.1313 23.3277 44.6588C22.5269 43.6334 21.6743 42.5733 20.522 42.3256C20.3476 42.29 20.17 42.2726 19.992 42.2737C19.0069 42.2737 18.0044 42.798 17.0308 43.3107C16.4354 43.6813 15.7806 43.9464 15.0951 44.0942H15.0317C14.784 43.9099 14.5478 42.7865 14.421 42.1873C14.1503 40.9026 13.8449 39.5718 12.9001 38.8286C12.1242 38.3333 11.2079 38.1048 10.2903 38.1776C9.94465 38.1776 9.58746 38.1776 9.23027 38.2064C8.87308 38.2352 8.58502 38.2064 8.27969 38.2064C7.90706 38.2568 7.52779 38.2173 7.17355 38.0912C7.01224 37.8262 7.31758 36.6221 7.46737 36.046C7.78999 34.7958 8.12414 33.5053 7.59412 32.4511C7.06409 31.3968 5.80241 30.861 4.58682 30.3482C4.01071 30.1005 2.89881 29.6339 2.76631 29.3689C2.76631 29.052 3.52101 28.1533 3.91853 27.6751C4.76541 26.6784 5.64686 25.6529 5.64686 24.4604C5.64686 23.2679 4.73661 22.2712 3.87244 21.2976C3.44612 20.8079 2.66261 19.9206 2.66261 19.6268C2.79511 19.3388 3.8782 18.8548 4.45431 18.5956C5.66414 18.0598 6.9143 17.501 7.42704 16.4237C7.93978 15.3463 7.57683 14.1192 7.23117 12.8518C7.06985 12.2757 6.72995 11.0601 6.89702 10.7951C6.93159 10.7605 7.15051 10.5877 8.3373 10.5877H9.0632H9.82942C10.805 10.6781 11.7828 10.4388 12.6063 9.90784C13.5511 9.15314 13.7988 7.82808 14.0466 6.54336C14.1618 5.92692 14.3807 4.78046 14.6227 4.60762H14.6976C15.3784 4.74393 16.0311 4.99526 16.6275 5.35081C17.5218 5.89299 18.5254 6.2295 19.5657 6.33596C19.7595 6.33648 19.9528 6.31522 20.1418 6.27258C21.294 6.00181 22.1121 4.92448 22.8899 3.88172C23.2528 3.39779 24.0075 2.38384 24.3186 2.35503C24.6297 2.32622 25.4017 3.37475 25.7704 3.84716C26.5827 4.89568 27.4354 5.95572 28.5876 6.20345C28.762 6.23909 28.9396 6.25646 29.1176 6.2553C30.1028 6.2553 31.1052 5.73104 32.0788 5.2183C32.6741 4.84762 33.329 4.58255 34.0146 4.43479H34.0779C34.3257 4.61915 34.5619 5.74256 34.6886 6.34172C34.9594 7.62644 35.2647 8.95726 36.2095 9.70044C36.9853 10.1961 37.9017 10.4247 38.8193 10.3514C39.165 10.3514 39.5222 10.3514 39.8794 10.3226C40.2365 10.2938 40.5246 10.3226 40.8299 10.3226C41.2044 10.2796 41.5837 10.327 41.9361 10.4609C42.0974 10.7259 41.792 11.93 41.6423 12.5061C41.3196 13.7563 40.9855 15.0468 41.5155 16.101C42.0455 17.1553 43.3072 17.6911 44.5171 18.2038C45.0932 18.4516 46.2051 18.9182 46.3376 19.189C46.3376 19.5058 45.5829 20.4046 45.1853 20.8828C44.3385 21.8794 43.457 22.9049 43.457 24.0974C43.457 25.29 44.3673 26.2867 45.2314 27.2603C45.6577 27.7385 46.447 28.6257 46.4413 28.9195C46.3088 29.2076 45.2257 29.6915 44.6496 29.9507C43.4397 30.4865 42.1896 31.0453 41.6768 32.1227C41.1641 33.2 41.527 34.4559 41.8727 35.6946C42.034 36.2707 42.3739 37.492 42.2068 37.7513C42.1723 37.7858 41.9534 37.9587 40.7666 37.9587H40.0407H39.2744C38.2989 37.8686 37.3213 38.108 36.4976 38.6385C35.5528 39.3932 35.305 40.7182 35.0631 42.003C34.9478 42.6194 34.7289 43.7659 34.487 43.9387H34.4121C33.7308 43.8007 33.0781 43.5474 32.4821 43.1898C31.5879 42.6476 30.5843 42.3111 29.5439 42.2046C29.3476 42.1919 29.1504 42.2015 28.9563 42.2334Z"
      fill="currentColor"
    />
    <path
      d="M41.3139 42.5388C41.1123 42.5388 39.7527 42.5388 39.6547 42.5388C39.6547 42.6368 39.4761 43.4894 39.3897 43.8581L44.1426 53.6462L39.4819 51.9179C39.3351 51.862 39.1784 51.8367 39.0214 51.8436C38.8645 51.8504 38.7106 51.8893 38.5692 51.9578C38.4278 52.0264 38.302 52.1231 38.1994 52.242C38.0967 52.361 38.0195 52.4997 37.9725 52.6496L36.6474 56.8552L32.6146 48.2135C31.7658 47.8963 30.9349 47.5328 30.1258 47.1247L29.7744 47.5913L35.8409 60.4789C35.933 60.6724 36.0772 60.8363 36.2573 60.9523C36.4374 61.0683 36.6464 61.1318 36.8606 61.1357H36.9355C37.1662 61.1199 37.3868 61.0351 37.5687 60.8923C37.7505 60.7495 37.8852 60.5553 37.9552 60.3349L39.7987 54.4816L46.0323 56.8552C46.2478 56.9369 46.483 56.9521 46.7072 56.8988C46.9315 56.8455 47.1347 56.7261 47.2904 56.5561C47.4461 56.3861 47.5473 56.1733 47.5808 55.9452C47.6143 55.7171 47.5786 55.4842 47.4783 55.2766L41.3139 42.5388Z"
      fill="currentColor"
    />
    <path
      d="M19.485 47.2284C19.3698 47.3091 18.2176 47.8852 17.7221 48.0984L13.6087 56.8264L12.2836 52.6208C12.2335 52.4742 12.1543 52.3392 12.0508 52.2239C11.9473 52.1086 11.8216 52.0153 11.6812 51.9497C11.5409 51.8841 11.3887 51.8474 11.2338 51.842C11.079 51.8365 10.9246 51.8624 10.78 51.918L6.11923 53.6463L10.3652 44.8894C10.1578 44.2557 9.86971 42.8846 9.84091 42.7636L8.81543 42.7981L2.77779 55.2709C2.67752 55.4785 2.64182 55.7114 2.67531 55.9395C2.70881 56.1676 2.80996 56.3804 2.96569 56.5504C3.12141 56.7204 3.32455 56.8398 3.54884 56.8931C3.77314 56.9464 4.00826 56.9312 4.22383 56.8495L10.4573 54.4759L12.3009 60.3292C12.3703 60.5515 12.5054 60.7476 12.6885 60.8916C12.8716 61.0356 13.094 61.1207 13.3264 61.1357H13.4013C13.6201 61.1357 13.8343 61.0734 14.019 60.9561C14.2036 60.8387 14.3511 60.6713 14.444 60.4732L20.2051 48.1733C20.0381 47.9371 19.5599 47.3091 19.485 47.2284Z"
      fill="currentColor"
    />
    <path
      d="M19.3507 30.8872V17.6292H21.5696V23.2086H27.8488V17.6292H30.0584V30.8872H27.8488V25.2894H21.5696V30.8872H19.3507Z"
      fill="currentColor"
    />
  </svg>
);
export default HTypeCertificationSvg;
