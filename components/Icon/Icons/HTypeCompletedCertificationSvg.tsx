/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const HTypeCompletedCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 41}
    height={props.height || 53}
    viewBox="0 0 49 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M41.3139 42.8103C41.1123 42.8103 39.7527 42.8103 39.6547 42.8103C39.6547 42.9082 39.4761 43.7609 39.3897 44.1296L44.1426 53.9177L39.4819 52.1894C39.3351 52.1335 39.1784 52.1082 39.0214 52.115C38.8645 52.1219 38.7106 52.1608 38.5692 52.2293C38.4278 52.2979 38.302 52.3946 38.1994 52.5135C38.0967 52.6325 38.0195 52.7712 37.9725 52.921L36.6474 57.1267L32.6146 48.485C31.7658 48.1678 30.9349 47.8043 30.1258 47.3961L29.7744 47.8628L35.8409 60.7504C35.933 60.9438 36.0772 61.1078 36.2573 61.2238C36.4374 61.3398 36.6464 61.4033 36.8606 61.4072H36.9355C37.1662 61.3914 37.3868 61.3066 37.5687 61.1638C37.7505 61.021 37.8852 60.8267 37.9552 60.6064L39.7987 54.7531L46.0323 57.1267C46.2478 57.2084 46.483 57.2236 46.7072 57.1703C46.9315 57.117 47.1347 56.9976 47.2904 56.8276C47.4461 56.6576 47.5473 56.4448 47.5808 56.2167C47.6143 55.9886 47.5786 55.7557 47.4783 55.5481L41.3139 42.8103Z"
      fill="currentColor"
    />
    <path
      d="M19.4845 47.4999C19.3693 47.5805 18.2171 48.1567 17.7216 48.3698L13.6082 57.0979L12.2831 52.8923C12.233 52.7457 12.1538 52.6107 12.0503 52.4953C11.9468 52.38 11.8211 52.2868 11.6808 52.2211C11.5404 52.1555 11.3882 52.1189 11.2333 52.1134C11.0785 52.108 10.9241 52.1338 10.7795 52.1894L6.11875 53.9178L10.3647 45.1609C10.1573 44.5272 9.86923 43.156 9.84042 43.035L8.81494 43.0696L2.7773 55.5424C2.67703 55.75 2.64133 55.9829 2.67483 56.211C2.70832 56.4391 2.80947 56.6519 2.9652 56.8219C3.12092 56.9919 3.32406 57.1112 3.54836 57.1646C3.77265 57.2179 4.00778 57.2027 4.22334 57.1209L10.4569 54.7474L12.3004 60.6006C12.3698 60.823 12.5049 61.0191 12.688 61.163C12.8711 61.307 13.0935 61.3922 13.3259 61.4072H13.4008C13.6196 61.4072 13.8338 61.3448 14.0185 61.2275C14.2031 61.1102 14.3506 60.9427 14.4435 60.7447L20.2047 48.4447C20.0376 48.2085 19.5594 47.5805 19.4845 47.4999Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M35.5009 46.2381C35.1609 46.3993 34.7883 46.4801 34.4121 46.4743C33.3696 46.3658 32.3641 46.0274 31.4681 45.4834C30.8631 45.1311 30.2028 44.8837 29.5151 44.7517H29.4518C29.1118 44.8324 28.432 45.7369 28.0288 46.2784L28.0042 46.3112C27.1575 47.4442 26.1946 48.7327 24.7795 48.7327C23.368 48.7327 22.3829 47.471 21.5129 46.336L21.4779 46.2915C21.0294 45.7221 20.3863 44.9054 20.0439 44.8324C19.3574 44.9681 18.7015 45.2278 18.1081 45.5986L18.0801 45.6132C17.1036 46.1215 16.0937 46.6471 15.1008 46.6471C14.7434 46.6506 14.3894 46.5758 14.0638 46.4282C12.7971 45.8576 12.4628 44.3262 12.1767 43.0152L12.1684 42.9773L12.1651 42.9615C12.0153 42.2528 11.7918 41.1955 11.5059 40.9782C11.4483 40.9264 11.1775 40.7996 10.2961 40.7996H9.33397H8.28545C7.27149 40.7996 6.11927 40.6671 5.39913 39.826C4.46934 38.7125 4.87182 37.1416 5.22803 35.7513L5.23206 35.7356C5.24084 35.7021 5.25008 35.667 5.25967 35.6307C5.41998 35.0224 5.67841 34.0419 5.53164 33.7538C5.37609 33.4484 4.32181 32.9991 3.68808 32.7341L3.65074 32.7183C2.33491 32.1635 0.856245 31.54 0.525237 30.1646C0.191093 28.7762 1.24537 27.5375 2.17867 26.4429C2.19492 26.4234 2.21177 26.4031 2.22914 26.3823C2.63663 25.8927 3.3309 25.0586 3.3309 24.7492C3.3309 24.4314 2.57626 23.5825 2.15722 23.1112L2.13835 23.09C1.18776 22.0126 0.104676 20.7625 0.410014 19.3856C0.715353 18.0087 2.13258 17.3749 3.50949 16.7643C3.55911 16.7413 3.61138 16.7172 3.66564 16.6921C4.30436 16.3976 5.21894 15.9759 5.33576 15.7157C5.4625 15.4335 5.15717 14.3388 4.99585 13.7627C4.6041 12.3743 4.1605 10.8015 5.07651 9.6493C5.83122 8.71024 7.08138 8.57774 8.32001 8.57774H9.092H9.80638C10.8491 8.57774 11.0969 8.44523 11.1487 8.40491C11.4205 8.18973 11.6255 7.11747 11.7528 6.45174L11.7594 6.41732L11.7644 6.39096C12.0282 5.00897 12.3263 3.44645 13.6029 2.83391C13.9395 2.67446 14.3079 2.59369 14.6803 2.59771C15.7283 2.70196 16.7399 3.0385 17.6415 3.58286C18.2391 3.9347 18.8915 4.18396 19.5715 4.32028H19.6348C19.9747 4.23962 20.6545 3.33513 21.0578 2.79359L21.0763 2.76875C21.9244 1.62833 22.8829 0.339355 24.3071 0.339355C25.7416 0.339355 26.7268 1.60104 27.6024 2.73598L27.6376 2.78058C28.086 3.35001 28.7291 4.16666 29.0715 4.23963C29.7579 4.10389 30.4139 3.84424 31.0073 3.4734L31.0353 3.45878C32.0118 2.95053 33.0216 2.42488 34.0145 2.42488C34.3701 2.42229 34.722 2.49699 35.0458 2.6438C36.3204 3.21793 36.6509 4.76476 36.9382 6.10964L36.9412 6.12351L36.9445 6.13938C37.0943 6.84802 37.3178 7.90532 37.6037 8.12261C37.6613 8.17446 37.9321 8.30121 38.8136 8.30121C38.9162 8.30121 39.017 8.30434 39.1188 8.30751C39.3255 8.31394 39.5363 8.32051 39.7757 8.30121C40.1328 8.2724 40.4843 8.2724 40.8242 8.2724C41.8381 8.2724 42.9904 8.40491 43.7163 9.22298C44.646 10.3364 44.2436 11.9073 43.8874 13.2977L43.8833 13.3134C43.8745 13.3469 43.8653 13.3819 43.8557 13.4183C43.6954 14.0266 43.437 15.0071 43.5837 15.2952C43.7393 15.6005 44.7936 16.0499 45.4273 16.3149L45.4846 16.3393C46.7954 16.8976 48.2608 17.5217 48.5902 18.8901C48.9243 20.2785 47.87 21.5172 46.9367 22.6118C46.9205 22.6314 46.9036 22.6516 46.8862 22.6725C46.4787 23.1621 45.7845 23.9961 45.7845 24.3056C45.7845 24.6234 46.5391 25.4722 46.9582 25.9435L46.977 25.9648C47.9276 27.0421 49.0107 28.2922 48.7054 29.6692C48.4 31.0461 46.9828 31.6798 45.6059 32.2905C44.9722 32.5728 43.8949 33.074 43.7681 33.3563C43.6414 33.6386 43.9467 34.7332 44.108 35.3093C44.4998 36.6977 44.9434 38.2705 44.0274 39.4227C43.2727 40.3618 42.0225 40.4943 40.7838 40.4943H40.0119H39.2975C38.2547 40.4943 38.007 40.6268 37.9551 40.6671C37.6834 40.8823 37.4783 41.9545 37.3511 42.6202L37.3445 42.6547L37.3433 42.6609C37.0786 44.0478 36.778 45.6226 35.5009 46.2381ZM19.3502 17.9007V31.1587H21.5691V25.5609H27.8483V31.1587H30.0579V17.9007H27.8483V23.4801H21.5691V17.9007H19.3502Z"
      fill="currentColor"
    />
  </svg>
);
export default HTypeCompletedCertificationSvg;
