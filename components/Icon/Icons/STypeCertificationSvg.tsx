/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const STypeCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 41}
    height={props.height || 53}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 41 53"
  >
    <path
      fill="currentColor"
      d="M28.839 39.565c.318.005.634-.063.921-.2 1.082-.521 1.336-1.857 1.56-3.032.107-.56.283-1.496.517-1.682.044-.034.253-.146 1.136-.146H34.23c1.049 0 2.106-.112 2.745-.907.775-.975.4-2.305.068-3.48-.136-.488-.395-1.414-.287-1.653.107-.239 1.018-.663 1.555-.902 1.165-.516 2.364-1.053 2.622-2.218.259-1.165-.658-2.223-1.462-3.134-.351-.395-1.01-1.131-1.01-1.404 0-.273.64-1.029.976-1.433.79-.926 1.681-1.974 1.399-3.15-.283-1.174-1.555-1.7-2.676-2.178-.537-.225-1.429-.605-1.56-.863-.132-.259.121-1.175.253-1.677.302-1.18.648-2.516-.141-3.461-.615-.692-1.59-.805-2.448-.805-.287 0-.584 0-.887.025-.302.024-.55 0-.814 0-.746 0-.975-.107-1.024-.151-.243-.186-.433-1.092-.56-1.692-.244-1.14-.522-2.457-1.604-2.944a2.076 2.076 0 0 0-.872-.186c-.849 0-1.712.454-2.545.888a4.875 4.875 0 0 1-1.638.648c-.297-.063-.863-.79-1.243-1.272-.741-.96-1.575-2.028-2.789-2.028-1.213 0-2.027 1.106-2.749 2.076-.341.459-.916 1.224-1.204 1.292h-.053a5.255 5.255 0 0 1-1.634-.624 5.996 5.996 0 0 0-2.505-.833 2.077 2.077 0 0 0-.912.2c-1.087.521-1.335 1.857-1.56 3.032-.107.56-.282 1.496-.516 1.682-.044.034-.254.146-1.136.146H6.76c-1.048 0-2.106.112-2.744.906-.776.975-.4 2.306-.069 3.481.137.488.395 1.414.288 1.653-.107.239-1.01.638-1.545.887-1.165.517-2.365 1.053-2.623 2.218s.658 2.223 1.462 3.134c.351.395 1.01 1.131 1.01 1.404 0 .273-.64 1.029-.976 1.434-.79.926-1.681 1.974-1.399 3.149.283 1.174 1.555 1.7 2.677 2.174.536.224 1.428.604 1.56.863.131.258-.122 1.174-.254 1.677-.302 1.18-.648 2.515.142 3.46.609.712 1.584.824 2.442.824h1.701c.746 0 .975.108 1.024.152.244.185.434 1.092.56 1.691.244 1.116.522 2.433 1.604 2.92.276.125.575.188.878.185.848 0 1.71-.453 2.544-.887a4.873 4.873 0 0 1 1.638-.648c.298.063.863.79 1.243 1.272.736.96 1.57 2.028 2.764 2.028 1.21 0 2.028-1.111 2.75-2.077.34-.458.916-1.223 1.204-1.291h.053c.582.111 1.14.32 1.653.619.758.46 1.609.746 2.49.838Zm-4.616-3.359c-.976.23-1.668 1.141-2.326 2.023-.307.41-.945 1.263-1.209 1.292-.263.03-.916-.863-1.228-1.262-.678-.868-1.4-1.765-2.374-1.975a2.166 2.166 0 0 0-.449-.044c-.833 0-1.681.444-2.505.878a5.16 5.16 0 0 1-1.638.663h-.054c-.21-.156-.41-1.107-.517-1.614-.229-1.087-.487-2.213-1.287-2.842a3.578 3.578 0 0 0-2.208-.55c-.292 0-.595 0-.897.024-.302.024-.546 0-.804 0-.315.043-.636.01-.936-.098-.137-.224.122-1.243.249-1.73.273-1.058.555-2.15.107-3.042-.449-.892-1.516-1.345-2.545-1.78-.487-.209-1.428-.604-1.54-.828 0-.268.638-1.029.975-1.433.716-.844 1.462-1.711 1.462-2.72 0-1.01-.77-1.853-1.501-2.677-.361-.414-1.024-1.165-1.024-1.413.112-.244 1.029-.654 1.516-.873 1.024-.453 2.082-.926 2.515-1.838.434-.911.127-1.95-.165-3.022-.137-.488-.424-1.516-.283-1.74.03-.03.214-.176 1.219-.176h1.262a3.705 3.705 0 0 0 2.35-.575c.8-.639 1.009-1.76 1.219-2.847.097-.522.282-1.492.487-1.638h.063a5.173 5.173 0 0 1 1.633.629 5.966 5.966 0 0 0 2.487.834c.164 0 .327-.018.487-.054.975-.23 1.667-1.14 2.325-2.023.308-.41.946-1.268 1.21-1.292.262-.024.916.863 1.228 1.263.687.887 1.409 1.784 2.383 1.993.148.03.298.045.449.044.834 0 1.682-.443 2.506-.877a5.153 5.153 0 0 1 1.638-.663h.053c.21.156.41 1.106.517 1.613.229 1.088.487 2.214 1.287 2.842.656.42 1.432.613 2.208.551.293 0 .595 0 .897-.024.302-.024.546 0 .804 0 .317-.036.638.004.936.117.137.224-.121 1.243-.248 1.73-.273 1.058-.556 2.15-.108 3.042.449.892 1.517 1.346 2.54 1.78.488.21 1.429.604 1.54.833 0 .268-.638 1.029-.974 1.433-.717.844-1.463 1.712-1.463 2.72 0 1.01.77 1.853 1.502 2.677.36.404 1.028 1.155 1.023 1.404-.112.244-1.028.653-1.516.872-1.023.454-2.081.927-2.515 1.838-.434.912-.127 1.974.166 3.023.136.487.424 1.52.282 1.74-.029.029-.214.175-1.218.175h-1.263a3.71 3.71 0 0 0-2.35.575c-.799.64-1.009 1.76-1.213 2.847-.098.522-.283 1.492-.488 1.638h-.063a5.17 5.17 0 0 1-1.633-.633 5.967 5.967 0 0 0-2.486-.834 2.19 2.19 0 0 0-.497.024Z"
    />
    <path
      fill="currentColor"
      d="M34.678 36.465h-1.404c0 .083-.15.804-.224 1.116l4.022 8.282-3.944-1.462a.976.976 0 0 0-1.277.62l-1.121 3.558-3.413-7.313a21.002 21.002 0 0 1-2.106-.92l-.297.394 5.133 10.905a.975.975 0 0 0 .863.556h.063a.975.975 0 0 0 .863-.678l1.56-4.953 5.275 2.009a.975.975 0 0 0 1.223-1.336l-5.216-10.778ZM16.209 40.433c-.098.068-1.073.556-1.492.736l-3.48 7.385-1.122-3.558a.974.974 0 0 0-1.272-.595l-3.944 1.462 3.593-7.41c-.176-.535-.42-1.696-.444-1.798l-.868.03-5.108 10.553a.975.975 0 0 0 1.223 1.336l5.275-2.008 1.56 4.952a.974.974 0 0 0 .867.683h.064a.975.975 0 0 0 .882-.56l4.875-10.408c-.142-.2-.546-.732-.61-.8ZM20.75 26.762c-.821 0-1.561-.142-2.221-.428a4.193 4.193 0 0 1-1.62-1.223c-.421-.535-.688-1.169-.803-1.9l1.948-.297c.166.665.506 1.179 1.02 1.542.52.364 1.117.546 1.792.546.4 0 .777-.063 1.13-.187.353-.125.639-.306.857-.545.223-.24.335-.533.335-.88 0-.157-.026-.3-.078-.43a1.021 1.021 0 0 0-.234-.358 1.447 1.447 0 0 0-.39-.28 2.648 2.648 0 0 0-.545-.226l-2.898-.857a8.19 8.19 0 0 1-.81-.288 3.49 3.49 0 0 1-.818-.507 2.604 2.604 0 0 1-.631-.825c-.161-.338-.241-.753-.241-1.247 0-.722.181-1.327.545-1.815a3.327 3.327 0 0 1 1.48-1.106c.623-.244 1.314-.364 2.072-.359.769.005 1.454.138 2.057.398a3.906 3.906 0 0 1 1.511 1.114c.405.488.691 1.077.857 1.768l-2.018.35a2.191 2.191 0 0 0-.483-1.004 2.254 2.254 0 0 0-.864-.64 2.911 2.911 0 0 0-2.127-.061c-.317.114-.574.28-.771.498a1.119 1.119 0 0 0-.288.764.95.95 0 0 0 .249.677c.166.172.371.31.615.413.25.099.501.182.756.25l2.01.56c.275.073.584.172.927.297.343.124.672.298.99.521.316.224.576.517.778.88.208.364.312.827.312 1.388 0 .581-.122 1.093-.366 1.534-.24.437-.564.8-.974 1.091-.41.29-.88.509-1.41.654a6.155 6.155 0 0 1-1.652.218Z"
    />
  </svg>
);
export default STypeCertificationSvg;
