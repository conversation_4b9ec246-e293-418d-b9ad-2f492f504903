/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const BookSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 32}
    height={props.height || 26}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 32 26"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M31.422 3.05c-.345-.213-8.364-5.023-15.366-.313C9.05-1.97 1.036 2.837.69 3.05a.843.843 0 0 0-.405.72v20.71a.845.845 0 0 0 1.288.719c.077-.047 7.79-4.655 13.979-.043.004 0 .006.004.01.007a.76.76 0 0 0 .083.053l.008.005a.297.297 0 0 1 .01.006l.008.006h.004a.705.705 0 0 0 .176.062l.011.003a.394.394 0 0 1 .022.005.77.77 0 0 0 .345 0c.01 0 .023-.004.034-.008l.008-.003c.05-.013.1-.03.15-.053.003 0 .01 0 .016-.006h.007a.07.07 0 0 0 .02-.012l.006-.005a.76.76 0 0 0 .083-.053l.007-.004a.03.03 0 0 1 .003-.003c6.172-4.599 13.858-.03 13.979.043a.847.847 0 0 0 1.288-.72l.001-20.71a.842.842 0 0 0-.405-.72h-.004ZM1.979 23.08V4.268c1.636-.856 7.925-3.702 13.234-.064V22.92c-5.206-2.922-10.771-.99-13.234.16Zm28.158 0c-1.505-.704-4.17-1.7-7.202-1.7-1.929 0-4.008.402-6.03 1.536V4.206c5.309-3.64 11.598-.794 13.234.063v18.812l-.002-.001ZM10.689 10.202l-2.904 3.364-1.332-1.183a.827.827 0 0 0-1.098 1.238l1.959 1.738a.831.831 0 0 0 .604.207h.002a.816.816 0 0 0 .567-.282l3.452-3.998a.826.826 0 0 0-.085-1.166l-.002-.001a.822.822 0 0 0-1.163.083Zm14.015.173a.838.838 0 0 1 1.184 1.184l-1.184 1.184 1.185 1.183a.838.838 0 0 1-1.185 1.184l-1.183-1.184-1.183 1.184a.837.837 0 0 1-1.185-1.184l1.184-1.183-1.184-1.184a.838.838 0 0 1 1.184-1.184l1.184 1.184 1.183-1.184Z"
      clipRule="evenodd"
    />
  </svg>
);
export default BookSvg;
