/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const ChatSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 28}
    height={props.height || 28}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 28 28"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M3.692 13.525c0-6.087 4.842-11.021 10.815-11.021 5.974 0 10.816 4.934 10.816 11.021 0 6.087-4.842 11.022-10.816 11.022-1.628 0-3.17-.366-4.553-1.021a.966.966 0 0 0-.737-.038L3.67 25.454c-.522.185-1.02-.33-.832-.859l1.96-5.51a1.02 1.02 0 0 0-.04-.777 11.147 11.147 0 0 1-1.067-4.783ZM14.507.5C7.447.5 1.725 6.332 1.725 13.525c0 1.872.388 3.653 1.087 5.264L.99 23.913c-.752 2.115 1.242 4.173 3.327 3.433l5.178-1.835c1.54.67 3.235 1.04 5.012 1.04 7.06 0 12.782-5.832 12.782-13.026C27.29 6.332 21.567.5 14.507.5ZM9.512 12.683c-.768.29-.768 1.396 0 1.686a7.124 7.124 0 0 1 4.169 4.248.876.876 0 0 0 1.654 0 7.124 7.124 0 0 1 4.168-4.248c.768-.29.768-1.396 0-1.686a7.124 7.124 0 0 1-4.168-4.248.876.876 0 0 0-1.654 0 7.125 7.125 0 0 1-4.17 4.248Z"
      clipRule="evenodd"
    />
  </svg>
);
export default ChatSvg;
