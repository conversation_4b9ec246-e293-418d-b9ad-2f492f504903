/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const QTypeLicenseSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 43}
    height={props.height || 46}
    viewBox="0 0 43 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.9579 3.99336L36.0266 3.99336C39.3454 3.99336 42.0468 6.6947 42.0452 10.0135V39.9799C42.0452 43.2987 39.3439 46 36.0251 46H6.84289C3.5241 46 0.822754 43.2987 0.822754 39.9799L0.822754 10.0135C0.822754 6.6947 3.5241 3.99336 6.84289 3.99336H9.91316C10.157 1.75356 12.0372 0 14.3403 0L28.5308 0C30.8339 0 32.714 1.7551 32.9579 3.99336ZM14.3387 2.77852C13.4033 2.77852 12.6407 3.54107 12.6407 4.47651V6.28872C12.6407 7.22416 13.4033 7.98671 14.3387 7.98671H28.5293C29.4647 7.98671 30.2272 7.22416 30.2272 6.28872V4.47651C30.2272 3.54107 29.4647 2.77852 28.5293 2.77852L14.3387 2.77852ZM36.0266 43.2215C37.8142 43.2215 39.2683 41.7674 39.2683 39.9799L39.2667 10.0135C39.2667 8.22597 37.8126 6.77188 36.0251 6.77188H32.9564C32.7125 9.01168 30.8324 10.7652 28.5293 10.7652L14.3387 10.7652C12.0356 10.7652 10.1555 9.01013 9.91161 6.77188H6.84289C5.05537 6.77188 3.60128 8.22597 3.60128 10.0135L3.60128 39.9799C3.60128 41.7674 5.05537 43.2215 6.84289 43.2215H36.0266ZM25.1367 32.2454L26.2021 33.3055L27.5739 31.9153L26.5214 30.8627C26.7073 30.5978 26.874 30.3133 27.0215 30.0094C27.5309 28.9721 27.7857 27.769 27.7857 26.4003C27.7857 25.0315 27.5309 23.8285 27.0215 22.7911C26.5182 21.7538 25.7908 20.9467 24.8394 20.3697C23.8942 19.7866 22.7587 19.4951 21.4329 19.4951C20.1071 19.4951 18.9715 19.7866 18.0263 20.3697C17.081 20.9467 16.3537 21.7538 15.8442 22.7911C15.3409 23.8285 15.0893 25.0315 15.0893 26.4003C15.0893 27.769 15.3409 28.9721 15.8442 30.0094C16.3537 31.0467 17.081 31.8569 18.0263 32.4401C18.9715 33.017 20.1071 33.3055 21.4329 33.3055C22.7587 33.3055 23.8942 33.017 24.8394 32.4401C24.9411 32.3777 25.0402 32.3129 25.1367 32.2454ZM24.8675 29.2089L23.8083 28.1496L22.418 29.5399L23.566 30.6822C22.9913 31.0436 22.2802 31.2214 21.4329 31.2155C20.5429 31.2032 19.8002 30.9976 19.2048 30.5987C18.6155 30.1997 18.1736 29.6442 17.879 28.9322C17.5844 28.2141 17.437 27.3701 17.437 26.4003C17.437 25.4305 17.5844 24.5834 17.879 23.8592C18.1797 23.1349 18.6247 22.5732 19.214 22.1743C19.8032 21.7753 20.5429 21.5789 21.4329 21.585C22.3229 21.5973 23.0625 21.8029 23.6517 22.2019C24.2471 22.6009 24.6921 23.1594 24.9868 23.8776C25.2875 24.5896 25.4379 25.4305 25.4379 26.4003C25.4379 27.3701 25.2875 28.2171 24.9868 28.9414C24.9494 29.0332 24.9097 29.1223 24.8675 29.2089Z"
      fill="currentColor"
    />
  </svg>
);
export default QTypeLicenseSvg;
