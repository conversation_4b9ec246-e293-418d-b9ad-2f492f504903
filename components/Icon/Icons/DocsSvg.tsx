/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const DocsSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 21}
    height={props.height || 25}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 21 25"
  >
    <path
      fill="currentColor"
      d="m18.128 24.56-15.133-.012c-.965 0-1.534-.407-1.841-.749-.685-.762-.657-1.806-.637-2.071V3.038c-.003-.893.358-1.445.662-1.75C1.852.613 2.774.56 3.041.56h15.087a2.481 2.481 0 0 1 2.479 2.479V22.08a2.482 2.482 0 0 1-2.48 2.479ZM3.058 2.188c-.178 0-.53.052-.726.248-.047.047-.189.19-.187.6v18.727a.826.826 0 0 1-.005.086c-.012.141.001.619.227.865.047.051.188.206.628.206l15.133.012c.47 0 .851-.382.851-.851V3.038a.852.852 0 0 0-.851-.85H3.058Z"
    />
    <path
      fill="currentColor"
      d="M16.146 8.388H4.971a.814.814 0 1 1 0-1.628h11.175a.814.814 0 0 1 0 1.628ZM16.146 13.916H4.971a.814.814 0 1 1 0-1.627h11.175a.814.814 0 0 1 0 1.627ZM16.146 19.446H4.971a.814.814 0 1 1 0-1.627h11.175a.814.814 0 0 1 0 1.627Z"
    />
  </svg>
);
export default DocsSvg;
