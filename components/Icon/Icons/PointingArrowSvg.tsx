/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const PointingArrowSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 50}
    height={props.height || 29}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 50 29"
  >
    <path
      fill="currentColor"
      stroke="currentColor"
      d="m44.967 1.898 3.394 8.76c.089.43-.16.853-.554.943-.395.09-.787-.184-.876-.615L44 4.451c-7.34 15.14-18.381 20.695-26.424 22.675-8.867 2.182-15.898.654-16.195.588-.396-.089-.656-.51-.58-.94a.778.778 0 0 1 .357-.538.669.669 0 0 1 .5-.083c.07.015 7.064 1.529 15.633-.596C28.37 22.81 36.85 15.56 42.53 4.02L35.24 6.066c-.352.213-.799.075-1-.31-.2-.386-.079-.87.273-1.083l9.378-3.309a.677.677 0 0 1 .658-.027.8.8 0 0 1 .418.561Z"
    />
  </svg>
);
export default PointingArrowSvg;
