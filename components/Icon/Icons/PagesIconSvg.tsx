/* eslint-disable react/destructuring-assignment */
import * as React from 'react';

const PagesIconSvg = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 31}
    height={props.height || 28}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 31 28"
  >
    <path
      fill="url(#paint0_linear_3185_13671)"
      fillRule="evenodd"
      d="M21.577 2.66H9.673c-2.796 0-5.048 2.354-5.048 5.278v12.444c0 2.924 2.252 5.278 5.048 5.278h11.904c2.473 0 4.52-1.841 4.96-4.29H9.42c-.15 0-.271-.138-.271-.311s.12-.313.27-.313h17.193l-.005.097q.02-.228.019-.46V7.937c0-2.924-2.252-5.278-5.048-5.278m4.997 18.482-.013.083-.003.02zm-15.547-1.56c-.018.018-.323.235-.68.484-.355.248-.674.456-.708.463-.115.02-.2-.088-.158-.203.045-.123.886-1.426.92-1.426.02 0 .176.146.347.325.27.284.307.33.279.357m2.27-1.807c-.811.76-1.154 1.046-1.73 1.448-.17.119-.325.216-.344.216-.041 0-.711-.695-.711-.738 0-.058.41-.685.721-1.105.256-.344.611-.77 1.33-1.593 1.073-1.229 3.2-3.55 3.255-3.55.043 0 1.376 1.394 1.376 1.44 0 .07-2.705 2.765-3.897 3.882m5.422-3.795c0 .078-.013.109-.073.172-.055.057-.092.076-.145.076-.241-.002-.406-.297-.342-.613.034-.167.163-.434.307-.633.062-.087.49-.562.952-1.056 1.383-1.481 2.011-2.178 2.365-2.622.185-.232.206-.27.197-.35-.005-.05-.016-.06-.06-.055-.06.007-.064.012-.696.725-.733.827-1.33 1.471-2.61 2.813-.68.713-1.251 1.297-1.27 1.297-.04 0-1.375-1.392-1.375-1.434 0-.09 2.796-2.972 4.19-4.318.632-.61 1.165-1.094 1.294-1.174.402-.25.834-.224 1.09.066.289.328.274.78-.042 1.267-.086.133-.101.224-.054.319.***************.126.411.001.2-.077.385-.257.608-.34.424-1.082 1.254-2.317 2.59-.925 1.002-1.057 1.15-1.185 1.333-.121.172-.154.28-.12.396.**************.025.182"
      clipRule="evenodd"
    />
    <defs>
      <linearGradient
        id="paint0_linear_3185_13671"
        x1="15.615"
        x2="15.638"
        y1="25.569"
        y2="2.94"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF8500" />
        <stop offset="1" stopColor="#FFB900" />
      </linearGradient>
    </defs>
  </svg>
);
export default PagesIconSvg;
