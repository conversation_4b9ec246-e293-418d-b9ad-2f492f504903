/* eslint-disable react/destructuring-assignment */
import * as React from 'react';

const SettingsSvg = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 28}
    height={props.height || 31}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 28 31"
  >
    <path
      fill="currentColor"
      stroke="currentColor"
      strokeWidth={0.8}
      d="M9.128 11.604V9.197a.422.422 0 1 0-.844 0v2.407a1.772 1.772 0 0 0 0 3.444v7.305a.422.422 0 0 0 .844 0v-7.305c.385-.094.728-.315.973-.627.245-.313.378-1.095.378-1.095a1.772 1.772 0 0 0-1.351-1.722Zm11.479 3.007a1.778 1.778 0 0 0-1.354-1.725v-3.68a.422.422 0 0 0-.844 0v3.68a1.773 1.773 0 0 0 0 3.447v6.02a.422.422 0 0 0 .844 0v-6.02a1.776 1.776 0 0 0 1.354-1.722Zm-6.417 2.077v-7.47a.422.422 0 1 0-.844 0v7.47a1.773 1.773 0 0 0 0 3.444v2.221a.422.422 0 1 0 .844 0v-2.22a1.773 1.773 0 0 0 0-3.445Z"
    />
    <path
      stroke="currentColor"
      strokeWidth={1.8}
      d="M11.588 2.266a4.334 4.334 0 0 1 4.108-.14l.258.14h.003l8.429 4.846v.001a4.39 4.39 0 0 1 2.187 3.807v9.75a4.39 4.39 0 0 1-1.94 3.651l-.247.156-8.429 4.846-.007.004c-1.17.685-2.882.728-4.119.128l-.24-.128-.007-.004-8.432-4.848A4.388 4.388 0 0 1 .97 20.67v-9.75l.009-.294a4.387 4.387 0 0 1 2.174-3.51l8.432-4.85h.004Z"
    />
  </svg>
);
export default SettingsSvg;
