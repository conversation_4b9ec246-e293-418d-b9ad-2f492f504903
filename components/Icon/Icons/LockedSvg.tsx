/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const LockedSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 16}
    height={props.height || 20}
    viewBox="0 0 16 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.6815 10.0002C15.6815 8.95361 14.8304 8.10249 13.7838 8.10249H12.835V5.25595C12.835 2.63998 10.7067 0.511719 8.09077 0.511719C5.4748 0.511719 3.34654 2.63998 3.34654 5.25595V8.10249H2.39769C1.35112 8.10249 0.5 8.95361 0.5 10.0002V17.591C0.5 18.6375 1.35112 19.4886 2.39769 19.4886H13.7838C14.8304 19.4886 15.6815 18.6375 15.6815 17.591V10.0002ZM5.24423 5.25595C5.24423 3.68656 6.52138 2.40941 8.09077 2.40941C9.66016 2.40941 10.9373 3.68656 10.9373 5.25595V8.10249H5.24423V5.25595Z"
      fill="currentColor"
    />
  </svg>
);
export default LockedSvg;
