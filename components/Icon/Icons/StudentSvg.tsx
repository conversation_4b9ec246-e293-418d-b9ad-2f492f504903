/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const StudentSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 30}
    height={props.height || 31}
    fill="none"
    preserveAspectRatio="none"
    viewBox="-.133 -0.725 30 31"
  >
    <g opacity={0.9}>
      <mask
        id="a"
        width={30}
        height={32}
        x={-0.133}
        y={-0.725}
        fill="currentColor"
        maskUnits="userSpaceOnUse"
      >
        <path fill="#fff" d="M-.133-.725h30v32h-30z" />
        <path
          fillRule="evenodd"
          d="m15.56.333 13.668 6.583.001-.001a.736.736 0 0 1 .444.72.737.737 0 0 1-.532.66l-3.374.962v1.87c.575.275.977.859.977 1.538v.977c0 .407-.33.735-.735.735h-1.953a.735.735 0 0 1-.735-.735v-.976c0-.679.4-1.261.976-1.538V9.676l-2.434.69h-.001v3.373a6.545 6.545 0 0 1-1.933 4.66 6.552 6.552 0 0 1-4.66 1.933 6.6 6.6 0 0 1-6.591-6.592v-3.373h-.002L1.4 8.297a.737.737 0 0 1-.531-.66.73.73 0 0 1 .443-.72L14.98.332a.75.75 0 0 1 .58 0ZM24.79 12.91h.484v-.242a.243.243 0 0 0-.242-.242.243.243 0 0 0-.242.242v.242ZM20 11.326c.***************.236.057h-.003l.**************.069.017v-4.32c-.284-.11-.878-.247-1.758-.354a28.206 28.206 0 0 0-3.366-.182c-1.379 0-2.508.077-3.365.182-.88.106-1.475.243-1.758.354v4.32l.068-.018.035-.009.053-.013a6.16 6.16 0 0 1 .236-.057l.006-.002.005-.001c.078-.018.16-.034.243-.05l.04-.008.041-.008.2-.036.044-.007.05-.008a10.977 10.977 0 0 1 .308-.044 14.296 14.296 0 0 1 .689-.08l.122-.01a172.007 172.007 0 0 1 .402-.033l.239-.016.101-.006.078-.005c.144-.009.293-.016.445-.022l.129-.005c.115-.004.231-.009.352-.012l.168-.004a27.355 27.355 0 0 1 .443-.01h.083a63.255 63.255 0 0 1 1.09 0h.082l.082.002.176.004.186.004.167.004c.12.003.238.008.353.012l.128.005.445.022.078.005.34.022.07.005.063.005.27.023h.007l.115.01.287.03.035.004a14.238 14.238 0 0 1 .675.09l.093.015a26.001 26.001 0 0 1 .247.045l.034.006c.085.017.166.033.243.05l.011.004Zm-4.73 7.537a5.089 5.089 0 0 0 3.622-1.502h-.01a5.089 5.089 0 0 0 1.502-3.621v-.778l-.015-.006a.3.3 0 0 0-.045-.017l-.02-.007a1.065 1.065 0 0 0-.062-.021l-.057-.018-.032-.01-.066-.018-.048-.012-.024-.006a3.271 3.271 0 0 1-.115-.029 3.182 3.182 0 0 0-.136-.03 3.047 3.047 0 0 0-.173-.035 14.495 14.495 0 0 0-.42-.073 5.724 5.724 0 0 0-.436-.063 34.5 34.5 0 0 0-.19-.023l-.15-.018a8.626 8.626 0 0 0-.278-.027l-.089-.008a10.336 10.336 0 0 0-.396-.033l-.13-.01-.11-.007a7.25 7.25 0 0 0-.092-.006l-.087-.006-.262-.013-.188-.01-.27-.01h-.016a7.665 7.665 0 0 0-.355-.01l-.162-.005c-.053 0-.104 0-.157-.002h-.012a29.784 29.784 0 0 0-1.049 0c-.056 0-.107 0-.16.002h-.009l-.328.008-.098.004-.1.003-.277.01-.126.007a6.19 6.19 0 0 1-.063.003l-.261.013-.087.006-.092.006a10.837 10.837 0 0 0-.356.026l-.057.004c-.076.006-.152.012-.225.02a5.75 5.75 0 0 0-.254.024 8.643 8.643 0 0 0-.112.011l-.15.018-.19.023a78.568 78.568 0 0 0-.214.029l-.099.014a3.558 3.558 0 0 1-.108.017l-.016.003a18.618 18.618 0 0 0-.42.073 1.57 1.57 0 0 1-.094.02c-.027.004-.053.01-.08.016a2.85 2.85 0 0 1-.053.011l-.082.018a2.417 2.417 0 0 0-.139.035 2.512 2.512 0 0 0-.114.03l-.032.01-.028.009a1.088 1.088 0 0 0-.111.037.408.408 0 0 1-.045.017l-.015.006v.778a5.13 5.13 0 0 0 5.124 5.123ZM24.3 8.15l1.463-.415h.002l.987-.282-11.48-5.644-11.48 5.644 4.887 1.39V6.81c0-.184 0-.535.552-.871.71-.432 2.332-.84 6.041-.84 3.71 0 5.331.408 6.041.84.553.336.553.686.553.87v2.035l2.433-.693ZM15.27 21.056c-5.199 0-9.426 3.395-9.426 7.567v.917c0 .405.409.735.915.735h17.026c.504 0 .915-.329.915-.735v-.916c0-4.174-4.23-7.568-9.426-7.568h-.004Zm-7.977 7.567c0-3.09 3.265-6.117 7.977-6.117h.004c4.71 0 7.977 3.026 7.977 6.117v.203H7.293v-.203Z"
          clipRule="evenodd"
        />
      </mask>
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="m15.56.333 13.668 6.583.001-.001a.736.736 0 0 1 .444.72.737.737 0 0 1-.532.66l-3.374.962v1.87c.575.275.977.859.977 1.538v.977c0 .407-.33.735-.735.735h-1.953a.735.735 0 0 1-.735-.735v-.976c0-.679.4-1.261.976-1.538V9.676l-2.434.69h-.001v3.373a6.545 6.545 0 0 1-1.933 4.66 6.552 6.552 0 0 1-4.66 1.933 6.6 6.6 0 0 1-6.591-6.592v-3.373h-.002L1.4 8.297a.737.737 0 0 1-.531-.66.73.73 0 0 1 .443-.72L14.98.332a.75.75 0 0 1 .58 0ZM24.79 12.91h.484v-.242a.243.243 0 0 0-.242-.242.243.243 0 0 0-.242.242v.242ZM20 11.326c.***************.236.057h-.003l.**************.069.017v-4.32c-.284-.11-.878-.247-1.758-.354a28.206 28.206 0 0 0-3.366-.182c-1.379 0-2.508.077-3.365.182-.88.106-1.475.243-1.758.354v4.32l.068-.018.035-.009.053-.013a6.16 6.16 0 0 1 .236-.057l.006-.002.005-.001c.078-.018.16-.034.243-.05l.04-.008.041-.008.2-.036.044-.007.05-.008a10.977 10.977 0 0 1 .308-.044 14.296 14.296 0 0 1 .689-.08l.122-.01a172.007 172.007 0 0 1 .402-.033l.239-.016.101-.006.078-.005c.144-.009.293-.016.445-.022l.129-.005c.115-.004.231-.009.352-.012l.168-.004a27.355 27.355 0 0 1 .443-.01h.083a63.255 63.255 0 0 1 1.09 0h.082l.082.002.176.004.186.004.167.004c.12.003.238.008.353.012l.128.005.445.022.078.005.34.022.07.005.063.005.27.023h.007l.115.01.287.03.035.004a14.238 14.238 0 0 1 .675.09l.093.015a26.001 26.001 0 0 1 .247.045l.034.006c.085.017.166.033.243.05l.011.004Zm-4.73 7.537a5.089 5.089 0 0 0 3.622-1.502h-.01a5.089 5.089 0 0 0 1.502-3.621v-.778l-.015-.006a.3.3 0 0 0-.045-.017l-.02-.007a1.065 1.065 0 0 0-.062-.021l-.057-.018-.032-.01-.066-.018-.048-.012-.024-.006a3.271 3.271 0 0 1-.115-.029 3.182 3.182 0 0 0-.136-.03 3.047 3.047 0 0 0-.173-.035 14.495 14.495 0 0 0-.42-.073 5.724 5.724 0 0 0-.436-.063 34.5 34.5 0 0 0-.19-.023l-.15-.018a8.626 8.626 0 0 0-.278-.027l-.089-.008a10.336 10.336 0 0 0-.396-.033l-.13-.01-.11-.007a7.25 7.25 0 0 0-.092-.006l-.087-.006-.262-.013-.188-.01-.27-.01h-.016a7.665 7.665 0 0 0-.355-.01l-.162-.005c-.053 0-.104 0-.157-.002h-.012a29.784 29.784 0 0 0-1.049 0c-.056 0-.107 0-.16.002h-.009l-.328.008-.098.004-.1.003-.277.01-.126.007a6.19 6.19 0 0 1-.063.003l-.261.013-.087.006-.092.006a10.837 10.837 0 0 0-.356.026l-.057.004c-.076.006-.152.012-.225.02a5.75 5.75 0 0 0-.254.024 8.643 8.643 0 0 0-.112.011l-.15.018-.19.023a78.568 78.568 0 0 0-.214.029l-.099.014a3.558 3.558 0 0 1-.108.017l-.016.003a18.618 18.618 0 0 0-.42.073 1.57 1.57 0 0 1-.094.02c-.027.004-.053.01-.08.016a2.85 2.85 0 0 1-.053.011l-.082.018a2.417 2.417 0 0 0-.139.035 2.512 2.512 0 0 0-.114.03l-.032.01-.028.009a1.088 1.088 0 0 0-.111.037.408.408 0 0 1-.045.017l-.015.006v.778a5.13 5.13 0 0 0 5.124 5.123ZM24.3 8.15l1.463-.415h.002l.987-.282-11.48-5.644-11.48 5.644 4.887 1.39V6.81c0-.184 0-.535.552-.871.71-.432 2.332-.84 6.041-.84 3.71 0 5.331.408 6.041.84.553.336.553.686.553.87v2.035l2.433-.693ZM15.27 21.056c-5.199 0-9.426 3.395-9.426 7.567v.917c0 .405.409.735.915.735h17.026c.504 0 .915-.329.915-.735v-.916c0-4.174-4.23-7.568-9.426-7.568h-.004Zm-7.977 7.567c0-3.09 3.265-6.117 7.977-6.117h.004c4.71 0 7.977 3.026 7.977 6.117v.203H7.293v-.203Z"
        clipRule="evenodd"
      />
      <path
        stroke="currentColor"
        strokeWidth={0.2}
        d="m15.56.333 13.668 6.583.001-.001a.736.736 0 0 1 .444.72.737.737 0 0 1-.532.66l-3.374.962v1.87c.575.275.977.859.977 1.538v.977c0 .407-.33.735-.735.735h-1.953a.735.735 0 0 1-.735-.735v-.976c0-.679.4-1.261.976-1.538V9.676l-2.434.69h-.001v3.373a6.545 6.545 0 0 1-1.933 4.66 6.552 6.552 0 0 1-4.66 1.933 6.6 6.6 0 0 1-6.591-6.592v-3.373h-.002L1.4 8.297a.737.737 0 0 1-.531-.66.73.73 0 0 1 .443-.72L14.98.332a.75.75 0 0 1 .58 0ZM24.79 12.91h.484v-.242a.243.243 0 0 0-.242-.242.243.243 0 0 0-.242.242v.242ZM20 11.326c.***************.236.057h-.003l.**************.069.017v-4.32c-.284-.11-.878-.247-1.758-.354a28.206 28.206 0 0 0-3.366-.182c-1.379 0-2.508.077-3.365.182-.88.106-1.475.243-1.758.354v4.32l.068-.018.035-.009.053-.013a6.16 6.16 0 0 1 .236-.057l.006-.002.005-.001c.078-.018.16-.034.243-.05l.04-.008.041-.008.2-.036.044-.007.05-.008a10.977 10.977 0 0 1 .308-.044 14.296 14.296 0 0 1 .689-.08l.122-.01a172.007 172.007 0 0 1 .402-.033l.239-.016.101-.006.078-.005c.144-.009.293-.016.445-.022l.129-.005c.115-.004.231-.009.352-.012l.168-.004a27.355 27.355 0 0 1 .443-.01h.083a63.255 63.255 0 0 1 1.09 0h.082l.082.002.176.004.186.004.167.004c.12.003.238.008.353.012l.128.005.445.022.078.005.34.022.07.005.063.005.27.023h.007l.115.01.287.03.035.004a14.238 14.238 0 0 1 .675.09l.093.015a26.001 26.001 0 0 1 .247.045l.034.006c.085.017.166.033.243.05l.011.004Zm-4.73 7.537a5.089 5.089 0 0 0 3.622-1.502h-.01a5.089 5.089 0 0 0 1.502-3.621v-.778l-.015-.006a.3.3 0 0 0-.045-.017l-.02-.007a1.065 1.065 0 0 0-.062-.021l-.057-.018-.032-.01-.066-.018-.048-.012-.024-.006a3.271 3.271 0 0 1-.115-.029 3.182 3.182 0 0 0-.136-.03 3.047 3.047 0 0 0-.173-.035 14.495 14.495 0 0 0-.42-.073 5.724 5.724 0 0 0-.436-.063 34.5 34.5 0 0 0-.19-.023l-.15-.018a8.626 8.626 0 0 0-.278-.027l-.089-.008a10.336 10.336 0 0 0-.396-.033l-.13-.01-.11-.007a7.25 7.25 0 0 0-.092-.006l-.087-.006-.262-.013-.188-.01-.27-.01h-.016a7.665 7.665 0 0 0-.355-.01l-.162-.005c-.053 0-.104 0-.157-.002h-.012a29.784 29.784 0 0 0-1.049 0c-.056 0-.107 0-.16.002h-.009l-.328.008-.098.004-.1.003-.277.01-.126.007a6.19 6.19 0 0 1-.063.003l-.261.013-.087.006-.092.006a10.837 10.837 0 0 0-.356.026l-.057.004c-.076.006-.152.012-.225.02a5.75 5.75 0 0 0-.254.024 8.643 8.643 0 0 0-.112.011l-.15.018-.19.023a78.568 78.568 0 0 0-.214.029l-.099.014a3.558 3.558 0 0 1-.108.017l-.016.003a18.618 18.618 0 0 0-.42.073 1.57 1.57 0 0 1-.094.02c-.027.004-.053.01-.08.016a2.85 2.85 0 0 1-.053.011l-.082.018a2.417 2.417 0 0 0-.139.035 2.512 2.512 0 0 0-.114.03l-.032.01-.028.009a1.088 1.088 0 0 0-.111.037.408.408 0 0 1-.045.017l-.015.006v.778a5.13 5.13 0 0 0 5.124 5.123ZM24.3 8.15l1.463-.415h.002l.987-.282-11.48-5.644-11.48 5.644 4.887 1.39V6.81c0-.184 0-.535.552-.871.71-.432 2.332-.84 6.041-.84 3.71 0 5.331.408 6.041.84.553.336.553.686.553.87v2.035l2.433-.693ZM15.27 21.056c-5.199 0-9.426 3.395-9.426 7.567v.917c0 .405.409.735.915.735h17.026c.504 0 .915-.329.915-.735v-.916c0-4.174-4.23-7.568-9.426-7.568h-.004Zm-7.977 7.567c0-3.09 3.265-6.117 7.977-6.117h.004c4.71 0 7.977 3.026 7.977 6.117v.203H7.293v-.203Z"
        clipRule="evenodd"
        mask="url(#a)"
      />
    </g>
  </svg>
);
export default StudentSvg;
