/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const DTypeLicenseSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 43}
    height={props.height || 46}
    viewBox="0 0 43 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.9579 3.99336L36.0266 3.99336C39.3454 3.99336 42.0468 6.6947 42.0452 10.0135V39.9799C42.0452 43.2987 39.3439 46 36.0251 46H6.84289C3.5241 46 0.822754 43.2987 0.822754 39.9799L0.822754 10.0135C0.822754 6.6947 3.5241 3.99336 6.84289 3.99336H9.91316C10.157 1.75356 12.0372 0 14.3403 0L28.5308 0C30.8339 0 32.714 1.7551 32.9579 3.99336ZM14.3387 2.77852C13.4033 2.77852 12.6407 3.54107 12.6407 4.47651V6.28872C12.6407 7.22416 13.4033 7.98671 14.3387 7.98671H28.5293C29.4647 7.98671 30.2272 7.22416 30.2272 6.28872V4.47651C30.2272 3.54107 29.4647 2.77852 28.5293 2.77852L14.3387 2.77852ZM36.0266 43.2215C37.8142 43.2215 39.2683 41.7674 39.2683 39.9799L39.2667 10.0135C39.2667 8.22597 37.8126 6.77188 36.0251 6.77188H32.9564C32.7125 9.01168 30.8324 10.7652 28.5293 10.7652L14.3387 10.7652C12.0356 10.7652 10.1555 9.01013 9.91161 6.77188H6.84289C5.05537 6.77188 3.60128 8.22597 3.60128 10.0135L3.60128 39.9799C3.60128 41.7674 5.05537 43.2215 6.84289 43.2215H36.0266ZM16.0776 19.7713V33.0293H18.2965V27.4315H24.5756V33.0293H26.7853V19.7713H24.5756V25.3507H18.2965V19.7713H16.0776Z"
      fill="white"
    />
  </svg>
);
export default DTypeLicenseSvg;
