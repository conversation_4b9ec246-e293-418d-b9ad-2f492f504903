/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const InProgressSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 32}
    height={props.height || 32}
    viewBox="0 0 33 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <mask
      id="path-1-outside-1_1755_5802"
      maskUnits="userSpaceOnUse"
      x="0.699219"
      y="0.306641"
      width="32"
      height="32"
      fill="black"
    >
      <rect fill="white" x="0.699219" y="0.306641" width="32" height="32" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.57402 3.26208C10.0326 2.35264 11.6587 1.68676 13.3955 1.32134V3.20906C12.1581 3.50827 10.9889 3.98237 9.91569 4.60374L8.57402 3.26208ZM19.5324 1.30664V3.19216C25.3377 4.56174 29.6583 9.77623 29.6583 15.9998C29.6583 23.2673 23.7668 29.1588 16.4993 29.1588C13.6173 29.1588 10.9517 28.2323 8.78414 26.6609L7.46784 27.9772C9.98019 29.8746 13.1084 30.9998 16.4993 30.9998C24.7836 30.9998 31.4993 24.2841 31.4993 15.9998C31.4993 8.75465 26.3626 2.70921 19.5324 1.30664ZM4.77854 21.9876C4.21679 20.8903 3.80464 19.7036 3.56897 18.4547H1.69922C1.98759 20.2064 2.57944 21.8558 3.41897 23.3472L4.77854 21.9876ZM3.86248 12.3178C4.22586 11.0685 4.76947 9.89598 5.46328 8.8302L4.13618 7.5031C3.14285 8.94569 2.39547 10.5708 1.95458 12.3178L3.86248 12.3178ZM11.8105 15.8074L14.5034 18.5003L21.2998 11.7039L22.1974 12.6016L14.5034 20.2956L10.9129 16.705L11.8105 15.8074Z"
      />
    </mask>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.57402 3.26208C10.0326 2.35264 11.6587 1.68676 13.3955 1.32134V3.20906C12.1581 3.50827 10.9889 3.98237 9.91569 4.60374L8.57402 3.26208ZM19.5324 1.30664V3.19216C25.3377 4.56174 29.6583 9.77623 29.6583 15.9998C29.6583 23.2673 23.7668 29.1588 16.4993 29.1588C13.6173 29.1588 10.9517 28.2323 8.78414 26.6609L7.46784 27.9772C9.98019 29.8746 13.1084 30.9998 16.4993 30.9998C24.7836 30.9998 31.4993 24.2841 31.4993 15.9998C31.4993 8.75465 26.3626 2.70921 19.5324 1.30664ZM4.77854 21.9876C4.21679 20.8903 3.80464 19.7036 3.56897 18.4547H1.69922C1.98759 20.2064 2.57944 21.8558 3.41897 23.3472L4.77854 21.9876ZM3.86248 12.3178C4.22586 11.0685 4.76947 9.89598 5.46328 8.8302L4.13618 7.5031C3.14285 8.94569 2.39547 10.5708 1.95458 12.3178L3.86248 12.3178ZM11.8105 15.8074L14.5034 18.5003L21.2998 11.7039L22.1974 12.6016L14.5034 20.2956L10.9129 16.705L11.8105 15.8074Z"
      fill="currentColor"
    />
    <path
      d="M13.3955 1.32134H14.0092V0.565098L13.2692 0.720801L13.3955 1.32134ZM8.57402 3.26208L8.24933 2.74132L7.59399 3.14993L8.14008 3.69602L8.57402 3.26208ZM13.3955 3.20906L13.5398 3.80555L14.0092 3.69204V3.20906H13.3955ZM9.91569 4.60374L9.48174 5.03769L9.81514 5.37108L10.2232 5.13484L9.91569 4.60374ZM19.5324 3.19216H18.9187V3.67792L19.3915 3.78946L19.5324 3.19216ZM19.5324 1.30664L19.6559 0.705496L18.9187 0.554128V1.30664H19.5324ZM8.78414 26.6609L9.14434 26.164L8.72043 25.8567L8.3502 26.227L8.78414 26.6609ZM7.46784 27.9772L7.03389 27.5433L6.53524 28.0419L7.09798 28.4669L7.46784 27.9772ZM3.56897 18.4547L4.17201 18.3409L4.07769 17.841H3.56897V18.4547ZM4.77854 21.9876L5.21248 22.4216L5.52838 22.1057L5.32481 21.708L4.77854 21.9876ZM1.69922 18.4547V17.841H0.976244L1.09368 18.5543L1.69922 18.4547ZM3.41897 23.3472L2.88419 23.6483L3.28096 24.3531L3.85291 23.7812L3.41897 23.3472ZM5.46328 8.8302L5.97759 9.16501L6.24902 8.74805L5.89722 8.39625L5.46328 8.8302ZM3.86248 12.3178L3.86248 12.9315L4.32309 12.9315L4.45174 12.4892L3.86248 12.3178ZM4.13618 7.5031L4.57013 7.06916L4.04883 6.54786L3.63073 7.15506L4.13618 7.5031ZM1.95458 12.3178L1.35955 12.1676L1.16677 12.9315H1.95458L1.95458 12.3178ZM14.5034 18.5003L14.0695 18.9342L14.5034 19.3682L14.9374 18.9342L14.5034 18.5003ZM11.8105 15.8074L12.2445 15.3735L11.8105 14.9395L11.3766 15.3735L11.8105 15.8074ZM21.2998 11.7039L21.7337 11.27L21.2998 10.8361L20.8659 11.27L21.2998 11.7039ZM22.1974 12.6016L22.6314 13.0355L23.0653 12.6016L22.6314 12.1676L22.1974 12.6016ZM14.5034 20.2956L14.0695 20.7295L14.5034 21.1635L14.9374 20.7295L14.5034 20.2956ZM10.9129 16.705L10.479 16.2711L10.045 16.705L10.479 17.139L10.9129 16.705ZM13.2692 0.720801C11.4607 1.1013 9.76762 1.79466 8.24933 2.74132L8.89872 3.78283C10.2976 2.91062 11.8568 2.27222 13.5219 1.92188L13.2692 0.720801ZM14.0092 3.20906V1.32134H12.7818V3.20906H14.0092ZM10.2232 5.13484C11.2463 4.5425 12.3606 4.09069 13.5398 3.80555L13.2513 2.61256C11.9556 2.92585 10.7316 3.42224 9.6082 4.07264L10.2232 5.13484ZM8.14008 3.69602L9.48174 5.03769L10.3496 4.1698L9.00796 2.82814L8.14008 3.69602ZM20.1461 3.19216V1.30664H18.9187V3.19216H20.1461ZM30.272 15.9998C30.272 9.48519 25.7494 4.02833 19.6733 2.59487L19.3915 3.78946C24.926 5.09516 29.0446 10.0673 29.0446 15.9998H30.272ZM16.4993 29.7725C24.1057 29.7725 30.272 23.6062 30.272 15.9998H29.0446C29.0446 22.9284 23.4279 28.5451 16.4993 28.5451V29.7725ZM8.42395 27.1578C10.6928 28.8025 13.4837 29.7725 16.4993 29.7725V28.5451C13.751 28.5451 11.2107 27.662 9.14434 26.164L8.42395 27.1578ZM7.90178 28.4112L9.21808 27.0948L8.3502 26.227L7.03389 27.5433L7.90178 28.4112ZM16.4993 30.3861C13.2464 30.3861 10.2472 29.3072 7.83769 27.4875L7.09798 28.4669C9.71317 30.442 12.9703 31.6135 16.4993 31.6135V30.3861ZM30.8856 15.9998C30.8856 23.9452 24.4447 30.3861 16.4993 30.3861V31.6135C25.1225 31.6135 32.113 24.623 32.113 15.9998H30.8856ZM19.409 1.90779C25.9595 3.25292 30.8856 9.05179 30.8856 15.9998H32.113C32.113 8.45751 26.7658 2.16549 19.6559 0.705496L19.409 1.90779ZM2.96592 18.5684C3.21266 19.8761 3.6442 21.1185 4.23226 22.2673L5.32481 21.708C4.78939 20.6621 4.39662 19.5312 4.17201 18.3409L2.96592 18.5684ZM1.69922 19.0683H3.56897V17.841H1.69922V19.0683ZM3.95375 23.0462C3.14868 21.616 2.58125 20.0345 2.30476 18.355L1.09368 18.5543C1.39393 20.3782 2.0102 22.0956 2.88419 23.6483L3.95375 23.0462ZM4.34459 21.5537L2.98503 22.9133L3.85291 23.7812L5.21248 22.4216L4.34459 21.5537ZM4.94897 8.49539C4.22283 9.61083 3.6537 10.8383 3.27321 12.1464L4.45174 12.4892C4.79802 11.2987 5.3161 10.1811 5.97759 9.16501L4.94897 8.49539ZM3.70224 7.93704L5.02934 9.26414L5.89722 8.39625L4.57013 7.06916L3.70224 7.93704ZM2.54961 12.4679C2.97229 10.7931 3.68886 9.23483 4.64163 7.85114L3.63073 7.15506C2.59684 8.65655 1.81865 10.3484 1.35955 12.1676L2.54961 12.4679ZM3.86248 11.7041L1.95458 11.7041L1.95458 12.9315L3.86248 12.9315L3.86248 11.7041ZM14.9374 18.0664L12.2445 15.3735L11.3766 16.2413L14.0695 18.9342L14.9374 18.0664ZM20.8659 11.27L14.0695 18.0664L14.9374 18.9342L21.7337 12.1379L20.8659 11.27ZM22.6314 12.1676L21.7337 11.27L20.8659 12.1379L21.7635 13.0355L22.6314 12.1676ZM14.9374 20.7295L22.6314 13.0355L21.7635 12.1676L14.0695 19.8616L14.9374 20.7295ZM10.479 17.139L14.0695 20.7295L14.9374 19.8616L11.3468 16.2711L10.479 17.139ZM11.3766 15.3735L10.479 16.2711L11.3468 17.139L12.2445 16.2413L11.3766 15.3735Z"
      fill="currentColor"
      mask="url(#path-1-outside-1_1755_5802)"
    />
  </svg>
);
export default InProgressSvg;
