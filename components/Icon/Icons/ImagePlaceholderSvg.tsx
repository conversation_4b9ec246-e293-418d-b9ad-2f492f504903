/* eslint-disable react/destructuring-assignment */
import * as React from 'react';

const ImagePlaceholderSvg = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 120}
    height={props.height || 121}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 120 121"
  >
    <circle cx={60} cy={60.159} r={57} stroke="#3A9EA7" strokeWidth={6} />
    <path
      stroke="#3A9EA7"
      strokeWidth={6}
      d="m12.922 92.424 35.482-28.386 42.952 44.446M55.871 70.203l19.235-16.436 34.362 35.295"
    />
    <path
      fill="#3A9EA7"
      d="M43.643 44.76a10.55 10.55 0 1 0-10.55-10.548 10.56 10.56 0 0 0 10.55 10.549Zm0-16.604a6.056 6.056 0 1 1-6.057 6.056 6.063 6.063 0 0 1 6.057-6.057Z"
    />
  </svg>
);
export default ImagePlaceholderSvg;
