/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const DTypeCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 41}
    height={props.height || 53}
    viewBox="0 0 49 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M34.4121 46.2707C34.7883 46.2765 35.1609 46.1957 35.5009 46.0345C36.7799 45.418 37.0795 43.8395 37.3445 42.4511C37.4712 41.7885 37.6786 40.6824 37.9552 40.4635C38.007 40.4232 38.2547 40.2907 39.2975 40.2907H40.0119H40.7839C42.0225 40.2907 43.2727 40.1582 44.0274 39.2191C44.9434 38.0669 44.4998 36.4941 44.108 35.1057C43.9467 34.5295 43.6414 33.4349 43.7681 33.1526C43.8949 32.8704 44.9722 32.3691 45.6059 32.0868C46.9828 31.4762 48.4 30.8424 48.7054 29.4655C49.0107 28.0886 47.9276 26.8385 46.977 25.7611C46.5622 25.2945 45.7845 24.4246 45.7845 24.1019C45.7845 23.7793 46.5392 22.8864 46.9367 22.4082C47.87 21.3136 48.9243 20.0749 48.5902 18.6865C48.256 17.2981 46.7524 16.6759 45.4273 16.1113C44.7936 15.8463 43.7393 15.3969 43.5838 15.0916C43.4282 14.7862 43.7278 13.7031 43.8833 13.1098C44.2405 11.7156 44.6496 10.137 43.7163 9.01937C42.9904 8.20129 41.8381 8.06879 40.8242 8.06879C40.4843 8.06879 40.1328 8.06879 39.7757 8.09759C39.4185 8.1264 39.1247 8.09759 38.8136 8.09759C37.9321 8.09759 37.6613 7.97085 37.6037 7.919C37.3157 7.70008 37.091 6.62851 36.9412 5.91989C36.6531 4.5718 36.3248 3.0163 35.0458 2.44019C34.722 2.29337 34.3701 2.21868 34.0146 2.22126C33.0121 2.22126 31.9924 2.75705 31.0073 3.26978C30.4139 3.64063 29.7579 3.90027 29.0715 4.03601C28.7201 3.96112 28.0518 3.10271 27.6024 2.53236C26.7268 1.39742 25.7416 0.135742 24.3071 0.135742C22.8726 0.135742 21.9105 1.44351 21.0578 2.58997C20.6545 3.13152 19.9747 4.03601 19.6348 4.11667H19.5715C18.8915 3.98034 18.2391 3.73109 17.6415 3.37925C16.7399 2.83489 15.7283 2.49834 14.6803 2.3941C14.3079 2.39008 13.9395 2.47085 13.603 2.6303C12.3182 3.24674 12.0244 4.82528 11.7594 6.21371C11.6327 6.87624 11.4253 7.98237 11.1487 8.20129C11.0969 8.24162 10.8491 8.37413 9.80638 8.37413H9.092H8.32001C7.08138 8.37413 5.83122 8.50663 5.07651 9.44569C4.1605 10.5979 4.6041 12.1707 4.99585 13.5591C5.15717 14.1352 5.4625 15.2298 5.33576 15.5121C5.20902 15.7944 4.14321 16.2668 3.50949 16.5607C2.13258 17.1713 0.715353 17.8051 0.410014 19.182C0.104676 20.5589 1.18776 21.809 2.13835 22.8864C2.55315 23.353 3.3309 24.2229 3.3309 24.5456C3.3309 24.8682 2.57619 25.7611 2.17867 26.2393C1.24537 27.3339 0.191093 28.5726 0.525237 29.961C0.859381 31.3494 2.36303 31.9716 3.68808 32.5304C4.32181 32.7955 5.37609 33.2448 5.53164 33.5502C5.68719 33.8555 5.38761 34.9386 5.23206 35.532C4.87487 36.9262 4.46583 38.5047 5.39913 39.6224C6.11927 40.4635 7.27149 40.596 8.28545 40.596C8.62535 40.596 8.97678 40.596 9.33397 40.596C9.69116 40.596 9.98497 40.596 10.2961 40.596C11.1775 40.596 11.4483 40.7227 11.5059 40.7746C11.794 40.9935 12.0186 42.0651 12.1684 42.7737C12.4565 44.093 12.7849 45.6485 14.0638 46.2246C14.3894 46.3722 14.7434 46.4469 15.1008 46.4435C16.1033 46.4435 17.123 45.9077 18.1081 45.395C18.7015 45.0242 19.3574 44.7645 20.0439 44.6288C20.3953 44.7037 21.0636 45.5621 21.513 46.1324C22.3829 47.2674 23.368 48.529 24.7795 48.529C26.2083 48.529 27.1761 47.2155 28.0288 46.0748C28.432 45.5333 29.1119 44.6288 29.4518 44.5481H29.5151C30.2028 44.6801 30.8631 44.9274 31.4681 45.2798C32.3641 45.8237 33.3696 46.1622 34.4121 46.2707ZM28.9563 42.3013C27.8041 42.5721 26.986 43.6494 26.2083 44.6921C25.8453 45.1761 25.0906 46.1843 24.7795 46.2188C24.4684 46.2534 23.6964 45.1991 23.3277 44.7267C22.5269 43.7012 21.6743 42.6412 20.522 42.3935C20.3476 42.3578 20.17 42.3405 19.992 42.3416C19.0069 42.3416 18.0044 42.8659 17.0308 43.3786C16.4354 43.7492 15.7806 44.0143 15.0951 44.1621H15.0317C14.784 43.9778 14.5478 42.8544 14.421 42.2552C14.1503 40.9705 13.8449 39.6397 12.9001 38.8965C12.1242 38.4012 11.2079 38.1726 10.2903 38.2455C9.94465 38.2455 9.58746 38.2455 9.23027 38.2743C8.87308 38.3031 8.58502 38.2743 8.27969 38.2743C7.90706 38.3247 7.52779 38.2852 7.17355 38.159C7.01224 37.894 7.31758 36.69 7.46737 36.1139C7.78999 34.8637 8.12414 33.5732 7.59412 32.5189C7.06409 31.4646 5.80241 30.9289 4.58682 30.4161C4.01071 30.1684 2.89881 29.7017 2.76631 29.4367C2.76631 29.1199 3.52101 28.2211 3.91853 27.743C4.76541 26.7463 5.64686 25.7208 5.64686 24.5283C5.64686 23.3357 4.73661 22.339 3.87244 21.3654C3.44612 20.8757 2.66261 19.9885 2.66261 19.6947C2.79511 19.4066 3.8782 18.9227 4.45431 18.6635C5.66414 18.1277 6.9143 17.5689 7.42704 16.4915C7.93978 15.4142 7.57683 14.1871 7.23117 12.9196C7.06985 12.3435 6.72995 11.1279 6.89702 10.8629C6.93159 10.8284 7.15051 10.6555 8.3373 10.6555H9.0632H9.82942C10.805 10.746 11.7828 10.5066 12.6063 9.97571C13.5511 9.22101 13.7988 7.89595 14.0466 6.61123C14.1618 5.99479 14.3807 4.84833 14.6227 4.67549H14.6976C15.3784 4.8118 16.0311 5.06313 16.6275 5.41868C17.5218 5.96086 18.5254 6.29737 19.5657 6.40383C19.7595 6.40435 19.9528 6.38309 20.1418 6.34046C21.294 6.06968 22.1121 4.99236 22.8899 3.9496C23.2528 3.46566 24.0075 2.45171 24.3186 2.4229C24.6297 2.3941 25.4017 3.44262 25.7704 3.91503C26.5827 4.96355 27.4354 6.02359 28.5876 6.27132C28.762 6.30696 28.9396 6.32433 29.1176 6.32317C30.1028 6.32317 31.1052 5.79891 32.0788 5.28617C32.6741 4.91549 33.329 4.65042 34.0146 4.50266H34.0779C34.3257 4.68702 34.5619 5.81043 34.6886 6.40959C34.9594 7.69431 35.2647 9.02513 36.2095 9.76831C36.9853 10.264 37.9017 10.4926 38.8193 10.4193C39.165 10.4193 39.5222 10.4193 39.8794 10.3905C40.2365 10.3617 40.5246 10.3905 40.8299 10.3905C41.2044 10.3475 41.5837 10.3949 41.9361 10.5288C42.0974 10.7938 41.792 11.9979 41.6423 12.574C41.3196 13.8241 40.9855 15.1146 41.5155 16.1689C42.0455 17.2232 43.3072 17.759 44.5171 18.2717C45.0932 18.5194 46.2051 18.9861 46.3376 19.2569C46.3376 19.5737 45.5829 20.4725 45.1853 20.9506C44.3385 21.9473 43.457 22.9728 43.457 24.1653C43.457 25.3579 44.3673 26.3545 45.2314 27.3282C45.6577 27.8063 46.447 28.6936 46.4413 28.9874C46.3088 29.2754 45.2257 29.7594 44.6496 30.0186C43.4397 30.5544 42.1896 31.1132 41.6768 32.1905C41.1641 33.2679 41.527 34.5238 41.8727 35.7624C42.034 36.3385 42.3739 37.5599 42.2068 37.8191C42.1723 37.8537 41.9534 38.0265 40.7666 38.0265H40.0407H39.2744C38.2989 37.9365 37.3213 38.1758 36.4976 38.7064C35.5528 39.4611 35.305 40.7861 35.0631 42.0708C34.9478 42.6873 34.7289 43.8337 34.487 44.0066H34.4121C33.7308 43.8686 33.0781 43.6153 32.4821 43.2576C31.5879 42.7154 30.5843 42.3789 29.5439 42.2725C29.3476 42.2598 29.1504 42.2694 28.9563 42.3013Z"
      fill="currentColor"
    />
    <path
      d="M41.3139 42.6067C41.1123 42.6067 39.7527 42.6067 39.6547 42.6067C39.6547 42.7046 39.4761 43.5573 39.3897 43.926L44.1426 53.7141L39.4819 51.9858C39.3351 51.9299 39.1784 51.9046 39.0214 51.9114C38.8645 51.9183 38.7106 51.9572 38.5692 52.0257C38.4278 52.0942 38.302 52.1909 38.1994 52.3099C38.0967 52.4289 38.0195 52.5675 37.9725 52.7174L36.6474 56.923L32.6146 48.2814C31.7658 47.9642 30.9349 47.6007 30.1258 47.1925L29.7744 47.6592L35.8409 60.5468C35.933 60.7402 36.0772 60.9042 36.2573 61.0202C36.4374 61.1362 36.6464 61.1997 36.8606 61.2035H36.9355C37.1662 61.1878 37.3868 61.103 37.5687 60.9602C37.7505 60.8174 37.8852 60.6231 37.9552 60.4027L39.7987 54.5495L46.0323 56.923C46.2478 57.0048 46.483 57.02 46.7072 56.9667C46.9315 56.9133 47.1347 56.794 47.2904 56.624C47.4461 56.454 47.5473 56.2412 47.5808 56.0131C47.6143 55.785 47.5786 55.5521 47.4783 55.3445L41.3139 42.6067Z"
      fill="currentColor"
    />
    <path
      d="M19.485 47.2963C19.3698 47.377 18.2176 47.9531 17.7221 48.1662L13.6087 56.8943L12.2836 52.6887C12.2335 52.5421 12.1543 52.4071 12.0508 52.2917C11.9473 52.1764 11.8216 52.0832 11.6812 52.0176C11.5409 51.9519 11.3887 51.9153 11.2338 51.9099C11.079 51.9044 10.9246 51.9303 10.78 51.9858L6.11923 53.7142L10.3652 44.9573C10.1578 44.3236 9.86971 42.9524 9.84091 42.8315L8.81543 42.866L2.77779 55.3388C2.67752 55.5464 2.64182 55.7793 2.67531 56.0074C2.70881 56.2355 2.80996 56.4483 2.96569 56.6183C3.12141 56.7883 3.32455 56.9077 3.54884 56.961C3.77314 57.0143 4.00826 56.9991 4.22383 56.9174L10.4573 54.5438L12.3009 60.3971C12.3703 60.6194 12.5054 60.8155 12.6885 60.9595C12.8716 61.1035 13.094 61.1886 13.3264 61.2036H13.4013C13.6201 61.2036 13.8343 61.1412 14.019 61.0239C14.2036 60.9066 14.3511 60.7391 14.444 60.5411L20.2051 48.2411C20.0381 48.0049 19.5599 47.377 19.485 47.2963Z"
      fill="currentColor"
    />
    <path
      d="M19.5485 30.9551V17.697H23.7745C23.885 17.697 24.1029 17.7001 24.4282 17.7063C24.7597 17.7124 25.0788 17.7339 25.3857 17.7707C26.4353 17.9057 27.3223 18.2802 28.0466 18.8939C28.777 19.5016 29.3294 20.2781 29.7038 21.2233C30.0782 22.1686 30.2654 23.2028 30.2654 24.3261C30.2654 25.4493 30.0782 26.4836 29.7038 27.4288C29.3294 28.3741 28.777 29.1536 28.0466 29.7674C27.3223 30.375 26.4353 30.7464 25.3857 30.8814C25.085 30.9183 24.7689 30.9397 24.4374 30.9459C24.106 30.952 23.885 30.9551 23.7745 30.9551H19.5485ZM21.8042 28.8651H23.7745C23.9587 28.8651 24.1919 28.859 24.4743 28.8467C24.7627 28.8344 25.0175 28.8068 25.2384 28.7638C25.8645 28.6472 26.374 28.3679 26.7668 27.926C27.1596 27.4841 27.4481 26.947 27.6322 26.3148C27.8225 25.6826 27.9177 25.0197 27.9177 24.3261C27.9177 23.6079 27.8225 22.9327 27.6322 22.3005C27.442 21.6683 27.1473 21.1374 26.7484 20.7077C26.3555 20.2781 25.8522 20.0049 25.2384 19.8883C25.0175 19.8392 24.7627 19.8116 24.4743 19.8054C24.1919 19.7932 23.9587 19.787 23.7745 19.787H21.8042V28.8651Z"
      fill="currentColor"
    />
  </svg>
);
export default DTypeCertificationSvg;
