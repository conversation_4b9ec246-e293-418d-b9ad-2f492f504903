/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const AccountSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 39}
    height={props.height || 42}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 39 42"
  >
    <g clipPath="url(#a)">
      <path
        fill="currentColor"
        d="M20.26 2.795c-7.715 0-13.977 6.262-13.977 13.977 0 7.715 6.262 13.976 13.977 13.976 7.715 0 13.976-6.261 13.976-13.976 0-7.715-6.261-13.977-13.976-13.977Zm0 5.59a4.897 4.897 0 0 1 4.892 4.893 4.897 4.897 0 0 1-4.892 4.891 4.897 4.897 0 0 1-4.892-4.891 4.897 4.897 0 0 1 4.892-4.892Zm0 19.568c-2.838 0-6.192-1.146-8.582-4.025a13.902 13.902 0 0 1 17.163 0c-2.39 2.879-5.744 4.025-8.581 4.025Z"
        opacity={0.6}
      />
    </g>
    <mask
      id="b"
      width={23}
      height={23}
      x={16.457}
      y={18.687}
      fill="currentColor"
      maskUnits="userSpaceOnUse"
    >
      <path fill="#fff" d="M16.457 18.687h23v23h-23z" />
      <path
        fillRule="evenodd"
        d="M32.219 33.481a6.986 6.986 0 0 1-3.881 1.178 6.994 6.994 0 0 1-6.986-6.986 6.986 6.986 0 1 1 10.867 5.808Zm-5.517-6.583a.875.875 0 0 0-.641 0 .842.842 0 0 0-.518.775c0 .*************.32s.104.195.182.273l.053.053 1.344 1.344a.838.838 0 0 0 1.185 0l2.795-2.794a.838.838 0 0 0-1.186-1.186l-2.201 2.202c-.087-.087-.174-.186-.264-.289-.24-.273-.5-.568-.813-.698Zm-7.484 10.58 1.522-.5a.565.565 0 0 1 .726.368l.558 1.677a.582.582 0 0 0 1.059.095l1.974-3.555a8.074 8.074 0 0 1-4.34-2.854l-2.188 3.936a.581.581 0 0 0 .69.832Zm15.552-4.769a8.075 8.075 0 0 1-4.34 2.854l1.974 3.555a.582.582 0 0 0 1.058-.095l.559-1.677a.564.564 0 0 1 .725-.368l1.522.5a.582.582 0 0 0 .69-.833l-2.188-3.936Z"
        clipRule="evenodd"
      />
    </mask>
    <path
      fill="#181D1C"
      fillRule="evenodd"
      d="M32.219 33.481a6.986 6.986 0 0 1-3.881 1.178 6.994 6.994 0 0 1-6.986-6.986 6.986 6.986 0 1 1 10.867 5.808Zm-5.517-6.583a.875.875 0 0 0-.641 0 .842.842 0 0 0-.518.775c0 .*************.32s.104.195.182.273l.053.053 1.344 1.344a.838.838 0 0 0 1.185 0l2.795-2.794a.838.838 0 0 0-1.186-1.186l-2.201 2.202c-.087-.087-.174-.186-.264-.289-.24-.273-.5-.568-.813-.698Zm-7.484 10.58 1.522-.5a.565.565 0 0 1 .726.368l.558 1.677a.582.582 0 0 0 1.059.095l1.974-3.555a8.074 8.074 0 0 1-4.34-2.854l-2.188 3.936a.581.581 0 0 0 .69.832Zm15.552-4.769a8.075 8.075 0 0 1-4.34 2.854l1.974 3.555a.582.582 0 0 0 1.058-.095l.559-1.677a.564.564 0 0 1 .725-.368l1.522.5a.582.582 0 0 0 .69-.833l-2.188-3.936Z"
      clipRule="evenodd"
    />
    <path
      fill="#E8E8E8"
      d="m24.817 28.009.299.646 1.452-.672-.299-.646-1.452.672Zm3.52 6.65-.001 1.6h.002v-1.6Zm3.882-1.178-.89-1.33.89 1.33Zm-8.819-.87 1.132-1.132L23.4 32.61Zm-2.048-4.938h-1.6v.002l1.6-.002Zm1.177-3.881 1.33.889-1.33-.889Zm3.135-2.573.612 1.478-.612-1.478Zm4.036-.397.313-1.57-.313 1.57Zm3.577 1.911-1.131 1.132 1.131-1.132Zm1.912 3.577-1.57.312 1.57-.312Zm-.398 4.036-1.478-.612 1.478.612Zm-8.73-3.448-.613-1.478.613 1.478Zm.641 0 .613-1.478-.613 1.478Zm-1.095.454-1.478-.612 1.478.612Zm-.064.321h1.6-1.6Zm.064.32-1.531.465.************** 1.411-.755Zm.182.273 1.131-1.131-1.131 1.13Zm.053.053 1.131-1.131-1.13 1.131Zm1.344 1.344 1.132-1.131-1.132 1.13Zm.272.182-.613 1.478.613-1.478Zm.641 0 .613 1.478-.613-1.478Zm.272-.182-1.131-1.131 1.131 1.13Zm2.795-2.794L32.297 28l.003-.003-1.134-1.128Zm.244-.593 1.6-.002-1.6.002Zm-.246-.591-1.131 1.131 1.131-1.131Zm-.591-.246.002-1.6-.002 1.6Zm-.593.244-1.128-1.134-.003.003 1.131 1.131Zm-2.201 2.202-1.132 1.131 1.132 1.132 1.131-1.132-1.131-1.131Zm-.264-.289 1.202-1.056-1.202 1.056Zm-6.775 9.382.499 1.52.014-.005.015-.005-.528-1.51Zm-1.522.5.495 1.52h.004l-.499-1.52Zm1.746-.53-.107 1.596.107-1.596Zm.217.06.725-1.427-.725 1.426Zm.285.338-1.532.465.007.02.007.021 1.518-.506Zm.558 1.677-1.518.506.005.012 1.513-.518Zm.192.27-.988 1.258.988-1.259Zm.307.12-.144 1.595.144-1.594Zm.324-.064.747 1.415-.747-1.415Zm.236-.23 1.397.78.002-.004-1.4-.777Zm1.974-3.556 1.398.776 1.003-1.805-2-.52-.401 1.549Zm-4.34-2.854 1.262-.983-1.486-1.908-1.175 2.114 1.399.777Zm-2.188 3.936-1.399-.778-.004.009 1.403.769Zm-.07.335-1.592.152 1.593-.152Zm.132.315 1.232-1.02-1.232 1.02Zm.286.188-.446 1.537.446-1.537Zm11.553-1.92-.402-1.549-2 .52 1.003 1.806 1.399-.777Zm4.34-2.854 1.399-.777-1.175-2.114-1.486 1.908 1.262.983Zm-2.366 6.41-1.4.776.003.003 1.397-.78Zm.236.23-.747 1.415.747-1.415Zm.324.065-.143-1.594.143 1.594Zm.307-.122.988 1.259-.988-1.259Zm.191-.27 1.514.52.004-.013-1.518-.506Zm.559-1.676 1.518.506.007-.02.006-.021-1.531-.465Zm.107-.198 1.227 1.028-1.227-1.028Zm.177-.14.725 1.426-.725-1.427Zm.218-.06-.108-1.597.108 1.596Zm.223.03-.527 1.51.014.005.015.005.498-1.52Zm1.522.5-.499 1.52h.004l.495-1.52Zm.342.005-.446-1.537.446 1.537Zm.286-.188-1.232-1.02 1.232 1.02Zm.13-.316-1.592-.152 1.593.153Zm-.068-.334 1.403-.77-.005-.008-1.398.778Zm-8.62-.386a8.586 8.586 0 0 0 4.77-1.447l-1.778-2.66a5.386 5.386 0 0 1-2.992.907v3.2Zm-6.07-2.517a8.594 8.594 0 0 0 6.068 2.517l.003-3.2a5.394 5.394 0 0 1-3.807-1.58l-2.263 2.263Zm-2.516-6.067a8.593 8.593 0 0 0 2.517 6.067l2.263-2.263a5.393 5.393 0 0 1-1.58-3.808l-3.2.004Zm1.447-4.772a8.586 8.586 0 0 0-1.447 4.77h3.2c0-1.065.316-2.106.907-2.992l-2.66-1.778Zm3.853-3.162a8.586 8.586 0 0 0-3.853 3.162l2.66 1.778a5.387 5.387 0 0 1 2.417-1.984l-1.224-2.956Zm4.96-.489a8.585 8.585 0 0 0-4.96.489l1.224 2.956a5.386 5.386 0 0 1 3.112-.306l.624-3.139Zm4.397 2.35a8.585 8.585 0 0 0-4.396-2.35l-.625 3.139a5.385 5.385 0 0 1 2.758 1.474l2.263-2.263Zm2.35 4.396a8.586 8.586 0 0 0-2.35-4.396l-2.263 2.263a5.386 5.386 0 0 1 1.474 2.757l3.138-.624Zm-.49 4.96c.65-1.568.82-3.294.49-4.96l-3.14.624a5.386 5.386 0 0 1-.306 3.112l2.957 1.225Zm-3.161 3.854a8.586 8.586 0 0 0 3.162-3.853l-2.957-1.225a5.385 5.385 0 0 1-1.983 2.417l1.778 2.66Zm-6.435-6.435a.756.756 0 0 1-.291.06.756.756 0 0 1-.292-.06l1.225-2.957a2.473 2.473 0 0 0-1.867 0l1.225 2.957Zm.412-.412a.759.759 0 0 1-.412.412l-1.225-2.957a2.441 2.441 0 0 0-1.32 1.32l2.957 1.225Zm.058-.292a.793.793 0 0 1-.058.292L24.13 26.74a2.373 2.373 0 0 0-.186.933h3.2Zm-1.536.32 1.531-.463v.002l.001.001v.002l.001.001-.002-.006a.808.808 0 0 1-.007-.029c-.003-.012-.003-.011 0 0a1.174 1.174 0 0 1 .013.171l-3.2.001c0 .266.06.514.075.576l.037.14.012.044.005.015.002.006v.003l1.532-.463Zm1.313-.859c.042.042.07.076.084.093l.028.037c.007.01.007.01 0 0a1.008 1.008 0 0 1-.017-.03l.001.002v.001l.001.001-1.41.756-1.41.755v.002l.002.002.002.005.008.014a3.212 3.212 0 0 0 .096.165c.032.054.165.272.353.46l2.262-2.263Zm.053.054-.053-.053-2.263 2.262.054.054 2.262-2.263Zm1.344 1.344-1.344-1.344-2.262 2.263 1.344 1.343 2.262-2.262Zm-.247-.165a.763.763 0 0 1 .248.165l-2.264 2.262c.227.226.495.406.791.529l1.225-2.956Zm-.291-.059c.1 0 .199.02.291.059l-1.225 2.956c.296.122.613.186.934.186v-3.2Zm-.292.059a.763.763 0 0 1 .292-.059v3.2c.32 0 .637-.063.933-.185l-1.225-2.956Zm-.247.165a.762.762 0 0 1 .247-.165l1.225 2.956c.296-.123.564-.303.79-.529l-2.262-2.262Zm2.794-2.795-2.794 2.795 2.263 2.262L32.297 28l-2.263-2.263Zm-.224.541c0-.201.08-.395.221-.538l2.269 2.257c.455-.457.71-1.077.71-1.723l-3.2.004Zm.223.538a.762.762 0 0 1-.223-.538l3.2-.003c-.001-.646-.258-1.265-.714-1.721l-2.263 2.262Zm.537.223a.762.762 0 0 1-.537-.223l2.263-2.262a2.438 2.438 0 0 0-1.721-.715l-.005 3.2Zm.539-.221a.762.762 0 0 1-.539.221l.005-3.2c-.646 0-1.265.255-1.723.71l2.257 2.269Zm-2.199 2.198 2.202-2.201-2.263-2.263-2.202 2.201 2.263 2.263Zm-2.597-.363c.082.093.204.233.334.363l2.263-2.262a4.93 4.93 0 0 1-.193-.214l-2.404 2.113Zm-.223-.276c-.087-.036-.103-.067-.041-.01.026.025.06.06.107.11s.095.105.157.176l2.404-2.113c-.171-.194-.678-.82-1.402-1.12l-1.225 2.957Zm-5.849 7.08-1.521.5.997 3.04 1.522-.499-.998-3.04Zm.83-.106c-.291-.02-.583.02-.859.116l1.056 3.021a1.037 1.037 0 0 1-.411.056l.214-3.193Zm.835.23a2.166 2.166 0 0 0-.835-.23l-.214 3.193a1.036 1.036 0 0 1-.4-.11l1.45-2.853Zm.678.54a2.162 2.162 0 0 0-.678-.54l-1.45 2.853a1.036 1.036 0 0 1-.324-.258l2.452-2.056Zm.413.76a2.164 2.164 0 0 0-.413-.76l-2.452 2.055a1.035 1.035 0 0 1-.198-.365l3.063-.93Zm.545 1.636-.559-1.677-3.035 1.012.559 1.677 3.035-1.012Zm-.339-.484c.155.122.271.286.335.472l-3.027 1.036c.136.399.385.75.717 1.01l1.975-2.518Zm-.537-.213c.196.018.383.092.537.213l-1.975 2.518c.332.26.731.419 1.151.456l.287-3.187Zm-.567.114c.174-.092.371-.131.567-.114l-.287 3.188c.42.037.842-.047 1.215-.244l-1.495-2.83Zm-.413.404c.096-.172.24-.312.413-.404l1.495 2.83a2.18 2.18 0 0 0 .886-.866l-2.794-1.56Zm1.972-3.552-1.974 3.556 2.798 1.553 1.973-3.556-2.797-1.553Zm-4.204-1.094a9.674 9.674 0 0 0 5.2 3.42l.805-3.098a6.474 6.474 0 0 1-3.48-2.288l-2.525 1.966Zm.473 3.73 2.188-3.935-2.797-1.555-2.188 3.935 2.797 1.555Zm.125-.594c.02.203-.022.407-.12.586l-2.806-1.538c-.21.383-.3.82-.26 1.256l3.186-.304Zm-.23-.554c.131.158.211.35.23.553l-3.185.305c.041.435.213.848.492 1.184l2.464-2.042Zm-.5-.328c.196.057.37.171.5.328l-2.463 2.042c.279.337.652.582 1.072.703l.891-3.073Zm-.599.01a1.02 1.02 0 0 1 .6-.01l-.892 3.074c.42.121.867.114 1.282-.021l-.99-3.043Zm12.11 1.155a9.675 9.675 0 0 0 5.2-3.419l-2.525-1.966a6.476 6.476 0 0 1-3.48 2.288l.804 3.097Zm2.97 1.23-1.974-3.555-2.798 1.553 1.974 3.556 2.798-1.553Zm-.416-.407c.174.092.318.232.414.404l-2.794 1.56c.205.368.513.669.886.866l1.494-2.83Zm-.566-.114c.195-.017.392.022.566.114l-1.494 2.83c.373.197.794.281 1.214.243l-.286-3.187Zm-.538.213c.155-.121.341-.195.538-.213l.286 3.188c.42-.038.82-.197 1.152-.457l-1.976-2.518Zm-.335.472c.064-.186.18-.35.335-.472l1.976 2.518c.331-.26.58-.611.717-1.01l-3.028-1.036Zm.555-1.665-.559 1.677 3.036 1.012.559-1.677-3.036-1.011Zm.4-.72a2.164 2.164 0 0 0-.413.762l3.062.929c-.04.133-.108.258-.197.365l-2.453-2.056Zm.677-.539c-.26.132-.49.316-.678.54l2.453 2.055c-.09.107-.2.194-.325.258l-1.45-2.853Zm.835-.23c-.29.02-.575.098-.835.23l1.45 2.853c-.125.063-.26.1-.4.11l-.215-3.193Zm.859.116a2.163 2.163 0 0 0-.859-.116l.215 3.193c-.14.01-.28-.01-.411-.056l1.055-3.02Zm1.493.49-1.522-.5-.997 3.041 1.521.5.998-3.041Zm-.603-.011c.197-.057.405-.053.6.01l-.99 3.043c.415.135.861.142 1.281.02l-.89-3.073Zm-.5.328c.13-.157.304-.271.5-.328l.891 3.073c.42-.121.794-.366 1.073-.703l-2.464-2.042Zm-.23.553c.02-.203.1-.395.23-.553l2.464 2.042c.279-.337.45-.75.492-1.184l-3.186-.305Zm.12.587a1.019 1.019 0 0 1-.12-.587l3.186.305a2.181 2.181 0 0 0-.26-1.256l-2.805 1.538Zm-2.182-3.928 2.187 3.936 2.797-1.555-2.187-3.935-2.797 1.555Z"
      mask="url(#b)"
    />
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M3.488 0h33.544v33.544H3.488z" />
      </clipPath>
    </defs>
  </svg>
);
export default AccountSvg;
