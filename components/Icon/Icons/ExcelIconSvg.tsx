/* eslint-disable react/destructuring-assignment */
import * as React from 'react';

const ExcelIconSvg = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 31}
    height={props.height || 26}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 31 26"
  >
    <g clipPath="url(#a)">
      <path
        fill="#185C37"
        d="M19.064 12.535 8.438 10.66v13.855c0 .632.513 1.145 1.146 1.145h18.334c.633 0 1.146-.513 1.146-1.145V19.41l-10-6.875Z"
      />
      <path
        fill="#21A366"
        d="M19.064.66h-9.48c-.633 0-1.146.513-1.146 1.146V6.91l10.626 6.25 5.625 1.875 4.375-1.875V6.91l-10-6.25Z"
      />
      <path fill="#107C41" d="M8.438 6.91h10.626v6.25H8.438V6.91Z" />
      <path
        fill="#000"
        d="M16.043 5.66H8.438v15.625h7.605a1.15 1.15 0 0 0 1.146-1.145V6.806a1.15 1.15 0 0 0-1.146-1.146Z"
        opacity={0.1}
      />
      <path
        fill="#000"
        d="M15.418 6.285h-6.98V21.91h6.98a1.15 1.15 0 0 0 1.146-1.145V7.43a1.15 1.15 0 0 0-1.146-1.146Z"
        opacity={0.2}
      />
      <path
        fill="#000"
        d="M15.418 6.285h-6.98V20.66h6.98a1.15 1.15 0 0 0 1.146-1.145V7.43a1.15 1.15 0 0 0-1.146-1.146Z"
        opacity={0.2}
      />
      <path
        fill="#000"
        d="M14.793 6.285H8.438V20.66h6.355a1.15 1.15 0 0 0 1.146-1.145V7.43a1.15 1.15 0 0 0-1.146-1.146Z"
        opacity={0.2}
      />
      <path
        fill="url(#b)"
        d="M3.334 6.285h11.459c.633 0 1.146.513 1.146 1.146V18.89c0 .632-.513 1.145-1.146 1.145H3.334a1.146 1.146 0 0 1-1.146-1.145V7.43c0-.632.513-1.145 1.146-1.145Z"
      />
      <path
        fill="#fff"
        d="m5.736 16.884 2.41-3.734-2.208-3.714h1.776l1.205 2.375c.112.226.188.393.23.504h.015c.079-.18.162-.355.25-.525l1.288-2.353h1.63l-2.264 3.692 2.322 3.755h-1.735l-1.392-2.607a2.186 2.186 0 0 1-.166-.348h-.02c-.041.118-.096.232-.162.338l-1.433 2.617H5.736Z"
      />
      <path
        fill="#33C481"
        d="M27.917.66h-8.855v6.25h10V1.806c0-.633-.512-1.146-1.145-1.146Z"
      />
      <path fill="#107C41" d="M19.063 13.16h10v6.25h-10v-6.25Z" />
    </g>
    <defs>
      <linearGradient
        id="b"
        x1={4.577}
        x2={13.55}
        y1={5.39}
        y2={20.93}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#18884F" />
        <stop offset={0.5} stopColor="#117E43" />
        <stop offset={1} stopColor="#0B6631" />
      </linearGradient>
      <clipPath id="a">
        <path fill="#fff" d="M2.188.66h26.875v25H2.188z" />
      </clipPath>
    </defs>
  </svg>
);
export default ExcelIconSvg;
