/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const STypeLicenseSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 43}
    height={props.height || 46}
    viewBox="0 0 43 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.9579 3.99336L36.0266 3.99336C39.3454 3.99336 42.0468 6.6947 42.0452 10.0135V39.9799C42.0452 43.2987 39.3439 46 36.0251 46H6.84289C3.5241 46 0.822754 43.2987 0.822754 39.9799L0.822754 10.0135C0.822754 6.6947 3.5241 3.99336 6.84289 3.99336H9.91316C10.157 1.75356 12.0372 0 14.3403 0L28.5308 0C30.8339 0 32.714 1.7551 32.9579 3.99336ZM14.3387 2.77852C13.4033 2.77852 12.6407 3.54107 12.6407 4.47651V6.28872C12.6407 7.22416 13.4033 7.98671 14.3387 7.98671H28.5293C29.4647 7.98671 30.2272 7.22416 30.2272 6.28872V4.47651C30.2272 3.54107 29.4647 2.77852 28.5293 2.77852L14.3387 2.77852ZM36.0266 43.2215C37.8142 43.2215 39.2683 41.7674 39.2683 39.9799L39.2667 10.0135C39.2667 8.22597 37.8126 6.77188 36.0251 6.77188H32.9564C32.7125 9.01168 30.8324 10.7652 28.5293 10.7652L14.3387 10.7652C12.0356 10.7652 10.1555 9.01013 9.91161 6.77188H6.84289C5.05537 6.77188 3.60128 8.22597 3.60128 10.0135L3.60128 39.9799C3.60128 41.7674 5.05537 43.2215 6.84289 43.2215H36.0266ZM18.9543 32.7991C19.7338 33.1367 20.6085 33.3055 21.5783 33.3055C22.2596 33.3055 22.9102 33.2196 23.5302 33.0477C24.1563 32.8758 24.7117 32.6181 25.1966 32.2743C25.6815 31.9306 26.0652 31.5009 26.3475 30.9853C26.636 30.4636 26.7802 29.859 26.7802 29.1716C26.7802 28.5087 26.6575 27.9624 26.412 27.5327C26.1726 27.1031 25.8657 26.7563 25.4913 26.4923C25.1168 26.2284 24.7271 26.0228 24.322 25.8755C23.9169 25.7282 23.5517 25.6115 23.2264 25.5256L20.851 24.8627C20.5502 24.7829 20.2525 24.6847 19.9579 24.5681C19.6694 24.4453 19.4269 24.2827 19.2305 24.0801C19.0341 23.8714 18.9359 23.6044 18.9359 23.2791C18.9359 22.9354 19.0495 22.6346 19.2766 22.3768C19.5098 22.119 19.8136 21.9226 20.1881 21.7876C20.5686 21.6464 20.9799 21.5789 21.4218 21.585C21.876 21.5973 22.3026 21.6894 22.7016 21.8612C23.1067 22.0331 23.4473 22.2848 23.7235 22.6162C24.0059 22.9415 24.1962 23.3374 24.2944 23.8039L26.679 23.3896C26.4825 22.5732 26.145 21.8766 25.6662 21.2996C25.1874 20.7226 24.5921 20.2838 23.88 19.983C23.168 19.6761 22.3578 19.5196 21.4494 19.5135C20.5533 19.5073 19.7369 19.6485 19.0004 19.937C18.2699 20.2255 17.6868 20.6613 17.251 21.2444C16.8214 21.8213 16.6065 22.5364 16.6065 23.3896C16.6065 23.9727 16.7017 24.4637 16.892 24.8627C17.0884 25.2555 17.337 25.5809 17.6377 25.8387C17.9446 26.0903 18.2669 26.2898 18.6045 26.4371C18.9482 26.5783 19.2674 26.6918 19.562 26.7778L22.987 27.7905C23.2325 27.8642 23.4473 27.9532 23.6315 28.0575C23.8217 28.1557 23.9752 28.2662 24.0918 28.389C24.2146 28.5117 24.3066 28.6529 24.368 28.8125C24.4294 28.966 24.4601 29.1347 24.4601 29.3189C24.4601 29.7301 24.3281 30.0769 24.0642 30.3593C23.8064 30.6416 23.4688 30.8565 23.0514 31.0038C22.634 31.1511 22.189 31.2247 21.7164 31.2247C20.9185 31.2247 20.2126 31.0099 19.5988 30.5802C18.9911 30.1506 18.5891 29.5429 18.3927 28.7573L16.091 29.1071C16.226 29.9726 16.5421 30.7214 17.0393 31.3536C17.5426 31.9797 18.1809 32.4615 18.9543 32.7991Z"
      fill="currentColor"
    />
  </svg>
);
export default STypeLicenseSvg;
