/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const CartSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 31}
    height={props.height || 28}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 31 28"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M29.701 4.88c.345 0 .655.137.862.413.207.276.276.62.207.931L27.39 19.26c-.137.482-.585.827-1.068.827H10.426c-.518 0-.931-.345-1.07-.827l-3.379-13v-.07l-.896-3.482H1.426A1.09 1.09 0 0 1 .322 1.603C.322.983.805.5 1.426.5h4.482c.518 0 .931.345 1.07.828l.896 3.551H29.7Zm-18.413 13H25.46l2.827-10.828H8.46l2.828 10.827Zm3.068-2.862a1.09 1.09 0 0 0 1.104.966h.103a1.09 1.09 0 0 0 .965-1.207l-.517-4.897a1.09 1.09 0 0 0-1.207-.965 1.09 1.09 0 0 0-.965 1.207l.517 4.896Zm6.828.966h.103c.552 0 1.035-.414 1.104-.966l.517-4.896c.069-.587-.38-1.138-.966-1.207-.586-.07-1.138.379-1.207.965l-.517 4.897c-.069.62.38 1.172.966 1.207ZM9.357 24.12a3.385 3.385 0 0 1 3.38-3.38c1.861 0 3.379 1.552 3.379 3.38a3.385 3.385 0 0 1-3.38 3.379 3.385 3.385 0 0 1-3.379-3.38Zm2.173.034c0 .655.551 1.207 1.206 1.207.656 0 1.173-.552 1.207-1.207 0-.655-.551-1.207-1.207-1.207-.655 0-1.207.552-1.207 1.207Zm12.482-3.414a3.385 3.385 0 0 0-3.379 3.38 3.385 3.385 0 0 0 3.38 3.379 3.385 3.385 0 0 0 3.378-3.38c.035-1.827-1.482-3.379-3.379-3.379Zm0 4.621a1.222 1.222 0 0 1-1.207-1.207c0-.655.552-1.207 1.207-1.207s1.207.552 1.207 1.207a1.2 1.2 0 0 1-1.207 1.207Z"
      clipRule="evenodd"
    />
  </svg>
);
export default CartSvg;
