/* eslint-disable react/destructuring-assignment */
import React, { SVGProps } from 'react';

import styles from '../styles/CheckmarkIcon.module.css';

const CheckMarkWholeSvg = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={props.width || 203}
      height={props.height || 201}
      fill="none"
      preserveAspectRatio="none"
      viewBox="0 0 203 201"
    >
      <mask
        id="mask0_1055_19944"
        style={{ maskType: 'luminance' }}
        width="290"
        height="290"
        x="-45"
        y="-45"
        maskUnits="userSpaceOnUse"
      >
        <path fill="#fff" d="M244.415-44.02h-288.97v288.97h288.97V-44.02z" />
      </mask>
      <g mask="url(#mask0_1055_19944)">
        <path
          fill="currentColor"
          d="M102.636 11.506c49.966 0 90.534 40.569 90.534 90.535 0 49.966-40.568 90.534-90.534 90.534-49.967 0-90.535-40.568-90.535-90.534 0-49.966 40.568-90.535 90.535-90.535z"
        />
        <path
          stroke="#fff"
          strokeLinecap="round"
          strokeWidth="13.747"
          d="M56.377 110.826l34.752 29.88 62.031-71.762"
          className={styles.animateRevealFromBottom}
        />
        <mask
          id="mask1_1055_19944"
          style={{ maskType: 'luminance' }}
          width="123"
          height="124"
          x="118"
          y="-21"
          maskUnits="userSpaceOnUse"
        >
          <path
            fill="#fff"
            d="M118.155 41.938l60.539-62.052 62.051 60.539-60.539 62.051-62.051-60.538z"
          />
        </mask>
        <g mask="url(#mask1_1055_19944)">
          <path
            fill="#fff"
            d="M195.469 17.328a4.277 4.277 0 016.045-.075 4.277 4.277 0 01.074 6.045 4.277 4.277 0 01-6.045.075 4.277 4.277 0 01-.074-6.045z"
            opacity="0.333"
          />
        </g>
        <mask
          id="mask2_1055_19944"
          style={{ maskType: 'luminance' }}
          width="73"
          height="74"
          x="122"
          y="4"
          maskUnits="userSpaceOnUse"
        >
          <path
            fill="#fff"
            d="M122.037 60.006l17.407-55.11 55.111 17.406-17.407 55.11-55.111-17.406z"
          />
        </mask>
        <g mask="url(#mask2_1055_19944)">
          <path
            fill="#fff"
            d="M160.67 22.146a2.85 2.85 0 115.433 1.718 2.85 2.85 0 01-5.433-1.718z"
            opacity="0.333"
          />
        </g>
        <mask
          id="mask3_1055_19944"
          style={{ maskType: 'luminance' }}
          width="74"
          height="74"
          x="145"
          y="26"
          maskUnits="userSpaceOnUse"
        >
          <path
            fill="#fff"
            d="M145.118 44.56l54.935-17.953 17.953 54.935-54.935 17.953-17.953-54.935z"
          />
        </mask>
        <g mask="url(#mask3_1055_19944)">
          <path
            fill="#fff"
            d="M198.395 53.907a2.851 2.851 0 011.77 5.418 2.851 2.851 0 01-3.594-1.824 2.852 2.852 0 011.824-3.594z"
            opacity="0.333"
          />
        </g>
      </g>
    </svg>
  );
};

export default CheckMarkWholeSvg;
