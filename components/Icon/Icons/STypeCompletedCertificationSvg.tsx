/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const STypeCompletedCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 41}
    height={props.height || 53}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 49 62"
  >
    <path
      d="M41.3139 42.7424C41.1123 42.7424 39.7527 42.7424 39.6547 42.7424C39.6547 42.8404 39.4761 43.693 39.3897 44.0617L44.1426 53.8498L39.4819 52.1215C39.3351 52.0656 39.1784 52.0403 39.0214 52.0472C38.8645 52.054 38.7106 52.0929 38.5692 52.1615C38.4278 52.23 38.302 52.3267 38.1994 52.4456C38.0967 52.5646 38.0195 52.7033 37.9725 52.8532L36.6474 57.0588L32.6146 48.4171C31.7658 48.0999 30.9349 47.7364 30.1258 47.3283L29.7744 47.7949L35.8409 60.6825C35.933 60.876 36.0772 61.0399 36.2573 61.1559C36.4374 61.272 36.6464 61.3354 36.8606 61.3393H36.9355C37.1662 61.3235 37.3868 61.2387 37.5687 61.0959C37.7505 60.9531 37.8852 60.7589 37.9552 60.5385L39.7987 54.6852L46.0323 57.0588C46.2478 57.1405 46.483 57.1557 46.7072 57.1024C46.9315 57.0491 47.1347 56.9297 47.2904 56.7597C47.4461 56.5897 47.5473 56.3769 47.5808 56.1488C47.6143 55.9207 47.5786 55.6878 47.4783 55.4802L41.3139 42.7424Z"
      fill="currentColor"
    />
    <path
      d="M19.4845 47.432C19.3693 47.5127 18.2171 48.0888 17.7216 48.3019L13.6082 57.03L12.2831 52.8244C12.233 52.6778 12.1538 52.5428 12.0503 52.4275C11.9468 52.3121 11.8211 52.2189 11.6808 52.1533C11.5404 52.0876 11.3882 52.051 11.2333 52.0456C11.0785 52.0401 10.9241 52.066 10.7795 52.1216L6.11875 53.8499L10.3647 45.093C10.1573 44.4593 9.86923 43.0881 9.84042 42.9672L8.81494 43.0017L2.7773 55.4745C2.67703 55.6821 2.64133 55.915 2.67483 56.1431C2.70832 56.3712 2.80947 56.584 2.9652 56.754C3.12092 56.924 3.32406 57.0434 3.54836 57.0967C3.77265 57.15 4.00778 57.1348 4.22334 57.0531L10.4569 54.6795L12.3004 60.5328C12.3698 60.7551 12.5049 60.9512 12.688 61.0952C12.8711 61.2392 13.0935 61.3243 13.3259 61.3393H13.4008C13.6196 61.3393 13.8338 61.277 14.0185 61.1596C14.2031 61.0423 14.3506 60.8748 14.4435 60.6768L20.2047 48.3768C20.0376 48.1406 19.5594 47.5127 19.4845 47.432Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M35.5009 46.1702C35.1609 46.3314 34.7883 46.4122 34.4121 46.4064C33.3696 46.2979 32.3641 45.9595 31.4681 45.4155C30.8631 45.0632 30.2028 44.8158 29.5151 44.6839H29.4518C29.1118 44.7645 28.432 45.669 28.0288 46.2106L28.0042 46.2434C27.1575 47.3763 26.1946 48.6648 24.7795 48.6648C23.368 48.6648 22.3829 47.4031 21.5129 46.2682L21.4779 46.2236C21.0294 45.6542 20.3863 44.8375 20.0439 44.7645C19.3574 44.9003 18.7015 45.1599 18.1081 45.5307L18.0801 45.5454C17.1036 46.0536 16.0937 46.5793 15.1008 46.5793C14.7434 46.5827 14.3894 46.508 14.0638 46.3603C12.7971 45.7897 12.4628 44.2584 12.1767 42.9473L12.1684 42.9094L12.1651 42.8936C12.0153 42.185 11.7918 41.1276 11.5059 40.9103C11.4483 40.8585 11.1775 40.7317 10.2961 40.7317H9.33397H8.28545C7.27149 40.7317 6.11927 40.5992 5.39913 39.7581C4.46934 38.6447 4.87182 37.0738 5.22803 35.6834L5.23206 35.6677C5.24084 35.6342 5.25008 35.5992 5.25967 35.5628C5.41998 34.9545 5.67841 33.974 5.53164 33.6859C5.37609 33.3806 4.32181 32.9312 3.68808 32.6662L3.65074 32.6504C2.33491 32.0956 0.856245 31.4721 0.525237 30.0967C0.191093 28.7083 1.24537 27.4697 2.17867 26.3751C2.19492 26.3555 2.21177 26.3353 2.22914 26.3144C2.63663 25.8248 3.3309 24.9907 3.3309 24.6813C3.3309 24.3635 2.57626 23.5147 2.15722 23.0433L2.13835 23.0221C1.18776 21.9448 0.104676 20.6946 0.410014 19.3177C0.715353 17.9408 2.13258 17.3071 3.50949 16.6964C3.55911 16.6734 3.61138 16.6493 3.66564 16.6243C4.30436 16.3298 5.21894 15.9081 5.33576 15.6479C5.4625 15.3656 5.15717 14.271 4.99585 13.6949C4.6041 12.3064 4.1605 10.7337 5.07651 9.58143C5.83122 8.64237 7.08138 8.50987 8.32001 8.50987H9.092H9.80638C10.8491 8.50987 11.0969 8.37736 11.1487 8.33703C11.4205 8.12186 11.6255 7.04959 11.7528 6.38386L11.7594 6.34945L11.7644 6.32309C12.0282 4.9411 12.3263 3.37858 13.6029 2.76604C13.9395 2.60659 14.3079 2.52582 14.6803 2.52984C15.7283 2.63409 16.7399 2.97063 17.6415 3.51499C18.2391 3.86683 18.8915 4.11609 19.5715 4.25241H19.6348C19.9747 4.17175 20.6545 3.26726 21.0578 2.72572L21.0763 2.70088C21.9244 1.56046 22.8829 0.271484 24.3071 0.271484C25.7416 0.271484 26.7268 1.53317 27.6024 2.6681L27.6376 2.71271C28.086 3.28214 28.7291 4.09879 29.0715 4.17175C29.7579 4.03602 30.4139 3.77637 31.0073 3.40553L31.0353 3.39091C32.0118 2.88266 33.0216 2.35701 34.0145 2.35701C34.3701 2.35442 34.722 2.42911 35.0458 2.57593C36.3204 3.15006 36.6509 4.69689 36.9382 6.04177L36.9412 6.05564L36.9445 6.07151C37.0943 6.78015 37.3178 7.83745 37.6037 8.05474C37.6613 8.10659 37.9321 8.23333 38.8136 8.23333C38.9162 8.23333 39.017 8.23647 39.1188 8.23964C39.3255 8.24607 39.5363 8.25264 39.7757 8.23333C40.1328 8.20453 40.4843 8.20453 40.8242 8.20453C41.8381 8.20453 42.9904 8.33703 43.7163 9.15511C44.646 10.2686 44.2436 11.8395 43.8874 13.2298L43.8833 13.2455C43.8745 13.279 43.8653 13.3141 43.8557 13.3505C43.6954 13.9587 43.437 14.9392 43.5837 15.2273C43.7393 15.5327 44.7936 15.982 45.4273 16.247L45.4846 16.2714C46.7954 16.8297 48.2608 17.4538 48.5902 18.8223C48.9243 20.2107 47.87 21.4493 46.9367 22.5439C46.9205 22.5635 46.9036 22.5837 46.8862 22.6046C46.4787 23.0942 45.7845 23.9283 45.7845 24.2377C45.7845 24.5555 46.5391 25.4043 46.9582 25.8756L46.977 25.8969C47.9276 26.9742 49.0107 28.2244 48.7054 29.6013C48.4 30.9782 46.9828 31.6119 45.6059 32.2226C44.9722 32.5049 43.8949 33.0061 43.7681 33.2884C43.6414 33.5707 43.9467 34.6653 44.108 35.2414C44.4998 36.6298 44.9434 38.2026 44.0274 39.3548C43.2727 40.2939 42.0225 40.4264 40.7838 40.4264H40.0119H39.2975C38.2547 40.4264 38.007 40.5589 37.9551 40.5992C37.6834 40.8144 37.4783 41.8866 37.3511 42.5524L37.3445 42.5868L37.3433 42.593C37.0786 43.9799 36.778 45.5547 35.5009 46.1702ZM22.227 30.8606C23.0065 31.1982 23.8812 31.367 24.851 31.367C25.5323 31.367 26.1829 31.2811 26.8028 31.1092C27.4289 30.9374 27.9844 30.6796 28.4693 30.3358C28.9542 29.9921 29.3378 29.5625 29.6202 29.0469C29.9087 28.5251 30.0529 27.9206 30.0529 27.2331C30.0529 26.5702 29.9301 26.0239 29.6846 25.5943C29.4452 25.1646 29.1383 24.8178 28.7639 24.5539C28.3895 24.2899 27.9997 24.0843 27.5946 23.937C27.1895 23.7897 26.8243 23.6731 26.499 23.5871L24.1236 22.9242C23.8229 22.8444 23.5252 22.7462 23.2305 22.6296C22.9421 22.5069 22.6996 22.3442 22.5032 22.1416C22.3068 21.933 22.2086 21.666 22.2086 21.3406C22.2086 20.9969 22.3221 20.6962 22.5492 20.4384C22.7825 20.1806 23.0863 19.9841 23.4607 19.8491C23.8413 19.7079 24.2525 19.6404 24.6944 19.6466C25.1487 19.6588 25.5752 19.7509 25.9742 19.9228C26.3793 20.0946 26.72 20.3463 26.9962 20.6777C27.2785 21.0031 27.4688 21.399 27.567 21.8654L29.9516 21.4511C29.7552 20.6348 29.4176 19.9381 28.9389 19.3611C28.4601 18.7842 27.8647 18.3453 27.1527 18.0445C26.4407 17.7376 25.6305 17.5811 24.7221 17.575C23.8259 17.5689 23.0096 17.71 22.273 17.9985C21.5426 18.287 20.9595 18.7228 20.5237 19.3059C20.094 19.8829 19.8792 20.5979 19.8792 21.4511C19.8792 22.0342 19.9743 22.5253 20.1646 22.9242C20.361 23.3171 20.6096 23.6424 20.9104 23.9002C21.2173 24.1518 21.5395 24.3513 21.8771 24.4986C22.2208 24.6398 22.54 24.7534 22.8346 24.8393L26.2596 25.8521C26.5051 25.9257 26.72 26.0147 26.9041 26.1191C27.0944 26.2173 27.2478 26.3277 27.3645 26.4505C27.4872 26.5733 27.5793 26.7144 27.6407 26.874C27.7021 27.0275 27.7327 27.1963 27.7327 27.3804C27.7327 27.7917 27.6008 28.1385 27.3368 28.4208C27.0791 28.7031 26.7415 28.918 26.3241 29.0653C25.9067 29.2126 25.4617 29.2863 24.9891 29.2863C24.1911 29.2863 23.4853 29.0714 22.8715 28.6418C22.2638 28.2121 21.8618 27.6044 21.6654 26.8188L19.3636 27.1687C19.4986 28.0341 19.8148 28.7829 20.3119 29.4152C20.8152 30.0412 21.4536 30.5231 22.227 30.8606Z"
      fill="currentColor"
    />
  </svg>
);
export default STypeCompletedCertificationSvg;
