/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const CheckMarkSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 27}
    height={props.height || 31}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 27 31"
  >
    <path
      fill="currentColor"
      stroke="currentColor"
      strokeWidth={0.4}
      d="M8.92 20.728 2.94 14.75.948 16.743l7.971 7.97L26 7.634 24.007 5.64 8.92 20.728Z"
    />
  </svg>
);
export default CheckMarkSvg;
