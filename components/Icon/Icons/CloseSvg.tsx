/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const CloseSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 11}
    height={props.height || 11}
    viewBox="0 0 11 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    preserveAspectRatio="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.66344 0.160233C9.66378 0.160334 9.66407 0.160493 9.66466 0.161086L10.4991 0.995497C10.4997 0.99609 10.4998 0.996364 10.4999 0.996712C10.5 0.997037 10.5 0.997384 10.4999 0.99771C10.4998 0.998042 10.4997 0.998317 10.4991 0.99891L6.33782 5.16016L10.4991 9.3214C10.4997 9.32198 10.4998 9.32227 10.4999 9.3226C10.5 9.32293 10.5 9.32329 10.4999 9.32362C10.4998 9.32393 10.4997 9.32422 10.4991 9.32482L9.66466 10.1592C9.66407 10.1598 9.66379 10.16 9.66344 10.1601C9.66312 10.1602 9.66277 10.1602 9.66245 10.1601C9.66211 10.16 9.66184 10.1598 9.66125 10.1592L5.5 5.99798L1.33875 10.1592C1.33818 10.1598 1.33789 10.16 1.33755 10.1601C1.33722 10.1602 1.33687 10.1602 1.33654 10.1601C1.33622 10.16 1.33593 10.1598 1.33534 10.1592L0.50093 9.32482C0.500337 9.32422 0.500178 9.32395 0.500077 9.3236C0.499977 9.32327 0.499977 9.32293 0.500077 9.3226C0.500178 9.32227 0.500337 9.32199 0.50093 9.3214L4.66216 5.16016L0.50093 0.99891C0.500337 0.998331 0.500178 0.998042 0.500077 0.99771C0.499974 0.99738 0.499974 0.997027 0.500077 0.996697C0.500178 0.996379 0.500337 0.99609 0.50093 0.995497L1.33534 0.161086C1.33593 0.160493 1.33621 0.160334 1.33656 0.160233C1.33688 0.160134 1.33723 0.160134 1.33755 0.160233C1.33789 0.160334 1.33816 0.160493 1.33875 0.161086L5.5 4.32232L9.66125 0.161086C9.66182 0.160493 9.66211 0.160334 9.66245 0.160233C9.66278 0.160131 9.66313 0.160131 9.66346 0.160233H9.66344Z"
      fill="currentColor"
    />
  </svg>
);
export default CloseSvg;
