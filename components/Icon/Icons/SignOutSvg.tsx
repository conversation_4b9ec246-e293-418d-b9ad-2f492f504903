/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const SignOutSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 35}
    height={props.height || 31}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 35 31"
  >
    <path
      fill="currentColor"
      d="M18.22 7.604a1.16 1.16 0 1 0-2.322 0v8.129a1.16 1.16 0 1 0 2.323 0v-8.13Zm-5.243 3.215a1.162 1.162 0 0 0-1.488-1.786 8.712 8.712 0 0 0 5.57 15.409 8.712 8.712 0 0 0 5.567-15.409 1.166 1.166 0 0 0-1.637.15c-.41.49-.34 1.226.15 1.636a6.368 6.368 0 0 1 2.304 4.914 6.387 6.387 0 1 1-10.47-4.914h.004Z"
    />
  </svg>
);
export default SignOutSvg;
