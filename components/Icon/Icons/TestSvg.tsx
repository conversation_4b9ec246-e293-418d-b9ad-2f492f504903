/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const TestSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 26}
    height={props.height || 28}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 26 28"
  >
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M19.88 2.75h1.816a3.566 3.566 0 0 1 3.562 3.564V24.05a3.568 3.568 0 0 1-3.563 3.563H4.423A3.568 3.568 0 0 1 .859 24.05V6.314A3.568 3.568 0 0 1 4.423 2.75H6.24A2.643 2.643 0 0 1 8.86.387h8.4a2.643 2.643 0 0 1 2.62 2.363ZM8.86 2.031c-.554 0-1.006.452-1.006 1.005V4.11c0 .554.452 1.005 1.005 1.005h8.4c.553 0 1.004-.451 1.004-1.005V3.036c0-.553-.451-1.005-1.005-1.005H8.86ZM21.695 25.97a1.92 1.92 0 0 0 1.918-1.919V6.314a1.92 1.92 0 0 0-1.92-1.92H19.88a2.643 2.643 0 0 1-2.62 2.364h-8.4a2.643 2.643 0 0 1-2.62-2.363H4.423a1.92 1.92 0 0 0-1.92 1.919V24.05a1.92 1.92 0 0 0 1.92 1.919h17.273ZM9.844 12.316h1.408a.704.704 0 1 1 0 1.408H9.844v1.408a.704.704 0 1 1-1.409 0v-1.408H7.027a.704.704 0 1 1 0-1.408h1.408v-1.408a.704.704 0 1 1 1.409 0v1.408Zm9.247 0h-4.225a.704.704 0 1 0 0 1.408h4.225a.704.704 0 1 0 0-1.408Zm-8.957 6.023a.704.704 0 0 1 .996.995l-.996.996.997.995a.704.704 0 0 1-.997.995l-.995-.995-.994.995a.704.704 0 0 1-.997-.995l.996-.995-.996-.996a.704.704 0 0 1 .996-.995l.995.995.995-.995Zm8.957.007h-4.225a.704.704 0 1 0 0 1.408h4.225a.704.704 0 1 0 0-1.408Zm-4.225 2.56h4.225a.704.704 0 1 1 0 1.408h-4.225a.704.704 0 1 1 0-1.408Z"
      clipRule="evenodd"
    />
  </svg>
);
export default TestSvg;
