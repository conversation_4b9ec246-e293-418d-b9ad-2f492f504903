/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const FailIconSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 104}
    height={props.height || 104}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 104 104"
  >
    <rect
      x="0.613281"
      y="0.620605"
      width="102.775"
      height="102.775"
      rx="51.3874"
      stroke="white"
    />
    <rect
      x="61.9189"
      y="40.1587"
      width="3.34549"
      height="30.1692"
      rx="1.67274"
      transform="rotate(45 61.9189 40.1587)"
      fill="white"
    />
    <rect
      x="63.4141"
      y="61.4917"
      width="3.34549"
      height="30.1692"
      rx="1.67274"
      transform="rotate(135 63.4141 61.4917)"
      fill="white"
    />
  </svg>
);
export default FailIconSvg;
