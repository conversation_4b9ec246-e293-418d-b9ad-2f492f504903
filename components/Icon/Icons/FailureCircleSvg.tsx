/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const FailureCircleSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 145}
    height={props.height || 145}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 145 145"
  >
    <rect
      width={139}
      height={139}
      x={3.275}
      y={2.531}
      stroke="currentColor"
      strokeWidth={5}
      rx={69.5}
    />
    <rect
      width={4.083}
      height={36.821}
      x={84.881}
      y={57.569}
      fill="currentColor"
      rx={2.042}
      transform="rotate(45 84.88 57.57)"
    />
    <rect
      width={4.083}
      height={36.821}
      x={86.707}
      y={83.606}
      fill="currentColor"
      rx={2.042}
      transform="rotate(135 86.707 83.606)"
    />
  </svg>
);
export default FailureCircleSvg;
