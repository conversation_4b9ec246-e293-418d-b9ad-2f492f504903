/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const CalculatorArrowSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 151}
    height={props.height || 127}
    viewBox="0 0 151 127"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M150 63.8206C150 60.8286 147.485 58.5312 142.463 53.9227C133.984 46.1571 122.026 35.4199 112.547 27.7514C106.076 22.5101 97.937 16.4298 90.414 10.9477C80.819 3.95934 76.0215 0.465316 71.927 2.30874C67.8252 4.15217 67.6367 9.9161 67.2599 21.4508C67.1077 26.1556 66.9555 31.1974 66.8178 36.3079C59.1866 35.9502 50.6206 35.6475 43.6699 35.6475C34.9154 35.6475 23.5813 36.1359 14.7912 36.5967C8.305 36.9338 5.0583 37.1057 3.0293 39.1417C1.0002 41.1776 1 44.2935 1 50.5251V75.3565C1 81.5882 1 84.704 3.0293 86.7399C5.0585 88.7758 8.3052 88.9478 14.7912 89.2848C23.5819 89.7457 34.9169 90.234 43.6699 90.234C50.6054 90.234 59.157 89.9314 66.7733 89.5737C66.9182 95.2827 67.0849 100.943 67.2589 106.185C67.6357 117.727 67.8241 123.49 71.9259 125.334C76.0205 127.177 80.8181 123.683 90.413 116.695C97.936 111.213 106.074 105.132 112.546 99.891C122.025 92.2218 133.984 81.4851 142.462 73.7199C147.484 69.1114 150 66.8126 150 63.8206Z"
      fill="#73F64A"
      stroke="#518541"
      strokeWidth="2"
    />
  </svg>
);
export default CalculatorArrowSvg;
