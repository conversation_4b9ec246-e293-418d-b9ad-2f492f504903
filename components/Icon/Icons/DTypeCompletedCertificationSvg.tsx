/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const DTypeCompletedCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 41}
    height={props.height || 53}
    viewBox="0 0 49 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M41.3139 42.8782C41.1123 42.8782 39.7527 42.8782 39.6547 42.8782C39.6547 42.9761 39.4761 43.8288 39.3897 44.1975L44.1426 53.9856L39.4819 52.2573C39.3351 52.2013 39.1784 52.176 39.0214 52.1829C38.8645 52.1898 38.7106 52.2287 38.5692 52.2972C38.4278 52.3657 38.302 52.4624 38.1994 52.5814C38.0967 52.7003 38.0195 52.839 37.9725 52.9889L36.6474 57.1945L32.6146 48.5529C31.7658 48.2356 30.9349 47.8721 30.1258 47.464L29.7744 47.9307L35.8409 60.8183C35.933 61.0117 36.0772 61.1757 36.2573 61.2917C36.4374 61.4077 36.6464 61.4712 36.8606 61.475H36.9355C37.1662 61.4593 37.3868 61.3745 37.5687 61.2317C37.7505 61.0889 37.8852 60.8946 37.9552 60.6742L39.7987 54.8209L46.0323 57.1945C46.2478 57.2763 46.483 57.2915 46.7072 57.2381C46.9315 57.1848 47.1347 57.0655 47.2904 56.8955C47.4461 56.7255 47.5473 56.5127 47.5808 56.2846C47.6143 56.0565 47.5786 55.8236 47.4783 55.616L41.3139 42.8782Z"
      fill="currentColor"
    />
    <path
      d="M19.4845 47.5678C19.3693 47.6484 18.2171 48.2245 17.7216 48.4377L13.6082 57.1658L12.2831 52.9602C12.233 52.8135 12.1538 52.6785 12.0503 52.5632C11.9468 52.4479 11.8211 52.3546 11.6808 52.289C11.5404 52.2234 11.3882 52.1868 11.2333 52.1813C11.0785 52.1759 10.9241 52.2017 10.7795 52.2573L6.11875 53.9856L10.3647 45.2288C10.1573 44.595 9.86923 43.2239 9.84042 43.1029L8.81494 43.1375L2.7773 55.6103C2.67703 55.8179 2.64133 56.0508 2.67483 56.2789C2.70832 56.5069 2.80947 56.7197 2.9652 56.8897C3.12092 57.0597 3.32406 57.1791 3.54836 57.2324C3.77265 57.2857 4.00778 57.2705 4.22334 57.1888L10.4569 54.8152L12.3004 60.6685C12.3698 60.8908 12.5049 61.0869 12.688 61.2309C12.8711 61.3749 13.0935 61.46 13.3259 61.4751H13.4008C13.6196 61.475 13.8338 61.4127 14.0185 61.2954C14.2031 61.1781 14.3506 61.0106 14.4435 60.8125L20.2047 48.5126C20.0376 48.2764 19.5594 47.6484 19.4845 47.5678Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M35.5009 46.306C35.1609 46.4671 34.7883 46.548 34.4121 46.5422C33.3696 46.4337 32.3641 46.0952 31.4681 45.5513C30.8631 45.1989 30.2028 44.9515 29.5151 44.8196H29.4518C29.1118 44.9003 28.432 45.8047 28.0288 46.3463L28.0042 46.3791C27.1575 47.512 26.1946 48.8005 24.7795 48.8005C23.368 48.8005 22.3829 47.5388 21.5129 46.4039L21.4779 46.3594C21.0294 45.7899 20.3863 44.9732 20.0439 44.9003C19.3574 45.036 18.7015 45.2956 18.1081 45.6665L18.0801 45.6811C17.1036 46.1894 16.0937 46.715 15.1008 46.715C14.7434 46.7184 14.3894 46.6437 14.0638 46.4961C12.7971 45.9255 12.4628 44.3941 12.1767 43.083L12.1684 43.0452L12.1651 43.0294C12.0153 42.3207 11.7918 41.2634 11.5059 41.0461C11.4483 40.9942 11.1775 40.8675 10.2961 40.8675H9.33397H8.28545C7.27149 40.8675 6.11927 40.735 5.39913 39.8939C4.46934 38.7804 4.87182 37.2095 5.22803 35.8192L5.23206 35.8035C5.24084 35.77 5.25008 35.7349 5.25967 35.6985C5.41998 35.0903 5.67841 34.1098 5.53164 33.8216C5.37609 33.5163 4.32181 33.0669 3.68808 32.8019L3.65074 32.7862C2.33491 32.2314 0.856245 31.6079 0.525237 30.2325C0.191093 28.844 1.24537 27.6054 2.17867 26.5108C2.19492 26.4913 2.21177 26.471 2.22914 26.4501C2.63663 25.9606 3.3309 25.1265 3.3309 24.817C3.3309 24.4992 2.57626 23.6504 2.15722 23.1791L2.13835 23.1578C1.18776 22.0805 0.104676 20.8304 0.410014 19.4534C0.715353 18.0765 2.13258 17.4428 3.50949 16.8321C3.55911 16.8091 3.61138 16.785 3.66564 16.76C4.30436 16.4655 5.21894 16.0438 5.33576 15.7836C5.4625 15.5013 5.15717 14.4067 4.99585 13.8306C4.6041 12.4422 4.1605 10.8694 5.07651 9.71718C5.83122 8.77812 7.08138 8.64561 8.32001 8.64561H9.092H9.80638C10.8491 8.64561 11.0969 8.5131 11.1487 8.47278C11.4205 8.2576 11.6255 7.18534 11.7528 6.51961L11.7594 6.48519L11.7644 6.45883C12.0282 5.07684 12.3263 3.51433 13.6029 2.90179C13.9395 2.74233 14.3079 2.66156 14.6803 2.66558C15.7283 2.76983 16.7399 3.10637 17.6415 3.65073C18.2391 4.00257 18.8915 4.25183 19.5715 4.38815H19.6348C19.9747 4.3075 20.6545 3.403 21.0578 2.86146L21.0763 2.83662C21.9244 1.6962 22.8829 0.407227 24.3071 0.407227C25.7416 0.407227 26.7268 1.66891 27.6024 2.80385L27.6376 2.84845C28.086 3.41788 28.7291 4.23453 29.0715 4.3075C29.7579 4.17176 30.4139 3.91211 31.0073 3.54127L31.0353 3.52665C32.0118 3.0184 33.0216 2.49275 34.0145 2.49275C34.3701 2.49016 34.722 2.56486 35.0458 2.71167C36.3204 3.28581 36.6509 4.83263 36.9382 6.17751L36.9412 6.19138L36.9445 6.20725C37.0943 6.91589 37.3178 7.97319 37.6037 8.19048C37.6613 8.24233 37.9321 8.36908 38.8136 8.36908C38.9162 8.36908 39.017 8.37221 39.1188 8.37538C39.3255 8.38181 39.5363 8.38838 39.7757 8.36908C40.1328 8.34027 40.4843 8.34027 40.8242 8.34027C41.8381 8.34027 42.9904 8.47278 43.7163 9.29085C44.646 10.4043 44.2436 11.9752 43.8874 13.3655L43.8833 13.3812C43.8745 13.4147 43.8653 13.4498 43.8557 13.4862C43.6954 14.0945 43.437 15.075 43.5837 15.3631C43.7393 15.6684 44.7936 16.1178 45.4273 16.3828L45.4846 16.4072C46.7954 16.9654 48.2608 17.5895 48.5902 18.958C48.9243 20.3464 47.87 21.5851 46.9367 22.6797C46.9205 22.6992 46.9036 22.7195 46.8862 22.7403C46.4787 23.2299 45.7845 24.064 45.7845 24.3734C45.7845 24.6912 46.5391 25.54 46.9582 26.0114L46.977 26.0326C47.9276 27.11 49.0107 28.3601 48.7054 29.737C48.4 31.1139 46.9828 31.7476 45.6059 32.3583C44.9722 32.6406 43.8949 33.1418 43.7681 33.4241C43.6414 33.7064 43.9467 34.801 44.108 35.3771C44.4998 36.7656 44.9434 38.3384 44.0274 39.4906C43.2727 40.4296 42.0225 40.5621 40.7838 40.5621H40.0119H39.2975C38.2547 40.5621 38.007 40.6946 37.9551 40.735C37.6834 40.9501 37.4783 42.0224 37.3511 42.6881L37.3445 42.7226L37.3433 42.7287C37.0786 44.1157 36.778 45.6904 35.5009 46.306ZM19.548 17.9685V31.2266H23.774C23.8845 31.2266 24.1055 31.2235 24.4369 31.2174C24.7684 31.2112 25.0845 31.1897 25.3853 31.1529C26.4349 31.0179 27.3218 30.6465 28.0461 30.0389C28.7765 29.4251 29.3289 28.6455 29.7033 27.7003C30.0777 26.755 30.2649 25.7208 30.2649 24.5975C30.2649 23.4743 30.0777 22.44 29.7033 21.4948C29.3289 20.5495 28.7765 19.7731 28.0461 19.1654C27.3218 18.5516 26.4349 18.1772 25.3853 18.0422C25.0784 18.0054 24.7592 17.9839 24.4277 17.9777C24.1024 17.9716 23.8845 17.9685 23.774 17.9685H19.548ZM23.774 29.1366H21.8037V20.0585H23.774C23.9582 20.0585 24.1914 20.0646 24.4738 20.0769C24.7623 20.0831 25.017 20.1107 25.2379 20.1598C25.8517 20.2764 26.3551 20.5495 26.7479 20.9792C27.1469 21.4089 27.4415 21.9398 27.6318 22.572C27.822 23.2042 27.9172 23.8794 27.9172 24.5975C27.9172 25.2911 27.822 25.954 27.6318 26.5863C27.4476 27.2185 27.1591 27.7555 26.7663 28.1975C26.3735 28.6394 25.864 28.9187 25.2379 29.0353C25.017 29.0783 24.7623 29.1059 24.4738 29.1182C24.1914 29.1304 23.9582 29.1366 23.774 29.1366Z"
      fill="currentColor"
    />
  </svg>
);
export default DTypeCompletedCertificationSvg;
