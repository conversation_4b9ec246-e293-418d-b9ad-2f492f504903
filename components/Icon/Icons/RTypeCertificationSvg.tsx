/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const RTypeCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 49}
    height={props.height || 62}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 49 62"
  >
    <path
      fill="currentColor"
      d="M34.412 46.339c.376.005.749-.075 1.089-.237 1.279-.616 1.579-2.195 1.843-3.583.127-.663.335-1.769.611-1.988.052-.04.3-.172 1.343-.172h1.486c1.239 0 2.489-.133 3.243-1.072.916-1.152.473-2.725.081-4.114-.161-.576-.467-1.67-.34-1.953.127-.282 1.204-.783 1.838-1.065 1.377-.611 2.794-1.245 3.1-2.622.305-1.377-.778-2.627-1.729-3.704-.415-.467-1.192-1.337-1.192-1.66 0-.322.754-1.215 1.152-1.693.933-1.095 1.987-2.333 1.653-3.722-.334-1.388-1.838-2.01-3.163-2.575-.633-.265-1.688-.714-1.843-1.02-.156-.305.144-1.388.3-1.981.356-1.395.766-2.973-.168-4.09-.726-.819-1.878-.951-2.892-.951-.34 0-.691 0-1.048.028-.358.03-.651 0-.962 0-.882 0-1.153-.126-1.21-.178-.288-.22-.513-1.29-.663-2-.288-1.347-.616-2.903-1.895-3.479a2.454 2.454 0 0 0-1.031-.219c-1.003 0-2.023.536-3.008 1.049-.593.37-1.25.63-1.936.766-.35-.075-1.02-.933-1.469-1.504-.875-1.135-1.86-2.396-3.295-2.396-1.434 0-2.396 1.307-3.25 2.454-.402.541-1.082 1.446-1.422 1.527h-.064a6.21 6.21 0 0 1-1.93-.738 7.086 7.086 0 0 0-2.96-.985 2.454 2.454 0 0 0-1.078.236c-1.285.617-1.579 2.195-1.844 3.584-.126.662-.334 1.768-.61 1.987-.052.04-.3.173-1.343.173H8.32c-1.239 0-2.489.133-3.243 1.072-.917 1.152-.473 2.725-.081 4.113.161.576.466 1.67.34 1.953-.127.282-1.193.755-1.827 1.048C2.133 17.24.715 17.873.41 19.25c-.305 1.377.778 2.627 1.728 3.704.415.467 1.193 1.337 1.193 1.66 0 .322-.755 1.215-1.152 1.693C1.245 27.402.19 28.64.525 30.03c.334 1.388 1.838 2.01 3.163 2.57.634.264 1.688.714 1.844 1.019.155.305-.144 1.389-.3 1.982-.357 1.394-.766 2.973.167 4.09.72.841 1.872.974 2.886.974h2.011c.882 0 1.152.127 1.21.178.288.22.513 1.291.662 2 .289 1.319.617 2.874 1.896 3.45.325.148.68.223 1.037.22 1.002 0 2.022-.536 3.007-1.05.593-.37 1.25-.63 1.936-.765.351.075 1.02.933 1.469 1.503.87 1.135 1.855 2.397 3.266 2.397 1.43 0 2.397-1.314 3.25-2.454.403-.542 1.083-1.446 1.423-1.527h.063c.688.132 1.348.38 1.953.732a7.088 7.088 0 0 0 2.944.99Zm-5.456-3.97c-1.152.27-1.97 1.348-2.748 2.391-.363.484-1.117 1.492-1.428 1.527-.312.034-1.084-1.02-1.452-1.492-.801-1.026-1.654-2.086-2.806-2.334a2.563 2.563 0 0 0-.53-.051c-.985 0-1.988.524-2.961 1.037-.596.37-1.25.635-1.936.783h-.063c-.248-.184-.484-1.308-.611-1.907-.27-1.285-.576-2.615-1.52-3.359a4.23 4.23 0 0 0-2.61-.65c-.346 0-.704 0-1.06.028-.358.029-.646 0-.951 0-.373.05-.752.011-1.106-.115-.162-.265.144-1.47.293-2.045.323-1.25.657-2.54.127-3.595-.53-1.055-1.792-1.59-3.007-2.103-.576-.248-1.688-.714-1.82-.98 0-.316.754-1.215 1.152-1.693.846-.997 1.728-2.022 1.728-3.215 0-1.192-.91-2.19-1.775-3.163-.426-.49-1.21-1.377-1.21-1.67.133-.289 1.216-.772 1.792-1.032 1.21-.535 2.46-1.094 2.973-2.172.513-1.077.15-2.304-.196-3.572-.161-.576-.501-1.791-.334-2.056.035-.035.254-.208 1.44-.208H9.83a4.379 4.379 0 0 0 2.777-.68c.945-.754 1.193-2.08 1.44-3.364.116-.616.335-1.763.577-1.936h.075c.68.137 1.333.388 1.93.744a7.052 7.052 0 0 0 2.938.985c.194 0 .387-.021.576-.064 1.152-.27 1.97-1.348 2.748-2.39.363-.484 1.117-1.498 1.429-1.527.31-.029 1.083 1.02 1.451 1.492.813 1.048 1.665 2.108 2.818 2.356.174.036.352.053.53.052.985 0 1.987-.524 2.96-1.037a6.09 6.09 0 0 1 1.937-.783h.063c.248.184.484 1.307.61 1.906.271 1.285.577 2.616 1.521 3.36a4.224 4.224 0 0 0 2.61.65c.346 0 .703 0 1.06-.029.358-.028.646 0 .95 0 .375-.043.755.005 1.107.139.161.265-.144 1.469-.294 2.045-.322 1.25-.657 2.54-.127 3.595.53 1.054 1.792 1.59 3.002 2.103.576.247 1.688.714 1.82.985 0 .317-.754 1.215-1.152 1.694-.846.996-1.728 2.022-1.728 3.214 0 1.193.91 2.19 1.774 3.163.427.478 1.216 1.365 1.21 1.66-.132.287-1.215.771-1.791 1.03-1.21.536-2.46 1.095-2.973 2.172-.513 1.078-.15 2.334.196 3.572.161.576.5 1.798.334 2.057-.035.035-.254.207-1.44.207H39.274a4.384 4.384 0 0 0-2.776.68c-.945.755-1.193 2.08-1.435 3.365-.115.616-.334 1.763-.576 1.935h-.075a6.103 6.103 0 0 1-1.93-.748 7.05 7.05 0 0 0-2.938-.986 2.586 2.586 0 0 0-.588.03Z"
    />
    <path
      fill="currentColor"
      d="M41.314 42.675h-1.66c0 .098-.178.95-.264 1.319l4.753 9.788-4.661-1.728a1.152 1.152 0 0 0-1.51.731l-1.325 4.206-4.032-8.642c-.85-.317-1.68-.68-2.49-1.089l-.35.467 6.066 12.888a1.153 1.153 0 0 0 1.02.656h.075a1.152 1.152 0 0 0 1.02-.8l1.843-5.854 6.233 2.374a1.152 1.152 0 0 0 1.446-1.579l-6.164-12.737ZM19.485 47.364c-.115.08-1.267.657-1.763.87l-4.113 8.728-1.325-4.205a1.151 1.151 0 0 0-1.504-.703l-4.66 1.728 4.245-8.757c-.207-.633-.495-2.005-.524-2.126l-1.026.035-6.037 12.473a1.152 1.152 0 0 0 1.446 1.578l6.233-2.373 1.844 5.853a1.152 1.152 0 0 0 1.025.807h.075a1.153 1.153 0 0 0 1.043-.663l5.761-12.3a22.041 22.041 0 0 0-.72-.945ZM19.917 31.023V17.765h5.478c.129 0 .294.006.497.018.203.006.39.025.562.056.767.116 1.4.37 1.896.764.504.393.875.89 1.114 1.491.246.596.369 1.259.369 1.989 0 1.08-.274 2.01-.82 2.79-.546.773-1.384 1.252-2.513 1.436l-.949.083h-3.415v4.63h-2.22Zm7.614 0-2.615-5.395 2.256-.497 2.872 5.892h-2.513Zm-5.395-6.703h3.167c.122 0 .26-.006.414-.018.154-.012.295-.037.424-.074.368-.092.656-.255.865-.488.215-.233.365-.497.451-.792.092-.294.138-.583.138-.865 0-.282-.046-.57-.138-.865a1.964 1.964 0 0 0-.451-.802c-.209-.233-.497-.395-.865-.487a1.796 1.796 0 0 0-.424-.065 5.234 5.234 0 0 0-.414-.018h-3.167v4.474Z"
    />
  </svg>
);
export default RTypeCertificationSvg;
