/* eslint-disable react/destructuring-assignment */
import * as React from 'react';

const RTypeCompletedCertificationSvg = (
  props: React.SVGProps<SVGSVGElement>
) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 49}
    height={props.height || 62}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 49 62"
  >
    <path
      fill="currentColor"
      d="M41.314 42.946h-1.66c0 .098-.178.95-.264 1.32l4.753 9.788-4.661-1.729a1.152 1.152 0 0 0-1.51.732l-1.325 4.205-4.032-8.641c-.85-.318-1.68-.681-2.49-1.09l-.35.468 6.066 12.887a1.152 1.152 0 0 0 1.02.657h.075a1.151 1.151 0 0 0 1.02-.8l1.843-5.854 6.233 2.373a1.152 1.152 0 0 0 1.446-1.578l-6.164-12.738ZM19.485 47.636c-.115.08-1.267.656-1.763.87l-4.113 8.728-1.325-4.206a1.151 1.151 0 0 0-1.504-.703l-4.66 1.729 4.245-8.757c-.207-.634-.495-2.005-.524-2.126l-1.026.035-6.037 12.472a1.152 1.152 0 0 0 1.446 1.579l6.233-2.374 1.844 5.853a1.152 1.152 0 0 0 1.025.807h.075a1.153 1.153 0 0 0 1.043-.663l5.761-12.3a22.041 22.041 0 0 0-.72-.944Z"
    />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M35.5 46.374c-.34.161-.712.242-1.088.236a7.086 7.086 0 0 1-2.944-.99 6.203 6.203 0 0 0-1.953-.732h-.063c-.34.08-1.02.985-1.423 1.526l-.025.033c-.847 1.133-1.81 2.421-3.225 2.421-1.411 0-2.396-1.261-3.266-2.396l-.035-.045c-.449-.57-1.092-1.386-1.434-1.459a5.761 5.761 0 0 0-1.936.766l-.028.015c-.976.508-1.986 1.034-2.98 1.034a2.455 2.455 0 0 1-1.036-.219c-1.267-.57-1.601-2.102-1.887-3.413l-.009-.038-.003-.016c-.15-.708-.373-1.766-.66-1.983-.057-.052-.328-.179-1.209-.179H8.285c-1.014 0-2.166-.132-2.886-.973-.93-1.114-.527-2.685-.171-4.075l.004-.016.028-.105c.16-.608.418-1.588.272-1.877-.156-.305-1.21-.754-1.844-1.02l-.037-.015C2.335 32.3.856 31.676.525 30.3c-.334-1.388.72-2.627 1.654-3.721l.05-.061c.408-.49 1.102-1.324 1.102-1.633 0-.318-.755-1.167-1.174-1.638l-.019-.021C1.188 22.148.105 20.898.41 19.52c.305-1.377 1.723-2.01 3.1-2.621l.156-.072c.638-.295 1.553-.716 1.67-.976.127-.283-.179-1.377-.34-1.954-.392-1.388-.836-2.96.08-4.113.755-.939 2.005-1.072 3.244-1.072h1.486c1.043 0 1.29-.132 1.343-.172.272-.216.477-1.288.604-1.954l.006-.034.005-.026c.264-1.382.562-2.945 1.839-3.557.337-.16.705-.24 1.077-.237 1.048.105 2.06.441 2.962.986a6.21 6.21 0 0 0 1.93.737h.063c.34-.08 1.02-.985 1.423-1.527l.018-.024c.848-1.14 1.807-2.43 3.231-2.43 1.435 0 2.42 1.262 3.295 2.397l.036.044c.448.57 1.091 1.386 1.433 1.46a5.762 5.762 0 0 0 1.936-.767l.028-.014c.977-.509 1.987-1.034 2.98-1.034.355-.003.707.072 1.03.219 1.275.574 1.606 2.12 1.893 3.465l.003.014.003.016c.15.709.374 1.766.66 1.983.057.052.328.179 1.21.179.102 0 .203.003.305.006.206.007.417.013.657-.006.357-.029.708-.029 1.048-.029 1.014 0 2.166.133 2.892.95.93 1.114.528 2.685.171 4.075l-.004.016-.027.105c-.16.608-.419 1.589-.272 1.877.155.305 1.21.755 1.843 1.02l.058.024c1.31.558 2.776 1.182 3.105 2.55.334 1.39-.72 2.628-1.653 3.723l-.05.06c-.408.49-1.102 1.324-1.102 1.633 0 .318.754 1.167 1.173 1.638l.019.021c.95 1.078 2.034 2.328 1.728 3.705-.305 1.377-1.722 2.01-3.1 2.621-.633.282-1.71.784-1.837 1.066-.127.282.179 1.377.34 1.953.392 1.388.835 2.961-.08 4.113-.755.94-2.005 1.072-3.244 1.072h-1.487c-1.042 0-1.29.133-1.342.173-.272.215-.477 1.287-.604 1.953l-.007.034v.007c-.265 1.387-.566 2.961-1.843 3.577ZM20.404 17.742V31h2.219v-4.631h3.15L28.018 31h2.514l-2.449-5.023A3.265 3.265 0 0 0 29.5 24.85c.546-.78.819-1.71.819-2.79 0-.73-.123-1.393-.368-1.989a3.498 3.498 0 0 0-1.114-1.491c-.498-.393-1.13-.648-1.897-.764-.172-.031-.36-.05-.562-.056a8.517 8.517 0 0 0-.497-.018h-5.478Zm5.386 6.555h-3.167v-4.474h3.167c.123 0 .26.006.414.018.154.006.295.028.424.065.368.092.657.254.865.488.215.233.366.5.451.8.093.295.139.584.139.866 0 .282-.046.57-.139.866a1.971 1.971 0 0 1-.45.791c-.21.234-.498.396-.866.488-.129.037-.27.062-.424.074a5.234 5.234 0 0 1-.414.018Z"
      clipRule="evenodd"
    />
  </svg>
);
export default RTypeCompletedCertificationSvg;
