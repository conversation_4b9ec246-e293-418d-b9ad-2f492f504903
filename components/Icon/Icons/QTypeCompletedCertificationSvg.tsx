/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const QTypeCompletedCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 41}
    height={props.height || 53}
    viewBox="0 0 49 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M41.3139 42.946C41.1123 42.946 39.7527 42.946 39.6547 42.946C39.6547 43.044 39.4761 43.8966 39.3897 44.2653L44.1426 54.0535L39.4819 52.3251C39.3351 52.2692 39.1784 52.2439 39.0214 52.2508C38.8645 52.2577 38.7106 52.2965 38.5692 52.3651C38.4278 52.4336 38.302 52.5303 38.1994 52.6493C38.0967 52.7682 38.0195 52.9069 37.9725 53.0568L36.6474 57.2624L32.6146 48.6207C31.7658 48.3035 30.9349 47.94 30.1258 47.5319L29.7744 47.9985L35.8409 60.8861C35.933 61.0796 36.0772 61.2435 36.2573 61.3595C36.4374 61.4756 36.6464 61.5391 36.8606 61.5429H36.9355C37.1662 61.5272 37.3868 61.4424 37.5687 61.2995C37.7505 61.1567 37.8852 60.9625 37.9552 60.7421L39.7987 54.8888L46.0323 57.2624C46.2478 57.3441 46.483 57.3593 46.7072 57.306C46.9315 57.2527 47.1347 57.1333 47.2904 56.9633C47.4461 56.7933 47.5473 56.5805 47.5808 56.3524C47.6143 56.1243 47.5786 55.8914 47.4783 55.6839L41.3139 42.946Z"
      fill="currentColor"
    />
    <path
      d="M19.4845 47.6356C19.3693 47.7163 18.2171 48.2924 17.7216 48.5056L13.6082 57.2336L12.2831 53.028C12.233 52.8814 12.1538 52.7464 12.0503 52.6311C11.9468 52.5158 11.8211 52.4225 11.6808 52.3569C11.5404 52.2913 11.3882 52.2546 11.2333 52.2492C11.0785 52.2437 10.9241 52.2696 10.7795 52.3252L6.11875 54.0535L10.3647 45.2966C10.1573 44.6629 9.86923 43.2918 9.84042 43.1708L8.81494 43.2053L2.7773 55.6781C2.67703 55.8857 2.64133 56.1186 2.67483 56.3467C2.70832 56.5748 2.80947 56.7876 2.9652 56.9576C3.12092 57.1276 3.32406 57.247 3.54836 57.3003C3.77265 57.3536 4.00778 57.3384 4.22334 57.2567L10.4569 54.8831L12.3004 60.7364C12.3698 60.9587 12.5049 61.1548 12.688 61.2988C12.8711 61.4428 13.0935 61.5279 13.3259 61.5429H13.4008C13.6196 61.5429 13.8338 61.4806 14.0185 61.3632C14.2031 61.2459 14.3506 61.0785 14.4435 60.8804L20.2047 48.5805C20.0376 48.3442 19.5594 47.7163 19.4845 47.6356Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M35.5009 46.3738C35.1609 46.535 34.7883 46.6158 34.4121 46.61C33.3696 46.5015 32.3641 46.1631 31.4681 45.6191C30.8631 45.2668 30.2028 45.0194 29.5151 44.8875H29.4518C29.1118 44.9681 28.432 45.8726 28.0288 46.4142L28.0042 46.447C27.1575 47.5799 26.1946 48.8684 24.7795 48.8684C23.368 48.8684 22.3829 47.6067 21.5129 46.4718L21.4779 46.4272C21.0294 45.8578 20.3863 45.0411 20.0439 44.9681C19.3574 45.1039 18.7015 45.3635 18.1081 45.7344L18.0801 45.749C17.1036 46.2572 16.0937 46.7829 15.1008 46.7829C14.7434 46.7863 14.3894 46.7116 14.0638 46.564C12.7971 45.9933 12.4628 44.462 12.1767 43.1509L12.1684 43.1131L12.1651 43.0972C12.0153 42.3886 11.7918 41.3312 11.5059 41.1139C11.4483 41.0621 11.1775 40.9354 10.2961 40.9354H9.33397H8.28545C7.27149 40.9354 6.11927 40.8028 5.39913 39.9617C4.46934 38.8483 4.87182 37.2774 5.22803 35.8871L5.23206 35.8713C5.24084 35.8378 5.25008 35.8028 5.25967 35.7664C5.41998 35.1581 5.67841 34.1776 5.53164 33.8895C5.37609 33.5842 4.32181 33.1348 3.68808 32.8698L3.65074 32.8541C2.33491 32.2992 0.856245 31.6757 0.525237 30.3003C0.191093 28.9119 1.24537 27.6733 2.17867 26.5787C2.19492 26.5591 2.21177 26.5389 2.22914 26.518C2.63663 26.0284 3.3309 25.1943 3.3309 24.8849C3.3309 24.5671 2.57626 23.7183 2.15722 23.2469L2.13835 23.2257C1.18776 22.1484 0.104676 20.8982 0.410014 19.5213C0.715353 18.1444 2.13258 17.5107 3.50949 16.9C3.55911 16.877 3.61138 16.8529 3.66564 16.8279C4.30436 16.5334 5.21894 16.1117 5.33576 15.8515C5.4625 15.5692 5.15717 14.4746 4.99585 13.8985C4.6041 12.51 4.1605 10.9373 5.07651 9.78505C5.83122 8.84599 7.08138 8.71348 8.32001 8.71348H9.092H9.80638C10.8491 8.71348 11.0969 8.58098 11.1487 8.54065C11.4205 8.32547 11.6255 7.25321 11.7528 6.58748L11.7594 6.55307L11.7644 6.5267C12.0282 5.14471 12.3263 3.5822 13.6029 2.96966C13.9395 2.81021 14.3079 2.72943 14.6803 2.73345C15.7283 2.8377 16.7399 3.17424 17.6415 3.7186C18.2391 4.07044 18.8915 4.3197 19.5715 4.45602H19.6348C19.9747 4.37537 20.6545 3.47087 21.0578 2.92933L21.0763 2.9045C21.9244 1.76407 22.8829 0.475098 24.3071 0.475098C25.7416 0.475098 26.7268 1.73678 27.6024 2.87172L27.6376 2.91632C28.086 3.48575 28.7291 4.3024 29.0715 4.37537C29.7579 4.23963 30.4139 3.97998 31.0073 3.60914L31.0353 3.59453C32.0118 3.08627 33.0216 2.56062 34.0145 2.56062C34.3701 2.55803 34.722 2.63273 35.0458 2.77954C36.3204 3.35368 36.6509 4.9005 36.9382 6.24538L36.9412 6.25925L36.9445 6.27512C37.0943 6.98376 37.3178 8.04106 37.6037 8.25835C37.6613 8.3102 37.9321 8.43695 38.8136 8.43695C38.9162 8.43695 39.017 8.44008 39.1188 8.44325C39.3255 8.44969 39.5363 8.45625 39.7757 8.43695C40.1328 8.40814 40.4843 8.40814 40.8242 8.40814C41.8381 8.40814 42.9904 8.54065 43.7163 9.35872C44.646 10.4722 44.2436 12.0431 43.8874 13.4334L43.8833 13.4491C43.8745 13.4826 43.8653 13.5177 43.8557 13.5541C43.6954 14.1623 43.437 15.1428 43.5837 15.4309C43.7393 15.7363 44.7936 16.1856 45.4273 16.4506L45.4846 16.475C46.7954 17.0333 48.2608 17.6574 48.5902 19.0259C48.9243 20.4143 47.87 21.6529 46.9367 22.7475C46.9205 22.7671 46.9036 22.7873 46.8862 22.8082C46.4787 23.2978 45.7845 24.1319 45.7845 24.4413C45.7845 24.7591 46.5391 25.6079 46.9582 26.0793L46.977 26.1005C47.9276 27.1778 49.0107 28.428 48.7054 29.8049C48.4 31.1818 46.9828 31.8155 45.6059 32.4262C44.9722 32.7085 43.8949 33.2097 43.7681 33.492C43.6414 33.7743 43.9467 34.8689 44.108 35.445C44.4998 36.8334 44.9434 38.4062 44.0274 39.5584C43.2727 40.4975 42.0225 40.63 40.7838 40.63H40.0119H39.2975C38.2547 40.63 38.007 40.7625 37.9551 40.8028C37.6834 41.018 37.4783 42.0903 37.3511 42.756L37.3445 42.7904L37.3433 42.7966C37.0786 44.1835 36.778 45.7583 35.5009 46.3738ZM28.4094 30.5105L29.4747 31.5706L30.8466 30.1804L29.794 29.1279C29.98 28.8629 30.1467 28.5785 30.2942 28.2745C30.8036 27.2372 31.0583 26.0342 31.0583 24.6654C31.0583 23.2966 30.8036 22.0936 30.2942 21.0563C29.7908 20.019 29.0635 19.2118 28.1121 18.6349C27.1669 18.0517 26.0313 17.7602 24.7055 17.7602C23.3797 17.7602 22.2442 18.0517 21.2989 18.6349C20.3537 19.2118 19.6263 20.019 19.1169 21.0563C18.6136 22.0936 18.3619 23.2966 18.3619 24.6654C18.3619 26.0342 18.6136 27.2372 19.1169 28.2745C19.6263 29.3119 20.3537 30.1221 21.2989 30.7052C22.2442 31.2822 23.3797 31.5706 24.7055 31.5706C26.0313 31.5706 27.1669 31.2822 28.1121 30.7052C28.2138 30.6429 28.3129 30.578 28.4094 30.5105ZM28.1402 27.474L27.0809 26.4147L25.6907 27.805L26.8386 28.9474C26.2639 29.3087 25.5529 29.4865 24.7055 29.4807C23.8155 29.4684 23.0728 29.2628 22.4774 28.8638C21.8882 28.4648 21.4463 27.9093 21.1516 27.1973C20.857 26.4792 20.7097 25.6352 20.7097 24.6654C20.7097 23.6956 20.857 22.8486 21.1516 22.1243C21.4524 21.4 21.8974 20.8384 22.4866 20.4394C23.0759 20.0404 23.8155 19.844 24.7055 19.8502C25.5955 19.8624 26.3352 20.0681 26.9244 20.467C27.5198 20.866 27.9648 21.4246 28.2594 22.1427C28.5602 22.8547 28.7106 23.6956 28.7106 24.6654C28.7106 25.6352 28.5602 26.4823 28.2594 27.2065C28.2221 27.2983 28.1823 27.3875 28.1402 27.474Z"
      fill="currentColor"
    />
  </svg>
);
export default QTypeCompletedCertificationSvg;
