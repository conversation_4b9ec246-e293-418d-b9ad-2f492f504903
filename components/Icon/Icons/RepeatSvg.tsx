/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const RepeatSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 30}
    height={props.height || 30}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 30 30"
  >
    <path
      d="M15 3v3c-4.959 0-9 4.041-9 9s4.041 9 9 9 9-4.041 9-9a8.94 8.94 0 0 0-1.725-5.275L20.85 11.15A6.979 6.979 0 0 1 22 15c0 3.878-3.122 7-7 7s-7-3.122-7-7 3.122-7 7-7v3l5-4-5-4z"
      fill="currentColor"
    />
  </svg>
);
export default RepeatSvg;
