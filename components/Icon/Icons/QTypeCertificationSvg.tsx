/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const QTypeCertificationSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 41}
    height={props.height || 53}
    viewBox="0 0 49 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M34.4121 46.3386C34.7883 46.3443 35.1609 46.2635 35.5009 46.1024C36.7799 45.4859 37.0795 43.9074 37.3445 42.5189C37.4712 41.8564 37.6786 40.7503 37.9552 40.5314C38.007 40.491 38.2547 40.3585 39.2975 40.3585H40.0119H40.7839C42.0225 40.3585 43.2727 40.226 44.0274 39.287C44.9434 38.1347 44.4998 36.562 44.108 35.1735C43.9467 34.5974 43.6414 33.5028 43.7681 33.2205C43.8949 32.9382 44.9722 32.437 45.6059 32.1547C46.9828 31.544 48.4 30.9103 48.7054 29.5334C49.0107 28.1565 47.9276 26.9063 46.977 25.829C46.5622 25.3624 45.7845 24.4924 45.7845 24.1698C45.7845 23.8472 46.5392 22.9542 46.9367 22.4761C47.87 21.3814 48.9243 20.1428 48.5902 18.7544C48.256 17.366 46.7524 16.7438 45.4273 16.1792C44.7936 15.9142 43.7393 15.4648 43.5838 15.1594C43.4282 14.8541 43.7278 13.771 43.8833 13.1776C44.2405 11.7834 44.6496 10.2049 43.7163 9.08724C42.9904 8.26916 41.8381 8.13666 40.8242 8.13666C40.4843 8.13666 40.1328 8.13666 39.7757 8.16546C39.4185 8.19427 39.1247 8.16546 38.8136 8.16546C37.9321 8.16546 37.6613 8.03872 37.6037 7.98687C37.3157 7.76795 37.091 6.69638 36.9412 5.98777C36.6531 4.63967 36.3248 3.08417 35.0458 2.50806C34.722 2.36124 34.3701 2.28655 34.0146 2.28913C33.0121 2.28913 31.9924 2.82492 31.0073 3.33766C30.4139 3.7085 29.7579 3.96815 29.0715 4.10388C28.7201 4.02899 28.0518 3.17058 27.6024 2.60023C26.7268 1.4653 25.7416 0.203613 24.3071 0.203613C22.8726 0.203613 21.9105 1.51138 21.0578 2.65785C20.6545 3.19939 19.9747 4.10388 19.6348 4.18454H19.5715C18.8915 4.04821 18.2391 3.79896 17.6415 3.44712C16.7399 2.90276 15.7283 2.56621 14.6803 2.46197C14.3079 2.45795 13.9395 2.53872 13.603 2.69817C12.3182 3.31461 12.0244 4.89315 11.7594 6.28158C11.6327 6.94411 11.4253 8.05024 11.1487 8.26916C11.0969 8.30949 10.8491 8.442 9.80638 8.442H9.092H8.32001C7.08138 8.442 5.83122 8.5745 5.07651 9.51356C4.1605 10.6658 4.6041 12.2386 4.99585 13.627C5.15717 14.2031 5.4625 15.2977 5.33576 15.58C5.20902 15.8623 4.14321 16.3347 3.50949 16.6285C2.13258 17.2392 0.715353 17.8729 0.410014 19.2498C0.104676 20.6267 1.18776 21.8769 2.13835 22.9542C2.55315 23.4209 3.3309 24.2908 3.3309 24.6134C3.3309 24.936 2.57619 25.829 2.17867 26.3072C1.24537 27.4018 0.191093 28.6404 0.525237 30.0289C0.859381 31.4173 2.36303 32.0395 3.68808 32.5983C4.32181 32.8633 5.37609 33.3127 5.53164 33.618C5.68719 33.9234 5.38761 35.0065 5.23206 35.5999C4.87487 36.994 4.46583 38.5726 5.39913 39.6902C6.11927 40.5314 7.27149 40.6639 8.28545 40.6639C8.62535 40.6639 8.97678 40.6639 9.33397 40.6639C9.69116 40.6639 9.98497 40.6639 10.2961 40.6639C11.1775 40.6639 11.4483 40.7906 11.5059 40.8425C11.794 41.0614 12.0186 42.133 12.1684 42.8416C12.4565 44.1609 12.7849 45.7164 14.0638 46.2925C14.3894 46.4401 14.7434 46.5148 15.1008 46.5114C16.1033 46.5114 17.123 45.9756 18.1081 45.4629C18.7015 45.092 19.3574 44.8324 20.0439 44.6966C20.3953 44.7715 21.0636 45.6299 21.513 46.2003C22.3829 47.3352 23.368 48.5969 24.7795 48.5969C26.2083 48.5969 27.1761 47.2834 28.0288 46.1427C28.432 45.6011 29.1119 44.6966 29.4518 44.616H29.5151C30.2028 44.7479 30.8631 44.9953 31.4681 45.3476C32.3641 45.8916 33.3696 46.23 34.4121 46.3386ZM28.9563 42.3692C27.8041 42.6399 26.986 43.7173 26.2083 44.76C25.8453 45.2439 25.0906 46.2521 24.7795 46.2867C24.4684 46.3213 23.6964 45.267 23.3277 44.7946C22.5269 43.7691 21.6743 42.7091 20.522 42.4613C20.3476 42.4257 20.17 42.4083 19.992 42.4095C19.0069 42.4095 18.0044 42.9337 17.0308 43.4465C16.4354 43.8171 15.7806 44.0821 15.0951 44.23H15.0317C14.784 44.0456 14.5478 42.9222 14.421 42.3231C14.1503 41.0383 13.8449 39.7075 12.9001 38.9643C12.1242 38.4691 11.2079 38.2405 10.2903 38.3133C9.94465 38.3133 9.58746 38.3133 9.23027 38.3421C8.87308 38.3709 8.58502 38.3421 8.27969 38.3421C7.90706 38.3925 7.52779 38.353 7.17355 38.2269C7.01224 37.9619 7.31758 36.7578 7.46737 36.1817C7.78999 34.9316 8.12414 33.6411 7.59412 32.5868C7.06409 31.5325 5.80241 30.9967 4.58682 30.484C4.01071 30.2363 2.89881 29.7696 2.76631 29.5046C2.76631 29.1877 3.52101 28.289 3.91853 27.8108C4.76541 26.8142 5.64686 25.7887 5.64686 24.5961C5.64686 23.4036 4.73661 22.4069 3.87244 21.4333C3.44612 20.9436 2.66261 20.0564 2.66261 19.7626C2.79511 19.4745 3.8782 18.9906 4.45431 18.7313C5.66414 18.1956 6.9143 17.6367 7.42704 16.5594C7.93978 15.4821 7.57683 14.255 7.23117 12.9875C7.06985 12.4114 6.72995 11.1958 6.89702 10.9308C6.93159 10.8962 7.15051 10.7234 8.3373 10.7234H9.0632H9.82942C10.805 10.8139 11.7828 10.5745 12.6063 10.0436C13.5511 9.28888 13.7988 7.96382 14.0466 6.6791C14.1618 6.06266 14.3807 4.9162 14.6227 4.74337H14.6976C15.3784 4.87967 16.0311 5.131 16.6275 5.48655C17.5218 6.02873 18.5254 6.36524 19.5657 6.4717C19.7595 6.47222 19.9528 6.45097 20.1418 6.40833C21.294 6.13755 22.1121 5.06023 22.8899 4.01747C23.2528 3.53353 24.0075 2.51958 24.3186 2.49077C24.6297 2.46197 25.4017 3.51049 25.7704 3.9829C26.5827 5.03142 27.4354 6.09146 28.5876 6.33919C28.762 6.37483 28.9396 6.39221 29.1176 6.39104C30.1028 6.39104 31.1052 5.86678 32.0788 5.35404C32.6741 4.98336 33.329 4.71829 34.0146 4.57053H34.0779C34.3257 4.75489 34.5619 5.8783 34.6886 6.47746C34.9594 7.76219 35.2647 9.093 36.2095 9.83618C36.9853 10.3318 37.9017 10.5604 38.8193 10.4872C39.165 10.4872 39.5222 10.4872 39.8794 10.4584C40.2365 10.4296 40.5246 10.4584 40.8299 10.4584C41.2044 10.4153 41.5837 10.4628 41.9361 10.5967C42.0974 10.8617 41.792 12.0657 41.6423 12.6418C41.3196 13.892 40.9855 15.1825 41.5155 16.2368C42.0455 17.2911 43.3072 17.8268 44.5171 18.3396C45.0932 18.5873 46.2051 19.054 46.3376 19.3247C46.3376 19.6416 45.5829 20.5403 45.1853 21.0185C44.3385 22.0152 43.457 23.0406 43.457 24.2332C43.457 25.4257 44.3673 26.4224 45.2314 27.396C45.6577 27.8742 46.447 28.7614 46.4413 29.0552C46.3088 29.3433 45.2257 29.8272 44.6496 30.0865C43.4397 30.6223 42.1896 31.1811 41.6768 32.2584C41.1641 33.3357 41.527 34.5917 41.8727 35.8303C42.034 36.4064 42.3739 37.6278 42.2068 37.887C42.1723 37.9216 41.9534 38.0944 40.7666 38.0944H40.0407H39.2744C38.2989 38.0044 37.3213 38.2437 36.4976 38.7742C35.5528 39.5289 35.305 40.854 35.0631 42.1387C34.9478 42.7551 34.7289 43.9016 34.487 44.0744H34.4121C33.7308 43.9365 33.0781 43.6832 32.4821 43.3255C31.5879 42.7833 30.5843 42.4468 29.5439 42.3404C29.3476 42.3276 29.1504 42.3373 28.9563 42.3692Z"
      fill="currentColor"
    />
    <path
      d="M41.3139 42.6746C41.1123 42.6746 39.7527 42.6746 39.6547 42.6746C39.6547 42.7725 39.4761 43.6251 39.3897 43.9939L44.1426 53.782L39.4819 52.0536C39.3351 51.9977 39.1784 51.9724 39.0214 51.9793C38.8645 51.9862 38.7106 52.0251 38.5692 52.0936C38.4278 52.1621 38.302 52.2588 38.1994 52.3778C38.0967 52.4967 38.0195 52.6354 37.9725 52.7853L36.6474 56.9909L32.6146 48.3492C31.7658 48.032 30.9349 47.6685 30.1258 47.2604L29.7744 47.727L35.8409 60.6146C35.933 60.8081 36.0772 60.972 36.2573 61.0881C36.4374 61.2041 36.6464 61.2676 36.8606 61.2714H36.9355C37.1662 61.2557 37.3868 61.1709 37.5687 61.0281C37.7505 60.8852 37.8852 60.691 37.9552 60.4706L39.7987 54.6173L46.0323 56.9909C46.2478 57.0726 46.483 57.0878 46.7072 57.0345C46.9315 56.9812 47.1347 56.8618 47.2904 56.6918C47.4461 56.5218 47.5473 56.309 47.5808 56.081C47.6143 55.8529 47.5786 55.62 47.4783 55.4124L41.3139 42.6746Z"
      fill="currentColor"
    />
    <path
      d="M19.485 47.3641C19.3698 47.4448 18.2176 48.0209 17.7221 48.2341L13.6087 56.9622L12.2836 52.7565C12.2335 52.6099 12.1543 52.4749 12.0508 52.3596C11.9473 52.2443 11.8216 52.151 11.6812 52.0854C11.5409 52.0198 11.3887 51.9832 11.2338 51.9777C11.079 51.9723 10.9246 51.9981 10.78 52.0537L6.11923 53.782L10.3652 45.0251C10.1578 44.3914 9.86971 43.0203 9.84091 42.8993L8.81543 42.9339L2.77779 55.4067C2.67752 55.6142 2.64182 55.8471 2.67531 56.0752C2.70881 56.3033 2.80996 56.5161 2.96569 56.6861C3.12141 56.8561 3.32455 56.9755 3.54884 57.0288C3.77314 57.0821 4.00826 57.0669 4.22383 56.9852L10.4573 54.6116L12.3009 60.4649C12.3703 60.6872 12.5054 60.8833 12.6885 61.0273C12.8716 61.1713 13.094 61.2564 13.3264 61.2715H13.4013C13.6201 61.2714 13.8343 61.2091 14.019 61.0918C14.2036 60.9744 14.3511 60.807 14.444 60.6089L20.2051 48.309C20.0381 48.0728 19.5599 47.4448 19.485 47.3641Z"
      fill="currentColor"
    />
    <path
      d="M29.4752 31.2992L25.6912 27.5335L27.0814 26.1433L30.8471 29.9089L29.4752 31.2992ZM24.706 31.2992C23.3802 31.2992 22.2447 31.0107 21.2994 30.4337C20.3542 29.8506 19.6268 29.0404 19.1174 28.0031C18.6141 26.9657 18.3624 25.7627 18.3624 24.3939C18.3624 23.0252 18.6141 21.8221 19.1174 20.7848C19.6268 19.7475 20.3542 18.9403 21.2994 18.3634C22.2447 17.7803 23.3802 17.4887 24.706 17.4887C26.0318 17.4887 27.1673 17.7803 28.1126 18.3634C29.064 18.9403 29.7913 19.7475 30.2946 20.7848C30.8041 21.8221 31.0588 23.0252 31.0588 24.3939C31.0588 25.7627 30.8041 26.9657 30.2946 28.0031C29.7913 29.0404 29.064 29.8506 28.1126 30.4337C27.1673 31.0107 26.0318 31.2992 24.706 31.2992ZM24.706 29.2092C25.596 29.2153 26.3356 29.0189 26.9249 28.6199C27.5203 28.221 27.9653 27.6593 28.2599 26.9351C28.5607 26.2108 28.711 25.3637 28.711 24.3939C28.711 23.4241 28.5607 22.5832 28.2599 21.8712C27.9653 21.1531 27.5203 20.5945 26.9249 20.1956C26.3356 19.7966 25.596 19.591 24.706 19.5787C23.816 19.5725 23.0764 19.769 22.4871 20.1679C21.8979 20.5669 21.4529 21.1285 21.1521 21.8528C20.8575 22.5771 20.7102 23.4241 20.7102 24.3939C20.7102 25.3637 20.8575 26.2077 21.1521 26.9258C21.4467 27.6379 21.8887 28.1933 22.4779 28.5923C23.0733 28.9913 23.816 29.1969 24.706 29.2092Z"
      fill="currentColor"
    />
  </svg>
);
export default QTypeCertificationSvg;
