/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const CertificationTestSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width={props.width || 118}
    height={props.height || 114}
    viewBox="0 0 119 114"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.4497 113.366H107.549C113.647 113.366 118.637 108.376 118.637 102.277V96.918C118.637 95.8092 117.898 95.07 116.789 95.07H111.245V38.1498C111.245 34.0841 107.918 30.7576 103.852 30.7576H95.9058V19.2996C95.9058 18.93 95.721 18.3756 95.3514 18.006L78.5341 1.18869C78.1645 0.819083 77.6101 0.634277 77.2405 0.634277H27.8974C25.8645 0.634277 24.2013 2.29753 24.2013 4.33039V30.7576H16.2546C12.1889 30.7576 8.86239 34.0841 8.86239 38.1498V95.07H2.20938C1.10055 95.07 0.361328 95.8092 0.361328 96.918V102.277C0.361328 108.376 5.35108 113.366 11.4497 113.366ZM89.6225 17.4516H79.0885V6.91767L89.6225 17.4516ZM27.8974 4.33039H75.3924V17.4516C75.3924 19.4845 77.0557 21.1477 79.0885 21.1477H92.2097C92.2097 44.4332 92.2097 61.8049 92.2097 83.4272H27.8974V4.33039ZM4.05744 98.7661H82.9695C84.0783 98.7661 84.8175 98.0268 84.8175 96.918C84.8175 95.8092 84.0783 95.07 82.9695 95.07H12.5585V38.1498C12.5585 36.117 14.2218 34.4537 16.2546 34.4537H24.2013V83.4272C24.2013 85.4601 25.8645 87.1233 27.8974 87.1233H92.2097C94.2426 87.1233 95.9058 85.4601 95.9058 83.4272V34.4537H103.852C105.885 34.4537 107.549 36.117 107.549 38.1498V95.07H93.873C92.7641 95.07 92.0249 95.8092 92.0249 96.918C92.0249 98.0268 92.7641 98.7661 93.873 98.7661H114.941V102.277C114.941 106.343 111.614 109.67 107.549 109.67H11.4497C7.38394 109.67 4.05744 106.343 4.05744 102.277V98.7661Z"
      fill="currentColor"
    />
    <path
      d="M46.933 27.2463L39.5407 34.6385L36.7687 31.8665C36.0294 31.1272 34.9206 31.1272 34.1814 31.8665C33.4421 32.6057 33.4421 33.7145 34.1814 34.4537L38.2471 38.5195C38.9863 39.2587 40.0952 39.2587 40.8344 38.5195L49.5202 29.8336C50.2595 29.0944 50.2595 27.9855 49.5202 27.2463C48.781 26.5071 47.6722 26.5071 46.933 27.2463Z"
      fill="currentColor"
    />
    <path
      d="M47.3029 57.5544C46.5637 56.8152 45.4549 56.8152 44.7156 57.5544L41.7587 60.5113L38.8019 57.5544C38.0626 56.8152 36.9538 56.8152 36.2146 57.5544C35.4754 58.2936 35.4754 59.4025 36.2146 60.1417L39.1715 63.0986L36.2146 66.0555C35.4754 66.7947 35.4754 67.9035 36.2146 68.6428C36.9538 69.382 38.0626 69.382 38.8019 68.6428L41.7587 65.6859L44.7156 68.6428C45.4549 69.382 46.5637 69.382 47.3029 68.6428C48.0421 67.9035 48.0421 66.7947 47.3029 66.0555L44.346 63.0986L47.3029 60.1417C48.0421 59.4025 48.0421 58.2936 47.3029 57.5544Z"
      fill="currentColor"
    />
    <path
      d="M84.8175 36.3013H58.2055C57.0966 36.3013 56.3574 37.0405 56.3574 38.1493C56.3574 39.2582 57.0966 39.9974 58.2055 39.9974H84.8175C85.9263 39.9974 86.6656 39.2582 86.6656 38.1493C86.6656 37.0405 85.7415 36.3013 84.8175 36.3013Z"
      fill="currentColor"
    />
    <path
      d="M58.2055 30.5731H75.5772C76.686 30.5731 77.4253 29.8338 77.4253 28.725C77.4253 27.6162 76.686 26.877 75.5772 26.877H58.2055C57.0966 26.877 56.3574 27.6162 56.3574 28.725C56.3574 29.8338 57.0966 30.5731 58.2055 30.5731Z"
      fill="currentColor"
    />
    <path
      d="M84.8175 65.5015H58.2055C57.0966 65.5015 56.3574 66.2407 56.3574 67.3495C56.3574 68.4584 57.0966 69.1976 58.2055 69.1976H84.8175C85.9263 69.1976 86.6656 68.4584 86.6656 67.3495C86.6656 66.2407 85.7415 65.5015 84.8175 65.5015Z"
      fill="currentColor"
    />
    <path
      d="M58.2055 59.7723H75.5772C76.686 59.7723 77.4253 59.0331 77.4253 57.9242C77.4253 56.8154 76.686 56.0762 75.5772 56.0762H58.2055C57.0966 56.0762 56.3574 56.8154 56.3574 57.9242C56.3574 59.0331 57.0966 59.7723 58.2055 59.7723Z"
      fill="currentColor"
    />
    <path
      d="M46.0082 105.973H72.805C73.9139 105.973 74.6531 105.234 74.6531 104.125C74.6531 103.017 73.9139 102.277 72.805 102.277H46.0082C44.8994 102.277 44.1602 103.017 44.1602 104.125C44.1602 105.234 45.0842 105.973 46.0082 105.973Z"
      fill="currentColor"
    />
  </svg>
);
export default CertificationTestSvg;
