/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const LogoFullSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 181}
    height={props.height || 75}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 181 75"
  >
    <g fill="currentColor" opacity={0.8}>
      <path d="M37.551.031h.151l.305.41c2.368-.176 4.622.817 6.603 2.017 1.642 1.015 2.823 2.562 4.081 3.992 1.72-2.482 4.19-4.543 7.151-5.318 4.02-1.295 8.59-.188 11.749 2.562 2.734 2.297 4.46 5.797 4.45 9.387.034 10.595-.003 21.19.019 31.783-1.24-.003-2.48-.003-3.716.003.013-9.811-.003-19.625.01-29.437-.028-1.531-.191-3.057-.345-4.576-.664-2.408-2.377-4.53-4.672-5.567-2.648-1.328-5.874-1.144-8.48.2-2.555 1.606-4.462 4.41-4.397 7.505-.043 10.616.012 21.233-.028 31.85-1.276.065-2.556.05-3.835.037.012-10.134 0-20.268.009-30.405-.018-1.578.086-3.214-.554-4.696-1.1-3.104-4.152-5.254-7.372-5.616-1.48-.084-2.992-.053-4.404.452-2.685.88-4.829 3.287-5.238 6.11-3.321 21.311-6.56 42.64-9.872 63.957a75.595 75.595 0 0 0-3.34-.009c-1.24-4.764-2.612-9.494-3.851-14.258-.683-2.384-1.304-4.783-1.956-7.175-.406-1.48-.803-2.971-1.415-4.377-.477-1.092-1.335-1.938-2.159-2.771-1.735-1.077-3.74-1.596-5.785-1.415-.003-1.058.046-2.128-.16-3.174.05-.19.148-.572.2-.763 2.082.142 4.186.44 6.096 1.326 3.33 1.636 5.573 4.94 6.372 8.507 1.107 4.33 2.393 8.618 3.42 12.97l.443.172c1.781-10.774 3.294-21.59 5.056-32.364.939-6.64 2.052-13.26 3.005-19.896.286-1.861.732-3.746 1.781-5.336 1.82-3.014 5.02-5.078 8.467-5.666.738-.117 1.566.037 2.211-.421ZM154.122 44.598c-.003-14.69.006-29.378-.003-44.067 1.237.012 2.476.009 3.715.003-.009 6.05-.009 12.1 0 18.149.529-.357 1.031-.75 1.569-1.092l-.034-.184.225-.019c.99-.775 2.159-1.261 3.291-1.784 3.881-1.322 8.405-.744 11.711 1.716.265.16.532.32.803.48l-.031.126c.111.108.332.326.443.437l.252-.092-.083.249c.114.117.339.344.449.458l.253-.086-.096.25.449.454.231-.092-.031.255c.763.763 1.292 1.707 1.799 2.648.779 1.895 1.2 3.91 1.089 5.96.252 1.006.129 2.052.191 3.08.04 4.431-.009 8.866.024 13.298a92.528 92.528 0 0 0-3.81 0c-.16-1.667-.117-3.34-.04-5.01-.215-4.216-.037-8.445-.117-12.668-.086-.855-.271-1.695-.468-2.531a40.723 40.723 0 0 0-.267-.619c-1.144-2.19-3.073-4.075-5.499-4.736l-.028-.16c-.529-.052-1.055-.108-1.553-.295-1.476-.092-2.968-.065-4.389.397a10.12 10.12 0 0 0-4.992 3.915c-.762 1.442-1.374 3.026-1.353 4.684.016 5.622.003 11.244.009 16.866-1.236 0-2.472-.003-3.709.01ZM90.872 15.509c2.254-.732 4.647-.563 6.93-.065.291.092.577.188.867.29l.882.27c1.003.384 1.953.889 2.857 1.464.729.498 1.437 1.03 2.073 1.642.256.514.834.68 1.317.914.015-1.572.006-3.146.003-4.718 1.239.025 2.479.018 3.718-.003-.015 9.759-.009 19.517-.003 29.276-1.236.013-2.473 0-3.709.019 0-1.596-.012-3.19.009-4.786a79.85 79.85 0 0 1-.879.077l.055.314-.2.018c-.763.904-1.728 1.596-2.713 2.236a21.1 21.1 0 0 1-2.528 1.273c-.298.09-.593.176-.886.268a14.36 14.36 0 0 1-7.756.27c-.29-.086-.575-.175-.861-.27a56.937 56.937 0 0 0-.892-.268c-1-.4-1.947-.913-2.857-1.476a20.782 20.782 0 0 1-1.461-1.129l-.08-.003a62.772 62.772 0 0 0-.462-.43l.077-.243-.393.067.095-.393-.415.104.11-.252c-.116-.11-.344-.338-.458-.449l-.23.09.064-.37h-.249a85.18 85.18 0 0 0-.997-1.356l-.061-.252c-.603-.77-.966-1.68-1.372-2.556a30.683 30.683 0 0 1-.203-1.034l-.23-.033c-.01-.166-.034-.499-.047-.665-.682-2.27-.67-4.702-.012-6.975.022-.13.065-.388.083-.517.16-.397.305-.8.44-1.209.563-1.587 1.507-2.98 2.5-4.324.237-.197.48-.393.723-.587l-.04-.083c.117-.111.345-.335.459-.446l.083.018c.193-.24.39-.48.59-.716.501-.336.993-.683 1.483-1.04.9-.575 1.86-1.052 2.832-1.498.348-.07.698-.135 1.049-.194l.034-.22.661-.05Zm-.194 3.967c-3.192 1.105-5.748 3.79-6.84 6.97-.092.39-.188.778-.31 1.16-.25 1.983-.271 4.053.424 5.96 1.123 3.06 3.638 5.603 6.723 6.677.483.138.969.27 1.44.446 1.482.169 2.98.203 4.46-.028.414-.126.842-.215 1.272-.292l.034-.126c3.18-.97 5.767-3.617 6.856-6.739.147-.458.28-.926.455-1.375.209-1.986.255-4.047-.431-5.954-.418-1.126-1.024-2.165-1.731-3.134-1.329-1.584-3.033-2.903-5.01-3.565-.48-.141-.957-.27-1.421-.446a18.935 18.935 0 0 0-4.494 0c-.47.163-.953.299-1.427.446Z" />
      <path d="M46.272 54.03c2.592.059 5.182-.098 7.775-.012 1.956.17 3.982.984 5.176 2.605.24.418.473.84.713 1.258.443 1.258.594 2.614.292 3.928-.415 1.802-1.725 3.223-3.29 4.14-.539.156-1.074.325-1.609.488-2.017.366-4.072.179-6.111.213.046 2.73.006 5.462.024 8.196a314.42 314.42 0 0 0-2.961-.003c0-6.939.015-13.874-.01-20.813Zm3.004 9.535c1.732-.04 3.463.049 5.192-.04.815.009 1.485-.51 2.137-.93.477-.86.985-1.826.714-2.84-.15-1.486-1.532-2.575-2.977-2.637-1.686-.101-3.374-.021-5.06-.034.007 2.16.01 4.322-.006 6.48ZM64.355 74.845c.003-6.938.012-13.874-.003-20.81 2.93.111 5.89-.267 8.805.13 1.735.387 3.43 1.313 4.383 2.86.855 1.701 1.304 3.777.378 5.546l.04.396-.221-.021c-.219.538-.514 1.042-.825 1.534l-.144-.033c-.111.107-.336.322-.446.43l.037.13c-.766.565-1.606 1.036-2.525 1.306.027.151.086.456.113.606.757 1.114 1.378 2.313 2.055 3.476.575.99 1.132 1.987 1.698 2.98l.218.058-.062.517.219.065.572.842c-1.147-.064-2.331.13-3.442-.246-.233-.526-.526-1.024-.809-1.525l-.23-.059.074-.529-.237-.024c-.557-1.052-1.215-2.045-1.753-3.106l-.231-.037c.015-.102.043-.299.055-.4-.566-.69-.971-1.483-1.356-2.28-1.169.004-2.334.01-3.5-.002.028 2.734.03 5.468.006 8.202-.956-.015-1.913-.012-2.87-.006Zm3.001-17.77c.013 2.165.004 4.327.004 6.492 2.152-.145 4.376.286 6.47-.366 2.107-1.27 2.107-5.072-.42-5.84-1.96-.57-4.045-.164-6.054-.287ZM86.749 54.439a9.834 9.834 0 0 1 4.78-.295c1.011.261 1.946.735 2.844 1.26.96.766 1.772 1.726 2.236 2.873.901 1.886.566 4.017.624 6.034-.061 1.53.148 3.082-.147 4.592-.083.397-.163.797-.25 1.194-.707 2.156-2.574 3.687-4.668 4.42-.492.082-.99.144-1.483.205-1.131.277-2.273-.058-3.392-.212-2.19-.612-4.038-2.362-4.749-4.52-.658-1.822-.286-3.787-.381-5.678.09-2.043-.326-4.202.584-6.118.357-.975 1.043-1.762 1.775-2.479.292-.197.584-.397.876-.593.44-.246.892-.47 1.35-.683Zm-.984 4.373c-.788 1.28-.588 2.845-.603 4.276.113 2.414-.554 5.003.76 7.209 1.31 1.402 3.296 1.679 5.117 1.454a47.243 47.243 0 0 0 1.88-.873c1.719-1.633 1.214-4.127 1.282-6.247-.086-1.986.338-4.118-.658-5.954-1.904-2.276-6.02-2.34-7.779.135Z" />
    </g>
    <path
      fill="currentColor"
      d="m129.788 14.657 4.011-.006c.012 4.321.003 8.645.006 12.97 4.331-.019 8.661.006 12.991-.016.003 1.363.01 2.725-.006 4.09-4.324-.018-8.654-.1-12.979.044-.021 4.287.006 8.574-.012 12.862-1.335-.022-2.67-.019-4.001.003-.022-4.297-.003-8.593-.013-12.887-4.318-.107-8.642-.027-12.96-.043a49.148 49.148 0 0 1-.015-4.063c4.324.013 8.651-.012 12.975.016.01-4.325-.003-8.649.003-12.97Z"
    />
  </svg>
);
export default LogoFullSvg;
