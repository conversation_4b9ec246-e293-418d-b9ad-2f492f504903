/* eslint-disable react/destructuring-assignment */
import * as React from 'react';
import { SVGProps } from 'react';

const FiltersSvg = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 21}
    height={props.height || 16}
    fill="none"
    preserveAspectRatio="none"
    viewBox="0 0 21 16"
  >
    <path
      fill="currentColor"
      d="M3.882 7.93c0-.695.563-1.258 1.258-1.258h10.065a1.258 1.258 0 1 1 0 2.516H5.14A1.258 1.258 0 0 1 3.882 7.93ZM.107 1.85C.107 1.154.671.59 1.366.59h17.613a1.258 1.258 0 0 1 0 2.517H1.366A1.258 1.258 0 0 1 .107 1.849Zm7.55 12.161c0-.695.562-1.258 1.257-1.258h2.517a1.258 1.258 0 0 1 0 2.516H8.914a1.258 1.258 0 0 1-1.258-1.258Z"
      opacity={0.8}
    />
  </svg>
);
export default FiltersSvg;
