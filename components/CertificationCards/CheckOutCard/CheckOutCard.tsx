import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import { ProductDetailsType } from '@/types/common';

import Card from '../../Card/Card';
import Text from '../../Text/Text';
import styles from './CheckOutCard.module.css';

type CheckOutCardProps = {
  productDetails: ProductDetailsType;
};

const CheckOutCard = ({ productDetails }: CheckOutCardProps) => {
  return (
    <div>
      <Card size="sm" radius="xs">
        <div className={styles.wrapper}>
          <div className={styles.testContent}>
            <Image
              src={productDetails.images[0]}
              alt="product"
              width={131}
              height={131}
              placeholder="blur"
              blurDataURL={BLUR_IMAGE_SVG}
              className={styles.image}
            />

            <div className={styles.descriptionWrapper}>
              <Text untranslatedText={productDetails.name} type="h3" />

              <Text
                transKey="typeCardDescription"
                type="subTitle2"
                transVariables={{
                  productType: productDetails.name,
                }}
              />
            </div>
          </div>

          <div className={styles.priceContent}>
            <Text
              untranslatedText={`${CURRENCY_SYMBOLS[productDetails.price.currency]}${getFormattedPrice(productDetails.price.amount)}`}
              type="h3"
              color="blue"
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CheckOutCard;
