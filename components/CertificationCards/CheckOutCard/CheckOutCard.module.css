.wrapper {
  display: flex;
  width: 100%;
  min-height: 125px;
  background-color: var(--color-white);

  @media screen and (max-width: 574px) {
    min-height: fit-content;
  }
}

.testContent {
  width: 100%;
  display: flex;
  align-items: center;

  @media screen and (max-width: 574px) {
    & img {
      width: 80px;
      height: 80px;
    }

    & h3 {
      font-size: 18px;
    }
  }

  @media screen and (max-width: 385px) {
    flex-direction: column;
    align-items: flex-start;

    & img {
      margin-bottom: var(--spacing-sm);
      margin-left: var(--spacing-sm);
    }
  }
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  margin: 0 var(--spacing-lg);
  gap: var(--spacing-mdl);

  @media screen and (max-width: 574px) {
    margin: 0;
    gap: var(--spacing-sm);
  }
}

.priceContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 var(--spacing-lg);

  @media screen and (max-width: 574px) {
    margin-right: var(--spacing-sm);
    margin-left: var(--spacing-md);

    & h3 {
      font-size: 18px;
    }
  }

  @media screen and (max-width: 385px) {
    margin-left: var(--spacing-sm);
    justify-content: flex-start;
    margin-top: var(--spacing-lg);

    & h3 {
      font-size: 24px;
    }
  }
}

.image {
  border-radius: var(--radius-xs);
  overflow: hidden;
  min-width: 131px;
}
