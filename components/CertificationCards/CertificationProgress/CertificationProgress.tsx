/* eslint-disable @typescript-eslint/no-unused-vars */
import { Flex, Progress, Skeleton } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG } from '@/common/consts';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import baseTheme from '@/styles/baseTheme';

import s from './CertificationProgress.module.css';

type CertificationProgressProsType = {
  title: string;
  description: string;
  image: string;
  isCompleted?: boolean;
  isLoading?: boolean;
  steps: {
    id: string | number;
    progress: number;
  }[];
  numberOfCompletedSteps: number;
  padding?: 'sm' | 'xl';
};

const PROGRESS_BAR_WIDTH = {
  normal: 180,
  large: 300,
  extraLarge: 420,
};

const getProgressBarWidth = (progress: number) => {
  if (progress > 5) return 'extraLarge';

  if (progress > 3) return 'large';
  return 'normal';
};

const CertificationProgress = ({
  description,
  image,
  isCompleted = false,
  isLoading = false,
  numberOfCompletedSteps,
  padding = 'xl',
  steps,
  title,
}: CertificationProgressProsType) => {
  return isLoading ? (
    <Skeleton height={196} radius="lg" />
  ) : (
    <Card
      size={padding}
      shadow="none"
      radius="sm"
      borderSize={2}
      borderColor="gray50"
    >
      <div className={s.wrapper}>
        <Image
          src={image}
          alt="a certification image"
          width={130}
          height={130}
          placeholder="blur"
          blurDataURL={BLUR_IMAGE_SVG}
        />

        <div className={s.testContent}>
          <div className={s.descriptionWrapper}>
            <Text untranslatedText={title} type="h3" />

            {description && (
              <Text untranslatedText={description} type="subTitle2" />
            )}
          </div>

          {isCompleted ? (
            <div className={s['completed-modules']}>
              <Icon name="CheckMarkSvg" color="green" />

              <Text
                transKey="completed-capital"
                type="subTitle2"
                color="green"
                fw={600}
              />
            </div>
          ) : (
            <div className={s['completed-modules']}>
              <Progress.Root
                h={6}
                w={PROGRESS_BAR_WIDTH[getProgressBarWidth(steps.length)]}
                className={s['progress-wrapper']}
              >
                {steps.map((step) => (
                  <div
                    key={step.id}
                    className={`${s['progress-section-wrapper']}`}
                  >
                    <Progress.Section
                      value={step.progress >= 100 ? 100 : step.progress}
                      color={baseTheme.colors.green}
                      className={s['progress-section']}
                    />
                  </div>
                ))}
              </Progress.Root>

              <Flex gap={8}>
                <Text
                  untranslatedText={`${numberOfCompletedSteps}/${steps.length}`}
                  type="subTitle2"
                  color="darkerGreen"
                  fw={600}
                />

                <Text
                  transKey="modules-completed"
                  type="subTitle2"
                  color="darkerGreen"
                  fw={600}
                />
              </Flex>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CertificationProgress;
