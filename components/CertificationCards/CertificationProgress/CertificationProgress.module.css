.wrapper {
  display: flex;
  background-color: var(--color-white);
  gap: var(--spacing-lg);
}

.testContent {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.descriptionWrapper {
  min-width: 30%;
  max-width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  gap: var(--spacing-mdl);
}

.progress-wrapper {
  gap: var(--spacing-md);
  background-color: transparent;
  justify-content: center;
}

.progress-section-wrapper {
  width: 100%;
  min-height: 6px;
  background-color: var(--color-gray50);
}

.progress-section {
  border-radius: 0;
}

.completed-modules {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;

  padding-right: var(--spacing-2xl);
}
