import { Tooltip } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import Image from 'next/image';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS, PRODUCTS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import IconButton from '@/components/IconButton/IconButton';
import { PurchasesContext } from '@/context/PurchasesProvider';
import { ProductDetailsType } from '@/types/common';

import Card from '../../Card/Card';
import Text from '../../Text/Text';
import styles from './CertificationBuyCard.module.css';

type CertificationBuyCardProps = {
  productDetails: ProductDetailsType;
  isDisabled?: boolean;
  tooltipText?: string;
};

const CertificationBuyCard = ({
  isDisabled = false,
  productDetails,
  tooltipText = '',
}: CertificationBuyCardProps) => {
  const { openCheckout } = PurchasesContext();
  const { t } = useTranslation();

  const image = productDetails?.type
    ? `/images/tests/${PRODUCTS[productDetails.type].certificateImage}`
    : '';

  return (
    <Card
      size="sm"
      radius="sm"
      borderSize={2}
      borderColor="gray50"
      shadow="none"
    >
      <div className={styles.wrapper}>
        <div className={styles.testContent}>
          <Image
            src={productDetails.images[0] || image}
            alt="product"
            width={131}
            height={131}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <div className={styles.descriptionWrapper}>
            <Text untranslatedText={productDetails.name} type="h3" />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: productDetails.name,
              }}
            />
          </div>
        </div>

        <Tooltip label={tooltipText} disabled={!isDisabled}>
          <div className={styles.buttonsWrapper}>
            <Button
              transKey="buy-capital"
              isDisabled={isDisabled}
              leftSection={
                <span className={styles.price}>
                  <Text
                    untranslatedText={`${CURRENCY_SYMBOLS[productDetails.price.currency]}${getFormattedPrice(productDetails.price.amount)}`}
                    color="white"
                    fw={300}
                    type="body1"
                  />
                </span>
              }
              onClick={() => openCheckout([productDetails])}
            />

            {/* <IconButton
              iconName="CartAddProductSvg"
              isDisabled={isDisabled}
              onClick={() =>
                notifications.show({
                  title: `${productDetails.name || ''} ${t(productDetails.kind || '')}`,
                  message: t('product-added-to-cart'),
                  color: 'green',
                })
              }
            /> */}
          </div>
        </Tooltip>
      </div>
    </Card>
  );
};

export default CertificationBuyCard;
