import { Tooltip } from '@mantine/core';
// import { notifications } from '@mantine/notifications';
import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import IconButton from '@/components/IconButton/IconButton';
import { PurchasesContext } from '@/context/PurchasesProvider';
import { ProductDetailsType } from '@/types/common';

import Card from '../../Card/Card';
import Text from '../../Text/Text';
import styles from './CertificationBuyCard.module.css';

type CertificationBuyCardProps = {
  productDetails: ProductDetailsType;
  isDisabled?: boolean;
  tooltipText?: string;
  isAddedToCart?: boolean;
  isSinglePurchaseEnabled?: boolean;
  onAddToCart?: () => void;
  onRemoveFromCart?: () => void;
};

const CertificationBuyCard = ({
  isAddedToCart = false,
  isDisabled = false,
  isSinglePurchaseEnabled = true,
  onAddToCart,
  onRemoveFromCart,
  productDetails,
  tooltipText = '',
}: CertificationBuyCardProps) => {
  const { openCheckout } = PurchasesContext();

  // const image = productDetails?.type
  //   ? `/images/tests/${PRODUCTS[productDetails.type].certificateImage}`
  //   : '';

  return (
    <Card
      size="sm"
      radius="sm"
      borderSize={2}
      borderColor="gray50"
      shadow="none"
    >
      <div className={styles.wrapper}>
        <div className={styles.testContent}>
          <Image
            src={productDetails?.images[0]}
            alt="product"
            width={131}
            height={131}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <div className={styles.descriptionWrapper}>
            <Text untranslatedText={productDetails.name} type="h3" />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: productDetails.name,
              }}
            />
          </div>
        </div>

        <Tooltip label={tooltipText} disabled={!isDisabled}>
          <div className={styles.buttonsWrapper}>
            {isSinglePurchaseEnabled && (
              <Button
                transKey="buy-capital"
                isDisabled={isDisabled}
                leftSection={
                  <span className={styles.price}>
                    <Text
                      untranslatedText={`${CURRENCY_SYMBOLS[productDetails.price.currency]}${getFormattedPrice(productDetails.price.totalAmount || productDetails.price.amount || 0)}`}
                      color="white"
                      fw={300}
                      type="body1"
                    />
                  </span>
                }
                onClick={() => openCheckout([productDetails])}
              />
            )}

            {(onAddToCart || onRemoveFromCart) && (
              <IconButton
                variant={isAddedToCart ? 'dangerOutlined' : 'primary'}
                iconName={isAddedToCart ? 'CloseSvg' : 'CartAddProductSvg'}
                iconSize={isAddedToCart ? 16 : 20}
                isDisabled={isDisabled}
                onClick={isAddedToCart ? onRemoveFromCart : onAddToCart}
                color={isAddedToCart ? 'red' : 'white'}
              />
            )}
          </div>
        </Tooltip>
      </div>
    </Card>
  );
};

export default CertificationBuyCard;
