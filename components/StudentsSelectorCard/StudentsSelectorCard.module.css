.wrapper {
  width: 100%;
  background-color: var(--color-white);
  border-radius: var(--radius-sm);
}

.header {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xl);
}

.searchInputWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.inputRightSection {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.radiosWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.classesWrapper {
  padding: 0 var(--spacing-smd) var(--spacing-xl) var(--spacing-xl);
}

.tableWrapper {
  padding: 0 var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
  /* height: 100%; */
}
