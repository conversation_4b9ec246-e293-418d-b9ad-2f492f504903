import {
  Box,
  Grid,
  LoadingOverlay,
  Pagination,
  Paper,
  ScrollArea,
  Table,
  TableData,
  Tooltip,
} from '@mantine/core';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { getStudentsDisplayName } from '@/common/helpers';
import { UserContext } from '@/context/UserProvider';
import {
  ClassesFetchListType,
  // GroupsFetchListType,
  SelectedEntityByIdAndDisplayedNameType,
  StudentType,
} from '@/types/common';

import Checkbox from '../Checkbox/Checkbox';
import Icon from '../Icon/Icon';
import ClassInfoCard from '../InformationActionCard/ClassInfoCard/ClassInfoCard';
import GroupInfoCard from '../InformationActionCard/GroupInfoCard/GroupInfoCard';
import Input from '../Inputs/Input/Input';
import Radio from '../Radio/Radio';
import TableSkeleton from '../TableSkeleton/TableSkeleton';
import Text from '../Text/Text';
import s from './StudentsSelectorCard.module.css';

const VIEW_OPTIONS = {
  CARDS: 'cards',
  TABLE: 'table',
} as const;

type ViewOptionsType = (typeof VIEW_OPTIONS)[keyof typeof VIEW_OPTIONS];

type StudentsSelectorCardPropsType = {
  students: StudentType[];
  selectedStudents: SelectedEntityByIdAndDisplayedNameType[];
  scrollAreaHeight: number;
  isLoading?: boolean;
  isFetching?: boolean;
  isDisabled?: boolean;
  // isCardViewVisible?: boolean;
  cardViewProps: {
    displayedClassesOrGroups: ClassesFetchListType['results'];
    // GroupsFetchListType['results'];
    onClassOrGroupSelection: (uniqueId: string, isChecked: boolean) => void;
    totalNumberOfClassesOrGroups: number;
    studentsByClassOrGroupId: Record<
      string,
      SelectedEntityByIdAndDisplayedNameType[]
    >;
  };
  tableProps: {
    paginationData: {
      activePage: number;
      totalNumberOfPages: number;
      totalNumberOfResults: number;
      totalNumberOfSearchResults: number;
      onPageChange: (page: number) => void;
    };
    isGlobalCheckBoxDisabled?: boolean;
    areSelectionCheckBoxesDisabled?: boolean;
    onUpdateSelectedStudentList: (
      updatedStudentsList: SelectedEntityByIdAndDisplayedNameType[]
    ) => void;
  };
  searchProps: {
    searchStudentQuery: string;
    searchClassesQuery: string;
    onInputChange: (value: string, view: ViewOptionsType) => void;
  };
};

const StudentsSelectorCard = ({
  cardViewProps: {
    displayedClassesOrGroups,
    onClassOrGroupSelection,
    studentsByClassOrGroupId,
    totalNumberOfClassesOrGroups,
  },
  isDisabled = false,
  // isCardViewVisible = true,
  isFetching = false,
  isLoading = false,
  scrollAreaHeight,
  searchProps: { onInputChange, searchClassesQuery, searchStudentQuery },
  selectedStudents,
  students,
  tableProps: {
    areSelectionCheckBoxesDisabled = false,
    isGlobalCheckBoxDisabled = false,
    onUpdateSelectedStudentList,
    paginationData,
    // totalNumberOfResults,
  },
}: StudentsSelectorCardPropsType): JSX.Element => {
  const { t } = useTranslation();
  const { isSchoolRole } = UserContext();
  const [selectedView, setSelectedView] = useState<ViewOptionsType>(
    VIEW_OPTIONS.TABLE
  );

  const selectedStudentsById = selectedStudents.map((student) => student.id);

  const isGlobalCheckboxChecked = students.every((student) =>
    selectedStudentsById.includes(student.id)
  );

  const isGlobalCheckboxIndeterminate = students.some((student) =>
    selectedStudentsById.includes(student.id)
  );

  const onGlobalCheckBoxSelection = () => {
    if (isGlobalCheckboxIndeterminate) {
      const filteredStudents = selectedStudents.filter(
        (selectedStudent) =>
          // meaning if it gets included it will return false so filter will remove it
          !students.map((student) => student.id).includes(selectedStudent.id)
      );

      onUpdateSelectedStudentList(filteredStudents);
    } else {
      const updatedList = [
        ...selectedStudents,
        ...students.map((student) => ({
          displayedName: getStudentsDisplayName({
            firstName: student.firstName,
            lastName: student.lastName,
            code: student.code || '',
          }),
          id: student.id,
        })),
      ];

      onUpdateSelectedStudentList(updatedList);
    }
  };

  const onIndividualTableSelection = (student: StudentType) => {
    const isStudentSelected = selectedStudents.some(
      (selectedStudent) => selectedStudent.id === student.id
    );

    if (isStudentSelected) {
      const filteredSelectedStudentList = selectedStudents.filter(
        (selectedStudent) => selectedStudent.id !== student.id
      );
      onUpdateSelectedStudentList(filteredSelectedStudentList);
    } else {
      const updatedSelectedStudentList = [
        ...selectedStudents,
        {
          displayedName: getStudentsDisplayName({
            firstName: student.firstName,
            lastName: student.lastName,
            code: student.code || '',
          }),
          id: student.id,
        },
      ];

      onUpdateSelectedStudentList(updatedSelectedStudentList);
    }
  };

  const tableData: TableData = {
    head: [
      t('student-capital'),
      isSchoolRole ? t('class-capital') : t('group-capital'),
      t('grade-capital'),
      <Tooltip
        key="checkbox-icon"
        label="You can only select 1 student when this device is selected"
        disabled={!isGlobalCheckBoxDisabled}
      >
        <Box>
          <Checkbox
            value=""
            indeterminate={
              isGlobalCheckboxIndeterminate && !isGlobalCheckboxChecked
            }
            isChecked={isGlobalCheckboxChecked}
            onChange={onGlobalCheckBoxSelection}
            isDisabled={isDisabled || isGlobalCheckBoxDisabled}
          />
        </Box>
      </Tooltip>,
    ],
    body: students.map((student) => [
      getStudentsDisplayName({
        firstName: student.firstName,
        lastName: student.lastName,
        code: student.code || '',
      }),
      isSchoolRole ? student.className || 'N/A' : student.groupName || 'N/A',
      student.grade,
      <Tooltip
        key="checkbox-icon"
        label="You can only select 1 student when this device is selected"
        disabled={!areSelectionCheckBoxesDisabled}
      >
        <Box>
          <Checkbox
            value={student.id}
            key={student.id}
            isChecked={selectedStudents.some(
              (selectedStudent) => selectedStudent.id === student.id
            )}
            onChange={() => onIndividualTableSelection(student)}
            isDisabled={isDisabled || areSelectionCheckBoxesDisabled}
          />
        </Box>
      </Tooltip>,
    ]),
  };

  const TABLE_VIEW_COMPONENT = isLoading ? (
    <TableSkeleton numberOfColumns={tableData?.head?.length} />
  ) : (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
      }}
    />
  );

  return (
    <Paper className={s.wrapper}>
      <LoadingOverlay
        visible={isLoading}
        zIndex={2}
        overlayProps={{ radius: 'sm', blur: 1 }}
      />

      <div className={s.header}>
        <div className={s.searchInputWrapper}>
          <Input
            value={
              selectedView === VIEW_OPTIONS.CARDS
                ? searchClassesQuery
                : searchStudentQuery
            }
            variant="filled"
            isDisabled={isDisabled || isFetching}
            placeholder={
              selectedView === VIEW_OPTIONS.CARDS
                ? t('search-classes')
                : t('search-students')
            }
            onChange={(value) => onInputChange(value, selectedView)}
            rightSectionWidth={60}
            rightSection={
              <div className={s.inputRightSection}>
                <Icon name="StudentSvg" color="black" size="sm" />

                <Text
                  untranslatedText={
                    selectedView === VIEW_OPTIONS.CARDS
                      ? `${totalNumberOfClassesOrGroups}`
                      : `${paginationData.totalNumberOfSearchResults}`
                  }
                  type="body2"
                />
              </div>
            }
          />

          <div className={s.inputRightSection}>
            <Icon name="CheckMarkSvg" color="turquoise" size="md" />

            <Text
              color="turquoise"
              untranslatedText={`${selectedStudents.length}`}
              type="body1"
            />
          </div>
        </div>

        <div className={s.radiosWrapper}>
          <Radio
            isDisabled={isDisabled || isFetching}
            label="students"
            value={VIEW_OPTIONS.TABLE}
            isChecked={selectedView === VIEW_OPTIONS.TABLE}
            onChange={() => setSelectedView(VIEW_OPTIONS.TABLE)}
          />

          <Radio
            isDisabled={isDisabled || isFetching}
            label={isSchoolRole ? 'classes' : 'groups'}
            value={VIEW_OPTIONS.CARDS}
            isChecked={selectedView === VIEW_OPTIONS.CARDS}
            onChange={() => setSelectedView(VIEW_OPTIONS.CARDS)}
          />
        </div>
      </div>

      {selectedView === VIEW_OPTIONS.CARDS && (
        <div className={s.classesWrapper}>
          <ScrollArea
            h={scrollAreaHeight}
            scrollbarSize={4}
            scrollbars="y"
            offsetScrollbars
            classNames={{ thumb: 'thumb' }}
          >
            <>
              {displayedClassesOrGroups.length === 0 && (
                <Text
                  transKey="no-results-found"
                  type="h4"
                  align="center"
                  mt={64}
                />
              )}

              <Grid grow columns={2} mr={12}>
                {displayedClassesOrGroups.map((item) => {
                  // is checked if at least one student from the studentsByClassId[item.id] is in the selectedStudents array

                  const isCardChecked = studentsByClassOrGroupId[
                    item.id
                  ]?.every((student) =>
                    selectedStudents.some(
                      (selectedStudent) => selectedStudent.id === student.id
                    )
                  );

                  return (
                    <Grid.Col key={item.id} span={1} maw={441}>
                      {isSchoolRole ? (
                        <ClassInfoCard
                          key={item.id}
                          title={item.name}
                          studentsCount={item.studentsCount}
                          grade={item.grade}
                          value={item.id}
                          isChecked={isCardChecked}
                          isDisabled={isFetching}
                          onChange={() =>
                            onClassOrGroupSelection(item.id, isCardChecked)
                          }
                        />
                      ) : (
                        <GroupInfoCard
                          key={item.id}
                          value={item.id}
                          title={item.name}
                          studentsCount={item.studentsCount}
                          isChecked={isCardChecked}
                          isDisabled={isFetching}
                          onChange={() =>
                            onClassOrGroupSelection(item.id, isCardChecked)
                          }
                        />
                      )}
                    </Grid.Col>
                  );
                })}
              </Grid>
            </>
          </ScrollArea>
        </div>
      )}

      {selectedView === VIEW_OPTIONS.TABLE && students.length > 0 && (
        <ScrollArea
          h={scrollAreaHeight}
          scrollbarSize={4}
          scrollbars="y"
          offsetScrollbars
          classNames={{ thumb: 'thumb' }}
        >
          <div className={s.tableWrapper}>
            {TABLE_VIEW_COMPONENT}

            <Pagination
              total={paginationData.totalNumberOfPages}
              value={paginationData.activePage}
              hideWithOnePage
              withControls={false}
              onChange={(value) => paginationData.onPageChange(value)}
              disabled={isFetching}
              m="0 auto"
            />
          </div>
        </ScrollArea>
      )}

      {/* {(totalNumberOfClassesOrGroups === 0 ||
        displayedClassesOrGroups.length === 0) &&
        selectedView === VIEW_OPTIONS.CARDS && (
          <Text transKey="no-results-found" type="h4" align="center" mt={64} />
        )} */}

      {selectedView === VIEW_OPTIONS.TABLE &&
        (paginationData.totalNumberOfResults === 0 || students.length === 0) &&
        !isFetching && (
          <Text transKey="no-results-found" type="h4" align="center" mt={64} />
        )}
    </Paper>
  );
};

export default StudentsSelectorCard;
