import {
  Combobox,
  Input,
  InputBase,
  ScrollArea,
  useCombobox,
} from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { RiCloseFill } from 'react-icons/ri';

import baseTheme from '@/styles/baseTheme';

import Checkbox from '../Checkbox/Checkbox';
import Text from '../Text/Text';
import s from './SelectDropdown.module.css';

type SelectDropdownProps = {
  value: string | string[];
  type?: 'select' | 'multi-select';
  data: {
    label: string;
    value: string;
    disabled?: boolean;
  }[];
  required?: boolean;
  onChange: (value: string) => void;
  placeholder: string;
  label?: string;
  error?: string;
  clearable?: boolean;
  onClear?: () => void;
  allValuesSelectedText?: string;
  onSelectAllToggle?: (value: string[] | []) => void;
  isDisabled?: boolean;
  unstyled?: boolean;
  isFirstOptionSeparated?: boolean;
  maxContentHeight?: number;
};

const SelectDropdown = ({
  allValuesSelectedText = '',
  clearable = false,
  data,
  error,
  isDisabled,
  isFirstOptionSeparated = false,
  label,
  maxContentHeight,
  onChange,
  onClear,
  onSelectAllToggle,
  placeholder,
  required,
  type = 'select',
  unstyled = false,
  value,
}: SelectDropdownProps) => {
  const { t } = useTranslation();

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const areAllChecked = type === 'multi-select' && data.length === value.length;

  const getDisplayedSelectedValues = () => {
    if (!Array.isArray(value) || value.length === 0) {
      return '';
    }

    if (areAllChecked) {
      return allValuesSelectedText;
    }

    return value
      .map((val) => data.find((item) => item.value === val)?.label)
      .filter(Boolean)
      .join(', ');
  };

  const options = data.map((item, index) => (
    <Combobox.Option
      key={item.value}
      value={item.value}
      classNames={{
        option:
          isFirstOptionSeparated && index === 0
            ? s.optionWithSeparator
            : s.option,
      }}
      onMouseOver={() => combobox.selectOption(index)}
      selected={value === item.value}
      className={value === item.value ? s.selectedOption : ''}
      disabled={item.disabled}
    >
      {type === 'multi-select' && (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 10,
          }}
        >
          <Checkbox
            isChecked={value.includes(item.value) || false}
            onChange={() => {
              if (value === item.value) {
                onChange('');
              } else {
                onChange(item.value);
              }
            }}
            value={item.value}
          />

          <div>{item.label}</div>
        </div>
      )}

      {type === 'select' && item.label}
    </Combobox.Option>
  ));

  return (
    <Combobox
      store={combobox}
      withinPortal={false}
      onOptionSubmit={(val) => {
        onChange(val);

        if (type === 'select') {
          combobox.closeDropdown();
        }
      }}
    >
      <Combobox.Target>
        <InputBase
          w="100%"
          variant={unstyled ? 'unstyled' : undefined}
          disabled={isDisabled}
          label={label}
          required={required}
          component="button"
          type="button"
          pointer
          rightSectionPointerEvents={clearable ? 'auto' : 'none'}
          rightSection={
            clearable ? (
              !value ? (
                <Combobox.Chevron c={baseTheme.colors.blue} />
              ) : (
                <RiCloseFill
                  className="cursorPointer"
                  fontSize={20}
                  color={baseTheme.colors.blue}
                  onClick={() => {
                    combobox.resetSelectedOption();
                    onChange('');

                    if (onClear) {
                      onClear();
                    }
                  }}
                />
              )
            ) : (
              <Combobox.Chevron c={baseTheme.colors.blue} />
            )
          }
          onClick={() => {
            if (clearable && !value) {
              combobox.toggleDropdown();

              return;
            }

            combobox.toggleDropdown();
          }}
          error={error}
        >
          {(type === 'select' && value) ||
          (type === 'multi-select' && value.length > 0) ? (
            <Text
              untranslatedText={
                type === 'multi-select'
                  ? getDisplayedSelectedValues()
                  : data.find((item) => item.value === value)?.label ||
                    `........`
              }
              type="body2"
              lineClamp={1}
              mr={6}
            />
          ) : (
            <Input.Placeholder>
              <Text
                untranslatedText={placeholder}
                type="body2"
                lineClamp={1}
                color="gray400"
              />
            </Input.Placeholder>
          )}
        </InputBase>
      </Combobox.Target>

      <Combobox.Dropdown
        p={0}
        style={{
          boxShadow: 'var(--mantine-shadow-md)',
        }}
      >
        <ScrollArea.Autosize
          mah={maxContentHeight || 200}
          type="always"
          scrollbarSize={4}
        >
          {type === 'multi-select' && (
            <>
              <div
                className={`${s.selectAll} ${value === '' ? s.selectedOption : s.option}`}
                role="button"
                tabIndex={0}
                onClick={() => {
                  if (!areAllChecked) {
                    onSelectAllToggle?.(data.map((item) => item.value));
                  } else {
                    onSelectAllToggle?.([]);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    if (!areAllChecked) {
                      onChange('all');
                    } else {
                      onChange('');
                    }
                  }
                }}
              >
                <Checkbox
                  isChecked={areAllChecked}
                  onChange={() => {}}
                  value="all"
                />

                <div>{allValuesSelectedText}</div>
              </div>

              <div className={s.divider} />
            </>
          )}

          {options.length === 0 ? (
            <Combobox.Empty>
              <Text transKey="no-available-options" type="body2" />
            </Combobox.Empty>
          ) : (
            options
          )}

          {type === 'multi-select' && (
            <>
              <div className={s.divider} />

              <div className={s.uncheckWrapper}>
                <Text
                  untranslatedText={t('uncheck-all-capital')}
                  type="button"
                  onClick={() => {
                    onSelectAllToggle?.([]);
                  }}
                  color="turquoise"
                />
              </div>
            </>
          )}
        </ScrollArea.Autosize>
      </Combobox.Dropdown>
    </Combobox>
  );
};

export default SelectDropdown;
