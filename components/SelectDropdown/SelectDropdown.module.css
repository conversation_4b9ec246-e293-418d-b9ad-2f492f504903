.option {
  padding: 12px;
  padding-left: 16px;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  color: var(--color-gray800);

  &[data-combobox-selected] {
    background-color: var(--color-gray25);
  }
}

.selectedOption {
  background-color: var(--color-gray25);
}

.divider {
  height: 1px;
  background-color: var(--color-gray200);
}

.uncheckWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-smd) var(--spacing-sm);

  cursor: pointer;
}

.selectAll {
  display: flex;
  align-items: center;
  gap: 10px;

  cursor: pointer;

  &:hover {
    background-color: var(--color-gray25);
  }
}
