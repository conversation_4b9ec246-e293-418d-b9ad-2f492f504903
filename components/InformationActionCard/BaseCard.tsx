import { Box, Skeleton } from '@mantine/core';
import React from 'react';

import Checkbox from '@/components/Checkbox/Checkbox';
import Text from '@/components/Text/Text';

import s from './BaseCard.module.css';

export type BasicCardType = {
  isLoading?: boolean;
  isChecked?: boolean;
  value?: string;
  isDisabled?: boolean;
  actionComponent?: React.ReactNode;
  onChange?: (v: string) => void;
};

type BaseCardPropsType = {
  title: string;
  children: React.ReactNode;
} & BasicCardType;

const BaseCard = ({
  actionComponent,
  children,
  isChecked,
  isDisabled,
  isLoading,
  onChange,
  title,
  value,
}: BaseCardPropsType) => {
  return (
    <Box
      className={`${s.wrapper} ${isChecked && s.checked} ${isDisabled && 'disabled'}`}
    >
      <Box className={`${s.dataWrapper}`}>
        {isLoading ? (
          <Skeleton height={16.8} />
        ) : (
          <Text untranslatedText={title} type="h4" fw={300} lineClamp={1} />
        )}

        <div className={s.row}>
          {isLoading ? (
            <Skeleton height={16.8} />
          ) : (
            <div className={s.descriptionItem}>{children}</div>
          )}
        </div>
      </Box>

      <Box className={s.actionsWrapper} onClick={(e) => e.stopPropagation()}>
        {value && onChange && (
          <Checkbox
            value={value}
            onChange={onChange}
            isChecked={isChecked || false}
            variant="primary"
            isRequired
            isDisabled={isDisabled}
          />
        )}

        {actionComponent && !isLoading ? actionComponent : null}
      </Box>
    </Box>
  );
};

export default BaseCard;
