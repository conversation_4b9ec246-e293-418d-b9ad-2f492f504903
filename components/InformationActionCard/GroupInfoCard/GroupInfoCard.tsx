import React from 'react';

import Text from '@/components/Text/Text';

import BaseCard, { BasicCardType } from '../BaseCard';
import s from '../BaseCard.module.css';

type GroupInfoCardType = BasicCardType & {
  title: string;
  studentsCount: number;
};

const GroupInfoCard = ({
  actionComponent,
  isChecked = false,
  isDisabled = false,
  isLoading = false,
  onChange,
  studentsCount,
  title,
  value,
}: GroupInfoCardType) => {
  return (
    <BaseCard
      title={title}
      actionComponent={actionComponent}
      isChecked={isChecked}
      isDisabled={isDisabled}
      isLoading={isLoading}
      onChange={onChange}
      value={value}
    >
      {/* <div className={s.verticalLine} /> */}

      <Text transKey="students-capital" type="label" fw={500} mr={6} mt={2} />
      <Text untranslatedText={` ${studentsCount}`} type="body2" fw={700} />
    </BaseCard>
  );
};

export default GroupInfoCard;
