import React from 'react';
import { BsIncognito } from 'react-icons/bs';

import Text from '@/components/Text/Text';

import BaseCard, { BasicCardType } from '../BaseCard';
import s from '../BaseCard.module.css';

type StudentInfoCardProps = BasicCardType & {
  fullName: string;
  grade: string;
  className?: string;
  groupName?: string;
  isAnonymous: boolean;
};

const StudentInfoCard = ({
  actionComponent,
  className,
  fullName,
  grade,
  groupName,
  isAnonymous,
  isChecked,
  isDisabled,
  isLoading,
  onChange,
  value,
}: StudentInfoCardProps) => {
  return (
    <BaseCard
      title={fullName}
      actionComponent={actionComponent}
      isChecked={isChecked}
      isDisabled={isDisabled}
      isLoading={isLoading}
      onChange={onChange}
      value={value}
    >
      {grade && (
        <>
          <Text transKey="grade-capital" type="label" fw={500} mr={6} mt={2} />

          <Text untranslatedText={grade} type="body2" fw={700} />
        </>
      )}

      {(className || groupName) && <div className={s.verticalLine} />}

      {className && (
        <>
          <Text transKey="class-capital" type="label" fw={500} mr={6} mt={2} />
          <span className={s.classTextLabel}>
            <Text
              untranslatedText={className}
              type="body2"
              fw={700}
              lineClamp={1}
            />
          </span>
        </>
      )}

      {groupName && (
        <>
          <Text transKey="group-capital" type="label" fw={500} mr={6} mt={2} />

          <Text untranslatedText={groupName} type="body2" fw={700} />
        </>
      )}

      {isAnonymous && <div className={s.verticalLine} />}

      {isAnonymous && <BsIncognito fontSize={20} color="var(--color-blue)" />}
    </BaseCard>
  );
};

export default StudentInfoCard;
