import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Flex, Tooltip } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BsIncognito } from 'react-icons/bs';
import { FaKey } from 'react-icons/fa';

import Text from '@/components/Text/Text';

import BaseCard, { BasicCardType } from '../BaseCard';
import s from '../BaseCard.module.css';

type StudentInfoCardProps = BasicCardType & {
  fullName: string;
  grade: string;
  className?: string;
  groupName?: string;
  isAnonymous: boolean;
  enterCode: string;
};

const StudentInfoCard = ({
  actionComponent,
  className,
  enterCode,
  fullName,
  grade,
  groupName,
  isAnonymous,
  isChecked,
  isDisabled,
  isLoading,
  onChange,
  value,
}: StudentInfoCardProps) => {
  const { t } = useTranslation();

  return (
    <BaseCard
      title={fullName}
      titleLeftSection={
        isAnonymous && <BsIncognito fontSize={20} color="var(--color-blue)" />
      }
      actionComponent={actionComponent}
      isChecked={isChecked}
      isDisabled={isDisabled}
      isLoading={isLoading}
      onChange={onChange}
      value={value}
    >
      {grade && (
        <>
          <Text transKey="grade-capital" type="label" fw={500} mr={6} mt={2} />

          <Text untranslatedText={grade} type="body2" fw={700} />
        </>
      )}

      {(className || groupName) && <div className={s.verticalLine} />}

      {className && (
        <>
          <Text transKey="class-capital" type="label" fw={500} mr={6} mt={2} />

          <span className={s.classTextLabel}>
            <Text
              untranslatedText={className}
              type="body2"
              fw={700}
              lineClamp={1}
            />
          </span>
        </>
      )}

      {groupName && (
        <>
          <Text transKey="group-capital" type="label" fw={500} mr={6} mt={2} />

          <Text untranslatedText={groupName} type="body2" fw={700} />
        </>
      )}

      {enterCode && <div className={s.verticalLine} />}

      {enterCode && (
        <CopyButton value={enterCode} timeout={2000}>
          {({ copied, copy }) => (
            <Tooltip
              label={copied ? t('copied') : t('copy')}
              withArrow
              position="top"
            >
              <Flex
                onClick={copy}
                align="center"
                gap="xs"
                style={{ cursor: 'pointer' }}
              >
                <>
                  <Text
                    transKey="login-capital"
                    type="label"
                    fw={500}
                    mr={6}
                    mt={2}
                  />

                  <FaKey size={13} color="var(--color-turquoise)" />

                  <Text untranslatedText={enterCode} type="body2" fw={700} />
                </>
              </Flex>
            </Tooltip>
          )}
        </CopyButton>
      )}
    </BaseCard>
  );
};

export default StudentInfoCard;
