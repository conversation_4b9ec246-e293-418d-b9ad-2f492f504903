import React from 'react';

import Text from '@/components/Text/Text';
import { TeacherStatusType, TranslationKeysType } from '@/types/common';

import styles from './StatusChip.module.css';

type StatusChipProps = {
  status: TeacherStatusType;
};

const bgColors: { [key: string]: string } = {
  active: 'green',
  invited: 'turquoise',
  revoked: 'gray',
  inactive: 'yellow',
  declined: 'red',
  canceled: 'red',
};

const StatusChip = ({ status }: StatusChipProps) => {
  return (
    <div className={`${styles.wrapper} ${styles[bgColors[status]]}`}>
      <Text
        transKey={`${status}-capital` as TranslationKeysType}
        type="label"
        fw={500}
        color={
          status === 'inactive' || status === 'revoked' ? 'black' : 'white'
        }
      />
    </div>
  );
};

export default StatusChip;
