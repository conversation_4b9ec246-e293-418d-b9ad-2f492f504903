import React from 'react';

import Text from '@/components/Text/Text';
import { TeacherStatusType } from '@/types/common';

import BaseCard, { BasicCardType } from '../BaseCard';
import StatusChip from './StatusChip/StatusChip';

type TeacherInfoCardProps = BasicCardType & {
  fullName: string;
  email?: string;
  actionComponent?: React.ReactNode;
  invitationStatus?: TeacherStatusType;
};

const TeacherInfoCard = ({
  actionComponent,
  email,
  fullName,
  invitationStatus,
  isDisabled,
  isLoading,
}: TeacherInfoCardProps) => {
  return (
    <BaseCard
      title={fullName}
      actionComponent={
        <>
          {invitationStatus &&
            (isLoading ? <div /> : <StatusChip status={invitationStatus} />)}

          {actionComponent && !isLoading && actionComponent}
        </>
      }
      isDisabled={isDisabled}
      isLoading={isLoading}
    >
      {email && (
        <Text
          untranslatedText={`${email}`}
          type="body2"
          fw={700}
          lineClamp={1}
        />
      )}
    </BaseCard>
  );
};

export default TeacherInfoCard;
