.wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  border: 2px solid var(--color-gray50);
  border-radius: var(--radius-xs);
  transition: all 0.4s ease;
  overflow: hidden;
}

.checked {
  background-color: var(--color-veryLightGreen);
}

.classTextLabel {
  max-width: 60%;
}

.dataWrapper {
  max-width: 85%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-mdl);

  overflow: hidden;
}

.descriptionItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  min-height: 16px;
}

.actionsWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  align-items: center;
  justify-content: center;
}

.row {
  display: flex;
  align-items: center;
}

.verticalLine {
  width: 1px;
  height: 14px;
  background-color: rgb(163, 165, 164);
  margin: 0 var(--spacing-md);
}
