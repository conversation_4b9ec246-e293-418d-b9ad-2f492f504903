import React from 'react';

import Text from '@/components/Text/Text';

import BaseCard, { BasicCardType } from '../BaseCard';
import s from '../BaseCard.module.css';

type ClassInfoCardType = BasicCardType & {
  title: string;
  grade: string;
  studentsCount: number;
};

const ClassInfoCard = ({
  actionComponent,
  grade,
  isChecked = false,
  isDisabled = false,
  isLoading = false,
  onChange,
  studentsCount,
  title,
  value,
}: ClassInfoCardType) => {
  return (
    <BaseCard
      title={title}
      actionComponent={actionComponent}
      isChecked={isChecked}
      isDisabled={isDisabled}
      isLoading={isLoading}
      onChange={onChange}
      value={value}
    >
      <Text transKey="grade-capital" type="label" fw={500} mr={6} mt={2} />
      <Text untranslatedText={grade} type="body2" fw={700} />

      <div className={s.verticalLine} />

      <Text transKey="students-capital" type="label" fw={500} mr={6} mt={2} />
      <Text untranslatedText={` ${studentsCount}`} type="body2" fw={700} />
    </BaseCard>
  );
};

export default ClassInfoCard;
