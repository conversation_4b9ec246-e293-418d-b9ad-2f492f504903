import { Skeleton, Space, Spoiler } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import Text from '@/components/Text/Text';

import Card from '../../Card/Card';
import Icon from '../../Icon/Icon';
import s from './CollapsibleDescription.module.css';

type CollapsibleCardPropsType = {
  title: string;
  description: string;
  isExpanded?: boolean;
  isLoading?: boolean;
};

const CollapsibleDescription = ({
  description,
  isExpanded,
  isLoading = false,
  title,
}: CollapsibleCardPropsType): JSX.Element => {
  const [opened, { toggle }] = useDisclosure(isExpanded);

  return isLoading ? (
    <Skeleton height={160} radius="lg" />
  ) : (
    <div className={s.wrapper}>
      <Card
        size="xl"
        shadow="none"
        radius="sm"
        borderSize={2}
        borderColor="gray50"
      >
        <div className={s['action-title']}>
          <Text untranslatedText={title} type="h3" />

          <Icon
            name="ArrowDown"
            color="gray500"
            onClick={toggle}
            className={`${s['close-icon']} ${opened ? s.rotate : s['rotate-reset']}`}
          />
        </div>

        <Space pb={20} />

        <Spoiler
          maxHeight={40}
          showLabel=""
          hideLabel=""
          expanded={opened}
          transitionDuration={400}
          className={s['content-wrapper']}
          mb={0}
        >
          <Text untranslatedText={description} type="subTitle1" />
        </Spoiler>
      </Card>
    </div>
  );
};

export default CollapsibleDescription;
