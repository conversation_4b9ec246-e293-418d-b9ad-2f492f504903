import { useTranslation } from 'react-i18next';

import { BrowserCompatibilityContext } from '@/context/BrowserCompatibilityProvider';

import styles from './BrowserCompatibility.module.css';

const BrowserCompatibility = () => {
  const { t } = useTranslation();

  const {
    browserInfo,
    isIncompatibleBrowser,
    minimumCompatibleBrowserVersion,
  } = BrowserCompatibilityContext();

  return isIncompatibleBrowser ? (
    <div className={styles.wrapper}>
      <p className={styles.message}>
        {t('browser-incompatible-message', {
          browserName: browserInfo.browser,
          browserVersion:
            minimumCompatibleBrowserVersion[
              browserInfo.browser as keyof typeof minimumCompatibleBrowserVersion
            ],
        })}
      </p>
    </div>
  ) : null;
};

export default BrowserCompatibility;
