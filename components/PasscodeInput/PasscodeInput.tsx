import { PasswordInput, Popover, Progress } from '@mantine/core';
import { useState } from 'react';
import { FaCheck, FaTimes } from 'react-icons/fa';

import Text from '@/components/Text/Text';
import baseTheme from '@/styles/baseTheme';
import { TranslationKeysType } from '@/types/common';

import s from './PasscodeInput.module.css';

type RequirementsType = {
  re: RegExp;
  label: TranslationKeysType;
};

type PasscodeInputPropsType = {
  value: string;
  onChange: (value: string, areRequirementsMet: boolean) => void;
  requirements: RequirementsType[];
  placeholderText?: string;
  type?: 'password' | 'confirm-password';
  error: string;
};

const { colors } = baseTheme;

const PasswordRequirement = ({
  label,
  meets,
}: {
  meets: boolean;
  label: TranslationKeysType;
}) => {
  return (
    <div className={s['password-requirement-wrapper']}>
      {meets ? (
        <FaCheck fontSize={16} color={colors.green} />
      ) : (
        <FaTimes fontSize={16} color={colors.red} />
      )}
      <Text transKey={label} type="body2" />
    </div>
  );
};

const getStrength = (password: string, requirements: RequirementsType[]) => {
  // Early return if only one requirement is provided
  if (requirements.length === 1) {
    return requirements[0].re.test(password) ? 100 : 0;
  }

  const MIN_LENGTH = 10;
  const BASE_STRENGTH = 100;
  const MIN_STRENGTH = 0;

  // Start with a multiplier based on password length
  let failedRequirements = password.length > MIN_LENGTH ? 0 : 1;

  // Check how many requirements the password fails to meet
  requirements.forEach((requirement) => {
    if (!requirement.re.test(password)) {
      failedRequirements += 1;
    }
  });

  // Calculate the strength
  const deductionPerFailure = BASE_STRENGTH / (requirements.length + 1);
  const finalStrength =
    BASE_STRENGTH - deductionPerFailure * failedRequirements;

  // Return at least the minimum strength
  return Math.max(finalStrength, MIN_STRENGTH);
};

const getColorStrength = (strength: number): string => {
  if (strength < 50) return 'red';

  if (strength < 90) return 'yellow';
  return 'green';
};

const PasscodeInput = ({
  error,
  onChange,
  placeholderText,
  requirements,
  type = 'password',
  value,
}: PasscodeInputPropsType): JSX.Element => {
  const [isPasswordPopOverOpened, setIsPasswordPopOverOpened] = useState(false);

  const checks = requirements.map((requirement) => (
    <PasswordRequirement
      key={requirement.label}
      label={requirement.label}
      meets={requirement.re.test(value)}
    />
  ));

  const strength = getStrength(value, requirements);

  return (
    <Popover
      opened={isPasswordPopOverOpened}
      position="bottom"
      width="target"
      transitionProps={{ transition: 'pop-top-left' }}
    >
      <Popover.Target>
        <div
          onFocusCapture={() => setIsPasswordPopOverOpened(true)}
          onBlurCapture={() => setIsPasswordPopOverOpened(false)}
        >
          <PasswordInput
            placeholder={placeholderText || ''}
            value={value}
            onChange={(e) => {
              const areRequirementsMet = requirements.every((requirement) =>
                requirement.re.test(e.currentTarget.value)
              );
              onChange(e.currentTarget.value, areRequirementsMet);
            }}
            classNames={s}
            error={error}
          />
        </div>
      </Popover.Target>

      <Popover.Dropdown>
        {type === 'password' ? (
          <Progress
            color={getColorStrength(strength)}
            c={baseTheme.colors.red}
            value={strength}
            size={5}
            mb="xs"
          />
        ) : null}

        {checks}
      </Popover.Dropdown>
    </Popover>
  );
};

export default PasscodeInput;
