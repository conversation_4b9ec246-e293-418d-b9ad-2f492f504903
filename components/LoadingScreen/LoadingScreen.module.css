.wrapper {
  width: 100%;
  height: 100vh;
  /* background: #16222a;
  background: -webkit-linear-gradient(to right, #3a6073, #16222a);
  background: linear-gradient(to right, #3a6073, #16222a); */

  background: linear-gradient(136.27deg, #1590c1 9.59%, #3a9ea7 56.18%);
}

.loader {
  width: 400px;
  aspect-ratio: 1;
  display: grid;
}

.loader:before,
.loader:after {
  content: '';
  grid-area: 1/1;
  --c: #0000 calc(100% / 3), #046d8b 0 calc(2 * 100% / 3), #0000 0;
  --c1: linear-gradient(90deg, var(--c));
  --c2: linear-gradient(0deg, var(--c));
  background: var(--c1), var(--c2), var(--c1), var(--c2);
  background-size:
    300% 4px,
    4px 300%;
  background-repeat: no-repeat;
  animation: l11 1s infinite linear;
}

.loader:after {
  margin: 10px;
  transform: scaleX(-1);
  animation-delay: -0.25s;
}

@keyframes l11 {
  0% {
    background-position:
      50% 0,
      100% 100%,
      0 100%,
      0 0;
  }
  25% {
    background-position:
      0 0,
      100% 50%,
      0 100%,
      0 0;
  }
  50% {
    background-position:
      0 0,
      100% 0,
      50% 100%,
      0 0;
  }
  75% {
    background-position:
      0 0,
      100% 0,
      100% 100%,
      0 50%;
  }
  75.01% {
    background-position:
      100% 0,
      100% 0,
      100% 100%,
      0 50%;
  }
  100% {
    background-position:
      50% 0,
      100% 0,
      100% 100%,
      0 100%;
  }
}
