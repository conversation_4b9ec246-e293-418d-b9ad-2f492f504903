import { LoadingOverlay, Paper } from '@mantine/core';
import { JSX } from 'react';

import baseTheme from '@/styles/baseTheme';
import { BorderRadiusType, ColorsType, SpacingType } from '@/types/common';

// For the shadow radius and paddings leave the default values from mantine
// Depending on the designs and how it feels and looks we can adjust them

type SizeType = SpacingType | 'none';

type CardPropsType = {
  children: React.ReactNode;
  shadow?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'none';
  isLoading?: boolean;
  radius?: BorderRadiusType | 'none';
  size?: SizeType;
  bg?: ColorsType;
  borderColor?: ColorsType;
  borderSize?: 0 | 1 | 2 | 3;
  className?: string;
  hasDynamicHeight?: boolean;
  overflow?: 'hidden' | 'visible' | 'auto';
};

const { borderRadius, colors, spacing } = baseTheme;

const Card = ({
  bg = 'white',
  borderColor = 'gray200',
  borderSize = 0,
  children,
  className,
  hasDynamicHeight = false,
  isLoading = false,
  overflow = 'hidden',
  radius = 'sm',
  shadow = 'xs',
  size = 'lg',
}: CardPropsType): JSX.Element => {
  return (
    <Paper
      w="100%"
      bg={colors[bg]}
      h={hasDynamicHeight ? 'fit-content' : '100%'}
      className={className}
      shadow={shadow}
      pos="relative"
      radius={radius === 'none' ? 0 : borderRadius[radius]}
      p={size === 'none' ? 0 : spacing[size]}
      withBorder={Boolean(borderSize)}
      style={{
        border: borderSize
          ? `${borderSize}px solid ${colors[borderColor]}`
          : 'none',
        overflow,
      }}
    >
      <LoadingOverlay visible={isLoading} overlayProps={{ blur: 1 }} />

      {children}
    </Paper>
  );
};

export default Card;
