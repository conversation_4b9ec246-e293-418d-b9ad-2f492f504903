import Image from 'next/image';
import React from 'react';

import Text from '../../Text/Text';
import styles from './WhoTrustsMathPro.module.css';

const TRUSTED_PARTNERS = [
  {
    name: 'nua',
    image: '/images/logos/nua.svg',
    width: 220,
    height: 66,
  },
  {
    name: 'ucl',
    image: '/images/logos/ucl.svg',
    width: 150,
    height: 55,
  },
  {
    name: 'um',
    image: '/images/logos/um.svg',
    width: 150,
    height: 66,
  },
  {
    name: 'uoi',
    image: '/images/logos/uoi.svg',
    width: 220,
    height: 66,
  },
  {
    name: 'ru',
    image: '/images/logos/ru.svg',
    width: 200,
    height: 66,
  },
  {
    name: 'ACS',
    image: '/images/logos/acs.png',
    width: 180,
    height: 65,
  },
  {
    name: 'pamukkale',
    image: '/images/logos/pamukkale.png',
    width: 70,
    height: 70,
  },
  {
    name: 'ku',
    image: '/images/logos/ku.svg',
    width: 150,
    height: 55,
  },
  {
    name: 'pisa',
    image: '/images/logos/pisa.svg',
    width: 150,
    height: 55,
  },
  {
    name: 'milano',
    image: '/images/logos/milano.png',
    width: 65,
    height: 65,
  },
  {
    name: 'linneaus',
    image: '/images/logos/linneaus.png',
    width: 150,
    height: 35,
  },
];

const WhoTrustsMathPro = () => {
  return (
    <div className={styles.trustsWrapper}>
      <div className={styles.trustsContainer}>
        <div className={styles.trustsDetails}>
          <Text
            transKey="who-trusts"
            type="h2"
            mb={20}
            fw={400}
            className={styles.title}
          />

          <Text type="body2" transKey="who-trusts-desc" fw={400} lh={1.5} />
        </div>

        <div className={styles.trustsImagesWrapper}>
          {TRUSTED_PARTNERS.map((trustedPartner) => (
            <Image
              key={trustedPartner.name}
              src={trustedPartner.image}
              alt={trustedPartner.name}
              width={trustedPartner.width}
              height={trustedPartner.height}
              draggable={false}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default WhoTrustsMathPro;
