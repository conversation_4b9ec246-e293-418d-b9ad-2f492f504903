.trustsWrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: var(--spacing-4xl) var(--spacing-lg);
  background-color: white;
}

.trustsContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-2xl);
}

.trustsDetails {
  display: flex;
  flex-direction: column;
  height: fit-content;
  align-self: center;

  max-width: 420px;
  min-width: 330px;
}

.trustsImagesWrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  max-width: 810px;

  flex-wrap: wrap;
  width: 100%;
  gap: var(--spacing-2xl);
  min-height: 180px;
}

@media screen and (max-width: 1031px) {
  .trustsContainer {
    gap: var(--spacing-lg);
  }
}

@media screen and (max-width: 1024px) {
  .trustsDetails {
    max-width: 320px;
  }
}

@media screen and (max-width: 900px) {
  .title {
    font-size: 30px;
    text-align: center !important;
  }

  .trustsContainer {
    flex-wrap: wrap;
    gap: var(--spacing-lg);
  }

  .trustsImagesWrapper {
    margin-top: var(--spacing-lg);
    max-width: 100%;
  }

  .trustsDetails {
    width: 100%;
    max-width: 100%;
  }

  .trustsWrapper {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }
}

@media screen and (max-width: 480px) {
  .title {
    font-size: 28px;
  }
}
