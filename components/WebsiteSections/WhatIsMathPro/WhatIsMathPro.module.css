.aboutSection {
  background: linear-gradient(136.27deg, #1590c1 9.59%, #3a9ea7 56.18%),
    linear-gradient(0deg, #fefefe, #fefefe);

  color: white;

  overflow: visible;

  padding: var(--spacing-4xl) var(--spacing-lg);
  display: flex;
  justify-content: center;
}

.aboutContainer {
  width: 100%;
  max-width: 1224px;
  display: flex;
  justify-content: space-between;
  justify-self: center;
  align-self: center;
  gap: var(--spacing-xl);

  position: relative;
}

.aboutContent {
  width: 100%;
  max-width: 550px;

  display: flex;
  flex-direction: column;
}

.aboutTitle {
  font-size: 42px;
  font-weight: 400 !important;
  margin-bottom: var(--spacing-lg) !important;
}

.tabletImageWrapper {
  margin-right: -24px;

  z-index: 1;
}

.tabletImage {
  width: 100%;
  max-width: 720px;
  height: auto;
  position: absolute;
  right: -100px;
  bottom: -150px;
}

@media screen and (max-width: 1426px) {
  .tabletImage {
    right: -60px;
  }

  .aboutContent {
    max-width: 500px;
  }
}

@media screen and (max-width: 1345px) {
  .tabletImage {
    right: -40px;
  }
}

@media screen and (max-width: 1306px) {
  .tabletImage {
    right: -20px;
  }
}

@media screen and (max-width: 1290px) {
  .tabletImage {
    right: -20px;
    max-width: 700px;
  }
}

@media screen and (max-width: 1211px) {
  .tabletImage {
    right: -20px;
    max-width: 600px;
  }
}

@media screen and (max-width: 1115px) {
  .aboutContent {
    max-width: 470px;
  }

  .tabletImage {
    max-width: 70vw;
    bottom: -61vw;

    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .aboutContent {
    max-width: 100%;
  }

  .aboutSection {
    max-height: fit-content;
  }

  .aboutContainer {
    margin-bottom: 47vw;
  }
}

@media screen and (max-width: 990px) {
  .aboutSection {
    max-height: fit-content;
  }
}

@media screen and (max-width: 900px) {
  .aboutTitle {
    font-size: 32px;
  }
  .aboutSection {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }
}

@media screen and (max-width: 768px) {
  .tabletImage {
    max-width: 100%;
  }

  .aboutContainer {
    margin-bottom: 63vw;
  }

  .tabletImage {
    bottom: -76vw;
  }
}

@media screen and (max-width: 480px) {
  .aboutTitle {
    font-size: 30px;
  }

  .tabletImage {
    bottom: -83vw;
  }
}
