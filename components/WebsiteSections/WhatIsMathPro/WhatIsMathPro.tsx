import Image from 'next/image';
import React from 'react';

import Text from '../../Text/Text';
import styles from './WhatIsMathPro.module.css';

const WhatIsMathPro = ({ type }: { type: 'home' | 'schools' }) => {
  return (
    <div className={`${styles.aboutSection} specialBackground`}>
      <div className={styles.aboutContainer}>
        <div className={styles.aboutContent}>
          <Text
            transKey="what-is-math-pro"
            color="white"
            type="h2"
            className={styles.aboutTitle}
          />

          <Text
            type="subTitle1"
            color="white"
            transKey="what-is-math-pro-desc"
            lh={1.4}
          />
        </div>

        <Image
          src={`/images/tablet_${type}.png`}
          alt="MathPro Platform Interface"
          className={styles.tabletImage}
          draggable={false}
          width={640}
          height={496}
        />
      </div>
    </div>
  );
};

export default WhatIsMathPro;
