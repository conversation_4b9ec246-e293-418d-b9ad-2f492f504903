import router from 'next/router';
import React from 'react';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';

import styles from './Hero.module.css';

type HeroProps = {
  page: 'home' | 'schools' | 'products' | 'about';
};

const Hero = ({ page }: HeroProps) => {
  return (
    <div className={`${styles.hero} ${styles[page]}`}>
      <div className={styles.heroContent}>
        <div className={styles.heroDetails}>
          <Text
            type="h1"
            className={styles.title}
            transKey={`${page}-hero-title`}
            fw={400}
          />

          {page !== 'home' && (
            <Text
              type="subTitle1"
              className={styles.description}
              transKey={`${page}-hero-desc`}
              fw={400}
              lh={1.4}
            />
          )}

          {page === 'home' && (
            <div className={styles.buttons}>
              <Button
                transKey="student-capital"
                onClick={() => router.push('conduct-student-test')}
              />

              <Button
                transKey="educator-capital"
                onClick={() => router.push('auth/login')}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Hero;
