.hero {
  background-image: url('../../../public/images/hero_bg.jpg');
  background-size: cover;
  background-position: center;

  height: 710px;

  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg);

  overflow: hidden;

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hero::-webkit-scrollbar {
  display: none;
}

.heroContent {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  justify-self: center;
  align-self: center;
  position: relative;
  height: 100%;
  justify-content: center;

  transition: all 0.2s ease;
}

.heroContent::before {
  content: '';
  position: absolute;
  bottom: -24px;
  right: 0;
  background-size: cover;
  background-position: center;
}

/* Home page Background */
.hero.home .heroContent::before {
  background-image: url('../../../public/images/hero_child.webp');
  z-index: 1;

  width: 800px;
  height: 650px;
  right: -130px;

  @media screen and (max-width: 1100px) {
    width: 600px;
    height: 500px;
    right: -130px;
  }

  @media screen and (max-width: 900px) {
    width: 500px;
    height: 400px;
    right: -130px;
  }
}

/* Schools page Background */
.hero.schools .heroContent::before {
  background-image: url('../../../public/images/hero_girls_0.webp');
  z-index: 1;

  width: 1100px;
  height: 650px;

  right: -180px;

  @media screen and (max-width: 1100px) {
    width: 850px;
    height: 450px;
    right: -120px;
  }

  @media screen and (max-width: 900px) {
    width: 600px;
    height: 300px;
    right: -120px;
  }
}

/* Products page Background */
.hero.products .heroContent::before {
  background-image: url('../../../public/images/products_header_no_bg.webp');

  z-index: 1;

  width: 900px;
  height: 600px;

  right: -70px;

  @media screen and (max-width: 1100px) {
    width: 700px;
    height: 500px;
    right: -130px;
  }

  @media screen and (max-width: 900px) {
    width: 600px;
    height: 400px;
    right: -240px;
  }
}

/* About us page Background */
.hero.about .heroContent::before {
  background-image: url('../../../public/images/tablet_about_us.webp');
  z-index: 1;

  width: 800px;
  height: 500px;

  right: -100px;
  bottom: 40px;

  @media screen and (max-width: 1100px) {
    width: 600px;
    height: 400px;
    right: -60px;
  }

  @media screen and (max-width: 900px) {
    width: 350px;
    height: 250px;
    right: -40px;
    bottom: 70px;
  }
}

.heroDetails {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--spacing-xl);
  z-index: 1;
  width: fit-content;
  max-width: 420px;
}

.title {
  max-width: 500px;
  text-align: start;
}

.description {
  max-width: 500px;
  text-align: start;
}

.imageContainer {
  flex: 1;
  justify-content: start;
}

.buttons {
  display: flex;
  gap: var(--spacing-lg);
}

@media screen and (max-width: 1100px) {
  .hero {
    height: 600px;
  }
}

@media screen and (max-width: 900px) {
  .hero {
    height: 500px;
    background-image: url('../../../public/images/hero_bg.jpg');
  }

  .title {
    font-size: 32px;
  }
}

@media screen and (max-width: 768px) {
  .hero {
    height: 450px;
  }

  .title {
    max-width: 350px;
  }
}

@media screen and (max-width: 700px) {
  .heroContent::before {
    display: none;
  }

  .title {
    max-width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .title {
    font-size: 30px;
  }
}

/* .hero {
  background-image: url('../../../public/images/hero_home.jpg');

  background-size: cover;
  background-position: center;
  height: 710px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: var(--spacing-lg);

  @media screen and (max-width: 900px) {
    background-image: url('../../../public/images/hero_bg.jpg');
  }
}

.home {
  background-image: url('../../../public/images/hero_home.jpg');
}

.schools {
  background-image: url('../../../public/images/hero_schools.png');
}

.heroContent {
  width: 100%;
  max-width: 1224px;
  display: flex;
  flex-direction: column;
  justify-self: center;
  align-self: center;
  position: relative;
  height: 100%;
  justify-content: center;

  transition: all 0.2s ease;
}

.heroContent::before {
  content: '';
  position: absolute;
  bottom: -24px;
  right: 0;
  background-size: cover;
  background-position: center;
}

.heroDetails {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--spacing-xl);
  z-index: 1;
  width: fit-content;
  padding: var(--spacing-md);

  margin-left: -16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px) saturate(160%);
  -webkit-backdrop-filter: blur(12px) saturate(160%);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.25);

  @media screen and (max-width: 768px) {
    width: 100%;
    margin-left: 0;
  }
}

.title {
  max-width: 500px;
  text-align: start;
}

.description {
  max-width: 500px;
  text-align: start;
}

.imageContainer {
  flex: 1;
  justify-content: start;
}

.buttons {
  display: flex;
  gap: var(--spacing-lg);
}

@media screen and (max-width: 1100px) {
  .hero {
    height: 600px;
  }
}

@media screen and (max-width: 900px) {
  .hero {
    height: 500px;
  }

  .title {
    font-size: 32px;
  }
}

@media screen and (max-width: 768px) {
  .hero {
    height: 450px;
  }
}

@media screen and (max-width: 700px) {
  .heroContent::before {
    display: none;
  }

  .title {
    max-width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .title {
    font-size: 30px;
  }
} */
