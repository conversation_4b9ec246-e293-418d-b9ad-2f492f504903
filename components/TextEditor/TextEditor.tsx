import { RichTextEditor as MantineRichTextEditor } from '@mantine/tiptap';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useTranslation } from 'react-i18next';

import styles from './TextEditor.module.css';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  isDisabled?: boolean;
}

const TextEditor = ({ isDisabled, onChange, value }: RichTextEditorProps) => {
  const { t } = useTranslation();

  const editorProps = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Placeholder.configure({ placeholder: t('summary-placeholder') }),
    ],
    content: value,
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  return (
    <MantineRichTextEditor
      editor={editorProps}
      className={`${styles.editor} ${isDisabled ? styles.disabled : ''}`}
    >
      <MantineRichTextEditor.Toolbar className={styles.header}>
        <MantineRichTextEditor.ControlsGroup>
          <MantineRichTextEditor.Bold />
          <MantineRichTextEditor.Italic />
          <MantineRichTextEditor.Underline />
        </MantineRichTextEditor.ControlsGroup>

        <MantineRichTextEditor.ControlsGroup>
          <MantineRichTextEditor.H1 />
          <MantineRichTextEditor.H2 />
          <MantineRichTextEditor.H3 />
        </MantineRichTextEditor.ControlsGroup>

        <MantineRichTextEditor.ControlsGroup>
          <MantineRichTextEditor.BulletList />
          <MantineRichTextEditor.OrderedList />
        </MantineRichTextEditor.ControlsGroup>
      </MantineRichTextEditor.Toolbar>

      <MantineRichTextEditor.Content
        className={styles.editorContent}
        mah={200}
      />
    </MantineRichTextEditor>
  );
};

export default TextEditor;
