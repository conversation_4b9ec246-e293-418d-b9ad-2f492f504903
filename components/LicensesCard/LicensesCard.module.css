.wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-top: var(--spacing-2xl);
  gap: var(--spacing-2xl);
  justify-items: center;
  padding: 0 var(--spacing-xl);
}

.details-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-smd);
}

.details-wrapper-reverse {
  flex-direction: row-reverse;
}

.no-license-earned {
  opacity: 0.5;
}

.renew-text {
  position: absolute;
  right: 32px;
  bottom: 32px;
  text-decoration: none;

  &::after {
    content: '';
    display: block;
    width: 0px;
    height: 0px;
    background-color: var(--color-white);
  }

  &:hover {
    &::after {
      width: 100%;
      height: 2px;
      transition: width ease 0.4s;
    }
  }
}
