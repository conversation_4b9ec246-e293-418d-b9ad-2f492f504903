import Link from 'next/link';

import { PRODUCTS } from '@/common/consts';
import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';
import {
  ProductsType,
  TranslationKeysType,
  UserDetailsType,
} from '@/types/common';

import Icon from '../Icon/Icon';
import s from './LicensesCard.module.css';

type LicensesCardPropsType = {
  licenses: UserDetailsType['profile']['licenses'];
  hasUnlimitedLicenses?: boolean;
  headerTransKey: TranslationKeysType;
  hasRenewButton?: boolean;
};

const LicensesCard = ({
  hasRenewButton = true,
  hasUnlimitedLicenses = false,
  headerTransKey,
  licenses,
}: LicensesCardPropsType): JSX.Element => {
  return (
    <Card size="xl" bg="blue" shadow="none" className="specialBackground">
      <Text transKey={headerTransKey} type="h3" color="white" />

      {hasUnlimitedLicenses ? (
        <Text
          transKey="unlimited-licenses-researcher"
          type="h3"
          color="white"
          mt={20}
        />
      ) : (
        <div className={s.wrapper}>
          {licenses?.map((license, index) => {
            const numberOfLicenses = license.remaining || 0;
            const isLicenseAvailable = numberOfLicenses > 0;

            return (
              <div
                className={`${s['details-wrapper']} ${index % 2 === 0 && s['details-wrapper-reverse']} ${!isLicenseAvailable && s['no-license-earned']}`}
                key={license.type}
              >
                <Icon
                  name={PRODUCTS[license.type as ProductsType].licenseIcon}
                  color="white"
                  w={21}
                  h={26}
                />

                <Text
                  untranslatedText={`${numberOfLicenses}`}
                  type="h1"
                  color="white"
                  lh={0.8}
                />
              </div>
            );
          })}
        </div>
      )}

      {hasRenewButton && (
        <Link href="/dashboard/store?tab=licenses" className={s['renew-text']}>
          <Text transKey="renew-capital" fw={700} color="white" type="body3" />
        </Link>
      )}
    </Card>
  );
};

export default LicensesCard;
