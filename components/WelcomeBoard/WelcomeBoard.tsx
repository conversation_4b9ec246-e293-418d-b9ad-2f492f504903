import { Box, CloseButton, Flex } from '@mantine/core';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import PROFILES from '@/services/profiles';
import baseTheme from '@/styles/baseTheme';
import { IconType } from '@/types/common';

import Card from '../Card/Card';
import Checkbox from '../Checkbox/Checkbox';
import Icon from '../Icon/Icon';
import s from './WelcomeBoard.module.css';

type StepType = {
  icon: {
    name: IconType;
    h?: number;
    w?: number;
  };
  title: string;
  description: string;
};

const { spacing } = baseTheme;

const STEPS_DATA: StepType[] = [
  {
    icon: {
      name: 'STypeCertificationSvg',
      h: 50,
      w: 40,
    },
    title: 'welcome-board-earn-certification',
    description: 'welcome-board-earn-certification-description',
  },
  {
    icon: {
      name: 'StudentSvg',
      h: 44,
      w: 42,
    },
    title: 'welcome-board-add-students',
    description: 'welcome-board-add-students-description',
  },
  {
    icon: {
      name: 'TestSvg',
      h: 40,
      w: 36,
    },
    title: 'welcome-board-conduct-test',
    description: 'welcome-board-conduct-test-description',
  },
];

const WelcomeBoard = (): JSX.Element => {
  const [
    hasAgreedToNotDisplayWelcomeMessage,
    setHasAgreedToNotDisplayWelcomeMessage,
  ] = useState('');
  const { t } = useTranslation();
  const { updateUserProfile, user } = UserContext();
  const [isWelcomeBoardHidden, setIsWelcomeBoardHidden] = useState(
    Boolean(user.profile.onboarded)
  );

  const completeOnboardingMutation = useMutation({
    mutationFn: PROFILES.COMPLETE_ON_BOARD,
    onSuccess: () => {
      setTimeout(() => {
        updateUserProfile({
          onboarded: true,
        });
      }, 1000);
    },
  });

  return (
    <div className={isWelcomeBoardHidden ? s['hide-welcome-card'] : s.wrapper}>
      <Card size="2xl" bg="gray50">
        <Flex pos="absolute" top={32} right={32} gap={16}>
          <Checkbox
            variant="primary"
            label="do-not-show-this-again"
            value="agreed"
            onChange={(value) => setHasAgreedToNotDisplayWelcomeMessage(value)}
            isChecked={hasAgreedToNotDisplayWelcomeMessage === 'agreed'}
          />

          <CloseButton
            onClick={() => {
              if (hasAgreedToNotDisplayWelcomeMessage) {
                completeOnboardingMutation.mutate();
              }
              setIsWelcomeBoardHidden(true);
            }}
            c={baseTheme.colors.turquoise}
            w={12}
            h={12}
            style={{
              transform: 'translate(0px, -4px)',
            }}
            autoFocus={false}
          />
        </Flex>

        <Flex justify="center" align="center" direction="column">
          <Flex
            justify="center"
            align="center"
            direction="column"
            gap={spacing.sm}
            mb={spacing['3xl']}
          >
            <Text transKey="welcome-to-the-dashboard" type="h2" />

            <Text transKey="you-are-new-here" type="body2" />
          </Flex>

          <Flex wrap="wrap" gap={spacing['2xl']}>
            {STEPS_DATA.map((step, i) => {
              return (
                <Flex
                  key={step.title}
                  maw={266}
                  miw={222}
                  direction="column"
                  gap={spacing.md}
                  align="center"
                >
                  <Box w={50} h={50}>
                    <Icon
                      name={step.icon.name}
                      color="turquoise"
                      w={step.icon.w}
                      h={step.icon.h}
                    />
                  </Box>

                  <Text
                    untranslatedText={`${i + 1}. ${t(step.title)}`}
                    type="body1"
                    isBold
                    color="turquoise"
                    align="center"
                  />

                  <Text
                    untranslatedText={t(step.description)}
                    type="body2"
                    align="center"
                  />
                </Flex>
              );
            })}
          </Flex>
        </Flex>
      </Card>
    </div>
  );
};

export default WelcomeBoard;
