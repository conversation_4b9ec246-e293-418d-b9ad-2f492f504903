import { Loading<PERSON>ver<PERSON>, Tooltip } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { FaFileExport, FaFilter, FaUsers } from 'react-icons/fa';
import { RiDeleteBin5Fill, RiFilePaper2Fill } from 'react-icons/ri';

import Text from '@/components/Text/Text';

import s from './ActionButton.module.css';

type ActionButtonPropsType = {
  onClick?: () => void;
  isDisabled?: boolean;
  toolTip?: string;
  isLoading?: boolean;
  type:
    | 'unclaim'
    | 'delete'
    | 'claim'
    | 'assignToClass'
    | 'assignToGroup'
    | 'clearFilters'
    | 'conductTest'
    | 'exportTestResults';
};

const ACTION_DETAILS = {
  unclaim: {
    icon: <FaUsers />,
    content: 'unclaim-students-capital',
  },
  claim: {
    icon: <FaUsers />,
    content: 'claim-students-capital',
  },
  delete: {
    icon: <RiDeleteBin5Fill color="var(--color-danger)" />,
    content: 'delete-capital',
  },
  assignToClass: {
    icon: <FaUsers />,
    content: 'assign-to-class-capital',
  },
  assignToGroup: {
    icon: <FaUsers />,
    content: 'assign-to-group-capital',
  },
  clearFilters: {
    icon: <FaFilter />,
    content: 'clear-filters-capital',
  },
  conductTest: {
    icon: <RiFilePaper2Fill />,
    content: 'create-test-capital',
  },
  exportTestResults: {
    icon: <FaFileExport />,
    content: 'export-test-results-capital',
  },
};

/* Used under Students - Classes/Groups - Teachers to style the actions on the top right. Delete unclaim etc */
const ActionButton = ({
  isDisabled,
  isLoading,
  onClick,
  toolTip,
  type,
}: ActionButtonPropsType): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Tooltip label={toolTip} withArrow disabled={!isDisabled || !toolTip}>
      <div style={{ position: 'relative' }}>
        <button
          type="button"
          onClick={onClick}
          className={`${s.wrapper} ${isDisabled ? s.disabled : ''}`}
          disabled={isDisabled}
        >
          {ACTION_DETAILS[type].icon}

          <Text
            untranslatedText={t(`${ACTION_DETAILS[type].content}`)}
            type="button"
            color={isDisabled ? 'gray' : 'black'}
            hasUserSelect={false}
          />
        </button>

        <LoadingOverlay
          visible={isLoading}
          loaderProps={{
            type: 'bars',
            color: 'var(--color-turquoise)',
            size: 'xs',
            radius: 'sm',
          }}
        />
      </div>
    </Tooltip>
  );
};

export default ActionButton;
