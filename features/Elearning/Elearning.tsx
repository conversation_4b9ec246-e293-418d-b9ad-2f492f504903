import { Box } from '@mantine/core';
import Router from 'next/router';
import { JSX, useState } from 'react';

import { GLOBAL_ERRORS } from '@/common/errors';
import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import request from '@/modules/api';
import { PresignedUrlResponseType } from '@/types/common';

import s from './Elearning.module.css';

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const Elearning = (): JSX.Element => {
  const [isRedirecting, setIsRedirecting] = useState(false);

  const redirect = async () => {
    try {
      setIsRedirecting(true);

      const response = (await request(
        `${BASE_API_URL}/integrations/mathproresearch/url-link/login`
      )) as any;

      if (response.hasError) {
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }

      const { url } = response.data;

      Router.push(url);

      setIsRedirecting(false);
    } catch (error) {
      setIsRedirecting(false);
    }
  };

  return (
    <Box>
      <Text transKey="e-learning" type="h3" lineClamp={2} mt={40} />

      <div className={s.wrapper}>
        <div className={s.titleWrapper}>
          <Text
            transKey="redirect-to-elearning-desc"
            type="h4"
            fw={400}
            align="center"
          />

          <div className={s.titleText}>
            <Text transKey="whenever-you-are-ready" type="h4" fw={400} />

            <div className={s.dummyButton}>
              <Text transKey="redirect" type="label" isBold color="blue" />
            </div>

            <Icon
              name="PointingArrowSvg"
              color="blue"
              className={s.arrowIcon}
            />
          </div>
        </div>

        <Box maw={260} w="100%">
          <Button
            transKey="redirect-capital"
            size="lg"
            onClick={redirect}
            hasFullWidth
            isLoading={isRedirecting}
          />
        </Box>
      </div>
    </Box>
  );
};

export default Elearning;
