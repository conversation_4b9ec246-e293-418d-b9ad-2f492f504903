/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import { Modal } from '@mantine/core';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import AdditionFacts from '../DigiTools/AdditionFacts/AdditionFacts';
import Coins from '../DigiTools/Coins/Coins';
import NumberCards from '../DigiTools/NumberCards/NumberCards';
import TimesTable from '../DigiTools/TimesTable/TimesTable';
import { DigiTools } from '../types';
import s from './PlayDigitToolModal.module.css';

type PlayDigitToolModalProps = {
  selectedDigiTool: DigiTools;
  onCloseModal: () => void;
};

const PlayDigitToolModal = ({
  onCloseModal,
  selectedDigiTool,
}: PlayDigitToolModalProps): JSX.Element | null => {
  const [scale, setScale] = useState(1);

  const DIGI_TOOLS = useMemo(() => {
    return {
      coins: <Coins onCloseModal={onCloseModal} />,
      'number-cards': <NumberCards onCloseModal={onCloseModal} />,
      'times-table': <TimesTable onCloseModal={onCloseModal} />,
      'addition-facts': <AdditionFacts onCloseModal={onCloseModal} />,
    };
  }, [selectedDigiTool]);

  useEffect(() => {
    const handleResize = () => {
      const scaleFactor = Math.min(
        window.innerWidth / 1164,
        window.innerHeight / 864
      );

      if (scaleFactor < 1) {
        setScale(scaleFactor);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once to set initial scale

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [window.innerHeight, window.innerWidth]);

  return (
    <Modal
      opened={Boolean(selectedDigiTool)}
      onClose={onCloseModal}
      fullScreen
      withCloseButton={false}
      classNames={s}
      autoFocus={false}
      trapFocus
    >
      <div
        className={s.container}
        style={{
          transform: `scale(${scale})`,
          width: '1100px',
          height: '800px',
        }}
      >
        {/* <Header
          digiToolName={selectedDigiTool ? t(selectedDigiTool) : ''}
          onClose={onCloseModal}
          // onStartOver={}
        /> */}
        {selectedDigiTool ? DIGI_TOOLS[selectedDigiTool] : null}
      </div>
    </Modal>
  );
};

export default PlayDigitToolModal;
