.container {
  width: fit-content;
  min-width: 1100px;
  max-width: 1771px;
  height: 800px;

  display: flex;
  flex-direction: column;
  padding: var(--spacing-xl);
  background-color: var(--color-bg3);
  border-radius: var(--radius-sm);
}

/* ----------------MODAL STYLES------------------- */

.root {
  background-color: transparent;
}

.content {
  background-color: rgba(0, 0, 0, 0.92);
}

.overlay {
  background-color: transparent;
}

.body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
}
