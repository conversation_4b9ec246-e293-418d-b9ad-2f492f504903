import { Box, CloseButton } from '@mantine/core';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import s from './Header.module.css';

type ProgressHeaderPropsType = {
  digiToolName: string;
  onClose: () => void;
  onStartOver?: () => void;
  onDone?: () => void;
};

const Header = ({
  digiToolName,
  onClose,
  onDone,
  onStartOver,
}: ProgressHeaderPropsType): JSX.Element => {
  return (
    <Box className={s.wrapper}>
      <Text transKey={digiToolName as TranslationKeysType} type="h3" fw={250} />

      <div className={s.actions}>
        {onStartOver && (
          <Button
            transKey="start-over-capital"
            variant="primaryOutlined"
            onClick={() => {
              onStartOver();
            }}
          />
        )}

        {onDone && (
          <Button
            transKey="done-capital"
            variant="primary"
            onClick={() => {
              onDone();
            }}
          />
        )}

        <CloseButton onClick={onClose} variant="outlined" autoFocus={false} />
      </div>
    </Box>
  );
};

export default Header;
