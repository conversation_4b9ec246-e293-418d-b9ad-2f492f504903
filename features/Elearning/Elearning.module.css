.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: var(--spacing-3xl);
  width: 100%;
  padding: var(--spacing-4xl) var(--spacing-lg);
}

.titleWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-4xl);
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-md);
}

.dummyButton {
  background-color: var(--color-gray50);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-xl);
  border-radius: var(--radius-2xs);
  color: var(--color-white);
  margin-right: var(--spacing-smd);
}

.titleText {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-smd);
  margin-bottom: var(--spacing-4xl);
}

.dummyButtonWrapper {
  display: flex;
  align-items: center;
}

.arrowIcon {
  position: absolute;
  top: 56px;
  right: 140px;
  rotate: 120deg;
  transform: skew(-50deg, 50deg);

  @media screen and (max-width: 1440px) {
    top: 64px;
  }

  @media screen and (max-width: 896px) {
    top: 124px;
    right: 220px;
  }
}
