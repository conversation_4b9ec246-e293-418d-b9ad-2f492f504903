.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  background-color: var(--color-white);
  z-index: 10;
  height: 74px;
}

.container {
  height: calc(100% - 74px);
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.cardsWrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin: var(--spacing-xl) auto;
  width: 100%;
  max-width: fit-content;

  @media (max-width: 1050px) {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin: 60px 0;
    max-width: 100%;
    align-items: center;
  }
}

.card {
  display: flex;
  flex-direction: column;
  background-color: var(--color-white);
  border-radius: var(--radius-smd);
  overflow: hidden;
  text-align: center;
  max-width: 250px;
  min-width: 180px;
  transition: all 0.2s ease;
  border: 2px solid var(--color-gray50);
  cursor: pointer;

  @media (max-width: 1050px) {
    flex-direction: row;
    width: 100%;
    max-width: 600px;
    min-width: 480px;
  }
}

.card:hover {
  box-shadow: var(--shadow-hover);
}

.cardImage {
  object-fit: cover;
  width: 100%;
  height: 180px;

  @media (max-width: 1050px) {
    width: 160px;
    height: 120px;
  }
}

.cardDescription {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  text-align: center;

  @media (max-width: 1050px) {
    padding: var(--spacing-md) var(--spacing-xl);
    justify-content: center;
    align-items: flex-start;
    width: 100%;

    & > h3 {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    & > p {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

@media (min-width: 718px) {
  .cardsWrapper {
    grid-template-columns: repeat(3, 1fr);
  }
}
