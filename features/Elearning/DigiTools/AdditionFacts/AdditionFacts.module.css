.wrapper {
  margin: 0 auto;
  background-color: var(--color-white);
  padding: var(--spacing-md);
  margin: var(--spacing-xl) auto;
}

.colorGrid {
  display: grid;
  grid-template-columns: repeat(11, minmax(0, 1fr));
  background-color: var(--color-gray700);
  gap: 1px;
  padding: 1px;
  width: 100%;
  max-width: 600px;
}

.cell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
}

.sideHeader {
  background-color: rgb(229, 160, 68);
  color: rgb(178, 44, 88);
  font-weight: 600;
  font-size: 15px;
}

.contentCell {
  background-color: rgb(178, 44, 88);
  font-weight: 600;
  font-size: 14px;

  & > p {
    background-color: white;
    padding: 2px 6px 1px 6px;
    border-radius: 4px;
    color: var(--color-gray700);
  }
}

.diagonalCell {
  background-color: var(--color-white);
  font-size: 14px;
  font-weight: 600;
  color: white;

  cursor: pointer;

  & > p {
    background-color: rgb(178, 44, 88);
    padding: 2px 6px 1px 6px;
    border-radius: 4px;
    color: var(--color-white);
    transition: opacity 0.2s ease-in-out;

    opacity: 0;
  }

  &:hover {
    & > p {
      opacity: 1;
    }
  }
}
