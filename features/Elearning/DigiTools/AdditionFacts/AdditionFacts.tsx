import React, { useMemo } from 'react';

import Header from '../../PlayDigitToolModal/Header/Header';
import s from './AdditionFacts.module.css';

type AdditionFactsProps = {
  onCloseModal: () => void;
};
const AdditionFacts = ({ onCloseModal }: AdditionFactsProps) => {
  const useTableData = () => {
    return useMemo(() => {
      const data = [];
      for (let row = 0; row <= 10; row += 1) {
        for (let col = 0; col <= 10; col += 1) {
          let value;

          if (row === 10 && col === 0) {
            value = '+';
          } else if (row === 10 && col !== 0) {
            value = col;
          } else if (row !== 10 && col === 0) {
            value = 10 - row;
          } else {
            value = (10 - row + col).toString().padStart(2, '0');
          }

          data.push({ row, col, value });
        }
      }
      return data;
    }, []);
  };

  const useHighlightedCells = () => {
    return useMemo(() => {
      const highlightedCells = new Set<string>();

      let col = 1;
      let row = 9;

      while (col <= 10) {
        highlightedCells.add(`${row}-${col}`);
        col += 1;
        row -= 1;
      }

      return highlightedCells;
    }, []);
  };

  const highlightedCell = useHighlightedCells();

  const getCellClass = (
    row: number,
    col: number,
    highlightedCells: Set<string>
  ) => {
    if (col === 0 || row === 10) return s.sideHeader;

    if (highlightedCells.has(`${row}-${col}`)) return s.diagonalCell;
    return s.contentCell;
  };

  return (
    <>
      <Header
        digiToolName="addition-facts"
        onClose={onCloseModal}
        // onStartOver={}
      />

      <div className={s.wrapper}>
        <div className={s.colorGrid}>
          {useTableData().map(({ col, row, value }) => (
            <div
              key={`${row}-${col}`}
              className={`${s.cell} ${getCellClass(row, col, highlightedCell)}`}
            >
              <p>{value}</p>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default AdditionFacts;
