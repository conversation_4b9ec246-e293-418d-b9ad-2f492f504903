import { JSX, useState } from 'react';

import Header from '../../PlayDigitToolModal/Header/Header';

type CoinsProps = {
  onCloseModal: () => void;
};

const Coins = ({ onCloseModal }: CoinsProps): JSX.Element => {
  const [testState, setTestState] = useState([]);

  return (
    <>
      <Header
        digiToolName="coins"
        onClose={onCloseModal}
        onStartOver={() => setTestState([])}
      />
      <div>coins</div>
    </>
  );
};

export default Coins;
