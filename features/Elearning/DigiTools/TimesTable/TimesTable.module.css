.wrapper {
  margin: 0 auto;
  background-color: var(--color-white);
  padding: var(--spacing-md);
  margin: var(--spacing-xl) auto;
}

.timesTable {
  display: grid;
  grid-template-columns: repeat(11, minmax(0, 1fr));
  background-color: var(--color-gray900);
  gap: 1px;
  padding: 1px;
  width: 100%;
  max-width: 600px;
}

.cell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
}

.headerCell {
  background-color: var(--color-gray200);
}

.regularCell {
  background-color: var(--color-white);
}

.diagonalCell {
  background-color: var(--color-gray200);
  cursor: pointer;

  & > p {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  &:hover {
    & > p {
      opacity: 1;
    }
  }
}
