import React, { useMemo } from 'react';

import Text from '@/components/Text/Text';

import Header from '../../PlayDigitToolModal/Header/Header';
import s from './TimesTable.module.css';

type TimesTableProps = {
  onCloseModal: () => void;
};

const TimesTable = ({ onCloseModal }: TimesTableProps) => {
  const useTableData = () => {
    return useMemo(() => {
      const data = [];
      for (let row = 0; row <= 10; row += 1) {
        for (let col = 0; col <= 10; col += 1) {
          let value;

          if (row === 0 && col === 0) {
            value = 'x';
          } else if (row === 0) {
            value = col;
          } else if (col === 0) {
            value = row;
          } else {
            value = row * col;
          }

          data.push({ row, col, value });
        }
      }
      return data;
    }, []);
  };

  const getCellClass = (row: number, col: number) => {
    if (row === 0 || col === 0) return s.headerCell;

    if (row === col) return s.diagonalCell;

    return s.regularCell;
  };

  return (
    <>
      <Header
        digiToolName="times-table"
        onClose={onCloseModal}
        // onStartOver={}
      />

      <div className={s.wrapper}>
        <div className={s.timesTable}>
          {useTableData().map(({ col, row, value }) => (
            <div
              key={`${row}-${col}`}
              className={`${s.cell} ${getCellClass(row, col)}`}
            >
              <Text
                untranslatedText={String(value)}
                type="subTitle2"
                isBold={row === 0 || col === 0 || row === col}
                color="gray600"
              />
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default TimesTable;
