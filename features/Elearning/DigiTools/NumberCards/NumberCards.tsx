/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-array-index-key */
import { motion, PanInfo } from 'framer-motion';
import React, { useEffect, useState } from 'react';

import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import Header from '../../PlayDigitToolModal/Header/Header';
import s from './NumberCards.module.css';

type NumberCardsProps = {
  onCloseModal: () => void;
};

type numbersType =
  | 'to20'
  | 'to100'
  | 'to1000'
  | 'decimals'
  | '0.01to1000'
  | 'to100000';

const oneDigit = Array.from({ length: 9 }, (_, i) => i + 1);
const twoDigits = Array.from({ length: 9 }, (_, i) => (i + 1) * 10);
const threeDigits = Array.from({ length: 9 }, (_, i) => (i + 1) * 100);
const fourDigits = Array.from({ length: 9 }, (_, i) => (i + 1) * 1000);
const fiveDigits = Array.from({ length: 9 }, (_, i) => (i + 1) * 10000);
const decOne = Array.from({ length: 9 }, (_, i) => (i + 1) / 10);
const decTwo = Array.from({ length: 9 }, (_, i) => (i + 1) / 100);

const BOX_WIDTH = 33;

const getNumbersArrays = (type: numbersType) => {
  switch (type) {
    case 'to20':
      return [Array.from({ length: 2 }, (_, i) => (i + 1) * 10), oneDigit];
    case 'to100':
      return [[100], twoDigits, oneDigit];
    case 'to1000':
      return [[1000], threeDigits, twoDigits, oneDigit];
    case 'decimals':
      return [oneDigit, decOne, decTwo];
    case '0.01to1000':
      return [[1000], threeDigits, twoDigits, oneDigit, decOne, decTwo];
    case 'to100000':
      return [fiveDigits, fourDigits, threeDigits, twoDigits, oneDigit];
    default:
      return [];
  }
};

const NUMBERS_TYPE = {
  to20: { value: 'to20', transKey: 'to-20-capital' },
  to100: { value: 'to100', transKey: 'to-100-capital' },
  to1000: { value: 'to1000', transKey: 'to-1000-capital' },
  decimals: { value: 'decimals', transKey: 'decimals-capital' },
  decimalTo1000: { value: '0.01to1000', transKey: '0.01-to-1000-capital' },
  to100000: { value: 'to100000', transKey: 'to-100000-capital' },
};

const NumberCards = ({ onCloseModal }: NumberCardsProps): JSX.Element => {
  const [selectedNumbers, setSelectedNumbers] = useState<
    { number: number; x: number; y: number }[]
  >([]);
  const [numbersType, setNumbersType] = useState<numbersType>('to20');
  const [dropzoneCoordinates, setDropzoneCoordinates] = useState({
    x: 0,
    y: 0,
  });

  const [isDragging, setIsDragging] = useState(false);

  const [needsCalibration, setNeedsCalibration] = useState<number | null>(null);

  const [numberStacks, setNumberStacks] = useState(
    getNumbersArrays(numbersType).map((stack) => stack.map((num) => num))
  );

  const handleNumberTypeChange = (value: numbersType) => {
    if (value === numbersType) return;

    setNumbersType(value);

    setSelectedNumbers([]);

    setDropzoneCoordinates({
      x: 0,
      y: 0,
    });

    setNumberStacks([]);
    setIsDragging(false);
    setNeedsCalibration(null);

    setNumberStacks(
      getNumbersArrays(value).map((stack) => stack.map((num) => num))
    );
  };

  const numberOfColumns = numberStacks.length;

  const horizontalPositionPoint = numbersType !== 'decimals' ? 'right' : 'left';

  useEffect(() => {
    // the delay comes from the modal animation effect
    setTimeout(() => {
      const dropzone = document.getElementById('dropzone');

      if (dropzone) {
        const { left, right, top } = dropzone.getBoundingClientRect();
        setDropzoneCoordinates({
          x: horizontalPositionPoint === 'left' ? left : right,
          y: top,
        });
      }
    }, 200);
  }, [numbersType, horizontalPositionPoint]);

  const getDecimalCount = (number: number) => {
    return number.toString().split('.')[1]?.length || 0;
  };

  const getElementsCoordinates = (number: number) => {
    const element = document.getElementById(`number-${number}`);
    const elementTop = element?.getBoundingClientRect().top || 0;

    const yOffset = dropzoneCoordinates.y - elementTop;

    const elementHorizontal =
      element?.getBoundingClientRect()[horizontalPositionPoint] || 0;

    const decimalOffset =
      getDecimalCount(number) * (numbersType === 'decimals' ? 1 : BOX_WIDTH);

    const xOffset = dropzoneCoordinates.x - elementHorizontal + decimalOffset;

    return {
      x: xOffset,
      y: yOffset,
    };
  };

  const formatNumberForDisplay = (number: number) => {
    const numStr = number.toString().replace('.', ',');
    const chars = numStr.split('');
    const formattedNumbers: string[] = [];

    for (let i = 0; i < chars.length; i += 1) {
      if (chars[i] === ',' && i + 1 < chars.length) {
        formattedNumbers.push(chars[i] + chars[i + 1]);
        i += 1;
      } else {
        formattedNumbers.push(chars[i]);
      }
    }

    return formattedNumbers;
  };

  const handleNumberClick = (number: number) => {
    if (isDragging) return;

    setSelectedNumbers((prev) => {
      if (selectedNumbers.some((num) => num.number === number)) {
        return prev.filter((num) => num.number !== number);
      }

      const stackIndex = numberStacks.findIndex((stack) =>
        stack.includes(number)
      );

      const filteredPrev = prev.filter(
        (num) => !numberStacks[stackIndex].includes(num.number)
      );

      return [...filteredPrev, { number, ...getElementsCoordinates(number) }];
    });
  };

  const handleDragEnd = (
    event: MouseEvent | TouchEvent | PointerEvent,
    info: PanInfo,
    number: number
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setSelectedNumbers((prev) => {
      if (selectedNumbers.some((num) => num.number === number)) {
        return prev.filter((num) => num.number !== number);
      }

      const stackIndex = numberStacks.findIndex((stack) =>
        stack.includes(number)
      );

      const filteredPrev = prev.filter(
        (num) => !numberStacks[stackIndex].includes(num.number)
      );

      const coordinates = {
        x: getElementsCoordinates(number).x + info.offset.x,
        y: getElementsCoordinates(number).y + info.offset.y,
      };

      return [
        ...filteredPrev,
        {
          number,
          ...coordinates,
        },
      ];
    });

    setTimeout(() => {
      const coordinates = getElementsCoordinates(number);

      if (coordinates.x !== 0 && coordinates.y !== 0) {
        setNeedsCalibration(number);
      }
    }, 500);

    setIsDragging(false);
  };

  const calibrateNumber = (number: number) => {
    if (number !== needsCalibration) return;

    const coordinates = getElementsCoordinates(number);

    const previousCoordinates = selectedNumbers.find(
      (num) => num.number === number
    );

    setSelectedNumbers((prev) =>
      prev.map((num) => {
        if (num.number === number) {
          return {
            ...num,
            x: (previousCoordinates?.x || 0) + coordinates.x,
            y: (previousCoordinates?.y || 0) + coordinates.y,
          };
        }
        return num;
      })
    );

    setNeedsCalibration(null);
  };

  useEffect(() => {
    if (!needsCalibration) return;

    calibrateNumber(needsCalibration);
  }, [needsCalibration]);

  return (
    <>
      <Header
        digiToolName="playing-cards"
        onClose={onCloseModal}
        onStartOver={() => setSelectedNumbers([])}
      />

      <div className={s.wrapper}>
        <div className={s.gameTypeSelector}>
          {Object.values(NUMBERS_TYPE).map((item) => (
            <button
              type="button"
              key={item.value}
              className={`${s.gameTypeButton} ${
                item.value === numbersType && s.gameTypeButtonActive
              }`}
              onClick={() => handleNumberTypeChange(item.value as numbersType)}
            >
              <Text
                transKey={item.transKey as TranslationKeysType}
                type="button"
                fw={item.value === numbersType ? 600 : 300}
              />
            </button>
          ))}
        </div>

        <div className={s.content}>
          <div className={s.dropZoneWrapper}>
            <div
              className={s.dropZoneTarget}
              id="dropzone"
              style={{
                width:
                  numbersType === '0.01to1000'
                    ? 4 * BOX_WIDTH
                    : numberOfColumns * BOX_WIDTH,
                marginLeft: numbersType === '0.01to1000' ? -(2 * BOX_WIDTH) : 0,
              }}
            />
          </div>

          <div className={s.numbersToDropWrapper}>
            {dropzoneCoordinates.x > 0 &&
              dropzoneCoordinates.y > 0 &&
              numberStacks.map((stack, stackIndex) => {
                return (
                  <div key={stackIndex} className={s.numbersStack}>
                    {stack
                      .map((number) => {
                        const { x: xOffset, y: yOffset } =
                          selectedNumbers?.find(
                            (num) => num.number === number
                          ) || { x: 0, y: 0 };

                        return (
                          <motion.div
                            key={number}
                            id={`number-${number}`}
                            className={`${s.numberContainer} ${s[`columnColor-${numberStacks.length - stackIndex}`]}`}
                            onClick={() => handleNumberClick(number)}
                            drag
                            dragMomentum={false}
                            onDragStart={() => setIsDragging(true)}
                            onDragEnd={(event, info) =>
                              handleDragEnd(event, info, number)
                            }
                            animate={{
                              y: yOffset || 0,
                              x: xOffset || 0,
                            }}
                            transition={{
                              duration: 0.4,
                            }}
                            style={{
                              zIndex:
                                numbersType === 'decimals' ||
                                getDecimalCount(number) > 0
                                  ? numberStacks.length - stackIndex
                                  : stackIndex + 2,
                            }}
                          >
                            {formatNumberForDisplay(number).map(
                              (num, index) => (
                                <p key={index} className={s.number}>
                                  {num}
                                </p>
                              )
                            )}
                          </motion.div>
                        );
                      })
                      .reverse()}
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </>
  );
};

export default NumberCards;
