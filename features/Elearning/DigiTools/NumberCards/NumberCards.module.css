.wrapper {
  display: flex;
  flex-direction: row;
  position: relative;
  margin: var(--spacing-3xl) 0;
}

.gameTypeSelector {
  display: flex;
  flex-direction: column;
  width: fit-content;
  gap: var(--spacing-sm);
  position: absolute;
}

.gameTypeButton {
  padding: var(--spacing-mdl);
  display: flex;
  align-items: center;
  border-radius: var(--radius-xs);
  height: 57px;
  max-height: 57px;
  cursor: pointer;
  border: none;
  background-color: var(--color-gray50);
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: var(--color-gray300);
  }

  & > p {
    white-space: nowrap;
    letter-spacing: 0.4px;
    /* Prevent layout shift when bolding */
    font-variation-settings: 'wght' 400;
  }
}

.gameTypeButtonActive {
  background-color: var(--color-gray300);
  user-select: none;

  & > p {
    letter-spacing: 0;
    font-variation-settings: 'wght' 700;
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 670px;
  margin-left: 240px;
}

.dropZoneWrapper {
  background-color: var(--color-white);
  width: 100%;
  height: 100px;
  padding: var(--spacing-xs);
  border-radius: var(--radius-xs);
  border: 2px dashed rgb(163, 165, 164);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-2xl);
}

.dropZoneTarget {
  height: 36px;
}

.numbersToDropWrapper {
  display: flex;
  gap: var(--spacing-lg);
}

.numbersStack {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  justify-content: flex-end;
  height: 356px;
}

.numberContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white);
  cursor: pointer !important;
  text-align: center !important;
  height: 36px;

  &:hover {
    box-shadow: var(--shadow-hover);
  }
}

.number {
  color: rgba(48, 18, 40, 1);
  font-weight: 600;
  padding: var(--spacing-sm);
  width: 33px;
}

.columnColor-1 {
  background-color: rgba(218, 213, 70, 1);
}

.columnColor-2 {
  background-color: rgba(232, 86, 80, 1);
}

.columnColor-3 {
  background-color: rgba(144, 194, 85, 1);
}

.columnColor-4 {
  background-color: rgba(214, 102, 185, 1);
}

.columnColor-5 {
  background-color: rgba(80, 150, 220, 1);
}

.columnColor-6 {
  background-color: rgba(255, 165, 0, 1);
}
