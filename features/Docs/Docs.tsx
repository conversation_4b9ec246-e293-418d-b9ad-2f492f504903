import { useTranslation } from 'react-i18next';

const Docs = (): JSX.Element => {
  const { t } = useTranslation();

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        fontSize: '28px',
        color: 'var(--color-blue)',
        fontWeight: 'bold',
      }}
    >
      {t('Under construction..')}
    </div>
  );
};

export default Docs;
