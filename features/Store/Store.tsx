import { Box, Indicator, SegmentedControl } from '@mantine/core';
import { useRouter } from 'next/router';
import { useTranslation } from 'react-i18next';
import { GiShoppingCart } from 'react-icons/gi';

import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { CartContext } from '@/context/CartProvider';

import Cart from './components/Cart/Cart';
import Certifications from './components/Certifications/Certifications';
import s from './store.module.css';

const TABS = [
  {
    label: 'test-licenses-capital',
    value: 'licenses',
  },
  {
    label: 'certifications-capital',
    value: 'certifications',
  },
  {
    label: 'e-books-capital',
    value: 'e-books',
  },
];

const CART_QUERY_PARAM = 'cart';

const Store = (): JSX.Element => {
  const router = useRouter();
  const tabQueryParam = router.query.tab as string;
  const { t } = useTranslation();
  const { isCardUpdating, totalNumberOfItemInCart } = CartContext();

  console.log('isCardUpdating under store', isCardUpdating);

  return (
    <div className="routeWrapper">
      <div className={`pageHeaderWrapper ${s.header}`}>
        <Text transKey="store" type="h3" className="textPageIndicator" />

        <SegmentedControl
          value={tabQueryParam}
          onChange={(v) => {
            router.push(`${FULL_DASHBOARD_ROUTES.STORE}?tab=${v}`);
          }}
          withItemsBorders={false}
          data={TABS.map((item) => ({
            value: item.value,
            label: t(item.label),
          }))}
        />

        <Box className="actionButtonIndicator">
          <Indicator
            color="var(--color-yellow)"
            size={20}
            offset={3}
            label={
              <Text
                untranslatedText={`${totalNumberOfItemInCart}`}
                type="body2"
                fw={700}
              />
            }
            disabled={totalNumberOfItemInCart === 0}
            processing={isCardUpdating}
          >
            <Button
              transKey="your-cart"
              variant="turquoisePrimary"
              leftSection={<GiShoppingCart fontSize={24} />}
              onClick={() =>
                router.push(
                  `${FULL_DASHBOARD_ROUTES.STORE}?tab=${CART_QUERY_PARAM}`
                )
              }
            />
          </Indicator>
        </Box>
      </div>
      {/* {tabQueryParam === 'licenses' && <div>Licenses</div>} */}

      {tabQueryParam === 'certifications' && <Certifications />}

      {tabQueryParam === 'e-books' && <div>E-Books</div>}

      {tabQueryParam === CART_QUERY_PARAM && <Cart />}
    </div>
  );
};

export default Store;
