import { useState } from 'react';

import { CartContext } from '@/context/CartProvider';

const Cart = (): JSX.Element => {
  const { addToCart, isCardUpdating } = CartContext();

  console.log('isCardUpdating under Cart component', isCardUpdating);

  return (
    <div>
      {isCardUpdating ? 'Updating...' : 'Not updating...'}{' '}
      <button onClick={() => addToCart({ id: '1', name: 'item 1' })}>
        add new item
      </button>
    </div>
  );
};

export default Cart;
