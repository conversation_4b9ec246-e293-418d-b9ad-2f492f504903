import { Box, Flex, ScrollArea } from '@mantine/core';
import { AnimatePresence } from 'framer-motion';
import { JSX } from 'react';
import { GiShoppingCart } from 'react-icons/gi';

import { CURRENCY_SYMBOLS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { CartContext } from '@/context/CartProvider';
import { PurchasesContext } from '@/context/PurchasesProvider';

import s from './Cart.module.css';
import EmptyCartPlaceholder from './components/EmptyCartPlaceholder/EmptyCartPlaceholder';
import ProductPreviewCard from './components/ProductPreviewCard/ProductPreviewCard';

const Cart = (): JSX.Element => {
  const { openCheckout } = PurchasesContext();
  const { cartItems, checkoutPrice, removeFromCart, totalNumberOfItemInCart } =
    CartContext();

  if (totalNumberOfItemInCart === 0) {
    return (
      <Box className={s.wrapper}>
        <EmptyCartPlaceholder />
      </Box>
    );
  }

  return (
    <Box className={s.wrapper}>
      <Flex align="center" gap={16} mb={8}>
        <GiShoppingCart fontSize={30} color="var(--color-turquoise)" />

        <Text
          untranslatedText={`${cartItems.length} Items`}
          type="body2"
          fw={700}
        />
      </Flex>

      <ScrollArea.Autosize
        type="never"
        mah="calc(100vh - 332px)"
        h="calc(100vh - 332px)"
        mb={24}
      >
        <Flex direction="column" gap={16} mb={64} p="0 8px">
          <AnimatePresence>
            {cartItems.map((item) => (
              <ProductPreviewCard
                key={item.stripeId}
                productDetails={item}
                onRemoveFromCart={() => removeFromCart(item.stripeId)}
              />
            ))}
          </AnimatePresence>
        </Flex>
      </ScrollArea.Autosize>

      <Flex gap={16} justify="flex-end">
        <Flex direction="column">
          <Text
            untranslatedText={`Total : ${CURRENCY_SYMBOLS[cartItems[0]?.price?.currency || 'eur']}${getFormattedPrice(
              checkoutPrice
            )}`}
            color="blue"
            type="h3"
          />

          <Text untranslatedText="prices include vat" type="label" ml={4} />
        </Flex>

        <Button
          untranslatedText="Continue to payment"
          // isDisabled={isCardUpdating}
          // isLoading={isPaymentPending}
          // onClick={() => console.log('i will open the payment modal')}
          onClick={() => openCheckout(cartItems)}
        />
      </Flex>
    </Box>
  );
};

export default Cart;
