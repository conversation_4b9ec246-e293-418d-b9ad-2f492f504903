import { Box, Flex } from '@mantine/core';
import { motion } from 'framer-motion';
import Image from 'next/image';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { IoCloseOutline } from 'react-icons/io5';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';
import { ProductDetailsType } from '@/types/common';

import s from './ProductPreviewCard.module.css';

type ProductPreviewCardProps = {
  productDetails: ProductDetailsType & { quantity?: number };
  onRemoveFromCart?: () => void;
};

const ProductPreviewCard = ({
  onRemoveFromCart,
  productDetails,
}: ProductPreviewCardProps) => {
  const { t } = useTranslation();

  return (
    <motion.div
      layout
      initial={{ opacity: 1, scale: 1 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9, transition: { duration: 0.3 } }}
    >
      <Card
        size="none"
        radius="sm"
        borderSize={2}
        borderColor="gray50"
        shadow="none"
      >
        <Flex justify="space-between">
          <Box className={s.wrapper}>
            <Box className={s.testContent}>
              <Image
                src={productDetails.images[0]}
                alt="product"
                width={131}
                height={131}
                placeholder="blur"
                blurDataURL={BLUR_IMAGE_SVG}
              />

              <div className={s.descriptionWrapper}>
                <Text untranslatedText={productDetails.name} type="h3" />
                <Text
                  transKey="typeCardDescription"
                  type="subTitle2"
                  transVariables={{ productType: productDetails.name }}
                />
              </div>
            </Box>

            {productDetails.kind === 'licenses' && (
              <Box
                style={{
                  minWidth: '150px',
                  height: '46px',
                  padding: '8px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  border: '1px solid var(--color-blue)',
                  borderRadius: '4px',
                  marginRight: '16px',
                  userSelect: 'none',
                  cursor: 'default',
                }}
              >
                <Text
                  untranslatedText={`${productDetails?.quantity || 0} ${t(productDetails.kind)}`}
                  type="body2"
                  color="blue"
                  fw={800}
                  align="right"
                />

                <Text
                  untranslatedText={`
                        ${CURRENCY_SYMBOLS.eur}${getFormattedPrice((productDetails.price?.totalAmount || 0) / (productDetails?.quantity || 1) || 0)} per license`}
                  type="label"
                  color="blue"
                  ml={8}
                />
              </Box>
            )}

            <Box w={200}>
              <Text
                untranslatedText={`${CURRENCY_SYMBOLS[productDetails.price.currency]}${getFormattedPrice(productDetails.price?.totalAmount || 0)}`}
                color="blue"
                align="center"
                type="h3"
                mr={8}
              />
            </Box>
          </Box>

          <Box className={s.closeSection} onClick={onRemoveFromCart}>
            <IoCloseOutline fontSize={30} color="var(--color-deletion)" />
          </Box>
        </Flex>
      </Card>
    </motion.div>
  );
};

export default ProductPreviewCard;
