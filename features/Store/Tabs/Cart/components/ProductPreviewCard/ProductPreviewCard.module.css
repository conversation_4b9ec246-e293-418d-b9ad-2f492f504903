.wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--color-white);
  cursor: pointer;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
}

.testContent {
  width: 100%;
  display: flex;
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  margin: 0 var(--spacing-lg);
  gap: var(--spacing-mdl);
}

.closeSection {
  border-left: 2px solid var(--color-gray100);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-smd);

  &:hover {
    cursor: pointer;
  }
}
