import { Flex } from '@mantine/core';
import { JSX } from 'react';

import s from './SkeletonEmptyCard.module.css';

const SkeletonEmptyCard = (): JSX.Element => {
  return (
    <div className={s.cartCard}>
      <div className={s.imageContainer}>
        <div className={s.imagePlaceholder} />
      </div>

      <Flex w="100%" justify="space-between" align="center" gap={24}>
        {/* Left side */}
        <div className={s.contentContainer}>
          <div className={s.textLine} />

          <div className={s.textLine} />

          <div className={s.textLineShort} />
        </div>

        {/* Right side */}
        <Flex w="100%" maw={200} align="center" gap={24}>
          <div className={s.textLine} />

          <div className={s.textLine} />
        </Flex>
      </Flex>
    </div>
  );
};

export default SkeletonEmptyCard;
