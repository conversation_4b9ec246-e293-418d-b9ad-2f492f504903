.cartCard {
  min-width: 400px;
  width: 100%;
  display: flex;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray100);
  border-radius: var(--radius-xs);
  padding: var(--spacing-lg);
  gap: var(--spacing-lg);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.imageContainer {
  flex-shrink: 0;
}

.imagePlaceholder {
  width: 80px;
  height: 80px;
  background-color: var(--color-gray100);
  border-radius: 6px;
}

.contentContainer {
  max-width: 400px;
  min-width: 120px;
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-xs);
}

.textLine {
  width: 100%;
  height: 12px;
  background-color: var(--color-gray100);
  border-radius: 6px;
}

.textLine:nth-child(2) {
  width: 85%;
}

.textLineShort {
  width: 60%;
  height: 12px;
  background-color: var(--color-gray100);
  border-radius: 6px;
}
