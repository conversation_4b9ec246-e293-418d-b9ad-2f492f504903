import { Box, Flex } from '@mantine/core';
import { GiShoppingCart } from 'react-icons/gi';

import Text from '@/components/Text/Text';

import SkeletonEmptyCard from '../SkeletonEmptyCard/SkeletonEmptyCard';

const EmptyCartPlaceholder = () => {
  return (
    <Box>
      <Flex align="center" gap={16} mb={24}>
        <GiShoppingCart fontSize={30} color="var(--color-turquoise)" />

        <Text
          untranslatedText="Your cart is empty. Please Navigate to Licenses, Certifications and E-books to add items"
          type="body2"
          fw={700}
        />
      </Flex>

      <Flex direction="column" gap={24}>
        <SkeletonEmptyCard />

        <SkeletonEmptyCard />
      </Flex>
    </Box>
  );
};

export default EmptyCartPlaceholder;
