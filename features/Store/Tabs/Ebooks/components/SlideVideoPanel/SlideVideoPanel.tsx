import { JSX, useEffect, useRef, useState } from 'react';
import { FaPlay } from 'react-icons/fa';

import s from './SlideVideoPanel.module.css';

type SlideVideoPanelProps = {
  active: number; // current slide index
  posters: string[]; // same order as slides (use the image URLs)
  videos: string[]; // video URLs aligned with posters
  className?: string;
};

const SlideVideoPanel = ({
  active,
  className,
  posters,
  videos,
}: SlideVideoPanelProps): JSX.Element => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [srcMap, setSrcMap] = useState<Record<number, string>>({});
  const [isPlaying, setIsPlaying] = useState(false);

  // On slide change: pause + reset time, keep source cached if it was set before.
  useEffect(() => {
    const v = videoRef.current;

    if (!v) return;
    try {
      v.pause();
      v.currentTime = 0;
      setIsPlaying(false);
    } catch {}
  }, [active]);

  const handlePlayClick = async () => {
    const v = videoRef.current;

    if (!v) return;

    // Prime source only on demand
    if (!srcMap[active]) {
      setSrcMap((m) => ({ ...m, [active]: videos[active] }));
      // ensure the element picks up the new src before play()
      requestAnimationFrame(() => {
        const vv = videoRef.current;

        if (!vv) return;
        vv.load();
        vv.play().catch(() => {
          // If browser blocks autoplay-with-sound, the user can press play again
        });
      });
    } else {
      try {
        await v.play();
      } catch {}
    }
  };

  return (
    <div className={`${s.videoWrap} ${className ?? ''}`}>
      <video
        ref={videoRef}
        className={s.video}
        poster={posters[active]}
        // don't fetch anything until user chooses to play:
        src={srcMap[active] ?? undefined}
        preload="none"
        playsInline
        controls={isPlaying} // show controls after play starts
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
      {!isPlaying && (
        <button
          className={s.playBtn}
          onClick={handlePlayClick}
          aria-label="Play video"
        >
          <FaPlay />
        </button>
      )}
    </div>
  );
};

export default SlideVideoPanel;
