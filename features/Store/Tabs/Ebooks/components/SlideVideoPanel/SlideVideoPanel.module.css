.videoWrap {
  position: relative;
  width: 100%;
  max-width: 1000px; /* align with your slider width */
  margin: 16px auto 0;
  border-radius: 12px;
  overflow: hidden;
  background: #0f172a; /* slate-900 fallback while poster loads */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
}

.video {
  width: 100%;
  height: 600px;
  display: block;
  aspect-ratio: 3 / 2; /* optional: matches 600x400; tweak if needed */
  background: #0f172a;
}

.playBtn {
  position: absolute;
  inset: 0;
  margin: auto;
  width: 72px;
  height: 72px;
  border: none;
  border-radius: 999px;
  display: grid;
  place-items: center;
  cursor: pointer;
  color: #fff;
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(2px);
  transition:
    transform 0.15s ease,
    background 0.2s ease,
    opacity 0.2s ease;
}

.playBtn:hover {
  transform: scale(1.04);
  background: rgba(15, 23, 42, 0.85);
}

.playBtn svg {
  width: 28px;
  height: 28px;
}
