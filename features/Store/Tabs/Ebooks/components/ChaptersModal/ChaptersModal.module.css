.modalContent {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  max-width: 1540px;
  margin: 0 auto;
}

.root {
  background-color: transparent;
}

.content {
  background-color: transparent;
}

.overlay {
  background-color: var(--color-gray50);
  margin: 0;
  padding: 0;
}

.body {
  max-width: 1540px;
  min-width: 1000px;
  width: 100%;
  min-height: 100%;
  height: fit-content;
  padding: var(--spacing-xl);
  background-color: var(--color-gray50);
  margin: 0 auto;
}

.wrapper {
  position: relative;
}
.viewport {
  perspective: 1000px;
}
.slide {
  transition:
    transform 420ms ease,
    opacity 420ms ease;
  will-change: transform, opacity;
}
.card {
  position: relative;
  width: 100%;
  height: 100%;
}
.img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  display: block;
}

.left {
  transform: rotateY(10deg) translateX(-3%) translateZ(-20px) scale(0.92);
  opacity: 0.85;
  z-index: 2;
}
.center {
  transform: translateZ(60px) scale(1) translateY(-6px);
  opacity: 1;
  z-index: 3;
}
.right {
  transform: rotateY(-10deg) translateX(3%) translateZ(-20px) scale(0.92);
  opacity: 0.85;
  z-index: 2;
}

.ctrl {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.leftCtrl {
  left: 8px;
}
.rightCtrl {
  right: 8px;
}

.cardWrapper {
  padding: 16px 32px;
  background-color: var(--color-turquoise);
  max-width: 1200px;
  margin: 0 auto;
  border-radius: 16px;
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 auto;
  padding-bottom: var(--spacing-sm);
  /* border-bottom: 1px solid var(--color-gray200); */
  margin-bottom: var(--spacing-sm);
}

.headerActionsWrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-mdl);
}

.controls {
  width: 46px;
  height: 140px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin: 0 16px;
}

.control {
  width: 46px;
  height: 46px;
  background-color: var(--color-turquoise);
  color: var(--color-white);
  border: none;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.3s ease;
  z-index: 5;
}

.control:hover {
  background-color: var(--color-turquoise);
  opacity: 0.65;
}
