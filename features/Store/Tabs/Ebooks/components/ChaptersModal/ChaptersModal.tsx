import {
  <PERSON>,
  Divider,
  Flex,
  LoadingOverlay,
  Modal,
  Skeleton,
} from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { JSX, useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { IoMdVideocam } from 'react-icons/io';

import { STORE_BOOK_ERRORS } from '@/common/errors';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import EBOOKS from '@/services/ebooks';

import SlideVideoPanel from '../SlideVideoPanel/SlideVideoPanel';
import ThreeDSlider from '../ThreeDSlider/ThreeDSlider';
import s from './ChaptersModal.module.css';

export type ChapterThumb = { id: string | number; src: string; title?: string };

type Item = {
  id: string | number;
  title?: string;
  src: string;
  videoUrl: string;
  initialIndex?: number;
};

const ChaptersModal = (): JSX.Element => {
  const router = useRouter();

  const [activeSlideNUmber, setActiveSlideNumber] = useState(
    router.query.chapter ? Number(router.query.chapter) - 1 : 0
  );
  const selectedBookISBN = router.query.ISBN as string;
  const {
    data,
    error,
    isError,
    isFetching: areGuidesFetching,
    isLoading: areGuidesLoading,
  } = useQuery<any | null>({
    queryFn: () => EBOOKS.GET_EBOOKS_ISBN_GUIDES(selectedBookISBN),
    queryKey: ['booksGuidesByISBN', selectedBookISBN],
    enabled: Boolean(selectedBookISBN),
  });

  const isBookGuidesNotFound =
    error?.message === STORE_BOOK_ERRORS.GUIDES_NOT_FOUND;

  const items: Item[] =
    data?.chapters.map((chapter: any, i: number) => ({
      id: i,
      title: 'static title',
      src: chapter.photo,
      videoUrl: chapter.video,
    })) || [];

  const slides = items.map((item) => item.src);
  const N = slides.length;

  const videoUrls = items.map((item) => item.videoUrl);

  const prev = () => setActiveSlideNumber((i) => (i - 1 + N) % N);
  const next = () => setActiveSlideNumber((i) => (i + 1) % N);

  return (
    <Modal
      opened={
        router.query.ISBN !== undefined && router.query.chapter !== undefined
      }
      onClose={() => {}}
      withCloseButton={false}
      autoFocus={false}
      trapFocus
      classNames={s}
      closeOnClickOutside={false}
      fullScreen
      transitionProps={{
        duration: 400,
        transition: 'pop',
      }}
    >
      <LoadingOverlay
        visible={areGuidesFetching || areGuidesLoading}
        zIndex={1000}
        overlayProps={{ radius: 'sm', blur: 2 }}
      />

      <div className={s.header}>
        <div className={s.headerActionsWrapper}>
          {areGuidesFetching ? (
            <Skeleton w={200} h={60} />
          ) : (
            <Text
              untranslatedText={
                isError
                  ? 'Unavailable chapters'
                  : `Chapters ${activeSlideNUmber + 1}/${slides.length}`
              }
              type="h2"
              fw={400}
            />
          )}

          <CloseButton
            onClick={() => router.push(router.asPath.split('&ISBN=')[0])}
          />
        </div>
      </div>

      {isBookGuidesNotFound && !areGuidesFetching && (
        <Text
          untranslatedText="No guides found for the provided ISBN"
          type="h3"
          fw={400}
          mt={60}
          align="center"
        />
      )}

      {!isBookGuidesNotFound && !areGuidesFetching && (
        <Flex w="100%" align="center" justify="space-between">
          <ThreeDSlider images={slides} active={activeSlideNUmber} />

          <div className={`${s.controls}`}>
            <Box className={`${s.control}`} onClick={prev}>
              <FaChevronUp />
            </Box>

            <Box className={`${s.control}`} onClick={next}>
              <FaChevronDown />
            </Box>
          </div>

          <Box maw={660}>
            {/* <Flex align="center" justify="flex-end" mt="-24px">
              <Text
                untranslatedText="Only Video Link : "
                type="body1"
                fw={400}
              />

              <IoMdVideocam fontSize={24} color="var(--color-blue)" />
            </Flex> */}

            {/* <Divider color="var(--color-gray200)" mt={8} /> */}

            <SlideVideoPanel
              active={activeSlideNUmber}
              posters={slides}
              videos={videoUrls}
            />
          </Box>
        </Flex>
      )}
    </Modal>
  );
};

export default ChaptersModal;
