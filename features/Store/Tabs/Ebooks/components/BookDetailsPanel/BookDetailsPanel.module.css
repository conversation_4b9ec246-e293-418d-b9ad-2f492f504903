.wrapper {
  display: flex;
  flex-direction: column;
  max-width: 1280px;
}

.testContent {
  width: 100%;
  display: flex;
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  margin: 0 var(--spacing-lg);
  gap: var(--spacing-mdl);
}

.price {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  padding-right: var(--spacing-sm);
  height: var(--spacing-mdl);
}

/* Left the styling to be able to use it in the future if its needed */

/* .markdown {
} */

.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4 {
  margin-bottom: var(--spacing-sm);
}

.markdown p {
  margin: var(--spacing-sm) var(--spacing-md);
}

/* .markdown ul,
.markdown ol {
}

.markdown li {
}

.markdown blockquote {
}

.markdown pre {
}

.markdown code {
} */

.dot {
  width: 14px;
  height: 14px;
  overflow: hidden;
  background-color: var(--color-gray400);
  border-radius: 50%;
}

.dots {
  width: 100%;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.dots :global(li) :global(button)::before {
  content: '';
}

.dots :global(li) :global(button) {
  padding: 0;
  border: 0;
  background: transparent;
  line-height: 0;
}

.dots :global(li.slick-active) :global(button) .dot,
.dots :global(li.slick-active) > .dot {
  background: var(--color-blue);
  transform: scale(1.1);
}

.dots :global(li) :global(button):hover .dot,
.dots :global(li):hover > .dot,
.dots :global(li) :global(button):focus .dot,
.dots :global(li):focus-within > .dot {
  transform: scale(1.15);
}

.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrowLeft {
  left: -60px;
}

.arrowRight {
  right: -60px;
}

.overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modalSlider {
  width: 90%;
  height: 90%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.closeBtn {
  position: absolute;
  top: 20px;
  right: 30px;
  background: none;
  border: none;
  color: var(--color-white);
  cursor: pointer;
  z-index: 10;
}

.expandIcon {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 12px;
  right: 12px;
  cursor: pointer;
  color: var(--color-white);
  background-color: rgba(1, 1, 1, 0.5);
  border-radius: 50%;
  padding: var(--spacing-xs);
}
