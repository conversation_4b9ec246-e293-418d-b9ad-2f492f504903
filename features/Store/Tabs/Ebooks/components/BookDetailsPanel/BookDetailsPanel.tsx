// @ts-ignore
import 'slick-carousel/slick/slick.css';
// @ts-ignore
import 'slick-carousel/slick/slick-theme.css';

import { Box, Flex, LoadingOverlay, ScrollArea, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import Image from 'next/image';
import React, { JSX, useEffect, useState } from 'react';
import {
  FaChevronLeft,
  FaChevronRight,
  FaExpand,
  FaTimes,
} from 'react-icons/fa';
import ReactMarkdown from 'react-markdown';
import Slider from 'react-slick';
import rehypeHighlight from 'rehype-highlight';
import remarkGfm from 'remark-gfm';

import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import EBOOKS from '@/services/ebooks';

import s from './BookDetailsPanel.module.css';

type BookDetailsPanelProps = {
  bookISBN: string;
  bookTitle: string;
  onClosePanel: () => void;
};

const ImageOpener = ({
  image,
  isOpen,
  onClick,
}: {
  image: string;
  isOpen: boolean;
  onClick: () => void;
}) => {
  return (
    <>
      {isOpen && (
        <div className={s.overlay}>
          <button className={s.closeBtn} onClick={onClick}>
            <FaTimes size={30} />
          </button>

          <div className={s.modalSlider}>
            <div className={s.imageWrapper}>
              <Image
                src={image}
                alt="fullscreen-image"
                fill
                priority
                style={{ objectFit: 'contain' }}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const MarkdownView = ({ markdown }: { markdown: string }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[[rehypeHighlight, { ignoreMissing: true }]]}
    >
      {markdown}
    </ReactMarkdown>
  );
};

const NextArrow = ({ onClick }: { onClick?: () => void }) => (
  <Box onClick={onClick} className={`${s.arrow} ${s.arrowRight}`}>
    <FaChevronRight color="white" size={20} />
  </Box>
);

const PrevArrow = ({ onClick }: { onClick?: () => void }) => (
  <Box onClick={onClick} className={`${s.arrow} ${s.arrowLeft}`}>
    <FaChevronLeft color="white" size={20} />
  </Box>
);

const CustomPaging = () => <div className={s.dot} />;

const BookDetailsPanel = ({
  bookISBN,
  bookTitle,
  onClosePanel,
}: BookDetailsPanelProps): JSX.Element => {
  const { data: book, isLoading } = useQuery<any | null>({
    queryFn: () => EBOOKS.GET_EBOOK_DETAILS_BY_ISBN(bookISBN),
    queryKey: ['bookByISBN', bookISBN],
  });

  const [isOpen, setIsOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const settings = {
    arrows: true,
    dots: true,
    customPaging: CustomPaging,
    dotsClass: `slick-dots ${s.dots}`,
    infinite: false,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipe: false,
    draggable: false,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    beforeChange: (current: number, next: number) => {
      setActiveIndex(next);
    },
  } as const;

  const [markdown, setMarkdown] = useState<string>('');
  const [isMarkdownLoading, setMarkdownLoading] = useState(false);

  const images = [book?.cover, ...(book?.thumbnails || [])];

  // Downloads the md
  useEffect(() => {
    if (!book?.description) return;
    const controller = new AbortController();

    setMarkdownLoading(true);

    setMarkdown(''); // optional reset
    axios
      .get<string>(book.description, {
        responseType: 'text',
        transformResponse: [(d) => d],
        signal: controller.signal,
        headers: { Accept: 'text/markdown, text/plain, */*' },
      })
      .then((res) => !controller.signal.aborted && setMarkdown(res.data ?? ''))
      .catch((err) => {
        if (!axios.isCancel(err))
          console.error('Error fetching markdown:', err);
      });

    setMarkdownLoading(false);
  }, [book?.description]);

  return (
    <Card
      size="xl"
      radius="sm"
      borderSize={1}
      shadow="lg"
      bg="gray50"
      className={s.wrapper}
    >
      <Flex justify="space-between" mb={32}>
        <Flex gap={16} align="center" w="100%" justify="space-between">
          <Text untranslatedText={bookTitle} type="h2" />

          <CloseButton onClick={onClosePanel} variant="outlined" />
        </Flex>
      </Flex>

      <Flex gap={32} h="500px" mb={32} justify="space-between">
        <Box w={400} h="100%" ml={52} pos="relative">
          <Slider {...settings}>
            {images.map((src, imageIndex) => (
              <Image
                key={src}
                src={src}
                alt={`slide-${imageIndex}`}
                width={400}
                height={500}
              />
            ))}
          </Slider>

          <ImageOpener
            image={images[activeIndex]}
            isOpen={isOpen}
            onClick={() => setIsOpen(!isOpen)}
          />

          <FaExpand
            size={24}
            className={s.expandIcon}
            onClick={() => {
              setIsOpen(true);
            }}
          />
        </Box>

        <LoadingOverlay
          visible={isMarkdownLoading || isLoading}
          zIndex={2}
          overlayProps={{ radius: 'sm', blur: 2 }}
        />

        <Box
          h="100%"
          bg="var(--color-white)"
          maw={600}
          w="100%"
          p={24}
          pos="relative"
          style={{
            borderRadius: 'var(--radius-sm)',
          }}
        >
          {isMarkdownLoading ? (
            <Skeleton />
          ) : (
            <ScrollArea
              h={460}
              scrollbarSize={4}
              scrollbars="y"
              offsetScrollbars
              classNames={{ thumb: 'thumb' }}
            >
              <Box className={s.markdown}>
                <MarkdownView markdown={markdown} />
              </Box>
            </ScrollArea>
          )}
        </Box>
      </Flex>
    </Card>
  );
};

export default BookDetailsPanel;
