import { Box, Flex, Tooltip } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS, PRODUCTS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import IconButton from '@/components/IconButton/IconButton';
import Text from '@/components/Text/Text';
import { EbookProductsType } from '@/types/common';

import styles from './EBookBuyCard.module.css';

type EBookBuyCardProps = {
  bookDetails: EbookProductsType[0];
  isDisabled?: boolean;
  tooltipText?: string;
  isAddedToCart: boolean;
  onAddToCart?: () => void;
  onRemoveFromCart?: () => void;
  onCardClick?: () => void;
  onDownloadEbook?: () => void;
  onGuide?: () => void;
  onBuy?: () => void;
};

const EBookBuyCard = ({
  bookDetails,
  isAddedToCart,
  isDisabled = false,
  onAddToCart,
  onBuy,
  onCardClick,
  onDownloadEbook,
  onGuide,
  onRemoveFromCart,
  tooltipText = '',
}: EBookBuyCardProps) => {
  const isPurchased = true;

  return (
    <Card
      size="sm"
      radius="sm"
      borderSize={2}
      borderColor="gray50"
      shadow="none"
    >
      <Box
        className={styles.wrapper}
        onClick={onCardClick}
        style={{
          pointerEvents: isDisabled ? 'none' : 'auto',
          opacity: isDisabled ? 0.5 : 1,
        }}
      >
        <div className={styles.testContent}>
          <Image
            src={
              bookDetails.images[0]
              // fallback Image  || image
            }
            alt="product"
            width={131}
            height={131}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <div className={styles.descriptionWrapper}>
            <Text untranslatedText={bookDetails.name} type="h3" />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: bookDetails.name,
              }}
            />
          </div>
        </div>

        <Tooltip label={tooltipText} disabled={!isDisabled}>
          <div className={styles.buttonsWrapper}>
            {isPurchased ? (
              <Flex gap={16}>
                <Button
                  transKey="download-capital"
                  isDisabled={isDisabled}
                  onClick={onDownloadEbook}
                />

                <Button
                  untranslatedText="GUIDE"
                  isDisabled={isDisabled}
                  onClick={onGuide}
                />
              </Flex>
            ) : (
              <Button
                transKey="buy-capital"
                isDisabled={isDisabled}
                leftSection={
                  <span className={styles.price}>
                    <Text
                      untranslatedText={`${CURRENCY_SYMBOLS[bookDetails.price.currency]}${getFormattedPrice(bookDetails.price.amount)}`}
                      color="white"
                      fw={300}
                      type="body1"
                    />
                  </span>
                }
                onClick={onBuy}
              />
            )}

            <IconButton
              variant={isAddedToCart ? 'dangerOutlined' : 'primary'}
              iconName={isAddedToCart ? 'CloseSvg' : 'CartAddProductSvg'}
              iconSize={isAddedToCart ? 16 : 20}
              isDisabled={isDisabled}
              onClick={isAddedToCart ? onRemoveFromCart : onAddToCart}
              color={isAddedToCart ? 'red' : 'white'}
            />
          </div>
        </Tooltip>
      </Box>
    </Card>
  );
};

export default EBookBuyCard;
