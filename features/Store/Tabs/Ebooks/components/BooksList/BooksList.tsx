import { Box, Flex, Skeleton } from '@mantine/core';
import { cleanNotifications, notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';

import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import { CartContext } from '@/context/CartProvider';
import { PurchasesContext } from '@/context/PurchasesProvider';
import EBOOKS from '@/services/ebooks';
import { EbookProductsType, ProductDetailsType } from '@/types/common';

import BookDetailsPanel from '../BookDetailsPanel/BookDetailsPanel';
import ChaptersModal from '../ChaptersModal/ChaptersModal';
import EBookBuyCard from '../EBookBuyCard/EBookBuyCard';
import s from './BooksList.module.css';

type BooksListProps = {
  books: EbookProductsType;
  areBooksFetching: boolean;
  areBooksLoading: boolean;
};
const SKELETON_ARRAY = Array(5).fill(0);

// Fall back Image
// const image = productDetails?.type
//   ? `/images/tests/${PRODUCTS[productDetails.type].certificateImage}`
//   : '';

const BooksList = ({
  areBooksFetching,
  areBooksLoading,
  books,
}: BooksListProps): JSX.Element => {
  const { t } = useTranslation();
  const router = useRouter();
  const { openCheckout } = PurchasesContext();
  const { addToCart, isItemInCart, removeFromCart } = CartContext();
  const [isPDFDownloading, setIsPDFDownloading] = useState(false);
  // This state is kinda useless since the guides are opening from the url by isbn and chapter. Leave it for future use
  const [selectedModalAction, setSelectedModalAction] = useState<
    'details' | 'guide' | null
  >(null);
  const [selectedBook, setSelectedBook] = useState<EbookProductsType[0] | null>(
    null
  );

  const downloadFileFromUrl = async (url: string) => {
    try {
      setIsPDFDownloading(true);
      const response = await fetch(url);

      if (!response.ok) throw new Error('Failed to fetch file');

      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = url.split('/').pop()?.split('?')[0] || 'file.pdf';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // cleanup
      window.URL.revokeObjectURL(blobUrl);
      setIsPDFDownloading(false);
    } catch (err) {
      setIsPDFDownloading(false);
      console.error('Download error:', err);
    }
  };

  const onAddToCart = (product: EbookProductsType[0]) => {
    cleanNotifications();
    notifications.show({
      title: `${product.name || ''} ${t(product.kind || '')}`,
      message: t('product-added-to-cart'),
      color: 'green',
    });

    // TODO FIX THE TIME OF THE BOOK TO MATCH CORRECTLY DO NOT CAST
    addToCart(product as ProductDetailsType);
  };

  const onRemoveFromCart = (stripeId: string) => {
    cleanNotifications();
    notifications.show({
      title: 'Product removed from cart',
      message: 'Product removed from cart',
      color: 'red',
    });

    removeFromCart(stripeId);
  };

  const ebookDownloadMutation = useMutation({
    mutationFn: EBOOKS.GET_EBOOK_DOWNLOAD_URL_BY_ISBN,
    onSuccess: (res) => {
      console.log('downloaded ebook', res);
      // downloadFile(res.url, `book`, 'pdf');
      downloadFileFromUrl(res.url);
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const handleEbookDownload = (ISBN: string) => {
    // console.log('download ebook', book);

    ebookDownloadMutation.mutate(ISBN);
  };

  return (
    <Box>
      {areBooksFetching && areBooksLoading && (
        <Flex direction="column" gap={32}>
          {SKELETON_ARRAY.map((_, index) => (
            <Skeleton
              key={`license-skeleton-${index}`}
              height={150}
              radius="lg"
            />
          ))}
        </Flex>
      )}

      {!areBooksFetching && !areBooksLoading && books.length === 0 && (
        <div>NO RESULTS</div>
      )}

      <Flex direction="column" gap={32}>
        {books.map((book) => (
          <EBookBuyCard
            key={book.stripeId}
            bookDetails={book}
            isDisabled={
              areBooksFetching ||
              ebookDownloadMutation.isPending ||
              isPDFDownloading
            }
            tooltipText="contact-admin-to-buy"
            onAddToCart={() => onAddToCart(book)}
            onRemoveFromCart={() => onRemoveFromCart(book.stripeId)}
            isAddedToCart={isItemInCart(book.stripeId)}
            onCardClick={() => {
              setSelectedBook(book);
              setSelectedModalAction('details');
            }}
            // TODO FIX THE TIME OF THE BOOK TO MATCH CORRECTLY DO NOT CAST
            onBuy={() => openCheckout([book as ProductDetailsType])}
            onDownloadEbook={() => handleEbookDownload(book.ISBN)}
            onGuide={() => {
              router.push(`${router.asPath}&ISBN=${book.ISBN}&chapter=1`);
            }}
          />
        ))}
      </Flex>

      <PrimaryModal
        isOpen={selectedBook !== null && selectedModalAction === 'details'}
        content={
          selectedBook?.stripeId ? (
            <BookDetailsPanel
              bookTitle={selectedBook.name}
              bookISBN={selectedBook.ISBN}
              onClosePanel={() => setSelectedBook(null)}
            />
          ) : null
        }
      />

      {router.isReady && <ChaptersModal />}
    </Box>
  );
};

export default BooksList;
