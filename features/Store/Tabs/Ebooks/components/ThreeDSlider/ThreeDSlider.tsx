import { Box } from '@mantine/core';
import Image from 'next/image';

import { BLUR_IMAGE_SVG } from '@/common/consts';

import styles from './ThreeDSlider.module.css';

interface ThreeDSliderProps {
  images: string[];
  active: number;
}

const ThreeDSlider = ({ active, images }: ThreeDSliderProps) => {
  const totalNumberOfSlides = images.length;

  return (
    <Box className={styles.cardWrapper}>
      <div className={styles.wrapper}>
        <div className={styles.slider}>
          {images.map((src, i) => {
            const offset =
              (i - active + totalNumberOfSlides) % totalNumberOfSlides;
            let className = '';

            if (offset === 0) className = styles.center;
            else if (offset === 1) className = styles.right;
            else if (offset === totalNumberOfSlides - 1)
              className = styles.left;
            else className = styles.hidden;

            return (
              <div key={src} className={`${styles.slide} ${className}`}>
                <Image
                  src={src}
                  alt={`slide-${i}`}
                  fill
                  sizes="(max-width: 1000px) 100vw, 600px"
                  priority={offset === 0} // preload the center image
                  placeholder="blur"
                  blurDataURL={BLUR_IMAGE_SVG}
                />
              </div>
            );
          })}
        </div>
      </div>
    </Box>
  );
};

export default ThreeDSlider;
