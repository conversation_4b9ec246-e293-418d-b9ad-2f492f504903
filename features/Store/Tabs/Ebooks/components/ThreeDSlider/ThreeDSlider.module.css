.cardWrapper {
  max-width: 560px;
  min-width: 560px;
  width: 100%;
  padding-top: 180px;
  padding-bottom: 180px;
  padding-left: 30px;
  background-color: var(--color-turquoise);
  border-radius: 10px;
}

.wrapper {
  position: relative;
  max-width: 504px;
  width: 100%;
  perspective: 1200px;
}

.slider {
  position: relative;
  height: 300px;
}

.slide {
  position: absolute;
  width: 500px;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition:
    transform 0.7s ease,
    opacity 0.7s ease;
  opacity: 0;
  pointer-events: none;
}

.center {
  transform: translateX(0) translateZ(300px) scale(1);
  opacity: 1;
  z-index: 3;
  pointer-events: auto;
  object-fit: fill;
}

.left {
  transform: translateY(-150px) translateZ(150px) scale(0.8);
  opacity: 0.7;
  z-index: 2;
  object-fit: cover;
}

.right {
  transform: translateY(150px) translateZ(150px) scale(0.8);
  opacity: 0.7;
  z-index: 2;
  object-fit: cover;
}

.hidden {
  opacity: 0;
  transform: scale(0.8) translateZ(-300px);
  z-index: 1;
}
