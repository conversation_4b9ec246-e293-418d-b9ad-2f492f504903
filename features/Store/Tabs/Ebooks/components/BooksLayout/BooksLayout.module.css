.wrapper {
  display: flex;
  width: 100%;
  height: 100%;
}
.filters {
  min-width: fit-content;
  max-width: fit-content;
  z-index: 1;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.06);
  padding-right: var(--spacing-2xl);
  transition: all 0.4s ease-in-out;
}

.noResultsWrapper {
  width: 100%;
  height: fit-content;
  display: flex;
  justify-content: center;
  padding: var(--spacing-3xl);
}

.scrollAreaWrapper {
  width: 100%;
  /*
  204px reflects to the occupied spacings from the page elements aka page name segmented controls etc.
  And their spacing will the main content starts
  */
  max-height: calc(100vh - 204px);
}

.scrollFiltersWrapper {
  min-width: fit-content;
  margin-right: var(--spacing-xl);
  /*
  204px reflects to the occupied spacings from the page elements aka page name segmented controls etc.
  And their spacing will the main content starts
  */
  max-height: calc(100vh - 204px);
}

.paginationWrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-2xl);
}
