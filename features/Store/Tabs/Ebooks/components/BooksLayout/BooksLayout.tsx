import { ScrollArea } from '@mantine/core';

import Text from '@/components/Text/Text';

import styles from './BooksLayout.module.css';

type BooksLayoutProps = {
  filters?: React.ReactNode;
  listContent?: React.ReactNode;
  paginationComponent?: React.ReactNode;
  numberOfSearchResults: number | null;
  isInformationLoading: boolean;
  isInformationFetching: boolean;
  totalNumberOfResults: number | null;
};

const BooksLayout = ({
  filters,
  isInformationFetching,
  isInformationLoading,
  listContent,
  numberOfSearchResults,
  paginationComponent,
  totalNumberOfResults,
}: BooksLayoutProps) => {
  return (
    <div className={styles.wrapper}>
      <ScrollArea.Autosize
        type="always"
        scrollbarSize={4}
        className={styles.scrollFiltersWrapper}
      >
        <div className={`${styles.filters}`}>{filters}</div>
      </ScrollArea.Autosize>

      <ScrollArea.Autosize
        type="never"
        scrollbarSize={4}
        className={styles.scrollAreaWrapper}
      >
        {isInformationFetching && isInformationLoading && (
          <div>Books skeleton component</div>
        )}

        {!isInformationFetching && totalNumberOfResults === 0 && (
          <div className={styles.noResultsWrapper}>
            <Text
              untranslatedText="no-book-were-ever-provided-to-sell"
              type="h4"
              color="gray"
              fw={400}
            />
          </div>
        )}

        {!isInformationFetching &&
          totalNumberOfResults !== 0 &&
          numberOfSearchResults === 0 && (
            <div className={styles.noResultsWrapper}>
              <Text
                untranslatedText="no-books-found-with-your-selected-filters"
                type="h4"
                color="gray"
                fw={400}
              />
            </div>
          )}

        {totalNumberOfResults !== 0 &&
          numberOfSearchResults !== 0 &&
          listContent}

        <div className={styles.paginationWrapper}>{paginationComponent}</div>
      </ScrollArea.Autosize>
    </div>
  );
};

export default BooksLayout;
