import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { JSX, useState } from 'react';

import Checkbox from '@/components/Checkbox/Checkbox';
import Text from '@/components/Text/Text';
import PRODUCTS from '@/services/products';

import BooksLayout from './components/BooksLayout/BooksLayout';
import BooksList from './components/BooksList/BooksList';

type FiltersType = {
  grades: string[];
  language: string[];
};

type FilterCheckboxType = {
  label: string;
  value: string;
};

const FILTERS_MAPPING = {
  '1': ['1'],
  '2': ['2'],
  '3': ['3'],
  '4': ['4'],
  '5': ['5'],
  '6': ['6'],
  'middle-school': ['7', '8', '9'],
  'high-school': ['10', '11', '12'],
} as const;

const FILTERS_CHECKBOXES: Record<keyof FiltersType, FilterCheckboxType[]> = {
  grades: [
    { label: '1st Grade', value: '1' },
    { label: '2nd Grade', value: '2' },
    { label: '3rd Grade', value: '3' },
    { label: '4th Grade', value: '4' },
    { label: '5th Grade', value: '5' },
    { label: '6th Grade', value: '6' },
    { label: 'Middle School', value: 'middle-school' },
    { label: 'High School', value: 'high-school' },
  ],
  language: [
    { label: 'English', value: 'en' },
    { label: 'Greek', value: 'el' },
  ],
};

const Ebooks = (): JSX.Element => {
  const [selectedFilters, setSelectedFilters] = useState<FiltersType>({
    grades: [],
    language: [],
  });

  const { data, isFetching, isLoading } = useQuery<any | null>({
    queryFn: () =>
      PRODUCTS.GET_ALL_EBOOKS({
        grades: selectedFilters.grades
          .map(
            (grade) => FILTERS_MAPPING[grade as keyof typeof FILTERS_MAPPING]
          )
          .flat(),
        language: selectedFilters.language.join(','),
      }),
    queryKey: ['books', selectedFilters],
    placeholderData: keepPreviousData,
  });

  const books = data || [];

  const GENERATED_FILTERS = Object.keys(FILTERS_CHECKBOXES).map((filterKey) => {
    const filterKeyCasted = filterKey as keyof FiltersType;

    return (
      <div
        key={filterKey}
        style={{
          marginBottom: 32,
          display: 'flex',
          flexDirection: 'column',
          gap: 8,
        }}
      >
        <Text untranslatedText={filterKey.toUpperCase()} type="body1" mb={8} />

        {FILTERS_CHECKBOXES[filterKeyCasted].map((checkbox) => (
          <div
            key={checkbox.value}
            style={{
              display: 'flex',
              gap: 8,
              alignItems: 'center',
            }}
          >
            <Checkbox
              variant="primary"
              value={checkbox.value}
              onChange={() => {
                setSelectedFilters((prev) => ({
                  ...prev,
                  [filterKeyCasted]: prev[filterKeyCasted].includes(
                    checkbox.value
                  )
                    ? prev[filterKeyCasted].filter(
                        (value) => value !== checkbox.value
                      )
                    : [...prev[filterKeyCasted], checkbox.value],
                }));
              }}
              isChecked={selectedFilters[filterKeyCasted].includes(
                checkbox.value
              )}
            />

            <Text untranslatedText={checkbox.label} type="body1" />
          </div>
        ))}
      </div>
    );
  });

  return (
    <BooksLayout
      numberOfSearchResults={null}
      isInformationLoading={false}
      isInformationFetching={false}
      totalNumberOfResults={null}
      listContent={
        <BooksList
          books={books}
          areBooksFetching={isFetching}
          areBooksLoading={isLoading}
        />
      }
      filters={GENERATED_FILTERS}
    />
  );
};

export default Ebooks;
