import { <PERSON>lex, ScrollArea, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { JSX } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import Text from '@/components/Text/Text';
import PRODUCTS from '@/services/products';
import { LicensesProductsType, ProductsType } from '@/types/common';

import LicensesList from './components/LicensesList/LicensesList';

export type GroupedLicensesByCertificationTypeType = {
  [key in ProductsType]: LicensesProductsType;
};

const Licenses = (): JSX.Element => {
  const { data, isFetching, isLoading } = useQuery<LicensesProductsType | null>(
    {
      queryKey: [QUERY_KEYS.LICENSES_LIST],
      queryFn: () => PRODUCTS.GET_ALL_LICENSE_PRODUCTS(),
      staleTime: 1000 * 60 * 10, // 10 minutes
    }
  );

  const licenses = data || [];

  const sortedLicensesGroupedByType = Object.groupBy(
    licenses.toSorted(
      (a, b) => parseInt(a.quantity, 10) - parseInt(b.quantity, 10)
    ),
    (license) => license.type
  ) as GroupedLicensesByCertificationTypeType;

  if (isLoading) {
    return (
      <Flex direction="column" gap={32}>
        {Array(10)
          .fill(0)
          .map((_, index) => (
            <Skeleton
              key={`license-skeleton-${index}`}
              height={150}
              radius="lg"
            />
          ))}
      </Flex>
    );
  }

  if (!licenses.length && !isFetching) {
    return (
      <Text
        transKey="no-results-found"
        type="h4"
        align="center"
        mt={64}
        color="gray"
      />
    );
  }

  return (
    <ScrollArea.Autosize
      type="never"
      scrollbarSize={4}
      mah="calc(100vh - 270px)"
    >
      <LicensesList
        groupedLicensesByCertificationType={sortedLicensesGroupedByType}
      />
    </ScrollArea.Autosize>
  );
};

export default Licenses;
