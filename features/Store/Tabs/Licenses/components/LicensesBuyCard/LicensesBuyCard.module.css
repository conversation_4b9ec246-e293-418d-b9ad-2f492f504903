.wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 125px;
  background-color: var(--color-white);
  cursor: pointer;
}

.dropdownDisplayWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  padding: var(--spacing-sm) var(--spacing-md);
}

.testContent {
  width: 100%;
  display: flex;
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  margin: 0 var(--spacing-lg);
  gap: var(--spacing-mdl);
}

.buttonsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin: 0 var(--spacing-mdl);
}

.price {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  padding-right: var(--spacing-sm);
  height: var(--spacing-mdl);
}
