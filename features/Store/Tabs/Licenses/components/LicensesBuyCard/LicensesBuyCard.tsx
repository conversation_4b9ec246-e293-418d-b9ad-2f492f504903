import { Box, Flex, FocusTrap, Select, Tooltip } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import Image from 'next/image';
import { useTranslation } from 'react-i18next';
import { FaLock } from 'react-icons/fa6';
import { IoMdArrowDropdown, IoMdArrowDropup } from 'react-icons/io';
import { PiMoney } from 'react-icons/pi';

import { BLUR_IMAGE_SVG, CURRENCY_SYMBOLS, PRODUCTS } from '@/common/consts';
import { getFormattedPrice } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import IconButton from '@/components/IconButton/IconButton';
import Text from '@/components/Text/Text';
import { CurrencySymbolType, ProductsType } from '@/types/common';

import styles from './LicensesBuyCard.module.css';

type LicensePackagesDetailsType = {
  stripeId: string;
  quantity: number;
  totalPrice: number;
};

type LicensesBuyCardProps = {
  selectedLicenseStripeId: string;
  licensePackagesDetails: LicensePackagesDetailsType[];
  isLocked: boolean;
  isAddedToCart: boolean;
  remainingLicenses: number;
  licenseCertificationType: ProductsType;
  licenseCertificationImage: string;
  licenseCertificationCurrency: CurrencySymbolType;
  onAddToCart: (
    selectedLicenseStripeId: LicensePackagesDetailsType['stripeId']
  ) => void;
  onRemoveFromCart: (
    selectedLicenseStripeId: LicensePackagesDetailsType['stripeId']
  ) => void;
  onBuy: (
    selectedLicenseStripeId: LicensePackagesDetailsType['stripeId']
  ) => void;
  onCardClick: (
    selectedLicenseStripeId: LicensePackagesDetailsType['stripeId'],
    testType: ProductsType
  ) => void;
  onLicenseSelect: (
    selectedLicenseStripeId: LicensePackagesDetailsType['stripeId'] | 'none'
  ) => void;
};

const NONE_DROPDOWN_OPTION = 'none';

const INITIAL_LICENSE_PACKAGE = {
  quantity: 0,
  totalPrice: 0,
  stripeId: '',
};

const LicensesBuyCard = ({
  isAddedToCart,
  isLocked = false,
  licenseCertificationCurrency,
  licenseCertificationImage,
  licenseCertificationType,
  licensePackagesDetails,
  onAddToCart,
  onBuy,
  onCardClick,
  onLicenseSelect,
  onRemoveFromCart,
  remainingLicenses,
  selectedLicenseStripeId,
}: LicensesBuyCardProps) => {
  const { t } = useTranslation();

  const selectedLicensePackage =
    licensePackagesDetails.find(
      (item) => item.stripeId === selectedLicenseStripeId
    ) || INITIAL_LICENSE_PACKAGE;

  const [active, { toggle }] = useDisclosure(false);

  const LICENSES_DROPDOWN_VALUES = licensePackagesDetails.map((item) => ({
    label: `${item.quantity}`,
    value: item.stripeId,
  }));

  LICENSES_DROPDOWN_VALUES.unshift({
    label: t('none'),
    value: NONE_DROPDOWN_OPTION,
  });

  // Its responsible only for the displayed info for the select dropdown.
  const renderSelectOption = ({
    option,
  }: {
    option: {
      label: string;
      value: string;
    };
  }) => {
    const previewLicensePackage = licensePackagesDetails.find(
      (item) => item.stripeId === option.value
    );

    const pricePerLicense =
      (previewLicensePackage?.totalPrice || 0) /
      (previewLicensePackage?.quantity || 0);

    return option.value === NONE_DROPDOWN_OPTION ? (
      t('none')
    ) : (
      <Flex direction="column" w="100%">
        <Flex
          style={{
            width: 'fit-content',
          }}
          direction="column"
          w="100%"
          p="8px 0px"
        >
          <Text
            untranslatedText={`${option.label} LICENSES`}
            type="body2"
            color="blue"
            fw={800}
          />

          <Text
            untranslatedText={`${CURRENCY_SYMBOLS[licenseCertificationCurrency]}${getFormattedPrice(pricePerLicense)} per license`}
            type="label"
            color="blue"
          />
        </Flex>
      </Flex>
    );
  };

  return (
    <Card
      size="sm"
      radius="sm"
      borderSize={2}
      borderColor="gray50"
      shadow="none"
    >
      <Box
        className={styles.wrapper}
        onClick={() =>
          onCardClick(selectedLicenseStripeId, licenseCertificationType)
        }
      >
        <div className={styles.testContent}>
          <Image
            src={
              isLocked
                ? `/images/store/disabledLicense.png`
                : licenseCertificationImage
            }
            alt="an image displaying a letter representing the certification type the license belongs"
            width={131}
            height={131}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <div className={styles.descriptionWrapper}>
            {!isLocked && (
              <Text
                untranslatedText={
                  remainingLicenses > 0
                    ? `${remainingLicenses} licenses remain`
                    : 'No licenses remain'
                }
                type="label"
                color="red"
              />
            )}

            <Text
              untranslatedText={PRODUCTS[licenseCertificationType]?.typeName}
              type="h3"
            />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: PRODUCTS[licenseCertificationType]?.typeName,
              }}
            />
          </div>
        </div>

        {!isLocked && (
          <div className={styles.buttonsWrapper}>
            <FocusTrap active={active}>
              <Tooltip
                label="Remove item from cart to change license package"
                withArrow
                disabled={selectedLicensePackage.quantity === 0}
              >
                <Box
                  w={146}
                  h={46}
                  style={{
                    position: 'relative',
                    border: '1px solid var(--color-blue)',
                    borderRadius: 4,
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    toggle();
                  }}
                >
                  <Box className={styles.dropdownDisplayWrapper}>
                    {selectedLicensePackage.quantity === 0 ? (
                      <Text
                        untranslatedText="SELECT"
                        type="body2"
                        color="blue"
                        align="center"
                        fw={800}
                      />
                    ) : (
                      <Box
                        style={{
                          width: 'fit-content',
                        }}
                      >
                        <Text
                          untranslatedText={`${selectedLicensePackage.quantity} LICENSES`}
                          type="body2"
                          color="blue"
                          fw={800}
                        />

                        <Text
                          untranslatedText={`
                        ${CURRENCY_SYMBOLS[licenseCertificationCurrency]}${getFormattedPrice(selectedLicensePackage.totalPrice / selectedLicensePackage.quantity)} per license`}
                          type="label"
                          color="blue"
                          align="right"
                        />
                      </Box>
                    )}
                  </Box>

                  <Select
                    data={LICENSES_DROPDOWN_VALUES}
                    value={selectedLicenseStripeId}
                    disabled={isAddedToCart || isLocked}
                    onChange={(value) => {
                      onLicenseSelect(value || '');
                    }}
                    styles={{
                      root: {
                        width: '100%',
                        borderRadius: 4,
                        ...(isAddedToCart
                          ? { backgroundColor: 'var(--color-gray50)' }
                          : {}),
                        ...(isAddedToCart ? { opacity: 0.4 } : {}),
                      },
                      input: {
                        height: 44,
                        opacity: 0,
                      },
                    }}
                    rightSection={
                      active ? (
                        <IoMdArrowDropup
                          color="var(--color-blue)"
                          fontSize={24}
                        />
                      ) : (
                        <IoMdArrowDropdown
                          color="var(--color-blue)"
                          fontSize={24}
                        />
                      )
                    }
                    comboboxProps={{
                      transitionProps: {
                        transition: 'fade-down',
                        duration: 200,
                      },
                    }}
                    renderOption={renderSelectOption}
                  />
                </Box>
              </Tooltip>
            </FocusTrap>

            <Tooltip
              label={
                isAddedToCart
                  ? 'Remove item from cart to proceed with single purchase'
                  : 'Please select a license package first'
              }
              withArrow
              disabled={selectedLicensePackage.quantity > 0 && !isAddedToCart}
            >
              <Box miw={186}>
                <Button
                  transKey="buy-capital"
                  hasFullWidth
                  isDisabled={
                    selectedLicensePackage.quantity === 0 || isAddedToCart
                  }
                  leftSection={
                    <span className={styles.price}>
                      {selectedLicensePackage.quantity === 0 ? (
                        <PiMoney fontSize={22} />
                      ) : (
                        <Text
                          untranslatedText={`${CURRENCY_SYMBOLS[licenseCertificationCurrency]}${getFormattedPrice(
                            selectedLicensePackage.totalPrice
                          )}`}
                          color="white"
                          fw={300}
                          type="body1"
                        />
                      )}
                    </span>
                  }
                  onClick={() => onBuy(selectedLicenseStripeId)}
                />
              </Box>
            </Tooltip>

            <Tooltip
              label="Please select a license package first"
              withArrow
              disabled={selectedLicensePackage.quantity > 0}
            >
              <Box>
                <IconButton
                  variant={isAddedToCart ? 'dangerOutlined' : 'primary'}
                  iconName={isAddedToCart ? 'CloseSvg' : 'CartAddProductSvg'}
                  iconSize={isAddedToCart ? 16 : 20}
                  isDisabled={selectedLicensePackage.quantity === 0}
                  onClick={() => {
                    if (isAddedToCart) {
                      onRemoveFromCart(selectedLicenseStripeId);
                    } else {
                      onAddToCart(selectedLicenseStripeId);
                    }
                  }}
                  color={isAddedToCart ? 'red' : 'white'}
                />
              </Box>
            </Tooltip>
          </div>
        )}

        {isLocked && (
          <Flex
            align="center"
            justify="center"
            direction="column"
            style={{
              maxWidth: 346,
              width: '100%',
            }}
          >
            <FaLock fontSize={20} color="var(--color-turquoise)" />

            <Flex direction="column" mt={24}>
              <Text
                untranslatedText="CERTIFICATION"
                type="body1"
                color="turquoise"
                align="center"
                fw={700}
              />

              <Text
                untranslatedText="REQUIRED"
                type="body1"
                color="turquoise"
                align="center"
                fw={700}
              />
            </Flex>
          </Flex>
        )}
      </Box>
    </Card>
  );
};

export default LicensesBuyCard;
