.wrapper {
  display: flex;
  flex-direction: column;
  max-width: 1000px;
}

.testContent {
  width: 100%;
  display: flex;
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  margin: 0 var(--spacing-lg);
  gap: var(--spacing-mdl);
}

.price {
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  padding-right: var(--spacing-sm);
  height: var(--spacing-mdl);
}
