import { Flex } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { JSX, lazy, Suspense, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

// import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import { CartContext } from '@/context/CartProvider';
import { PurchasesContext } from '@/context/PurchasesProvider';
import { UserContext } from '@/context/UserProvider';
import { ProductDetailsType, ProductsType } from '@/types/common';

import { GroupedLicensesByCertificationTypeType } from '../../Licenses';
import LicensesBuyCard from '../LicensesBuyCard/LicensesBuyCard';

// const LicenseDetailsPanel = lazy(
//   () => import('../LicenseDetailsPanel/LicenseDetailsPanel')
// );

const LicensesList = ({
  groupedLicensesByCertificationType,
}: {
  groupedLicensesByCertificationType: GroupedLicensesByCertificationTypeType;
}): JSX.Element => {
  const { t } = useTranslation();
  const { openCheckout } = PurchasesContext();
  const { addToCart, cartItems, isItemInCart, removeFromCart } = CartContext();
  const { user, userCertificates } = UserContext();

  // const [selectedLicense, setSelectedLicense] =
  //   useState<ProductDetailsType | null>(null);

  const selectedLicensesFromCart = useMemo(() => {
    return cartItems
      .filter((item) => item.kind === 'licenses')
      .map((item) => ({
        stripeId: item.stripeId,
        type: item.type,
      }));
  }, [cartItems]);

  const [selectedLicensesOptions, setSelectedLicenseOptions] = useState<
    { stripeId: string; type: ProductsType }[]
  >(selectedLicensesFromCart);

  const onAddToCart = useCallback(
    (product: ProductDetailsType) => {
      notifications.show({
        title: `${product.name || ''}`,
        // ${t(product.kind || '')}
        message: t('product-added-to-cart'),
        color: 'green',
      });

      addToCart(product);
    },
    [addToCart, t]
  );

  const onRemoveFromCart = useCallback(
    (stripeId: string) => {
      notifications.show({
        title: 'Product removed from cart',
        message: '',
        color: 'green',
      });
      removeFromCart(stripeId);
    },
    [removeFromCart]
  );

  const handleAddToCart = useCallback(
    (testType: string, licenses: ProductDetailsType[], stripeId: string) => {
      const foundLicense = licenses.find((l) => l.stripeId === stripeId);

      if (foundLicense) onAddToCart(foundLicense);
    },
    [onAddToCart]
  );

  const handleBuy = useCallback(
    (licenses: ProductDetailsType[], stripeId: string) => {
      const foundLicense = licenses.find((l) => l.stripeId === stripeId);

      if (foundLicense) openCheckout([foundLicense]);
    },
    [openCheckout]
  );

  const handleLicenseSelect = useCallback(
    (testType: string, stripeId: string) => {
      if (!stripeId) return;

      setSelectedLicenseOptions((prev) => {
        // Find the current selected item for this testType
        const existing = prev.find((item) => item.type === testType);

        // If we're trying to deselect, and nothing was selected, do nothing
        if (stripeId === 'none') {
          return existing
            ? prev.filter((item) => item.type !== testType)
            : prev;
        }

        // If the same stripeId is already selected, return prev to avoid update
        if (existing?.stripeId === stripeId) {
          return prev;
        }

        // Replace or add the selection
        const updated = prev.filter((item) => item.type !== testType);
        return [...updated, { stripeId, type: testType as ProductsType }];
      });
    },
    []
  );

  return (
    <>
      <Flex direction="column" gap={32}>
        {Object.entries(groupedLicensesByCertificationType).map(
          ([testType, licenses]) => {
            const isLicenseInCart = licenses.some((license) =>
              isItemInCart(license.stripeId)
            );

            const selectedLicenseOption = selectedLicensesOptions.find(
              (item) => item.type === testType
            );

            const remainingLicenses =
              user.profile.licenses?.find(
                (license) => license.type === testType
              )?.remaining || 0;

            return (
              <LicensesBuyCard
                key={testType}
                selectedLicenseStripeId={selectedLicenseOption?.stripeId || ''}
                licensePackagesDetails={licenses.map((license) => ({
                  stripeId: license.stripeId,
                  quantity: Number(license.quantity || 0),
                  totalPrice: license.price.totalAmount || 0,
                }))}
                isLocked={
                  !userCertificates[testType as ProductsType]?.isCompleted
                }
                isAddedToCart={isLicenseInCart}
                remainingLicenses={remainingLicenses}
                licenseCertificationType={testType as ProductsType}
                licenseCertificationImage={licenses[0].images[0] || ''}
                licenseCertificationCurrency={licenses[0].price.currency}
                onAddToCart={(stripeId) =>
                  handleAddToCart(testType, licenses, stripeId)
                }
                onRemoveFromCart={onRemoveFromCart}
                onCardClick={() => {
                  // I will select the license to open its corresponding modal
                  // setSelectedLicense(licenses[0]);
                }}
                onBuy={(stripeId) => handleBuy(licenses, stripeId)}
                onLicenseSelect={(stripeId) =>
                  handleLicenseSelect(testType, stripeId)
                }
              />
            );
          }
        )}
      </Flex>

      {/* <PrimaryModal
        isOpen={selectedLicense !== null}
        content={
          selectedLicense?.stripeId ? (
            <Suspense fallback={null}>
              <LicenseDetailsPanel
                book={selectedLicense}
                isAddedToCart={isItemInCart(selectedLicense.stripeId)}
                onAddToCart={() => onAddToCart(selectedLicense)}
                onRemoveFromCart={() =>
                  onRemoveFromCart(selectedLicense.stripeId)
                }
                onClosePanel={() => setSelectedLicense(null)}
              />
            </Suspense>
          ) : null
        }
      /> */}
    </>
  );
};

export default LicensesList;
