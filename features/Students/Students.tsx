import { SegmentedControl } from '@mantine/core';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import { TranslationKeysType } from '@/types/common';

import AddClassForm from './components/Classes/AddClassForm';
import Classes from './components/Classes/Classes';
import AddGroupForm from './components/Groups/AddGroupForm';
import Groups from './components/Groups/Groups';
import AddStudentPanel from './components/StudentsList/AddStudentPanel/AddStudentPanel';
import ClaimYours from './components/StudentsList/ClaimYours/ClaimYours';
import StudentsList from './components/StudentsList/StudentsList';
import InviteTeacherForm from './components/Teachers/InviteTeacherForm';
import Teachers from './components/Teachers/Teachers';
import { BUTTON_ACTIONS_TRANSLATION_KEYS, SCHOOL_TAB_OPTIONS } from './consts';
import s from './Students.module.css';
import { ShoolTabsType } from './types';

type TabsType = {
  label: TranslationKeysType;
  value: ShoolTabsType;
  isDisplayed: boolean;
};

const Students = (): JSX.Element | null => {
  const router = useRouter();
  const { isSchoolRole, userRoles } = UserContext();
  const { t } = useTranslation();

  const tabQueryParam =
    (router.query.tab as ShoolTabsType) || SCHOOL_TAB_OPTIONS.STUDENTS;
  // const [tabQueryParam, setTabQueryParam] = useState(
  //   (tabQuery as ShoolTabsType) || SCHOOL_TAB_OPTIONS.STUDENTS
  // );

  const [isModalOpen, setIsModalOpen] = useState(false);

  // All this can be generated from an array. For now its much easier to read this way since we do not have such a big list

  const TABS: TabsType[] = useMemo(
    () => [
      {
        label: 'students-capital',
        value: SCHOOL_TAB_OPTIONS.STUDENTS,
        isDisplayed: true,
      },
      {
        label: 'groups-capital',
        value: SCHOOL_TAB_OPTIONS.GROUPS,
        isDisplayed: !isSchoolRole,
      },
      {
        label: 'classes-capital',
        value: SCHOOL_TAB_OPTIONS.CLASSES,
        isDisplayed: isSchoolRole,
      },
      {
        label: 'teachers-capital',
        value: SCHOOL_TAB_OPTIONS.TEACHERS,
        isDisplayed: userRoles.isSchoolAdmin,
      },
    ],
    [userRoles, isSchoolRole]
  );

  const TABS_VALUES = TABS.map((tab) => tab.value);

  const isValidQueryParam = TABS_VALUES.includes(tabQueryParam);

  const activeTab = TABS.find((item) => item.value === tabQueryParam);

  const onCloseModal = () => {
    setIsModalOpen(false);
  };

  const displayedModalContentSelector = () => {
    switch (tabQueryParam) {
      case SCHOOL_TAB_OPTIONS.STUDENTS:
        return userRoles.isSchoolTeacher ? (
          <ClaimYours closeModal={onCloseModal} />
        ) : (
          <AddStudentPanel onClosePanel={onCloseModal} />
        );
      case SCHOOL_TAB_OPTIONS.TEACHERS:
        return <InviteTeacherForm onClose={onCloseModal} />;
      case SCHOOL_TAB_OPTIONS.CLASSES:
        return <AddClassForm onClose={onCloseModal} />;
      case SCHOOL_TAB_OPTIONS.GROUPS:
        return <AddGroupForm onClose={onCloseModal} />;
      default:
        return null;
    }
  };

  if (!activeTab) return null;

  const getButtonDescription = () => {
    if (userRoles.isSchoolTeacher) {
      switch (activeTab?.value) {
        case SCHOOL_TAB_OPTIONS.STUDENTS:
          return BUTTON_ACTIONS_TRANSLATION_KEYS['claim-students'];
        default:
          return BUTTON_ACTIONS_TRANSLATION_KEYS[activeTab?.value];
      }
    }

    return BUTTON_ACTIONS_TRANSLATION_KEYS[activeTab?.value];
  };

  return (
    <div className="routeWrapper">
      <div className={s.header}>
        <Text transKey="Students" type="h3" className="textPageIndicator" />

        <SegmentedControl
          value={tabQueryParam}
          onChange={(v) => {
            // setTabQueryParam(v as ShoolTabsType);
            router.replace(`${FULL_DASHBOARD_ROUTES.STUDENTS}?tab=${v}`);
          }}
          withItemsBorders={false}
          data={TABS.filter((tab) => tab.isDisplayed).map((item) => ({
            value: item.value,
            label: t(item.label),
          }))}
          mr={60}
        />

        {userRoles.isSchoolTeacher && activeTab?.value === 'classes' ? null : (
          <div className="actionButtonIndicator">
            <Button
              transKey={getButtonDescription()}
              isLocked={false}
              onClick={() => isValidQueryParam && setIsModalOpen(true)}
            />
          </div>
        )}
      </div>

      {activeTab?.value === SCHOOL_TAB_OPTIONS.STUDENTS && <StudentsList />}

      {activeTab?.value === SCHOOL_TAB_OPTIONS.CLASSES && isSchoolRole && (
        <Classes />
      )}

      {activeTab?.value === SCHOOL_TAB_OPTIONS.TEACHERS &&
        userRoles.isSchoolAdmin && <Teachers />}

      {activeTab?.value === SCHOOL_TAB_OPTIONS.GROUPS && !isSchoolRole && (
        <Groups />
      )}

      <PrimaryModal
        isOpen={isModalOpen}
        content={displayedModalContentSelector()}
      />
    </div>
  );
};

export default Students;
