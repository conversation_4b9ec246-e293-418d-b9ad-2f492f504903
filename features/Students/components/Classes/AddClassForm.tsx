import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { SUPPORTED_SCHOOL_GRADES } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_CLASSES from '@/services/schools/classes';
import { SingleClassDetailsType } from '@/types/common';

import styles from './AddClassForm.module.css';

type AddClassFormProps = {
  onClose: () => void;
  selectedClass?: SingleClassDetailsType | null;
};

const AddClassForm = ({ onClose, selectedClass }: AddClassFormProps) => {
  const { t } = useTranslation();
  const { userRoles } = UserContext();

  const [className, setClassName] = useState<string>(selectedClass?.name || '');
  // The reason we do this its because we accept 'K' from BE as a valid value under zod but its not a valid type for the FE since "K" wont be displayed
  const [grade, setGrade] = useState<(typeof SUPPORTED_SCHOOL_GRADES)[number]>(
    selectedClass?.grade === undefined ||
      selectedClass.grade === 'K' ||
      selectedClass.grade === 'Alumni'
      ? SUPPORTED_SCHOOL_GRADES[0]
      : selectedClass.grade
  );
  const [errorMessage, setErrorMessage] = useState<'required' | null>(null);
  const queryClient = useQueryClient();

  const addClassMutation = useMutation({
    mutationFn: SCHOOL_CLASSES.ADD_CLASS,
    onSuccess: () => {
      notifications.show({
        message: t('class-added-successfully'),
        color: 'green',
      });

      // TODO Why invalidate on every single add when we can simply add into memory
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.CLASSES_LIST] });

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const editClassMutation = useMutation({
    mutationFn: SCHOOL_CLASSES.UPDATE_CLASS,
    onSuccess: () => {
      notifications.show({
        message: t('class-changes-saved-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.CLASSES_LIST] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_STUDENT_LIST],
      });

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const handleSubmitData = () => {
    if (!userRoles.isSchoolAdmin) return;

    if (!className) {
      setErrorMessage('required');
    } else if (selectedClass) {
      editClassMutation.mutate({
        classId: selectedClass.id,
        name: className,
        grade,
      });
    } else
      addClassMutation.mutate({
        name: className,
        grade,
      });
  };

  return (
    <Card size="2xl" bg="gray50" hasDynamicHeight className={styles.wrapper}>
      <div className={styles.header}>
        <Text
          transKey={selectedClass ? 'edit-class' : 'add-new-class'}
          type="h3"
        />

        <div className={styles.buttonsWrapper}>
          <Button
            transKey={selectedClass ? 'save-capital' : 'add-class-capital'}
            type="button"
            onClick={handleSubmitData}
            isDisabled={errorMessage !== null}
            isLoading={
              addClassMutation.isPending || editClassMutation.isPending
            }
          />

          <CloseButton onClick={onClose} variant="outlined" />
        </div>
      </div>
      <Card bg="white">
        <TextInput
          defaultValue={className}
          placeholder={t('class-name')}
          mb={16}
          error={errorMessage && t(errorMessage)}
          onChange={(e) => {
            setClassName(e.target.value);

            if (errorMessage) setErrorMessage(null);
          }}
        />

        <div className={styles.gradesWrapper}>
          <Text
            transKey="select-grade"
            type="body2"
            color="gray800"
            fw={400}
            mb={16}
          />
          <div className={styles.gradesSelectWrapper}>
            {SUPPORTED_SCHOOL_GRADES.map((gradeItem) => (
              <button
                key={gradeItem}
                className={`${styles.gradeItem} ${
                  grade === gradeItem && styles.gradeItemActive
                }`}
                type="button"
                onClick={() => setGrade(gradeItem)}
              >
                {gradeItem}
              </button>
            ))}
          </div>
        </div>
      </Card>
    </Card>
  );
};

export default AddClassForm;
