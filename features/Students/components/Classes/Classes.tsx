/* eslint-disable react-hooks/exhaustive-deps */
import { Menu, Table, TableData } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaEye, FaUsers } from 'react-icons/fa';
import { RiDeleteBin5Fill, RiEditFill } from 'react-icons/ri';

import { GLOBAL_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Checkbox from '@/components/Checkbox/Checkbox';
import Icon from '@/components/Icon/Icon';
import ClassAndGroupInfoCard from '@/components/InformationActionCard/ClassInfoCard/ClassInfoCard';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { ClassesContext } from '@/context/ClassesProvider';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_CLASSES from '@/services/schools/classes';
import baseTheme from '@/styles/baseTheme';
import { SingleClassDetailsType } from '@/types/common';

import { TEACHER_ACTIONS_TRANSLATION_KEYS } from '../../consts';
import GridPlaceholder from '../GridPlaceholder/GridPlaceholder';
import SchoolLayout from '../SchoolLayout/SchoolLayout';
import AddClassForm from './AddClassForm';
import styles from './Classes.module.css';
import ClassStudentsModal from './ClassStudentsModal';

type ModalActionsTypes = 'view' | 'delete' | 'edit';

const DEFAULT_SELECTED_CLASS_DETAILS: SingleClassDetailsType = {
  id: '',
  name: '',
  grade: '1',
  studentsCount: 0,
};

const Classes = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { userRoles } = UserContext();
  const { areClassesListLoading, classesError, classesList, isClassesError } =
    ClassesContext();

  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const [selectedClassDetails, setSelectedClassDetails] =
    useState<SingleClassDetailsType>(DEFAULT_SELECTED_CLASS_DETAILS);
  const [alertModalType, setAlertModalType] =
    useState<ModalActionsTypes | null>(null);

  const [selectedMenuId, setSelectedMenuId] = useState<string | null>(null);
  const [searchWord, setSearchWord] = useState('');

  const filteredClassesList = classesList.filter((item) =>
    item.name.toLowerCase().includes(searchWord.toLowerCase())
  );

  const areClassesEmptyAndNotLoading =
    filteredClassesList.length === 0 && !areClassesListLoading;

  const isAllowedToPerformAction =
    userRoles.isSchoolAdmin && selectedClassDetails;

  const handleCloseModalActions = (action: ModalActionsTypes) => {
    if (action === 'view') {
      setIsViewModalOpen(false);
    } else if (action === 'edit') {
      setIsEditModalOpen(false);
    } else {
      setAlertModalType(null);
    }
  };

  const actionsCallback = (action: ModalActionsTypes, revalidate: boolean) => {
    if (revalidate) {
      // invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.CLASSES_LIST] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_STUDENT_LIST],
      });
    }

    handleCloseModalActions(action);
  };

  const deleteClassMutation = useMutation({
    mutationFn: SCHOOL_CLASSES.DELETE_CLASS,
    onSuccess: () => {
      notifications.show({
        color: 'green',
        title: 'Success',
        message: t('delete-success'),
        autoClose: 2000,
      });

      actionsCallback('delete', true);
    },
    onError: (error) => {
      notifications.show({
        color: 'red',
        title: 'Error',
        message: t(error.message),
        autoClose: 2000,
      });
      actionsCallback('delete', false);
    },
  });

  const handleOpenModalActions = (
    action: ModalActionsTypes,
    selectedClassDetailsData: SingleClassDetailsType
  ) => {
    setSelectedClassDetails(selectedClassDetailsData);

    if (action === 'view') {
      setIsViewModalOpen(true);
    } else if (action === 'edit') {
      setIsEditModalOpen(true);
    } else {
      setAlertModalType(action);
    }
  };

  const handleMutationModalActions = (action: ModalActionsTypes) => {
    if (action === 'delete' && isAllowedToPerformAction) {
      deleteClassMutation.mutate({
        classId: selectedClassDetails.id,
      });
    }
  };

  const menuTable = useMemo(() => {
    const selectedClassDetailsData =
      filteredClassesList?.find((el) => el.id === selectedMenuId) || null;

    if (!selectedClassDetailsData) return null;

    return (
      <Menu.Dropdown className="menuDropdown">
        <Menu.Item
          onClick={() =>
            handleOpenModalActions('view', selectedClassDetailsData)
          }
          leftSection={<FaUsers size={19} />}
        >
          {t('students-list')}
        </Menu.Item>

        {userRoles.isSchoolAdmin && (
          <Menu.Item
            onClick={() => {
              setSelectedClassDetails(selectedClassDetailsData);
              handleOpenModalActions('edit', selectedClassDetailsData);
            }}
            leftSection={<RiEditFill size={19} />}
          >
            {t('edit')}
          </Menu.Item>
        )}

        {userRoles.isSchoolAdmin && (
          <Menu.Item
            onClick={() =>
              handleOpenModalActions('delete', selectedClassDetailsData)
            }
            leftSection={
              <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
            }
          >
            {t(TEACHER_ACTIONS_TRANSLATION_KEYS.delete)}
          </Menu.Item>
        )}
      </Menu.Dropdown>
    );
  }, [selectedMenuId, t]);

  const getDropdownMenu = (el: SingleClassDetailsType) => {
    return (
      <Menu
        opened={selectedMenuId === el.id}
        key={el.id}
        onChange={() => selectedMenuId === el.id && setSelectedMenuId(null)}
        shadow="md"
      >
        <Menu.Target>
          <div className={styles.dropDownWrapper}>
            <Icon
              name="MenuDotsSvg"
              onClick={() => {
                setSelectedMenuId(selectedMenuId === el.id ? null : el.id);
              }}
              color="turquoise"
              w={18}
              h={18}
            />
          </div>
        </Menu.Target>
        {menuTable}
      </Menu>
    );
  };

  const tableData: TableData = {
    head: [
      t('name-capital'),
      t('grade-capital'),
      t('number-of-students-capital'),
      // t('select-class-capital'),
      '',
    ],
    body: filteredClassesList?.map((el) => [
      el.name,
      el.grade,
      el.studentsCount,
      // <Checkbox
      //   key={se}
      //   value={selectedGroupIds[0]}
      //   isChecked={selectedGroupIds.includes(el.id)}
      //   onChange={() =>
      //     setSelectedGroupIds((prev) => (prev.includes(el.id) ? [] : [el.id]))
      //   }
      // />,
      userRoles.isSchoolTeacher ? (
        <FaEye
          size={20}
          color={baseTheme.colors.blue}
          cursor="pointer"
          onClick={() => handleOpenModalActions('view', el)}
        />
      ) : (
        getDropdownMenu(el)
      ),
    ]),
  };

  const CARDS_VIEW_COMPONENT = (
    <GridPlaceholder
      isPending={areClassesListLoading}
      cardsComponent={filteredClassesList?.map((el) => (
        <ClassAndGroupInfoCard
          key={el.id}
          title={el.name}
          grade={el.grade}
          studentsCount={el.studentsCount}
          actionComponent={
            userRoles.isSchoolTeacher ? (
              <FaEye
                size={20}
                color={baseTheme.colors.turquoise}
                cursor="pointer"
                onClick={() => handleOpenModalActions('view', el)}
              />
            ) : (
              getDropdownMenu(el)
            )
          }
          isLoading={areClassesListLoading}
        />
      ))}
    />
  );

  const TABLE_VIEW_COMPONENT = areClassesListLoading ? (
    <TableSkeleton numberOfColumns={tableData.head?.length} numberOfRows={6} />
  ) : (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
      }}
    />
  );

  if (
    isClassesError &&
    classesError?.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS
  ) {
    return (
      <div className={styles.errorWrapper}>
        <Text
          transKey="unauthorized-access"
          type="h3"
          fw={400}
          mt={60}
          align="center"
        />
      </div>
    );
  }

  return (
    <>
      <SchoolLayout
        type="classes"
        numberOfSearchResults={filteredClassesList?.length || 0}
        cardsContent={CARDS_VIEW_COMPONENT}
        listContent={TABLE_VIEW_COMPONENT}
        onSearch={(value) => {
          setSearchWord(value);
        }}
        isNoResultsVisible={areClassesEmptyAndNotLoading}
        searchWord={searchWord}
        searchInputTransKey="search-classes"
        isNoDataListDisplayed={
          filteredClassesList?.length === 0 &&
          !areClassesListLoading &&
          searchWord.length === 0
        }
      />

      <AlertDialog
        isOpen={alertModalType !== null}
        title="delete-class"
        description="delete-class-confirmation"
        transDescriptionVariables={{
          name: selectedClassDetails?.name || '',
        }}
        variant="danger"
        onCancel={() => handleCloseModalActions('delete')}
        onConfirmAction={() => handleMutationModalActions('delete')}
        isActionInProgress={deleteClassMutation.isPending}
      />

      <PrimaryModal
        isOpen={isEditModalOpen}
        content={
          <AddClassForm
            onClose={() => handleCloseModalActions('edit')}
            selectedClass={selectedClassDetails}
          />
        }
      />

      {selectedClassDetails && (
        <ClassStudentsModal
          isModalVisible={isViewModalOpen}
          onClose={() => handleCloseModalActions('view')}
          selectedClass={selectedClassDetails}
        />
      )}
    </>
  );
};

export default Classes;
