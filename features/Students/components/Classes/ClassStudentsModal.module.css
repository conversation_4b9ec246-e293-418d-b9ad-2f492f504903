.wrapper {
  width: 100vw;
  max-width: 916px;
  max-height: 90%;

  @media screen and (max-width: 983px) {
    width: calc(100vw - 48px);
  }

  /* prevent select */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-md);
}

.buttonsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.titleWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-sm);
}

.row {
  display: flex;
  align-items: center;
  justify-content: center;
}

.verticalLine {
  width: 1px;
  height: 14px;
  background-color: rgb(163, 165, 164);
  margin: 0 var(--spacing-md);
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 300px;
  max-height: 746px;
}

.noStudentsContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 500px;
}

.center {
  margin-top: var(--spacing-3xl);
  justify-content: center;
  align-items: center;
}

.inputRightSection {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-right: var(--spacing-lg);
}

.inputWrapper {
  display: flex;
  margin-bottom: var(--spacing-xl);
}

.tableWrapper {
  height: 250px;
  overflow: auto;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-xl);
}
