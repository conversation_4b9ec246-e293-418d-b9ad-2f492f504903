.wrapper {
  width: 100vw;
  max-width: 664px;
  max-height: 90%;

  @media screen and (max-width: 983px) {
    width: calc(100vw - 48px);
  }

  /* prevent select */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.buttonsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.gradesWrapper {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-gray200);
  border-radius: var(--radius-xs);
  padding: var(--spacing-md);
}

.gradesSelectWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.gradeItem {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border: 1px solid var(--color-gray200);
  background-color: var(--color-white);
  border-radius: 50%;
  padding: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-gray200);
  cursor: pointer;

  &:hover {
    background-color: var(--color-turquoise);
    color: var(--color-white);
    border-color: var(--color-turquoise);
  }
}

.gradeItemActive {
  background-color: var(--color-turquoise);
  color: var(--color-white);
  border-color: var(--color-turquoise);

  cursor: default;
  user-select: none;
}

.row {
  display: flex;
  gap: var(--spacing-md);
}

.content {
  overflow: visible !important;
}
