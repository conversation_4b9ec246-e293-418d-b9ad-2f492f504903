import { Skeleton } from '@mantine/core';

import s from './GridPlaceholder.module.css';

type GridPlaceholderPropsType = {
  cardsComponent: React.ReactNode[];
  isPending: boolean;
};
const GridPlaceholder = ({
  cardsComponent,
  isPending,
}: GridPlaceholderPropsType): JSX.Element | null => {
  return (
    <div className={s.wrapper}>
      {!isPending && cardsComponent}

      {isPending &&
        Array.from({ length: 12 }, (_, index) => index).map((index) => (
          <Skeleton height={142} key={index} radius={8} />
        ))}
    </div>
  );
};
export default GridPlaceholder;
