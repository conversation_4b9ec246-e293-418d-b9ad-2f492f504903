import { Menu } from '@mantine/core';

import Text from '@/components/Text/Text';

import s from './ActionsMenu.module.css';

type ActionsMenuPropsType = {
  data: { key: string; action: React.ReactNode }[];
};

/* Used under Students - Classes/Groups - Teachers to style the actions on the top right. Delete unclaim etc */
const ActionsMenu = ({ data }: ActionsMenuPropsType): JSX.Element => {
  return (
    <div>
      <Menu shadow="md">
        <Menu.Target>
          <button type="button" className={s.wrapper}>
            <Text transKey="actions-capital" type="button" color="blue" />
          </button>
        </Menu.Target>

        <Menu.Dropdown>
          {data.map((item) => (
            <Menu.Item key={item.key}>{item.action}</Menu.Item>
          ))}
        </Menu.Dropdown>
      </Menu>
    </div>
  );
};

export default ActionsMenu;
