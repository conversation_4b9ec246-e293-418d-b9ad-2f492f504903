.wrapper {
  width: 100vw;
  max-width: 800px;
  max-height: 90%;

  @media screen and (max-width: 1018px) {
    width: calc(100vw - 48px);
  }

  /* prevent select */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profileDetails {
  display: flex;
  gap: var(--spacing-lg);
}

.profileName {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--spacing-xs);
}

.buttonsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.content {
  margin-top: var(--spacing-xl);
}

.customerId {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

.actionButtonWrapper {
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;

  margin-top: var(--spacing-xl);
}

.certificationsList {
  margin-top: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.certificationsList > div {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding-bottom: var(--spacing-md);
}

.certificationsList > div:last-child {
  border-bottom: none !important;
  padding-bottom: var(--spacing-md);
}

.classesWrapper {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.classes {
  width: 100%;
  max-width: 336px;
}

.classAccess {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
