.wrapper {
  width: 100vw;
  max-width: 1005px;
  max-height: 90%;

  @media screen and (max-width: 1018px) {
    width: calc(100vw - 48px);
  }

  /* prevent select */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profileDetails {
  display: flex;
  gap: var(--spacing-lg);
}

.profileName {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--spacing-xs);
}

.buttonsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.content {
  margin-top: var(--spacing-2xl);
}

.customerId {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}
