.wrapper {
  max-width: 966px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.buttonsWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.addMoreButton {
  cursor: pointer;
  justify-self: flex-end;

  &:hover {
    opacity: 0.8;
  }
}

.teachersListWrapper {
  display: flex;
  flex-direction: column;
  max-height: 500px;
  overflow-y: auto;
}

.row {
  display: flex;
  gap: var(--spacing-md);

  height: 75px;
}

.certificateRow {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.inviteTeachersButtonWrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-xl);
}
