// TODO : FIX USING INDEX AS KEY. React can actually handle it, but it's not recommended. It's better to use a unique key.
/* eslint-disable react/no-array-index-key */
import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import INVITATIONS from '@/services/invitations';
import { INVITE_TEACHERS_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import styles from './InviteTeacherForm.module.css';

type InviteTeacherFormProps = {
  onClose: () => void;
};

type TeacherListType = z.infer<typeof INVITE_TEACHERS_FORM_SCHEMA>;

const DEFAULT_VALUES = {
  teachers: [
    {
      firstName: '',
      lastName: '',
      email: '',
    },
  ],
};

const InviteTeacherForm = ({ onClose }: InviteTeacherFormProps) => {
  const queryClient = useQueryClient();

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    setValue,
    watch,
  } = useForm<z.infer<typeof INVITE_TEACHERS_FORM_SCHEMA>>({
    resolver: zodResolver(INVITE_TEACHERS_FORM_SCHEMA),
    defaultValues: DEFAULT_VALUES,
    mode: 'onSubmit',
  });

  const { t } = useTranslation();

  const handleAddRow = () => {
    setValue('teachers', [
      ...watch('teachers'),
      {
        firstName: '',
        lastName: '',
        email: '',
      },
    ]);
  };

  const InviteTeacherFormsTeacherMutation = useMutation({
    mutationFn: INVITATIONS.INVITE_TEACHERS,
    onSuccess: () => {
      notifications.show({
        message: t('teachers-accepted'),
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_TEACHERS_LIST],
      });

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message, { email: error.cause }),
        color: 'red',
      });
    },
  });

  const onSubmit: SubmitHandler<TeacherListType> = (data: TeacherListType) => {
    InviteTeacherFormsTeacherMutation.mutate(data.teachers);
  };

  const handleSubmitData = (e: React.FormEvent) => {
    e.preventDefault();

    const clearedList = watch('teachers').filter(
      (teacher) =>
        teacher.firstName.trim() !== '' ||
        teacher.lastName.trim() !== '' ||
        teacher.email.trim() !== ''
    );

    const payload =
      clearedList.length === 0 ? DEFAULT_VALUES.teachers : clearedList;

    setValue('teachers', payload);

    // TODO fix this
    handleSubmit(() => onSubmit({ teachers: payload }))();
  };

  const { fields } = useFieldArray({
    name: 'teachers',
    control,
  });

  return (
    <Card
      size="2xl"
      bg="gray50"
      hasDynamicHeight
      className={`${styles.wrapper} preventUserSelect`}
    >
      <form onSubmit={handleSubmitData}>
        <div className={styles.header}>
          <Text transKey="invite-teachers" type="h3" />

          <div className={styles.buttonsWrapper}>
            <Button
              transKey="invite-teachers-capital"
              isLocked={false}
              type="submit"
              isLoading={InviteTeacherFormsTeacherMutation.isPending}
            />

            <CloseButton onClick={onClose} variant="outlined" />
          </div>
        </div>

        <Card>
          {fields.map((field, index) => (
            <div className={styles.row} key={field.id}>
              <TextInput
                {...register(`teachers.${index}.firstName`)}
                placeholder={t('name')}
                w="100%"
                error={t(errors?.teachers?.[index]?.firstName?.message || '')}
              />

              <TextInput
                {...register(`teachers.${index}.lastName`)}
                placeholder={t('surname')}
                w="100%"
                error={t(errors?.teachers?.[index]?.lastName?.message || '')}
              />

              <TextInput
                {...register(`teachers.${index}.email`)}
                placeholder={t('email')}
                w="100%"
                error={t(errors?.teachers?.[index]?.email?.message || '')}
              />
            </div>
          ))}

          <Text
            transKey="add-more-capital"
            type="button"
            color="blue"
            onClick={handleAddRow}
            className={styles.addMoreButton}
            mt={20}
          />
        </Card>
      </form>
    </Card>
  );
};

export default InviteTeacherForm;
