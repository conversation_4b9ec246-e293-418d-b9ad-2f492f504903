/* eslint-disable @typescript-eslint/no-unused-vars */
import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { PRODUCTS } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Checkbox from '@/components/Checkbox/Checkbox';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import INVITATIONS from '@/services/invitations';
import { INVITE_TEACHERS_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import styles from './InviteTeacherForm.module.css';

type InviteTeacherFormProps = {
  onClose: () => void;
};

type TeacherListType = z.infer<typeof INVITE_TEACHERS_FORM_SCHEMA>;

type CertificateCounterType = {
  [key: string]: number;
};

const InviteTeacherForm = ({ onClose }: InviteTeacherFormProps) => {
  const queryClient = useQueryClient();

  const { invalidateUser, user } = UserContext();

  const [certificatesCounter, setCertificatesCounter] =
    useState<CertificateCounterType>({});

  const bundles = user.school?.bundles || [];

  const DEFAULT_VALUES = {
    teachers: [
      {
        firstName: '',
        lastName: '',
        email: '',
        certificates: [],
      },
    ],
  };

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    setValue,
    watch,
  } = useForm<z.infer<typeof INVITE_TEACHERS_FORM_SCHEMA>>({
    resolver: zodResolver(INVITE_TEACHERS_FORM_SCHEMA),
    defaultValues: DEFAULT_VALUES,
    mode: 'onSubmit',
  });

  const { t } = useTranslation();

  const handleAddRow = () => {
    setValue('teachers', [...watch('teachers'), DEFAULT_VALUES.teachers[0]]);
  };

  const InviteTeacherFormsTeacherMutation = useMutation({
    mutationFn: INVITATIONS.INVITE_TEACHERS,
    onSuccess: () => {
      notifications.show({
        message: t('successfully-invited'),
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_TEACHERS_LIST],
      });

      invalidateUser();

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: error.cause
          ? t(error.message, { email: error.cause })
          : t(error.message),
        color: 'red',
      });
    },
  });

  const onSubmit: SubmitHandler<TeacherListType> = (data: TeacherListType) => {
    InviteTeacherFormsTeacherMutation.mutate(data.teachers);
  };

  const handleSubmitData = (e: React.FormEvent) => {
    e.preventDefault();

    const clearedList =
      watch('teachers').filter(
        (teacher) =>
          teacher.firstName.trim() !== '' ||
          teacher.lastName.trim() !== '' ||
          teacher.email.trim() !== ''
      ) || [];

    if (clearedList.length > 0) {
      setValue('teachers', clearedList);

      handleSubmit(() => onSubmit({ teachers: clearedList }))();
    }
  };

  const { fields } = useFieldArray({
    name: 'teachers',
    control,
  });

  const handleCertificateChange = ({
    certificate,
    currentCertificates,
    isChecked,
    path,
  }: {
    certificate: TeacherListType['teachers'][0]['certificates'][0];
    currentCertificates: TeacherListType['teachers'][0]['certificates'];
    isChecked: boolean;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    path: any;
  }) => {
    // 1. Update the certificates array for this teacher
    const updated = isChecked
      ? currentCertificates.filter((c) => c.type !== certificate.type)
      : [
          ...currentCertificates,
          {
            type: certificate.type,
            status: certificate.status,
          },
        ];

    setValue(path, updated, {
      shouldDirty: true,
      shouldValidate: true,
    });

    // 2. Update the global counter safely
    setCertificatesCounter((prev) => {
      const prevCount = prev[certificate.type] ?? 0;
      const newCount = isChecked ? Math.max(prevCount - 1, 0) : prevCount + 1;

      // Remove the key entirely if count hits 0
      if (newCount === 0) {
        const { [certificate.type]: _, ...rest } = prev;
        return rest;
      }

      return {
        ...prev,
        [certificate.type]: newCount,
      };
    });
  };

  return (
    <Card
      size="2xl"
      bg="gray50"
      hasDynamicHeight
      className={`${styles.wrapper} preventUserSelect`}
    >
      <form onSubmit={handleSubmitData}>
        <div className={styles.header}>
          <Text transKey="invite-teachers" type="h3" />

          <div className={styles.buttonsWrapper}>
            <CloseButton onClick={onClose} variant="outlined" />
          </div>
        </div>

        <Card>
          {fields.map((field, index) => (
            <div key={field.id}>
              <div className={styles.row}>
                <TextInput
                  {...register(`teachers.${index}.firstName`)}
                  placeholder={t('name')}
                  w="100%"
                  error={t(errors?.teachers?.[index]?.firstName?.message || '')}
                />

                <TextInput
                  {...register(`teachers.${index}.lastName`)}
                  placeholder={t('surname')}
                  w="100%"
                  error={t(errors?.teachers?.[index]?.lastName?.message || '')}
                />

                <TextInput
                  {...register(`teachers.${index}.email`)}
                  placeholder={t('email')}
                  w="100%"
                  error={t(errors?.teachers?.[index]?.email?.message || '')}
                />
              </div>

              <Text
                transKey="certificates-label"
                type="body2"
                fw={400}
                mb={8}
              />

              <div className={styles.certificateRow}>
                {bundles.map((certificate) => {
                  const path = `teachers.${index}.certificates` as const;
                  const currentCertificates = watch(path) ?? [];
                  const isChecked = currentCertificates.some(
                    (c) => c.type === certificate.type
                  );

                  return (
                    certificate?.type && (
                      <Checkbox
                        key={`${field.id}-${certificate.type}`}
                        label={
                          PRODUCTS[certificate.type as keyof typeof PRODUCTS]
                            .typeName
                        }
                        value={certificate.type}
                        isChecked={isChecked}
                        onChange={() => {
                          handleCertificateChange({
                            certificate,
                            currentCertificates,
                            isChecked,
                            path,
                          });
                        }}
                        variant="primaryList"
                        isDisabled={
                          !isChecked &&
                          certificate.certificates -
                            (certificatesCounter[certificate.type] || 0) ===
                            0
                        }
                      />
                    )
                  );
                })}
              </div>
            </div>
          ))}

          <Text
            transKey="add-more-capital"
            type="button"
            color="blue"
            onClick={handleAddRow}
            className={styles.addMoreButton}
            mt={20}
          />
        </Card>

        <div className={styles.inviteTeachersButtonWrapper}>
          <Button
            transKey="invite-teachers-capital"
            isLocked={false}
            type="submit"
            isLoading={InviteTeacherFormsTeacherMutation.isPending}
          />
        </div>
      </form>
    </Card>
  );
};

export default InviteTeacherForm;
