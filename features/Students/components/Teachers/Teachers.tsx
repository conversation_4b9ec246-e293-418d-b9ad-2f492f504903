import { Menu, Pagination, Table, TableData } from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AiFillEye } from 'react-icons/ai';
import { FaBan, FaRedo } from 'react-icons/fa';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import { capitalizeFirstLetter, getListDateTime } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Icon from '@/components/Icon/Icon';
import StatusChip from '@/components/InformationActionCard/TeachersInfoCard/StatusChip/StatusChip';
import TeacherInfoCard2 from '@/components/InformationActionCard/TeachersInfoCard/TeacherInfoCard';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import INVITATIONS from '@/services/invitations';
import SCHOOL_TEACHERS from '@/services/schools/teachers';
import {
  AccessLevelType,
  TeachersDetailsType,
  TeachersListType,
  TranslationKeysType,
} from '@/types/common';

import { TEACHER_ACTIONS_TRANSLATION_KEYS } from '../../consts';
import GridPlaceholder from '../GridPlaceholder/GridPlaceholder';
import SchoolLayout from '../SchoolLayout/SchoolLayout';
import TeacherDetails from './TeacherDetails';
import styles from './Teachers.module.css';

type ModalActionsTypes = 'view' | 'revoke' | 'resend' | 'delete';

const Teachers = () => {
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTeacherDetails, setSelectedTeacherDetails] =
    useState<TeachersDetailsType | null>(null);
  const [alertModalType, setAlertModalType] = useState<
    'delete' | 'revoke' | 'resend' | null
  >(null);

  const [selectedMenuId, setSelectedMenuId] = useState<string | null>(null);
  const [searchWord, setSearchWord] = useState('');
  const [debouncedSearchWord] = useDebouncedValue(searchWord, 500);

  const { t } = useTranslation();
  const router = useRouter();

  const [activePage, setActivePage] = useState(1);

  const queryClient = useQueryClient();

  const {
    data: teachersListData,
    error: getTeachersError,
    isError,
    isFetching: areTeachersFetching,
    isLoading: areTeachersLoading,
    isPending: isPagePending,
  } = useQuery<TeachersListType | null>({
    queryFn: () =>
      SCHOOL_TEACHERS.GET_TEACHERS({
        limit: GLOBAL_PAGINATION_FETCH_LIMIT,
        page: activePage,
        query: debouncedSearchWord,
      }),
    queryKey: [
      QUERY_KEYS.SCHOOL_TEACHERS_LIST,
      activePage,
      debouncedSearchWord,
    ],
    placeholderData: keepPreviousData,
    staleTime: 10000,
    enabled: Boolean(router.isReady),
  });

  const teachersList = teachersListData?.results || [];

  const totalNumberOfPages = Math.ceil(
    (teachersListData?.count || 1) / GLOBAL_PAGINATION_FETCH_LIMIT
  );

  const areTotalNumberOfTeachersZero = (teachersListData?.count || 0) === 0;
  const areTeachersArrayEmpty = teachersList.length === 0;
  const areTeachersEmptyAndNotFetching =
    areTeachersArrayEmpty && !areTotalNumberOfTeachersZero;

  const handleCloseModalActions = (action: ModalActionsTypes) => {
    if (action === 'view') {
      setIsViewModalOpen(false);
    } else {
      setAlertModalType(null);
    }

    setSelectedMenuId(null);
    setSelectedTeacherDetails(null);
  };

  const actionsCallback = (action: ModalActionsTypes, revalidate: boolean) => {
    if (revalidate) {
      // invalidate all relevant queries
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_TEACHERS_LIST],
      });
    }

    handleCloseModalActions(action);
  };

  const deleteTeacherMutation = useMutation({
    mutationFn: SCHOOL_TEACHERS.DELETE_TEACHER,
    onSuccess: () => {
      notifications.show({
        color: 'green',
        title: 'Success',
        message: t('delete-success'),
        autoClose: 2000,
      });
      actionsCallback('delete', true);
    },
    onError: (error) => {
      notifications.show({
        color: 'red',
        title: 'Error',
        message: t(error.message),
        autoClose: 2000,
      });
      actionsCallback('delete', false);
    },
  });

  const revokeTeacherMutation = useMutation({
    mutationFn: INVITATIONS.REVOKE_TEACHER_INVITATION,
    onSuccess: () => {
      notifications.show({
        color: 'green',
        title: 'Success',
        message: t('revoke-success'),
        autoClose: 2000,
      });
      actionsCallback('revoke', true);
    },
    onError: (error) => {
      notifications.show({
        color: 'red',
        title: 'Error',
        message: t(error.message),
        autoClose: 2000,
      });
      actionsCallback('revoke', false);
    },
  });

  const resendTeacherInvitationMutation = useMutation({
    mutationFn: INVITATIONS.RESEND_TEACHER_INVITATION,
    onSuccess: () => {
      notifications.show({
        color: 'green',
        title: 'Success',
        message: t('resend-success'),
        autoClose: 2000,
      });
      actionsCallback('resend', true);
    },
    onError: (error) => {
      notifications.show({
        color: 'red',
        title: 'Error',
        message: t(error.message),
        autoClose: 2000,
      });
      actionsCallback('resend', false);
    },
  });

  const handleOpenModalActions = (
    action: ModalActionsTypes,
    selectedTeacherDetailsData: TeachersDetailsType | null
  ) => {
    setSelectedTeacherDetails(selectedTeacherDetailsData);

    if (action === 'view') {
      setIsViewModalOpen(true);
    } else {
      setAlertModalType(action);
    }
  };

  const handleMutationModalActions = (action: ModalActionsTypes) => {
    if (action === 'delete') {
      deleteTeacherMutation.mutate({
        teacherId: selectedTeacherDetails?.id || '',
      });
    } else if (action === 'revoke') {
      revokeTeacherMutation.mutate({
        invitationId: selectedTeacherDetails?.invitation.id || '',
      });
    } else if (action === 'resend') {
      resendTeacherInvitationMutation.mutate({
        invitationId: selectedTeacherDetails?.invitation.id || '',
      });
    }
  };

  const menuTable = useMemo(() => {
    const selectedTeacherDetailsData =
      teachersList.find((el) => el.id === selectedMenuId) || null;

    if (!selectedTeacherDetailsData) return null;

    return (
      <Menu.Dropdown className="menuDropdown">
        <Menu.Item
          onClick={() =>
            handleOpenModalActions('view', selectedTeacherDetailsData)
          }
          leftSection={<AiFillEye size={19} />}
        >
          {t('teacher-details')}
        </Menu.Item>

        {selectedTeacherDetailsData?.status !== 'active' && (
          <>
            <Menu.Item
              onClick={() =>
                handleOpenModalActions('resend', selectedTeacherDetailsData)
              }
              leftSection={<FaRedo />}
            >
              {t(TEACHER_ACTIONS_TRANSLATION_KEYS.resend)}
            </Menu.Item>

            <Menu.Item
              onClick={() =>
                handleOpenModalActions('revoke', selectedTeacherDetailsData)
              }
              leftSection={<FaBan size={17} />}
            >
              {t(TEACHER_ACTIONS_TRANSLATION_KEYS.revoke)}
            </Menu.Item>
          </>
        )}

        <Menu.Item
          onClick={() =>
            handleOpenModalActions('delete', selectedTeacherDetailsData)
          }
          leftSection={
            <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
          }
        >
          {t(TEACHER_ACTIONS_TRANSLATION_KEYS.delete)}
        </Menu.Item>
      </Menu.Dropdown>
    );
  }, [selectedMenuId, t]);

  const getDropdownMenu = (el: TeachersDetailsType) => {
    return (
      <Menu
        opened={selectedMenuId === el.id}
        key={el.id}
        onChange={() => selectedMenuId === el.id && setSelectedMenuId(null)}
        shadow="md"
      >
        <Menu.Target>
          <div className={styles.dropDownWrapper}>
            <Icon
              name="MenuDotsSvg"
              onClick={() => {
                setSelectedMenuId(selectedMenuId === el.id ? null : el.id);
              }}
              color="turquoise"
              w={18}
              h={18}
            />
          </div>
        </Menu.Target>
        {menuTable}
      </Menu>
    );
  };

  const tableData: TableData = {
    head: [
      <div key="name" className={styles.tableHeaderItem}>
        {t('name-capital')}
      </div>,
      <div key="surname" className={styles.tableHeaderItem}>
        {t('surname-capital')}
      </div>,
      <div key="email" className={styles.tableHeaderItem}>
        {t('email-capital')}
      </div>,
      <div key="class" className={styles.tableHeaderItem}>
        {t('class-capital')}
      </div>,
      <div key="access" className={styles.tableHeaderItem}>
        {t('access-capital')}
      </div>,
      <div key="active-since" className={styles.tableHeaderItem}>
        {t('active-since-capital')}
      </div>,
      <div key="status" className={styles.tableHeaderItem}>
        {t('status-capital')}
      </div>,
      '',
    ],
    body: teachersList.map((el) => [
      el.firstName,
      el.lastName,
      el.email,
      el?.classes?.map((cl) => cl.name).join(', ') || '-',
      el?.access ? capitalizeFirstLetter(el.access) : '-',
      el?.joined ? getListDateTime(el.joined, t) : '-',
      <StatusChip key={el.id} status={el.status} isNormalHeight />,
      getDropdownMenu(el),
    ]),
  };

  const CARDS_VIEW_COMPONENT = (
    <GridPlaceholder
      isPending={isPagePending}
      cardsComponent={teachersList.map((teacher) => (
        <TeacherInfoCard2
          key={teacher.id}
          fullName={`${teacher.firstName} ${teacher.lastName}`}
          email={teacher.email}
          actionComponent={getDropdownMenu(teacher)}
          invitationStatus={teacher?.status}
          isDisabled={areTeachersFetching}
        />
      ))}
    />
  );

  const TABLE_VIEW_COMPONENT = areTeachersFetching ? (
    <TableSkeleton numberOfColumns={tableData.head?.length} />
  ) : (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
      }}
    />
  );

  const PAGINATION_COMPONENT =
    areTeachersEmptyAndNotFetching && !areTotalNumberOfTeachersZero ? null : (
      <Pagination
        total={totalNumberOfPages}
        value={activePage}
        hideWithOnePage
        withControls={false}
        onChange={(value) => setActivePage(value)}
        disabled={areTeachersFetching}
      />
    );

  if (
    isError &&
    getTeachersError.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS
  ) {
    return (
      <div className={styles.errorWrapper}>
        <Text
          transKey="unauthorized-access"
          type="h3"
          fw={400}
          mt={60}
          align="center"
        />
      </div>
    );
  }

  return (
    <>
      <SchoolLayout
        type="teachers"
        onClearFilters={() => {}}
        numberOfSearchResults={teachersListData?.count || null}
        onSearch={(value) => {
          setSearchWord(value);
        }}
        isNoResultsVisible={
          teachersList.length === 0 &&
          !areTeachersFetching &&
          teachersListData?.totalCount !== 0
        }
        searchWord={searchWord}
        cardsContent={CARDS_VIEW_COMPONENT}
        listContent={TABLE_VIEW_COMPONENT}
        searchInputTransKey="search-teachers"
        isNoDataListDisplayed={
          teachersList.length === 0 &&
          !areTeachersFetching &&
          teachersListData?.totalCount === 0
        }
        isInformationLoading={areTeachersLoading}
        paginationComponent={PAGINATION_COMPONENT}
      />

      <AlertDialog
        isOpen={alertModalType !== null}
        title={
          TEACHER_ACTIONS_TRANSLATION_KEYS[
            alertModalType || 'delete'
          ] as TranslationKeysType
        }
        description={
          `${
            TEACHER_ACTIONS_TRANSLATION_KEYS[alertModalType || 'delete']
          }-confirmation` as TranslationKeysType
        }
        transDescriptionVariables={{
          name: selectedTeacherDetails?.firstName || '',
          surname: selectedTeacherDetails?.lastName || '',
        }}
        variant={
          alertModalType === 'revoke' || alertModalType === 'delete'
            ? 'danger'
            : 'info'
        }
        onCancel={() => handleCloseModalActions(alertModalType || 'delete')}
        onConfirmAction={() =>
          handleMutationModalActions(alertModalType || 'delete')
        }
        isActionInProgress={
          deleteTeacherMutation.isPending ||
          revokeTeacherMutation.isPending ||
          resendTeacherInvitationMutation.isPending
        }
      />

      <PrimaryModal
        isOpen={isViewModalOpen}
        content={
          selectedTeacherDetails ? (
            <TeacherDetails
              teacherDetails={selectedTeacherDetails}
              onClose={() => handleCloseModalActions('view')}
              selectedClasses={selectedTeacherDetails.classes}
              selectedAccessLevel={
                selectedTeacherDetails.access as AccessLevelType
              }
            />
          ) : (
            <div />
          )
        }
      />
    </>
  );
};

export default Teachers;
