import { Grid, Skeleton } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PRODUCTS } from '@/common/consts';
import { GLOBAL_ERRORS, SCHOOLS_ERRORS } from '@/common/errors';
import { getFormattedDate } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Checkbox from '@/components/Checkbox/Checkbox';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import PersonalInfoCard from '@/components/PersonalInfoCard/PersonalInfoCard';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import SCHO<PERSON>_CLASSES from '@/services/schools/classes';
import SCHO<PERSON>_TEACHERS from '@/services/schools/teachers';
import {
  AccessLevelType,
  ClassesListFetchListType,
  TeacherDetailsType,
  TeachersDetailsType,
} from '@/types/common';

import styles from './TeacherDetails.module.css';

type TeacherDetailsProps = {
  teacherDetails: TeachersDetailsType;
  onClose: () => void;
  selectedClasses?: TeachersDetailsType['classes'] | null;
  selectedAccessLevel?: AccessLevelType;
};

const TeacherDetails = ({
  onClose,
  selectedAccessLevel,
  selectedClasses,
  teacherDetails,
}: TeacherDetailsProps) => {
  const { i18n, t } = useTranslation();
  const { invalidateUser, user } = UserContext();
  const queryClient = useQueryClient();

  const [accessLevel, setAccessLevel] = useState<AccessLevelType>(
    selectedAccessLevel || 'class'
  );

  const [selectedClassesIds, setSelectedClassesIds] = useState<string[]>(
    selectedClasses?.map((c) => c.id) || []
  );

  const [addedCertificates, setAddedCertificates] = useState<
    TeacherDetailsType['certificates'][0]['type'][]
  >([]);

  const bundles = user.school?.bundles || [];

  const {
    data: teacherData,
    error,
    isError,
    isLoading,
  } = useQuery<TeacherDetailsType | null>({
    queryFn: () =>
      SCHOOL_TEACHERS.GET_TEACHER_DETAILS({
        teacherId: teacherDetails.id,
      }),
    queryKey: [QUERY_KEYS.TEACHER_BY_ID, teacherDetails.id],
    staleTime: 1000 * 60 * 60 * 24,
    enabled: Boolean(teacherDetails.id),
  });

  const {
    data: unassignedClassesData,
    isError: isUnassignedClassesError,
    isLoading: isUnassignedClassesLoading,
  } = useQuery<ClassesListFetchListType | null>({
    queryFn: () => SCHOOL_CLASSES.GET_UNASSIGNED_ClASSES_LIST(),
    queryKey: [QUERY_KEYS.UNASSIGNED_CLASSES],
    staleTime: 1000 * 60 * 60 * 24,
    enabled: Boolean(teacherDetails.id),
  });

  const unAssignedClassesList =
    unassignedClassesData?.results.map((c) => ({
      label: c.name,
      value: c.id,
    })) || [];

  const assignedCertificates =
    teacherData?.certificates.map((c) => c.type) || [];

  const updateTeacherClassAccess = useMutation({
    mutationFn: SCHOOL_TEACHERS.UPDATE_CLASS_ACCESS,
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
    onSuccess: (res) => {
      invalidateUser();

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.TEACHER_BY_ID, teacherDetails.id],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_TEACHERS_LIST],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.UNASSIGNED_CLASSES],
      });

      onClose();
    },
  });

  const updateTeacherCertificates = useMutation({
    mutationFn: SCHOOL_TEACHERS.UPDATE_CERTIFICATES,
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
    onSuccess: (res) => {
      updateTeacherClassAccess.mutate({
        teacherId: teacherDetails.id,
        classesIds: selectedClassesIds,
        access: accessLevel,
      });
    },
  });

  const payload = addedCertificates.map((c) => {
    return {
      status: bundles.find((b) => b.type === c)?.status || '',
      type: c,
    };
  });

  return (
    <Card size="2xl" bg="gray50" hasDynamicHeight className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.profileDetails}>
          <Icon name="PersonSvg" color="turquoise" w={56} h={56} />

          <div className={styles.profileName}>
            <Text
              untranslatedText={`${teacherDetails?.firstName || ''} ${teacherDetails?.lastName || ''}`}
              type="h3"
            />

            <Text
              untranslatedText={`${teacherDetails?.email || ''}`}
              type="body2"
              color="gray500"
            />

            {teacherDetails?.affiliation && (
              <Text
                untranslatedText={teacherDetails?.affiliation}
                type="body3"
                fw={800}
              />
            )}
          </div>
        </div>

        <div className={styles.buttonsWrapper}>
          <CloseButton onClick={onClose} variant="outlined" />
        </div>
      </div>

      <div className={styles.content}>
        {isError && error.message === SCHOOLS_ERRORS.TEACHER_NOT_FOUND && (
          <div className={styles.error}>
            <Text transKey="teacher-not-found" type="h4" color="red" />
          </div>
        )}

        {isError && error.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS && (
          <div className={styles.error}>
            <Text
              transKey="unauthorized-access"
              type="h4"
              color="red"
              align="center"
            />
          </div>
        )}

        {!isError && (
          <Grid grow gutter="xl">
            <Grid.Col span={1} mih={289} miw={300}>
              {isLoading ? (
                <Skeleton visible height={285.98} width={281.67} radius="lg" />
              ) : (
                <PersonalInfoCard
                  dateOfBirth={
                    teacherData?.dob
                      ? getFormattedDate(i18n.language, teacherData.dob)
                      : '-'
                  }
                  phoneNumber={teacherData?.phoneNumber || '-'}
                  organisation={teacherData?.organisation || '-'}
                />
              )}
            </Grid.Col>

            <Grid.Col span={1} mih={289} miw={300}>
              {isLoading ? (
                <Skeleton visible height={285.98} width={281.67} radius="lg" />
              ) : (
                <Card
                  size="xl"
                  bg="purple"
                  shadow="none"
                  className="specialBackground"
                >
                  <Text transKey="certifications" type="h3" color="white" />

                  <div className={styles.certificationsList}>
                    {bundles.map((bundle) => {
                      const bundleType =
                        bundle.type as TeacherDetailsType['certificates'][0]['type'];

                      return (
                        <Checkbox
                          key={bundleType}
                          dataLocked={assignedCertificates.includes(bundleType)}
                          isDisabled={
                            !assignedCertificates.includes(bundleType) &&
                            bundle.certificates === 0
                          }
                          label={
                            PRODUCTS[bundle.type as keyof typeof PRODUCTS]
                              .typeName
                          }
                          value={bundleType}
                          isChecked={
                            addedCertificates.includes(bundleType) ||
                            teacherData?.certificates?.some(
                              (certificate) => certificate.type === bundleType
                            ) ||
                            false
                          }
                          onChange={() => {
                            if (addedCertificates.includes(bundleType)) {
                              setAddedCertificates(
                                addedCertificates.filter(
                                  (type) => type !== bundleType
                                )
                              );
                            } else {
                              setAddedCertificates([
                                ...addedCertificates,
                                bundleType,
                              ]);
                            }
                          }}
                          variant="card"
                        />
                      );
                    })}
                  </div>
                </Card>
              )}
            </Grid.Col>
          </Grid>
        )}

        <div className={styles.classesWrapper}>
          <div className={styles.classes}>
            <SelectDropdown
              value={selectedClassesIds || ''}
              isDisabled={
                isUnassignedClassesLoading || isUnassignedClassesError
              }
              data={[
                ...unAssignedClassesList,
                ...(selectedClasses
                  ? selectedClasses.map((selectedClass) => ({
                      label: `${selectedClass.name}`,
                      value: selectedClass.id,
                    }))
                  : []),
              ]}
              onChange={(value: string) => {
                const updatedList = selectedClassesIds.includes(value)
                  ? selectedClassesIds.filter((id) => id !== value)
                  : [...selectedClassesIds, value];

                setSelectedClassesIds(updatedList);
              }}
              placeholder={t('class')}
              type="multi-select"
              allValuesSelectedText={t('all-classes')}
              onSelectAllToggle={(v) => {
                if (selectedClassesIds.length > 0) {
                  setSelectedClassesIds([]);
                } else {
                  setSelectedClassesIds(v);
                }
              }}
              maxContentHeight={400}
            />
          </div>

          <div className={styles.classAccess}>
            <Checkbox
              label={t('access-all-school-students')}
              value="school"
              isChecked={accessLevel === 'school'}
              variant="outlined"
              size="md"
              onChange={() => {
                if (accessLevel === 'school') {
                  setAccessLevel('class');
                } else {
                  setAccessLevel('school');
                }
              }}
            />
          </div>
        </div>

        <div className={styles.actionButtonWrapper}>
          <Button
            transKey="save-capital"
            isLoading={
              updateTeacherCertificates.isPending ||
              updateTeacherClassAccess.isPending
            }
            onClick={() => {
              updateTeacherCertificates.mutate({
                teacherId: teacherDetails.id,
                certificates: payload,
              });
            }}
          />
        </div>
      </div>
    </Card>
  );
};

export default TeacherDetails;
