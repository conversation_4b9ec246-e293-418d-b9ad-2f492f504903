import { Grid, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { GLOBAL_ERRORS, SCHOOLS_ERRORS } from '@/common/errors';
import { getFormattedDate } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import DetailedCertificationsCard from '@/components/DetailedCertificationsCard/DetailedCertificationsCard';
import Icon from '@/components/Icon/Icon';
import LicensesCard from '@/components/LicensesCard/LicensesCard';
import PersonalInfoCard from '@/components/PersonalInfoCard/PersonalInfoCard';
import Text from '@/components/Text/Text';
import SCHOOL_TEACHERS from '@/services/schools/teachers';
import { TeacherDetailsType, TeachersDetailsType } from '@/types/common';

import styles from './TeacherDetails.module.css';

type TeacherDetailsProps = {
  teacherDetails: TeachersDetailsType;
  onClose: () => void;
};

const TeacherDetails = ({ onClose, teacherDetails }: TeacherDetailsProps) => {
  const { i18n } = useTranslation();

  const {
    data: teacherData,
    error,
    isError,
    isLoading,
  } = useQuery<TeacherDetailsType | null>({
    queryFn: () =>
      SCHOOL_TEACHERS.GET_TEACHER_DETAILS({
        teacherId: teacherDetails.id,
      }),
    queryKey: [QUERY_KEYS.TEACHER_BY_ID, teacherDetails.id],
    staleTime: 1000 * 60 * 60 * 24,
    enabled: Boolean(teacherDetails.id),
  });

  return (
    <Card size="2xl" bg="gray50" hasDynamicHeight className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.profileDetails}>
          <Icon name="PersonSvg" color="turquoise" w={56} h={56} />

          <div className={styles.profileName}>
            <Text
              untranslatedText={`${teacherDetails?.firstName || ''} ${teacherDetails?.lastName || ''}`}
              type="h3"
            />

            {teacherDetails?.affiliation && (
              <Text
                untranslatedText={teacherDetails?.affiliation}
                type="body3"
                fw={800}
              />
            )}
          </div>
        </div>

        <div className={styles.buttonsWrapper}>
          <CloseButton onClick={onClose} variant="outlined" />
        </div>
      </div>

      <div className={styles.content}>
        {isError && error.message === SCHOOLS_ERRORS.TEACHER_NOT_FOUND && (
          <div className={styles.error}>
            <Text transKey="teacher-not-found" type="h4" color="red" />
          </div>
        )}

        {isError && error.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS && (
          <div className={styles.error}>
            <Text
              transKey="unauthorized-access"
              type="h4"
              color="red"
              align="center"
            />
          </div>
        )}

        {!isError && (
          <Grid grow gutter="xl">
            <Grid.Col span={1} mih={289} miw={300}>
              {isLoading ? (
                <Skeleton visible height={285.98} width={281.67} radius="lg" />
              ) : (
                <PersonalInfoCard
                  dateOfBirth={
                    teacherData?.dob
                      ? getFormattedDate(i18n.language, teacherData.dob)
                      : '-'
                  }
                  phoneNumber={teacherData?.phoneNumber || '-'}
                  organisation={teacherData?.organisation || '-'}
                />
              )}
            </Grid.Col>

            <Grid.Col span={1} mih={289} miw={300}>
              {isLoading ? (
                <Skeleton visible height={285.98} width={281.67} radius="lg" />
              ) : (
                // TODO : FIX LICENSES TO BE ADDED
                <LicensesCard
                  licenses={[]}
                  headerTransKey="licenses"
                  hasRenewButton={false}
                />
              )}
            </Grid.Col>

            <Grid.Col span={1} mih={289} miw={300}>
              {isLoading ? (
                <Skeleton visible height={285.98} width={281.67} radius="lg" />
              ) : (
                <DetailedCertificationsCard
                  completedCertifications={
                    teacherData?.certificates.map((cert) => cert.type) || []
                  }
                  headerTransKey="certifications"
                  hasEarnMoreButton={false}
                />
              )}
            </Grid.Col>
          </Grid>
        )}

        {teacherDetails?.id && (
          <div className={styles.customerId}>
            <Text transKey="customer-id-capital" type="label" />
            <Text
              untranslatedText={teacherDetails?.id || ''}
              type="body2"
              isBold
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default TeacherDetails;
