.wrapper {
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-top: 5px;
}

.filters {
  max-width: 0;
  width: 100%;
  overflow-x: hidden;
  z-index: 1;
  opacity: 0;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.06);
  clip-path: inset(0 -15px 0 0);
  transition: all 0.4s ease-in-out;

  &.open {
    max-width: 180px;
    margin-right: var(--spacing-xl);
    padding-right: var(--spacing-sm);
    opacity: 1;
  }
}

.searchWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);

  & input {
    max-width: 360px;
  }
}

.filtersAndContainerWrapper {
  display: flex;
  width: 100%;
  height: 100%;
}

.filterButton {
  background-color: transparent;
  border-radius: var(--radius-2xs);
  border: none;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;

  & svg {
    cursor: pointer;
  }

  &:hover {
    opacity: 0.8;
  }
}

.inputRightSection {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.activeButton {
  background-color: var(--color-yellow);
}

.noResultsWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-3xl);
}

.scrollAreaWrapper {
  width: 100%;
  /*
  292px reflects to the occupied spacings from the elements
  above the cards and the table that are being displayed in the school
  Layout. For example the header with the search etc.
  */
  max-height: calc(100vh - 312px);
}

.paginationWrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-2xl);
}
