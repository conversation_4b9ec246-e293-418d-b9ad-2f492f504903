import {
  Indicator,
  ScrollArea,
  SegmentedControl,
  Skeleton,
} from '@mantine/core';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BiTable } from 'react-icons/bi';
import { CiBoxList } from 'react-icons/ci';
import { FaChalkboardTeacher } from 'react-icons/fa';
import { SiGoogleclassroom } from 'react-icons/si';

import Icon from '@/components/Icon/Icon';
import Input from '@/components/Inputs/Input/Input';
import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import ActionButton from '../../../../components/ActionButton/ActionButton';
import { ShoolTabsType } from '../../types';
import ZeroListData from '../ZeroListData/ZeroListData';
import styles from './SchoolLayout.module.css';

type SchoolLayoutProps = {
  type: ShoolTabsType;
  searchInputTransKey: TranslationKeysType;
  isInformationLoading?: boolean;
  filters?: React.ReactNode;
  actionsContent?: React.ReactNode;
  cardsContent?: React.ReactNode;
  listContent?: React.ReactNode;
  numberOFSelectedFilters?: number;
  onClearFilters?: () => void;
  searchWord: string;
  onSearch: (value: string) => void;
  numberOfSearchResults: number | null;
  isNoDataListDisplayed: boolean;
  paginationComponent?: React.ReactNode;
  isNoResultsVisible?: boolean;
};

type ContentDisplayType = 'cards' | 'list';

const TABS = [
  {
    value: 'list',
    label: <BiTable size={20} />,
  },
  {
    value: 'cards',
    label: <CiBoxList size={20} />,
  },
];

const SEARCH_BAR_ICONS: Record<ShoolTabsType, React.ReactNode> = {
  students: <Icon name="StudentSvg" color="black" size="sm" />,
  teachers: <FaChalkboardTeacher size={18} />,
  classes: <SiGoogleclassroom size={18} />,
  groups: <SiGoogleclassroom size={18} />,
};

const SchoolLayout = ({
  actionsContent,
  cardsContent,
  filters,
  isInformationLoading = false,
  isNoDataListDisplayed,
  isNoResultsVisible = false,
  listContent,
  numberOfSearchResults,
  numberOFSelectedFilters = 0,
  onClearFilters,
  onSearch,
  paginationComponent,
  searchInputTransKey,
  searchWord,
  type,
}: SchoolLayoutProps) => {
  const { t } = useTranslation();
  const [contentDisplayStyle, setContentDisplayStyle] =
    useState<ContentDisplayType>('list');
  const [areFiltersVisible, setAreFiltersVisible] = useState(false);

  return isNoDataListDisplayed ? (
    <ZeroListData type={type} />
  ) : (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.searchWrapper}>
          {filters && (
            <Indicator
              variant="filters"
              disabled={numberOFSelectedFilters === 0 || areFiltersVisible}
              inline
              label={numberOFSelectedFilters}
              size={20}
              offset={7}
              withBorder
            >
              <button
                type="button"
                className={`${styles.filterButton} ${areFiltersVisible && styles.activeButton}`}
                onClick={() => setAreFiltersVisible(!areFiltersVisible)}
              >
                <Icon
                  name="FiltersSvg"
                  color={areFiltersVisible ? 'white' : 'black'}
                />
              </button>
            </Indicator>
          )}

          <Input
            value={searchWord}
            variant="filled"
            placeholder={t(searchInputTransKey)}
            onChange={(value) => onSearch(value)}
            rightSectionWidth={80}
            rightSection={
              isInformationLoading ? (
                <Skeleton />
              ) : (
                <div className={styles.inputRightSection}>
                  {SEARCH_BAR_ICONS[type]}
                  <Text
                    untranslatedText={`${numberOfSearchResults || '0'}`}
                    type="body2"
                  />
                </div>
              )
            }
          />

          {numberOFSelectedFilters > 0 && (
            <ActionButton
              type="clearFilters"
              onClick={() => {
                setAreFiltersVisible(false);
                setTimeout(() => {
                  onClearFilters?.();
                }, 400);
              }}
            />
          )}
        </div>

        {/* TODO REMOVE INLINE STYLING */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
          }}
        >
          {actionsContent}

          <SegmentedControl
            variant="icons"
            value={contentDisplayStyle}
            onChange={(v) => {
              setContentDisplayStyle(v as ContentDisplayType);
            }}
            withItemsBorders={false}
            data={TABS.map((item) => ({
              value: item.value,
              label: item.label,
            }))}
          />
        </div>
      </div>

      <div className={styles.filtersAndContainerWrapper}>
        {/* SIDE BAR FILTERS */}
        <div
          className={`${styles.filters} ${areFiltersVisible && styles.open}`}
        >
          {filters}
        </div>

        {/* SCROLLABLE CONTENT AT THE RIGHT SIDE OF THE FILTERS */}
        {!isNoResultsVisible && (
          <ScrollArea.Autosize
            type="never"
            scrollbarSize={4}
            className={styles.scrollAreaWrapper}
          >
            {contentDisplayStyle === 'cards' ? cardsContent : listContent}

            <div className={styles.paginationWrapper}>
              {paginationComponent}
            </div>
          </ScrollArea.Autosize>
        )}

        {isNoResultsVisible && (
          <div className={styles.noResultsWrapper}>
            <Text
              transKey="no-search-results"
              type="h4"
              color="gray"
              fw={400}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default SchoolLayout;
