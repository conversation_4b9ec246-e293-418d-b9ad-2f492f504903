import { Box, Menu } from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';
import { RiEditFill } from 'react-icons/ri';

import Icon from '@/components/Icon/Icon';

type StudentActionsMenuProps = {
  studentId: string;
  onOpenMenu: () => void;
  onEditStudent: () => void;
};

const StudentActionsMenu = ({
  onEditStudent,
  onOpenMenu,
  studentId,
}: StudentActionsMenuProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Menu key={studentId} shadow="md" onOpen={onOpenMenu}>
      <Menu.Target>
        <Box>
          <Icon name="MenuDotsSvg" color="turquoise" w={18} h={18} hasCursor />
        </Box>
      </Menu.Target>

      <Menu.Dropdown className="menuDropdown">
        <Menu.Item
          onClick={onEditStudent}
          leftSection={<RiEditFill size={16} />}
        >
          {t('edit')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default StudentActionsMenu;
