import { TextInput } from '@mantine/core';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';

import { ExtrasType } from '../types';
import s from './ExtraStudentInfo.module.css';

type ExtraStudentInfoProps = {
  extras: ExtrasType;
  onUpdateExtras: (key: keyof ExtrasType, value: string) => void;
};

// const METRIC_SCORE = [
//   'poor',
//   'below-average',
//   'average',
//   'above-average',
//   'excellent',
// ];

const TEST_DEVICES = ['computer', 'tablet'];

const ExtraStudentInfo = ({
  extras,
  onUpdateExtras,
}: ExtraStudentInfoProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Card className={s.wrapper} size="xl">
      <Text transKey="extras-info" mb={12} />

      <div className={s.extrasWrapper}>
        <SelectDropdown
          value={extras.schoolType || ''}
          label={t('school-type')}
          data={['public', 'private', 'church'].map((item) => ({
            value: item,
            label: t(item),
          }))}
          onChange={(v) => {
            onUpdateExtras('schoolType', v);
          }}
          placeholder={t('public-placeholder')}
        />

        <SelectDropdown
          value={extras.area || ''}
          label={t('area')}
          data={['urban', 'semiurban', 'rural'].map((item) => ({
            value: item,
            label: t(item),
          }))}
          onChange={(v) => {
            onUpdateExtras('area', v);
          }}
          placeholder={t('urban-placeholder')}
        />

        <div className={s.groupedItems}>
          <TextInput
            w="100%"
            value={extras.firstLanguage}
            label={t('first-language')}
            placeholder={t('first-language-placeholder')}
            onChange={(e) => onUpdateExtras('firstLanguage', e.target.value)}
          />

          <TextInput
            w="100%"
            value={extras.curriculum}
            label={t('curriculum-type-level')}
            // placeholder={t('curriculum-type-level-placeholder')}
            onChange={(e) => onUpdateExtras('curriculum', e.target.value)}
          />
        </div>

        {/* <TextInput
          value={extras.SES}
          label={t('socio-economic')}
          placeholder={t('socio-economic-placeholder')}
          onChange={(e) => onUpdateExtras('SES', e.target.value)}
        /> */}

        <TextInput
          value={extras.neurodiversity}
          label={t('neurodiversity')}
          placeholder={t('neurodiversity-placeholder')}
          onChange={(e) => onUpdateExtras('neurodiversity', e.target.value)}
        />

        <div className={s.groupedItems}>
          <TextInput
            w="100%"
            value={extras.mathsTeachersScore}
            label={t('maths-teachers-score')}
            // placeholder={t('maths-teachers-score-placeholder')}
            onChange={(e) =>
              onUpdateExtras('mathsTeachersScore', e.target.value)
            }
          />

          {/* <SelectDropdown
            value={extras.mathsTeachersScore || ''}
            label={t('maths-teachers-score')}
            data={METRIC_SCORE.map((item) => ({
              value: item,
              label: t(item),
            }))}
            onChange={(v) => {
              onUpdateExtras('mathsTeachersScore', v);
            }}
            placeholder={t('maths-teachers-score-placeholder')}
          /> */}

          <TextInput
            w="100%"
            value={extras.literatureScore}
            label={t('literature-teachers-score')}
            // placeholder={t('literature-teachers-score-placeholder')}
            onChange={(e) => onUpdateExtras('literatureScore', e.target.value)}
          />

          {/* <SelectDropdown
            value={extras.literatureScore || ''}
            label={t('literature-teachers-score')}
            data={METRIC_SCORE.map((item) => ({
              value: item,
              label: t(item),
            }))}
            onChange={(v) => {
              onUpdateExtras('literatureScore', v);
            }}
            placeholder={t('literature-teachers-score-placeholder')}
          /> */}
        </div>

        <div className={s.groupedItems}>
          <TextInput
            w="100%"
            value={extras.mathsScore}
            label={t('maths-score')}
            placeholder={t('maths-score-placeholder')}
            onChange={(e) => onUpdateExtras('mathsScore', e.target.value)}
          />

          <SelectDropdown
            value={extras.testDevice || ''}
            label={t('test-device')}
            data={TEST_DEVICES.map((item) => ({
              value: item,
              label: t(item),
            }))}
            onChange={(v) => {
              onUpdateExtras('testDevice', v);
            }}
            placeholder={t('test-device-placeholder')}
          />
        </div>
      </div>
    </Card>
  );
};

export default ExtraStudentInfo;
