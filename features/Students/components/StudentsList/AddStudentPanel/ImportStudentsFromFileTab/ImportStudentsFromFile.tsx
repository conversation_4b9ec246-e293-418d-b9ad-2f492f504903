import { ActionIcon, Divider, LoadingOverlay } from '@mantine/core';
import { Dropzone, MIME_TYPES } from '@mantine/dropzone';
import Image from 'next/image';
import { JSX, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FaFileAlt } from 'react-icons/fa';

import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { DateFormatType } from '@/types/common';

import FileLoadingModal from './FileLoadingModal';
import s from './ImportStudentsFromFile.module.css';
import useUploadFile from './useUploadFile';

const ImportStudentsFromFile = ({
  onCloseModal,
}: {
  onCloseModal: () => void;
}): JSX.Element => {
  const dropZoneRef = useRef<() => void>(null);

  const { t } = useTranslation();

  const {
    closeLoadingModal,
    dateFormat,
    downloadTemplate,
    fileImportError,
    fileToUpload,
    handleFileImport,
    handleFileImportError,
    handleFileUpload,
    isFileUploadError,
    isLoadingModalVisible,
    progressData,
    updateDateFormat,
  } = useUploadFile();

  return (
    <div className={s.wrapper}>
      <div className={s.cardsWrapper}>
        <div
          className={`${s.stepWrapper} ${isLoadingModalVisible && s.isLoading}`}
        >
          <Card size="xl" className={s.cardWrapper} radius="none">
            <Text transKey="step-one" type="body2" fw={700} />

            <Text transKey="download-template" type="h4" />

            <div className={s.iconWrapper}>
              <ActionIcon
                name="DownloadSvg"
                className={s.downloadIcon}
                onClick={() => downloadTemplate.mutate()}
                variant="transparent"
                size={100}
                disabled={downloadTemplate.isPending || isLoadingModalVisible}
              >
                <LoadingOverlay
                  visible={downloadTemplate.isPending}
                  zIndex={1000}
                  overlayProps={{ radius: 'sm', blur: 2 }}
                  loaderProps={{
                    type: 'bars',
                    color: 'var(--color-blue)',
                    size: 'xs',
                    radius: 'sm',
                  }}
                />
                <div className={s.downloadIconWrapper}>
                  <Icon name="FileDownloadSvg" color="blue" />

                  <Text
                    transKey="download-capital"
                    type="button"
                    color="blue"
                  />
                </div>
              </ActionIcon>
            </div>
          </Card>
        </div>

        <div
          className={`${s.stepWrapper} ${isLoadingModalVisible && s.isLoading}`}
        >
          <Card size="xl" className={s.cardWrapper} radius="none">
            <Text transKey="step-two" type="body2" fw={700} />

            <Text transKey="edit-template" type="h4" />

            <div className={s.descriptionWrapper}>
              <div className={s.descriptionItem}>
                <div className={s.numberItem}>
                  <Text untranslatedText="1" type="button" color="blue" />
                </div>

                <Text transKey="edit-template-instructions1" type="body2" />
              </div>

              <Divider variant="dashed" w="100%" />

              <div className={s.descriptionItem}>
                <div className={s.numberItem}>
                  <Text untranslatedText="2" type="button" color="blue" />
                </div>

                <Text transKey="edit-template-instructions2" type="body2" />
              </div>

              <Divider variant="dashed" w="100%" />

              <div className={s.descriptionItem}>
                <div className={s.numberItem}>
                  <Text untranslatedText="3" type="button" color="blue" />
                </div>

                <Text transKey="edit-template-instructions3" type="body2" />
              </div>

              <div className={s.instructionsWrapper}>
                <div className={s.instructionContainer}>
                  <Icon name="ExcelIconSvg" className={s.icon} />

                  <div className={s.instructionDetails}>
                    <Text untranslatedText="MS Excel" type="body3" fw={700} />
                    <Text
                      transKey="file-extract-instructions"
                      type="body2"
                      fw={400}
                    />
                  </div>
                </div>

                <div className={s.instructionContainer}>
                  <Image
                    src="/images/macNumbers.svg"
                    width={31}
                    height={26}
                    alt="word icon"
                    className={s.icon}
                  />

                  <div className={s.instructionDetails}>
                    <Text
                      untranslatedText="Mac Numbers"
                      type="body3"
                      fw={700}
                    />
                    <Text
                      transKey="file-extract-instructions-numbers"
                      type="body2"
                      fw={400}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        <div
          className={`${s.stepWrapper} ${isLoadingModalVisible && s.isLoading}`}
        >
          <Card size="xl" className={s.cardWrapper} radius="none">
            <Text transKey="step-three" type="body2" fw={700} />

            <Text transKey="import-file" type="h4" mb={12} />

            <Dropzone
              onDrop={(file) => {
                handleFileImport(file[0]);
              }}
              onReject={(file) => {
                handleFileImportError(
                  file[0].errors[0].code === 'file-too-large'
                    ? 'file-too-large'
                    : 'file-invalid-type'
                );
              }}
              // max size 25 mb
              maxSize={25 * 1024 ** 2}
              className={s.dropzone}
              accept={[MIME_TYPES.xlsx, MIME_TYPES.xls]}
              disabled={isLoadingModalVisible}
              openRef={dropZoneRef}
              onChange={() => {}}
              activateOnClick
              useFsAccessApi
            >
              {fileImportError === 'file-invalid-type' && (
                <>
                  <Text
                    transKey="unsupported-file-type"
                    align="center"
                    color="gray"
                    type="body1"
                    mb={12}
                  />

                  <Text
                    transKey="supported-file-types"
                    align="center"
                    color="gray"
                    type="body2"
                  />
                </>
              )}

              {fileImportError === 'file-too-large' && (
                <>
                  <Text
                    transKey="file-too-large"
                    align="center"
                    color="gray"
                    type="body1"
                    mb={12}
                  />

                  <Text
                    transKey="max-file-size-25mb"
                    align="center"
                    color="gray"
                    type="body2"
                  />
                </>
              )}

              {fileToUpload && (
                <div className={s.iconWrapper}>
                  <FaFileAlt size={50} color="var(--color-blue)" />

                  <Text
                    untranslatedText={fileToUpload.name}
                    type="label"
                    fw={500}
                    mt={6}
                  />
                </div>
              )}

              {!fileToUpload && !fileImportError && (
                <>
                  <Text
                    transKey="click-capital"
                    type="button"
                    color="blue"
                    align="center"
                  />
                  <Text
                    transKey="or-capital"
                    type="button"
                    color="blue"
                    align="center"
                  />
                  <Text
                    transKey="drag-file-here-capital"
                    type="button"
                    color="blue"
                    align="center"
                  />
                </>
              )}
            </Dropzone>

            <SelectDropdown
              placeholder="Select file"
              isDisabled={!fileToUpload}
              value={dateFormat}
              onChange={(value) => {
                updateDateFormat(value as DateFormatType);
              }}
              data={[
                {
                  label: t('european-date-format'),
                  value: 'eu',
                },
                {
                  label: t('american-date-format'),
                  value: 'us',
                },
              ]}
            />
          </Card>
        </div>
      </div>

      <div className={s.action}>
        <Button
          transKey="upload-capital"
          isDisabled={fileToUpload === null}
          onClick={handleFileUpload}
        />
      </div>

      <FileLoadingModal
        isVisible={isLoadingModalVisible}
        closeLoadingModal={closeLoadingModal}
        onCloseModal={onCloseModal}
        progressData={progressData}
        isFileUploadError={isFileUploadError}
      />
    </div>
  );
};

export default ImportStudentsFromFile;
