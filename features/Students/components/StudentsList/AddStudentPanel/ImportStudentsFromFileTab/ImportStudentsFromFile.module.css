.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.cardsWrapper {
  display: flex;
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-mdl);
}

.stepWrapper {
  width: 100%;
  overflow: hidden;
  border: none;
  border-radius: var(--radius-sm);
}

.cardWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.action {
  align-self: flex-end;
}

.iconWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  & svg {
    cursor: pointer;
  }
}

.downloadIcon {
  color: var(--color-blue);
}

.downloadIconWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  gap: var(--spacing-smd);
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  gap: var(--spacing-mdl);
  margin-top: var(--spacing-lg);
}

.isLoading {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  opacity: 0.5;
}

.dropzone {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  border: 2px dashed var(--color-blue);
  border-radius: var(--radius-sm);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05);

  margin-top: var(--spacing-lg);

  & svg {
    fill: var(--color-blue);
  }
}

.modalWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-md);
  height: fit-content;
  min-height: 300px;
  transition: all 0.3s ease-in-out;
}

.modalCloseButton {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  width: 100%;
}

.modalFooter {
  width: 100%;
}

.parseErrorWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.descriptionItem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.numberItem {
  margin-right: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 25px;
  min-height: 25px;
  border-radius: 50%;
  border: 1px solid var(--color-blue);
}

.instructionsWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin: auto;
  padding: 0 var(--spacing-lg);
}

.instructionContainer {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  width: fit-content;
}

.instructionDetails {
  display: flex;
  flex-direction: column;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.body {
  background-color: var(--color-bg3);
  min-height: 350px;
}

.uploadFailWrapper {
  width: 100%;
  margin-bottom: var(--spacing-2xl);
  margin-top: var(--spacing-lg);
}

.uploadFailHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-smd);
  width: 100%;
  margin-bottom: var(--spacing-2xl);
}

.completedFooter {
  position: absolute;
  right: var(--spacing-lg);
  bottom: var(--spacing-lg);
}
