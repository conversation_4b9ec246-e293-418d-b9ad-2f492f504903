/* eslint-disable react-hooks/exhaustive-deps */
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import { UserContext } from '@/context/UserProvider';
import DATA from '@/services/data';
import { UploadFileProgressType } from '@/types/common';

const useUploadFile = () => {
  const { t } = useTranslation();

  const { isSchoolRole } = UserContext();

  const queryClient = useQueryClient();

  const [fileToUpload, setFileToUpload] = useState<File | null>(null);

  const [shouldGetProgress, setShouldGetProgress] = useState<boolean>(false);

  const [fileImportError, setFileImportError] = useState<
    'file-invalid-type' | 'file-too-large' | null
  >(null);

  const [isLoadingModalVisible, setIsLoadingModalVisible] =
    useState<boolean>(false);

  const [fileName, setFileName] = useState<string>('');

  const downloadTemplate = useMutation({
    mutationFn: () => DATA.GET_DOWNLOAD_TEMPLATE({ type: 'students' }),
    onSuccess: (response) => {
      if (response) {
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'students_list.xlsx');
        document.body.appendChild(link);
        link.click();
      }
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const uploadFile = useMutation({
    mutationFn: DATA.UPLOAD_FILE,
    onSuccess: () => {
      setShouldGetProgress(true);
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
      setIsLoadingModalVisible(false);
    },
  });

  const getUploadUrl = useMutation({
    mutationFn: DATA.GET_PRESIGNED_URL,
    onSuccess: (response) => {
      if (response) {
        setFileName(response.filename || '');

        uploadFile.mutate({
          preSignedUrl: response.url,
          file: fileToUpload!,
          fileNameFromBe: response.filename || '',
        });
      }
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
      setIsLoadingModalVisible(false);
    },
  });

  const { data: progressData, isError: isFileUploadError } = useQuery<
    UploadFileProgressType | null | undefined
  >({
    enabled: shouldGetProgress && Boolean(fileName),
    queryFn: () => DATA.GET_UPLOAD_PROGRESS({ fileName }),
    queryKey: [QUERY_KEYS.UPLOAD_PROGRESS_DURING_THE_CREATION_OF_STUDENTS],
    refetchInterval: shouldGetProgress && Boolean(fileName) ? 1000 : false,
  });

  const closeLoadingModal = () => {
    setIsLoadingModalVisible(false);
    setFileName('');
    setFileToUpload(null);
    setShouldGetProgress(false);

    queryClient.removeQueries({
      queryKey: [QUERY_KEYS.UPLOAD_PROGRESS_DURING_THE_CREATION_OF_STUDENTS],
    });
  };

  useEffect(() => {
    if (
      progressData &&
      (progressData.state === 'completed' || progressData.state === 'failed')
    ) {
      setShouldGetProgress(false);

      if (progressData.state === 'completed') {
        if (!isSchoolRole) {
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST],
          });

          queryClient.invalidateQueries({
            queryKey: [QUERY_KEYS.GROUPS_LIST],
          });
        } else {
          queryClient.invalidateQueries({
            queryKey: [QUERY_KEYS.SCHOOL_STUDENT_LIST],
          });

          queryClient.invalidateQueries({
            queryKey: [QUERY_KEYS.CLASSES_LIST],
          });
        }
      }
    }

    if (isFileUploadError) {
      setShouldGetProgress(false);
      setIsLoadingModalVisible(false);
      setFileName('');
      notifications.show({
        message: t('upload-failed'),
        color: 'red',
      });
    }

    return () => {
      if (isFileUploadError) closeLoadingModal();
    };
  }, [progressData, isFileUploadError]);

  const handleFileImport = (file: File) => {
    if (fileImportError) {
      setFileImportError(null);
    }

    setFileToUpload(file);
  };

  const handleFileUpload = () => {
    if (fileToUpload) {
      setIsLoadingModalVisible(true);

      getUploadUrl.mutate();
    }
  };

  const handleFileImportError = (
    type: 'file-too-large' | 'file-invalid-type'
  ) => {
    setFileImportError(type);
  };

  return {
    fileToUpload,
    isLoadingModalVisible,
    downloadTemplate,
    handleFileImport,
    fileImportError,
    handleFileUpload,
    progressData,
    handleFileImportError,
    closeLoadingModal,
    isFileUploadError,
  };
};

export default useUploadFile;
