import { Center, Divider, Modal, RingProgress } from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { FaCheck } from 'react-icons/fa';
import { MdErrorOutline } from 'react-icons/md';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { TranslationKeysType, UploadFileProgressType } from '@/types/common';

import s from './ImportStudentsFromFile.module.css';

type FileLoadingModalProps = {
  isVisible: boolean;
  closeLoadingModal: () => void;
  progressData: UploadFileProgressType | undefined | null;
  isFileUploadError: boolean;
  onCloseModal: () => void;
};

const NUMBER_TO_LETTERS = {
  0: 'A',
  1: 'B',
  2: 'C',
  3: 'D',
  4: 'E',
  5: 'F',
  6: 'G',
} as const;

const PARSE_ERRORS_TRANS_KEYS = {
  StudentCodeDuplicate: 'student-code-duplicate',
  PersonalDataMissing: 'personal-data-missing',
  BirthDateInvalid: 'birth-date-invalid',
  GradeInvalid: 'grade-invalid',
  GenderInvalid: 'gender-invalid',
  DataError: 'data-error',
  ClassMissing: 'class-missing',
} as const;

const FileLoadingModal = ({
  closeLoadingModal,
  isFileUploadError,
  isVisible,
  onCloseModal,
  progressData,
}: FileLoadingModalProps) => {
  const { t } = useTranslation();

  const isUploadedSuccessfully =
    progressData?.progress === 100 && progressData?.state === 'completed';

  const hasParseError =
    progressData &&
    progressData.parseError &&
    progressData.parseError?.message &&
    progressData.parseError?.row;

  return (
    <Modal
      opened={isVisible}
      onClose={closeLoadingModal}
      withCloseButton={false}
      centered
      closeOnClickOutside={false}
      size="xl"
      radius={14}
      classNames={{
        body: s.body,
      }}
      autoFocus={false}
      trapFocus
    >
      <div className={s.modalWrapper}>
        {(!progressData || (progressData && progressData.state !== 'failed')) &&
          !isFileUploadError && (
            <RingProgress
              transitionDuration={250}
              rootColor="var(--color-gray)"
              sections={[
                {
                  value: isUploadedSuccessfully
                    ? 100
                    : progressData?.progress || 0,
                  color: isUploadedSuccessfully
                    ? 'var(--color-green)'
                    : 'var(--color-turquoise)',
                },
              ]}
              label={
                isUploadedSuccessfully ? (
                  <Center>
                    <FaCheck style={{ width: 32, height: 32 }} color="green" />
                  </Center>
                ) : (
                  <Text
                    untranslatedText={`${progressData?.progress || 0}%`}
                    align="center"
                    type="body1"
                    isBold
                    color="turquoise"
                  />
                )
              }
            />
          )}

        {(!progressData ||
          (progressData?.state !== 'completed' &&
            progressData?.state !== 'failed')) &&
          !isFileUploadError && (
            <Text
              transKey="uploading-do-not-refresh-page"
              type="body1"
              color="turquoise"
              fw={700}
              align="center"
              mt={48}
            />
          )}

        {progressData && isUploadedSuccessfully && (
          <Text
            transKey="file-uploaded-successfully"
            type="body1"
            color="green"
            fw={700}
            align="center"
            mt={48}
          />
        )}

        {isFileUploadError ||
          (progressData?.state === 'failed' && (
            <div className={s.uploadFailWrapper}>
              <div className={s.uploadFailHeader}>
                <MdErrorOutline color="var(--color-danger)" size={34} />

                <Text
                  transKey="file-upload-error"
                  type="h3"
                  color="danger"
                  fw={300}
                />
              </div>

              <Text
                transKey="please-check-your-file"
                type="h4"
                fw={300}
                mb={48}
              />

              {hasParseError ? (
                <div className={s.parseErrorWrapper}>
                  {progressData?.parseError?.row && (
                    <Text
                      transKey="in-row"
                      type="h4"
                      color="danger"
                      fw={700}
                      transVariables={{
                        row: `${progressData?.parseError?.row}`,
                        name:
                          progressData?.parseError?.data[0] ||
                          progressData?.parseError?.data[1]
                            ? `${progressData?.parseError?.data[0] || ''} ${progressData?.parseError?.data[1] || ''}`
                            : t('anonymous'),
                      }}
                      mr={4}
                    />
                  )}
                  {progressData?.parseError?.column ? (
                    <Text
                      transKey="in-column"
                      type="h4"
                      color="danger"
                      fw={700}
                      transVariables={{
                        column: `${NUMBER_TO_LETTERS[Number(progressData?.parseError?.column) as keyof typeof NUMBER_TO_LETTERS]}`,
                        faultyData: `${progressData?.parseError?.data[Number(progressData?.parseError?.column)] || ''}`,
                        cause: `${t(PARSE_ERRORS_TRANS_KEYS[(progressData?.parseError?.cause as keyof typeof PARSE_ERRORS_TRANS_KEYS) || 'DataError'] as TranslationKeysType)}`,
                      }}
                      mr={4}
                    />
                  ) : (
                    <Text
                      transKey={
                        PARSE_ERRORS_TRANS_KEYS[
                          (progressData?.parseError
                            ?.cause as keyof typeof PARSE_ERRORS_TRANS_KEYS) ||
                            'DataError'
                        ] as TranslationKeysType
                      }
                      type="h4"
                      color="danger"
                      fw={700}
                      mr={4}
                    />
                  )}
                </div>
              ) : (
                <Text
                  untranslatedText={
                    progressData?.failReason ? progressData?.failReason : ' '
                  }
                  type="body2"
                  color="danger"
                  fw={500}
                  align="center"
                />
              )}
            </div>
          ))}

        {(isFileUploadError ||
          progressData?.state === 'completed' ||
          progressData?.state === 'failed') && (
          <div
            className={`${s.modalFooter} ${progressData?.state === 'completed' && s.completedFooter}`}
          >
            {progressData?.state === 'failed' && (
              <Divider variant="dashed" mb={30} w="100%" />
            )}

            <div className={s.modalCloseButton}>
              <Button
                onClick={
                  progressData?.state === 'completed'
                    ? () => {
                        onCloseModal();
                        closeLoadingModal();
                      }
                    : closeLoadingModal
                }
                transKey="ok-capital"
              />
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default FileLoadingModal;
