.wrapper {
  /* design height -  769px */
  max-width: 1120px;
  height: 904px;
  width: 100%;
  transition: all 0.4s ease;
  margin: 0 var(--spacing-md);
}

.importFromFileWrapper {
  max-width: 1320px;
  height: fit-content;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.segmentedWrapper {
  transform: translateX(-20px);
}

.segmentedIndicator {
  display: flex;
}

.textWrapper {
  max-width: 260px;
  margin-right: var(--spacing-xl);
}

/* .addStudentWrapper {
  max-width: 800px;
} */
