.wrapper {
  /* design height -  769px */
  max-width: 1120px;
  height: 904px;
  width: 100%;
  transition: all 0.4s ease;
  margin: 0 var(--spacing-md);
}

.importFromFileWrapper {
  max-width: 1320px;
  height: fit-content;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.segmentedIndicator {
  display: flex;
}

.textWrapper {
  width: fit-content;
  max-width: 260px;
  margin-right: var(--spacing-sm);
}

/* .addStudentWrapper {
  max-width: 800px;
} */

.closeButton {
  padding-left: var(--spacing-4xl);
}
