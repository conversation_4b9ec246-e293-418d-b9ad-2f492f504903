/* eslint-disable no-lone-blocks */
import { Box } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_STUDENTS from '@/services/schools/students';
import INDEPENDENT_TEACHER_STUDENTS from '@/services/students/students';
import { ArbitrarySkillType } from '@/types/common';

import ExtraStudentInfo from '../../ExtraStudentInfo/ExtraStudentInfo';
import ManageArbitrarySkills from '../../ManageArbitrarySkills/ManageArbitrarySkills';
import StudentForm from '../../StudentForm/StudentForm';
import { ExtrasType, StudentFormValuesType } from '../../types';
import s from './AddStudentTab.module.css';

type AddNewStudentTabPropsType = {
  onCloseModal: () => void;
  defaultStudentsSkills: ArbitrarySkillType[];
  defaultStudentValues: StudentFormValuesType;
};

const DEFAULT_EXTRAS: ExtrasType = {
  schoolType: null,
  area: null,
  firstLanguage: '',
  // SES: '',
  neurodiversity: '',
  mathsTeachersScore: '',
  mathsScore: '',
  literatureScore: '',
  testDevice: null,
  curriculum: '',
};

const AddStudentTab = ({
  defaultStudentsSkills,
  defaultStudentValues,
  onCloseModal,
}: AddNewStudentTabPropsType): JSX.Element => {
  const { t } = useTranslation();
  const { user, userRoles } = UserContext();
  const queryClient = useQueryClient();
  const [actionClicked, setActionClicked] = useState<
    null | 'actionAndClose' | 'actionAndKeepGoing'
  >(null);

  const [arbitrarySkills, setArbitrarySkills] = useState<ArbitrarySkillType[]>(
    defaultStudentsSkills
  );

  const [studentExtraDetails, setStudentExtraDetails] =
    useState<ExtrasType>(DEFAULT_EXTRAS);

  const onUpdateExtras = (item: keyof ExtrasType, value: string) => {
    setStudentExtraDetails((prev) => ({
      ...prev,
      [item]: value,
    }));
  };

  const addStudentMutation = useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: (data: { studentPayload: any; resetForm: () => void }) => {
      return userRoles.isSchoolAdmin
        ? SCHOOL_STUDENTS.CREATE_NEW_SCHOOL_STUDENT(data.studentPayload)
        : INDEPENDENT_TEACHER_STUDENTS.CREATE_NEW_STUDENT(data.studentPayload);
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
    onSuccess: (res, variables) => {
      if (res) {
        queryClient.invalidateQueries({
          queryKey: [
            userRoles.isSchoolAdmin
              ? QUERY_KEYS.SCHOOL_STUDENT_LIST
              : QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST,
          ],
        });

        if (actionClicked === 'actionAndClose') {
          onCloseModal();
        } else {
          variables.resetForm();
        }

        setArbitrarySkills(defaultStudentsSkills);
        setStudentExtraDetails(DEFAULT_EXTRAS);
        setActionClicked(null);

        notifications.show({
          title: t('student-added-successfully'),
          message: '',
          color: 'green',
        });
      }
    },
  });

  const onSubmitStudentForm = (
    formValues: StudentFormValuesType,
    resetForm: () => void
  ) => {
    const { code, firstName, gender, grade, isAnonymous, lastName } =
      formValues;

    const areRequiredFieldsValid =
      (!isAnonymous ? Boolean(firstName?.trim()) : true) &&
      (!isAnonymous ? Boolean(lastName?.trim()) : true) &&
      (isAnonymous ? Boolean(code?.trim()) : true) &&
      Boolean(grade) &&
      Boolean(gender);

    if (!areRequiredFieldsValid) return;

    // clear the arbitrary skills from the isCustom flag and default values
    const clearedArbitrarySkills = userRoles.isResearcher
      ? []
      : arbitrarySkills?.map((skill) => {
          const clearedSubskills = skill.subskills
            .map((subskill) => {
              return {
                ...(subskill.default ? {} : { default: subskill.default }),
                name: subskill.name,
                score: subskill.score,
              };
            })
            .filter((subskill) => subskill.name !== t('new-skill'));

          //   // from the clearedSubSkills remove duplicate entries where the name is the same
          const clearedSubskillsMap = new Map();
          clearedSubskills.forEach((subskill) => {
            clearedSubskillsMap.set(subskill.name, subskill);
          });

          const clearedSubskillsArray = Array.from(
            clearedSubskillsMap.values()
          );

          return {
            default: skill.default,
            name: skill.name,
            score: skill.score,
            subskills: clearedSubskillsArray,
          };
        });

    const studentPayload = {
      grade: formValues.grade || '1',
      dateOfBirth: formValues.dateOfBirth,
      gender: formValues.gender || 'other',
      anonymous: formValues.isAnonymous,
      ...(formValues.firstName && { firstName }),
      ...(formValues.lastName && { lastName }),
      ...(formValues.code && { code: formValues.code }),
      ...(formValues.schoolName && { schoolName: formValues.schoolName }),
      ...(formValues.residence && { residence: formValues.residence }),
      ...(userRoles.isResearcher && {
        extras: {
          ...(studentExtraDetails.schoolType && {
            schoolType: studentExtraDetails.schoolType,
          }),
          ...(studentExtraDetails.area && {
            area: studentExtraDetails.area,
          }),
          ...(studentExtraDetails.firstLanguage && {
            firstLanguage: studentExtraDetails.firstLanguage,
          }),
          ...(studentExtraDetails.neurodiversity && {
            neurodiversity: studentExtraDetails.neurodiversity,
          }),
          ...(studentExtraDetails.mathsTeachersScore && {
            mathsTeachersScore: studentExtraDetails.mathsTeachersScore,
          }),
          ...(studentExtraDetails.mathsScore && {
            mathsScore: studentExtraDetails.mathsScore,
          }),
          ...(studentExtraDetails.literatureScore && {
            literatureScore: studentExtraDetails.literatureScore,
          }),
          ...(studentExtraDetails.curriculum && {
            curriculum: studentExtraDetails.curriculum,
          }),
          ...(studentExtraDetails.testDevice && {
            testDevice: studentExtraDetails.testDevice,
          }),
        },
      }),
      ...(!userRoles.isResearcher && { skills: clearedArbitrarySkills }),

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any;

    addStudentMutation.mutate({
      studentPayload,
      resetForm,
    });
  };

  return (
    <Box className={s.wrapper} key={user.id}>
      <Box className={s.cardsWrapper}>
        <StudentForm
          onSubmitStudentForm={(values, resetForm) =>
            onSubmitStudentForm(values, resetForm)
          }
          defaultValues={defaultStudentValues}
          isShoolAdminOrSchoolTeacher={
            userRoles.isSchoolAdmin || userRoles.isSchoolTeacher
          }
        />

        {!userRoles.isResearcher && (
          <ManageArbitrarySkills
            onChangeArbitrarySkills={(skills) => setArbitrarySkills(skills)}
            value={arbitrarySkills}
          />
        )}

        {userRoles.isResearcher && (
          <ExtraStudentInfo
            extras={studentExtraDetails}
            onUpdateExtras={onUpdateExtras}
          />
        )}
      </Box>

      <Box className={s.actions}>
        <Button
          transKey="save-capital"
          onClick={() => {
            setActionClicked('actionAndClose');
            submitFormById('student-form');
          }}
          isLoading={
            addStudentMutation.isPending && actionClicked === 'actionAndClose'
          }
          isDisabled={addStudentMutation.isPending}
        />

        <Button
          transKey="save-and-keep-going-capital"
          onClick={() => {
            setActionClicked('actionAndKeepGoing');
            submitFormById('student-form');
          }}
          isLoading={
            addStudentMutation.isPending &&
            actionClicked === 'actionAndKeepGoing'
          }
          isDisabled={addStudentMutation.isPending}
        />
      </Box>
    </Box>
  );
};

export default AddStudentTab;
