import { SUPPORTED_GENDERS, SUPPORTED_SCHOOL_GRADES } from '@/common/consts';

export type StudentFormValuesType = {
  isAnonymous?: boolean;
  firstName?: string;
  lastName?: string;
  code?: string;
  dateOfBirth: string | null;
  gender: (typeof SUPPORTED_GENDERS)[number] | null;
  schoolName?: string;
  grade: (typeof SUPPORTED_SCHOOL_GRADES)[number] | null;
  residence?: string;
};

export type ExtrasType = {
  // schoolType: 'public' | 'private' | 'church' | null;
  // area: 'urban' | 'semi-urban' | 'rural' | null;
  // SES: string;
  curriculum: string;
  testDevice: string | null;
  schoolType: string | null;
  area: string | null;
  firstLanguage: string;
  neurodiversity: string;
  mathsTeachersScore: string;
  mathsScore: string;
  literatureScore: string;
};
