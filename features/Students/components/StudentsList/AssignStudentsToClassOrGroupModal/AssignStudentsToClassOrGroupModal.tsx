import { ScrollArea, Table, TableData } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Input from '@/components/Inputs/Input/Input';
import Radio from '@/components/Radio/Radio';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_CLASSES from '@/services/schools/classes';
import GROUPS from '@/services/students/groups';
import { ClassesFetchListType, SingleGroupDetailsType } from '@/types/common';

import s from './AssignStudentsToClassOrGroupModal.module.css';

type AssignStudentsToClassModalProps = {
  selectedStudents: string[];
  onClose: () => void;
  classesList: ClassesFetchListType['results'];
  groupsList: SingleGroupDetailsType[];
  onCompleteAssign: () => void;
};

const AssignStudentsToClassOrGroupModal = ({
  classesList,
  groupsList,
  onClose,
  onCompleteAssign,
  selectedStudents,
}: AssignStudentsToClassModalProps): JSX.Element => {
  const queryClient = useQueryClient();
  const { isSchoolRole, userRoles } = UserContext();
  const [searchValue, setSearchValue] = useState('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedGroup, setSelectedGroup] = useState<string>('');

  const { t } = useTranslation();

  const noSelectedStudents = selectedStudents.length === 0;

  const filteredClasses = classesList.filter((classItem) =>
    classItem.name.toLowerCase().match(searchValue.toLowerCase().trim())
  );

  const filteredGroups = groupsList.filter((group) =>
    group.name.toLowerCase().match(searchValue.toLowerCase().trim())
  );

  const classesTableData: TableData = {
    head: [
      t('class-capital'),
      t('grade-capital'),
      t('students-count-capital'),
      '',
    ],
    body: filteredClasses?.map((el) => [
      el.name,
      el.grade,
      el.studentsCount,
      <Radio
        key={el.id}
        value={el.id}
        isChecked={selectedClass === el.id}
        onChange={(value) => setSelectedClass(value)}
      />,
    ]),
  };

  const groupsTableData: TableData = {
    head: [
      t('group-capital'),
      t('description-capital'),
      t('students-count-capital'),
      '',
    ],
    body: filteredGroups?.map((el) => [
      el.name,
      el.description,
      el.studentsCount,
      <Radio
        key={el.id}
        value={el.id}
        isChecked={selectedGroup === el.id}
        onChange={(value) => setSelectedGroup(value)}
      />,
    ]),
  };

  const assignStudentsToClassMutation = useMutation({
    mutationFn: SCHOOL_CLASSES.ASSIGN_SCHOOL_STUDENTS_TO_CLASS,
    onError: () => {},
    onSuccess: async () => {
      onCompleteAssign();
      setSelectedClass('');
      onClose();

      notifications.show({
        title: t('students-assigned-to-class-successfully'),
        message: '',
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_STUDENT_LIST],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.CLASSES_LIST],
      });
    },
  });

  const assignStudentsToGroupMutation = useMutation({
    mutationFn: GROUPS.ASSIGN_STUDENTS_TO_GROUP,
    onError: () => {},
    onSuccess: async () => {
      onCompleteAssign();
      setSelectedClass('');
      onClose();

      notifications.show({
        title: t('students-assigned-to-group-successfully'),
        message: '',
        color: 'green',
      });

      // TODO - update the state instead of refetching the data cause they change order
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.GROUPS_LIST],
      });
    },
  });

  return (
    <Card size="xl" className={s.wrapper} bg="gray50">
      <div className={s.header}>
        <Text
          untranslatedText={
            isSchoolRole ? t('assign-to-class') : t('assign-to-group')
          }
        />

        <div className={s.actions}>
          <Button
            untranslatedText={t('assign')}
            isDisabled={noSelectedStudents}
            onClick={() => {
              if (userRoles.isSchoolAdmin)
                assignStudentsToClassMutation.mutate({
                  classId: selectedClass,
                  studentIds: selectedStudents,
                });

              if (!isSchoolRole)
                assignStudentsToGroupMutation.mutate({
                  groupId: selectedGroup,
                  studentIds: selectedStudents,
                });
            }}
          />

          <CloseButton onClick={onClose} variant="outlined" />
        </div>
      </div>

      <Card size="xl" className={s.tableWrapper}>
        <div className={s.inputWrapper}>
          <Input
            placeholder={isSchoolRole ? t('search-class') : t('search-group')}
            isDisabled={noSelectedStudents}
            value={searchValue}
            onChange={(v) => {
              setSearchValue(v);
            }}
          />
        </div>

        {noSelectedStudents ? (
          <Text
            transKey="no-selected-students"
            align="center"
            mt={32}
            type="h4"
          />
        ) : (
          <ScrollArea
            h={240}
            scrollbars="y"
            scrollbarSize={4}
            type="always"
            offsetScrollbars
          >
            <Table data={isSchoolRole ? classesTableData : groupsTableData} />
          </ScrollArea>
        )}
      </Card>
    </Card>
  );
};

export default AssignStudentsToClassOrGroupModal;
