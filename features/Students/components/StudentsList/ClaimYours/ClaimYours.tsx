import { useDebouncedCallback } from '@mantine/hooks';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import CloseButton from '@/components/CloseButton/CloseButton';
import SelectedStudentsBoard from '@/components/SelectedStudentsBoard/SelectedStudentsBoard';
import StudentsSelectorCard from '@/components/StudentsSelectorCard/StudentsSelectorCard';
import Text from '@/components/Text/Text';
import { ClassesContext } from '@/context/ClassesProvider';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_CLASSES from '@/services/schools/classes';
import SCHOOL_STUDENTS from '@/services/schools/students';
import {
  ClassDetailsType,
  PaginatedStudentList,
  SelectedEntityByIdAndDisplayedNameType,
} from '@/types/common';

import s from './ClaimYours.module.css';

const FETCH_DATA_LIMIT = 9;

type ClaimYoursPropsType = {
  closeModal: () => void;
};

const ClaimYours = ({ closeModal }: ClaimYoursPropsType): JSX.Element => {
  const queryClient = useQueryClient();
  const { userRoles } = UserContext();
  const { classesList } = ClassesContext();
  const [activePage, setActivePage] = useState(1);

  const [searchStudentInputValue, setStudentInputValue] = useState('');
  const [searchStudentQuery, setSearchStudentQuery] = useState('');

  const [searchClassQuery, setSearchClassQuery] = useState('');

  const [cachedStudentsByClassId, setCachedStudentsByClassId] = useState<{
    // where key is classId
    [key: string]: SelectedEntityByIdAndDisplayedNameType[];
  }>({});

  const [selectedStudents, setSelectedStudents] = useState<
    SelectedEntityByIdAndDisplayedNameType[]
  >([]);

  const { data, isFetching, isLoading } = useQuery<PaginatedStudentList | null>(
    {
      queryFn: () =>
        SCHOOL_STUDENTS.GET_SCHOOL_UNCLAIMED_STUDENTS({
          limit: FETCH_DATA_LIMIT,
          page: activePage,
          query: searchStudentQuery,
        }),
      queryKey: [
        QUERY_KEYS.UNCLAIMED_SCHOOL_STUDENT_LIST,
        activePage,
        searchStudentQuery,
      ],
      staleTime: 1000 * 60 * 60 * 24,
      enabled: userRoles.isSchoolTeacher,
    }
  );

  const unclaimedStudents = data?.results || [];

  const totalNumberOfPages = Math.ceil((data?.count || 0) / FETCH_DATA_LIMIT);

  const classesWithAssignedStudents = classesList
    .filter((classItem) => classItem.studentsCount > 0)
    .filter((classItem) =>
      classItem.name
        .toLowerCase()
        .includes(searchClassQuery.toLowerCase().trim())
    );

  const claimStudentsMutation = useMutation({
    mutationFn: SCHOOL_STUDENTS.CLAIM_SCHOOL_STUDENTS_AS_SCHOOL_TEACHER,
    onError: () => {
      // TODO - use CERTIFICATE_PROGRESS_ERRORS under errors.ts to handle each case separately if you want
      // document.location.reload();
    },
    onSuccess: async () => {
      setSelectedStudents([]);
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.UNCLAIMED_SCHOOL_STUDENT_LIST],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_STUDENT_LIST],
      });
      setCachedStudentsByClassId({});
      closeModal();
    },
  });

  const getStudentByClassIdMutation = useMutation({
    mutationFn: SCHOOL_CLASSES.GET_CLASS_DETAILS,
    onError: (error) => {
      console.log('error', error);
    },
    onSuccess: async (res: ClassDetailsType | null) => {
      if (res) {
        setCachedStudentsByClassId((prev) => ({
          ...prev,
          [res.id]: (res.students || []).map((student) => ({
            displayedName:
              student?.firstName && student?.lastName
                ? `${student.firstName} ${student.lastName}`
                : student?.code || '-',
            id: student.id,
          })),
        }));

        setSelectedStudents((prev) => [
          ...prev,
          ...(res.students || []).map((student) => ({
            displayedName:
              student?.firstName && student?.lastName
                ? `${student.firstName} ${student.lastName}`
                : student?.code || '-',
            id: student.id,
          })),
        ]);
      }
    },
  });

  const onUpdateSelectedStudents = (
    updatedStudentsList: SelectedEntityByIdAndDisplayedNameType[]
  ) => {
    setSelectedStudents(updatedStudentsList);
  };

  const handleStudentInputSearch = useDebouncedCallback(
    async (query: string) => {
      setActivePage(1);
      setSearchStudentQuery(query);
    },
    500
  );

  return (
    <div className={s.wrapper}>
      <div className={s.header}>
        <div className={s.textWrapper}>
          <Text transKey="claim-students" type="h3" />
        </div>

        <CloseButton onClick={closeModal} variant="outlined" />
      </div>

      <div className={s.cardsWrapper}>
        <StudentsSelectorCard
          scrollAreaHeight={410}
          students={unclaimedStudents}
          selectedStudents={selectedStudents}
          isLoading={isLoading}
          isDisabled={isLoading || getStudentByClassIdMutation.isPending}
          isFetching={isFetching}
          tableProps={{
            paginationData: {
              activePage,
              totalNumberOfPages,
              totalNumberOfSearchResults: data?.count || 0,
              onPageChange: (page) => {
                setActivePage(page);
              },
              totalNumberOfResults: data?.totalCount || 0,
            },
            onUpdateSelectedStudentList: (updatedStudentsList) =>
              onUpdateSelectedStudents(updatedStudentsList),
          }}
          cardViewProps={{
            studentsByClassOrGroupId: cachedStudentsByClassId,
            displayedClassesOrGroups: classesWithAssignedStudents,
            totalNumberOfClassesOrGroups: classesList.length,
            onClassOrGroupSelection: (classId, isChecked) => {
              if (isChecked) {
                // remove all students from the selectedStudents array that exist in the studentsByClassId[classItem.id]
                const studentsToRemove = cachedStudentsByClassId[classId]?.map(
                  (student) => student.id
                );

                setSelectedStudents((prev) =>
                  prev.filter(
                    (selectedStudent) =>
                      !studentsToRemove?.includes(selectedStudent.id)
                  )
                );
              } else {
                if (cachedStudentsByClassId[classId]) {
                  const uniqueStudentsToAdd = cachedStudentsByClassId[
                    classId
                  ].filter(
                    (student) =>
                      !selectedStudents.some(
                        (selectedStudent) => selectedStudent.id === student.id
                      )
                  );

                  setSelectedStudents((prev) => {
                    return [...prev, ...uniqueStudentsToAdd];
                  });

                  return;
                }

                getStudentByClassIdMutation.mutate(classId);
              }
            },
          }}
          searchProps={{
            searchStudentQuery: searchStudentInputValue,
            searchClassesQuery: searchClassQuery,
            onInputChange: (value, view) => {
              if (view === 'cards') {
                setSearchClassQuery(value);
              } else {
                setStudentInputValue(value);
                handleStudentInputSearch(value);
              }
            },
          }}
        />

        <SelectedStudentsBoard
          scrollAreaHeight={440}
          selectedStudents={selectedStudents}
          isLoading={getStudentByClassIdMutation.isPending}
          onDeleteStudent={(studentId) =>
            setSelectedStudents((prev) =>
              prev.filter((selectedStudent) => selectedStudent.id !== studentId)
            )
          }
        />
      </div>

      <div className={s.claimButtonWrapper}>
        <Button
          transKey="claim-yours-capital"
          isDisabled={
            data?.totalCount === 0 ||
            isLoading ||
            getStudentByClassIdMutation.isPending ||
            selectedStudents.length === 0
          }
          isLoading={claimStudentsMutation.isPending}
          onClick={() =>
            claimStudentsMutation.mutate(
              selectedStudents.map((student) => student.id)
            )
          }
        />
      </div>
    </div>
  );
};

export default ClaimYours;
