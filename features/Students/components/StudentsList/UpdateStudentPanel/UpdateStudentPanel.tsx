/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, LoadingOverlay, Skeleton } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { SUPPORTED_GENDERS } from '@/common/consts';
import { submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import SCHOOL_STUDENTS from '@/services/schools/students';
import INDEPENDENT_TEACHER_STUDENTS from '@/services/students/students';
import { ArbitrarySkillType, StudentType } from '@/types/common';

import ExtraStudentInfo from '../ExtraStudentInfo/ExtraStudentInfo';
import ManageArbitrarySkills from '../ManageArbitrarySkills/ManageArbitrarySkills';
import StudentForm from '../StudentForm/StudentForm';
import { ExtrasType, StudentFormValuesType } from '../types';
import s from './UpdateStudentPanel.module.css';

type UpdateStudentPanelPropsType = {
  studentIdToUpdate: string;
  onClosePanel: () => void;
};

const UpdateStudentPanel = ({
  onClosePanel,
  studentIdToUpdate,
}: UpdateStudentPanelPropsType): JSX.Element => {
  const { t } = useTranslation();
  const { isSchoolRole, userRoles } = UserContext();
  const [arbitrarySkills, setArbitrarySkills] = useState<ArbitrarySkillType[]>(
    []
  );

  const queryClient = useQueryClient();

  const {
    data: studentDetails,
    error,
    isError,
    isFetching,
  } = useQuery<StudentType | null>({
    enabled: Boolean(studentIdToUpdate),
    queryFn: () =>
      isSchoolRole
        ? SCHOOL_STUDENTS.GET_SCHOOL_STUDENT_DETAILS_BY_ID(studentIdToUpdate)
        : INDEPENDENT_TEACHER_STUDENTS.GET_SCHOOL_STUDENT_DETAILS_BY_ID(
            studentIdToUpdate
          ),
    queryKey: [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_BY_ID, studentIdToUpdate],
  });

  const defaultStudentValues: StudentFormValuesType = {
    isAnonymous: Boolean(studentDetails?.anonymous),
    firstName: studentDetails?.firstName || '',
    lastName: studentDetails?.lastName || '',
    code: studentDetails?.code || '',
    dateOfBirth: studentDetails?.dateOfBirth || null,
    gender: studentDetails?.gender || SUPPORTED_GENDERS[0],
    schoolName: studentDetails?.schoolName || '',
    grade: (studentDetails?.grade as any) || '1',
    residence: studentDetails?.residence || '',
  };

  const extras: ExtrasType = {
    schoolType: studentDetails?.extras?.schoolType || null,
    area: studentDetails?.extras?.area || null,
    firstLanguage: studentDetails?.extras?.firstLanguage || '',
    curriculum: studentDetails?.extras?.curriculum || '',
    neurodiversity: studentDetails?.extras?.neurodiversity || '',
    mathsTeachersScore: studentDetails?.extras?.mathsTeachersScore || '',
    mathsScore: studentDetails?.extras?.mathsScore || '',
    literatureScore: studentDetails?.extras?.literatureScore || '',
    testDevice: studentDetails?.extras?.testDevice || null,
    // SES: studentDetails?.extras?.SES || '',
  };

  const onUpdateExtras = useCallback(
    (item: keyof ExtrasType, value: string) => {
      queryClient.setQueryData(
        [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_BY_ID, studentIdToUpdate],
        (old: StudentType) => {
          return {
            ...old,
            extras: {
              ...old.extras,
              [item]: value,
            },
          };
        }
      );
    },
    [queryClient, studentIdToUpdate]
  );

  const updateStudentMutation = useMutation({
    mutationFn: (data: { studentPayload: any; studentId: string }) => {
      return userRoles.isSchoolAdmin
        ? SCHOOL_STUDENTS.UPDATE_SCHOOL_STUDENT(
            data.studentPayload,
            data.studentId
          )
        : INDEPENDENT_TEACHER_STUDENTS.UPDATE_STUDENT(
            data.studentPayload,
            data.studentId
          );
    },
    onError: (err) => {
      notifications.show({
        message: t(err.message),
        color: 'red',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          userRoles.isSchoolAdmin
            ? QUERY_KEYS.SCHOOL_STUDENT_LIST
            : QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST,
        ],
      });

      // const hasNameChanged =
      //   defaultStudentValues.firstName !== variables.studentPayload.firstName ||
      //   defaultStudentValues.lastName !== variables.studentPayload.lastName;

      // console.log('hasNameChanged', hasNameChanged);

      // BECAUSE THE STUDENT NAME IS DISPLAYED UNDER THE SESSIONS TESTS IF THEY CHANGE WE INVALIDATE
      // BUT IF ONLY THE NAME CHANGES NOT ANYTHING ELSE
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });

      onClosePanel();

      notifications.show({
        title: t('student-updated-successfully'),
        message: '',
        color: 'green',
      });
    },
  });

  const onSubmitStudentForm = (formValues: StudentFormValuesType) => {
    const { code, firstName, gender, grade, isAnonymous, lastName } =
      formValues;
    const areRequiredFieldsValid =
      (!isAnonymous ? Boolean(firstName?.trim()) : true) &&
      (!isAnonymous ? Boolean(lastName?.trim()) : true) &&
      (isAnonymous ? Boolean(code?.trim()) : true) &&
      Boolean(grade) &&
      Boolean(gender);

    if (!areRequiredFieldsValid) return;

    // clear the arbitrary skills from the isCustom flag and default values
    const clearedArbitrarySkills = userRoles.isResearcher
      ? []
      : arbitrarySkills?.map((skill) => {
          const clearedSubskills = skill.subskills
            .map((subskill) => {
              return {
                ...(subskill.default ? {} : { default: subskill.default }),
                name: subskill.name,
                score: subskill.score,
              };
            })
            .filter((subskill) => subskill.name !== t('new-skill'));

          //   // from the clearedSubSkills remove duplicate entries where the name is the same
          const clearedSubskillsMap = new Map();
          clearedSubskills.forEach((subskill) => {
            clearedSubskillsMap.set(subskill.name, subskill);
          });

          const clearedSubskillsArray = Array.from(
            clearedSubskillsMap.values()
          );

          return {
            default: skill.default,
            name: skill.name,
            score: skill.score,
            subskills: clearedSubskillsArray,
          };
        });

    const studentPayload = {
      grade: formValues.grade || '1',
      gender: formValues.gender || 'other',
      anonymous: formValues.isAnonymous,
      dateOfBirth: formValues.dateOfBirth,
      schoolName: formValues.schoolName,
      residence: formValues.residence,
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      code: formValues.code,
      ...(userRoles.isResearcher && { extras }),
      ...(!userRoles.isResearcher && { skills: clearedArbitrarySkills }),
    } as any;

    updateStudentMutation.mutate({
      studentPayload,
      studentId: studentIdToUpdate,
    });
  };

  useEffect(() => {
    if (studentDetails?.skills) {
      setArbitrarySkills(studentDetails.skills);
    }
  }, [studentDetails?.skills]);

  return (
    <Box className={s.wrapper}>
      <Card size="xl" bg="gray50" isLoading={false} className={s.card}>
        <div className={s.header}>
          <div className={s.textWrapper}>
            <Text transKey="update-student" type="h3" />
          </div>

          <CloseButton onClick={onClosePanel} variant="outlined" />
        </div>

        <Box className={s.cardsWrapper}>
          <LoadingOverlay
            visible={isFetching}
            zIndex={1000}
            overlayProps={{ radius: 'sm', blur: 2 }}
          />

          {isFetching && <Skeleton height={682} />}

          {studentDetails && !isFetching && (
            <StudentForm
              onSubmitStudentForm={(values) => onSubmitStudentForm(values)}
              defaultValues={defaultStudentValues}
              isShoolAdminOrSchoolTeacher={isSchoolRole}
            />
          )}

          {arbitrarySkills && !isFetching && !userRoles.isResearcher && (
            <ManageArbitrarySkills
              onChangeArbitrarySkills={(skills) => setArbitrarySkills(skills)}
              value={arbitrarySkills}
            />
          )}

          {studentDetails && !isFetching && userRoles.isResearcher && (
            <ExtraStudentInfo extras={extras} onUpdateExtras={onUpdateExtras} />
          )}
        </Box>

        <Box className={s.action}>
          <Button
            transKey="update-capital"
            onClick={() => {
              submitFormById('student-form');
            }}
            isLoading={updateStudentMutation.isPending}
          />
        </Box>
      </Card>
    </Box>
  );
};

export default UpdateStudentPanel;
