import {
  Box,
  CopyButton,
  Flex,
  Pagination,
  Table,
  TableData,
  Tooltip,
} from '@mantine/core';
import { useDebouncedCallback } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaKey } from 'react-icons/fa';

import {
  GLOBAL_PAGINATION_FETCH_LIMIT,
  SUPPORTED_SCHOOL_GRADES,
} from '@/common/consts';
import { getStudentsDisplayName } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Checkbox from '@/components/Checkbox/Checkbox';
import ConductTestModal from '@/components/ConductTestModal/ConductTestModal';
import StudentInfoCard from '@/components/InformationActionCard/StudentInfoCard/StudentInfoCard';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { ClassesContext } from '@/context/ClassesProvider';
import { GroupsContext } from '@/context/GroupsProvider';
import { UserContext } from '@/context/UserProvider';
import ConductSubTestsModal from '@/features/ConductSubTestsModal/ConductSubTestsModal';
import SCHOOL_STUDENTS from '@/services/schools/students';
import INDEPENDENT_TEACHER_STUDENTS from '@/services/students/students';
import {
  PaginatedStudentList,
  SelectedEntityByIdAndDisplayedNameType,
  StudentType,
  SupportedSchoolGradesType,
} from '@/types/common';

import ActionButton from '../../../../components/ActionButton/ActionButton';
import ActionsMenu from '../ActionsMenu/ActionsMenu';
import GridPlaceholder from '../GridPlaceholder/GridPlaceholder';
import SchoolLayout from '../SchoolLayout/SchoolLayout';
import AssignStudentsToClassOrGroupModal from './AssignStudentsToClassOrGroupModal/AssignStudentsToClassOrGroupModal';
import StudentActionsMenu from './StudentActionsMenu/StudentActionsMenu';
import s from './StudentsList.module.css';
import UpdateStudentPanel from './UpdateStudentPanel/UpdateStudentPanel';

type StudentsPaginationType = {
  activePage: number;
  debouncedSearchWord: string;
  className: string;
  groupName: string;
  grades: SupportedSchoolGradesType[];
};

const getSchoolStudents = ({
  activePage,
  className,
  debouncedSearchWord,
  grades,
}: StudentsPaginationType) =>
  SCHOOL_STUDENTS.GET_SCHOOL_STUDENTS({
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
    page: activePage,
    query: debouncedSearchWord,
    className,
    grades,
  });

const getStudentsAsIndependentTeacher = ({
  activePage,
  debouncedSearchWord,
  grades,
  groupName,
}: StudentsPaginationType) =>
  INDEPENDENT_TEACHER_STUDENTS.GET_STUDENTS({
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
    page: activePage,
    grades,
    query: debouncedSearchWord,
    groupName,
  });

const StudentsList = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { isSchoolRole, userCertificates, userRoles } = UserContext();
  const { areClassesListLoading, classesList, updateGradesList } =
    ClassesContext();
  const { groupsList } = GroupsContext();

  const [isConductTestModalOpen, setIsConductTestModalOpen] = useState(false);

  // all this can be moved to a custom hook

  const [searchWord, setSearchWord] = useState('');
  const [searchWordInputValue, setSearchWordInputValue] = useState('');
  const [grades, setGrades] = useState<SupportedSchoolGradesType[]>([]);

  const [gradesDisplayValues, setGradesDisplayValues] = useState<
    SupportedSchoolGradesType[]
  >([]);

  const [classOrGroupFilter, setClassOrGroupFilter] = useState<string | null>(
    null
  );

  const [activePage, setActivePage] = useState(1);
  // up 2 here
  //----------------

  const [selectedStudentDetails, setSelectedStudentDetails] =
    useState<StudentType | null>(null);
  const [isEditViewModalOpen, setIsViewModalOpen] = useState(false);

  const [isDeleteStudentsModalOpen, setIsDeleteStudentsModalOpen] =
    useState<boolean>(false);
  const [isUnclaimStudentsModalOpen, setIsUnclaimStudentsModalOpen] =
    useState(false);

  const [selectedStudents, setSelectedStudents] = useState<
    SelectedEntityByIdAndDisplayedNameType[]
  >([]);

  const selectedStudentsByIds = selectedStudents.map((student) => student.id);

  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [areFiltersGettingCleared, setAreFiltersGettingCleared] =
    useState(false);

  const defaultClassNameParam =
    classesList?.length === 0 && classOrGroupFilter !== 'unassigned'
      ? ''
      : classOrGroupFilter;

  const searchParams = {
    activePage,
    debouncedSearchWord: searchWord,
    className: defaultClassNameParam || '',
    groupName: classOrGroupFilter || '',
    grades,
  };

  // Also this! with the variables below can be moved into a custom hook
  const {
    data,
    isFetching: areStudentsFetching,
    isLoading: areStudentsLoading,
    isPending: areStudentsPending,
  } = useQuery<PaginatedStudentList | null>({
    queryFn: isSchoolRole
      ? () => getSchoolStudents(searchParams)
      : () => getStudentsAsIndependentTeacher(searchParams),
    queryKey: [
      `${isSchoolRole ? QUERY_KEYS.SCHOOL_STUDENT_LIST : QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST}`,
      activePage,
      searchWord,
      {
        className: isSchoolRole ? defaultClassNameParam || '' : null,
        groupName: !isSchoolRole ? classOrGroupFilter : null,
      },
      grades,
    ],
    placeholderData: keepPreviousData,
    staleTime: 1000 * 60 * 60,
    enabled: isSchoolRole ? !areClassesListLoading : true,
  });

  const students = data?.results || [];

  const totalNumberOfPages = Math.ceil(
    (data?.count || 1) / GLOBAL_PAGINATION_FETCH_LIMIT
  );
  const areTotalNumberOfStudentsZero = (data?.totalCount || 0) === 0;
  const areStudentsArrayEmpty = students.length === 0;

  const areStudentsEmptyAndNotFetching =
    areStudentsArrayEmpty && !areStudentsFetching;

  const areStudentsEmptyAndNotLoading =
    areStudentsArrayEmpty && !areStudentsLoading && !areStudentsPending;

  const deleteStudentsMutation = useMutation({
    mutationFn: userRoles.isSchoolAdmin
      ? SCHOOL_STUDENTS.DELETE_SCHOOL_STUDENTS
      : INDEPENDENT_TEACHER_STUDENTS.DELETE_STUDENTS,
    onError: (error) => {
      console.log('error', error);
    },
    onSuccess: async () => {
      setSelectedStudents([]);
      setIsDeleteStudentsModalOpen(false);

      queryClient.invalidateQueries({
        queryKey: [
          userRoles.isSchoolAdmin
            ? QUERY_KEYS.SCHOOL_STUDENT_LIST
            : QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST,
        ],
      });

      notifications.show({
        title: t('delete-success'),
        message: '',
        color: 'green',
      });
    },
  });

  const unclaimStudentsMutation = useMutation({
    mutationFn: SCHOOL_STUDENTS.UNCLAIM_SCHOOL_STUDENTS_AS_SCHOOL_TEACHER,
    onError: (error) => {
      // TODO : ERROR HANDLING
      console.log('error', error);
    },
    onSuccess: async () => {
      setSelectedStudents([]);
      setIsUnclaimStudentsModalOpen(false);
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.SCHOOL_STUDENT_LIST],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.UNCLAIMED_SCHOOL_STUDENT_LIST],
      });
    },
  });

  const CLASSES_DROPDOWN_OPTIONS = [
    {
      label: t('unassigned'),
      value: 'unassigned',
    },
    ...classesList.map((item) => ({
      label: item.name,
      value: item.id,
    })),
  ];

  const isGlobalCheckboxChecked = students.every((student) =>
    selectedStudentsByIds.includes(student.id)
  );

  const isGlobalCheckboxIndeterminate = students.some((student) =>
    selectedStudentsByIds.includes(student.id)
  );

  const onGlobalCheckBoxSelection = () => {
    if (isGlobalCheckboxIndeterminate) {
      const filteredStudents = selectedStudents.filter(
        (selectedStudent) =>
          // meaning if it gets included it will return false so filter will remove it
          !students.map((student) => student.id).includes(selectedStudent.id)
      );

      setSelectedStudents(filteredStudents);
    } else {
      const updatedList = [
        ...selectedStudents,
        ...students.map((student) => ({
          displayedName: student.anonymous
            ? student.code || ''
            : `${student.firstName} ${student.lastName}`,
          id: student.id,
        })),
      ];

      setSelectedStudents(updatedList);
    }
  };

  const onIndividualTableSelection = (student: StudentType) => {
    const isStudentSelected = selectedStudents.some(
      (selectedStudent) => selectedStudent.id === student.id
    );

    if (isStudentSelected) {
      const filteredSelectedStudentList = selectedStudents.filter(
        (selectedStudent) => selectedStudent.id !== student.id
      );
      setSelectedStudents(filteredSelectedStudentList);
    } else {
      const updatedSelectedStudentList = [
        ...selectedStudents,
        {
          displayedName: student.anonymous
            ? student.code || ''
            : `${student?.firstName || ''} ${student?.lastName || ''}`,
          id: student.id,
        },
      ];

      setSelectedStudents(updatedSelectedStudentList);
    }
  };

  const handleStudentInputSearch = useDebouncedCallback(
    async (query: string) => {
      setActivePage(1);
      setSearchWord(query);
    },
    500
  );

  const handleGradesChange = useDebouncedCallback(
    async (updatedGradesValues: SupportedSchoolGradesType[]) => {
      setGrades(updatedGradesValues);
      updateGradesList(updatedGradesValues);
      setActivePage(1);
    },
    500
  );

  const tableData: TableData = {
    head: [
      t('name-capital'),
      t('surname-capital'),
      t('code-capital'),
      t('grade-capital'),
      isSchoolRole ? t('class-capital') : t('group-capital'),
      t('login-capital'),
      <Checkbox
        key="checkbox-icon"
        value=""
        indeterminate={
          isGlobalCheckboxIndeterminate && !isGlobalCheckboxChecked
        }
        isChecked={isGlobalCheckboxChecked}
        onChange={onGlobalCheckBoxSelection}
      />,
      ...(userRoles.isSchoolTeacher ? [] : ['']),
    ],
    body: students.map((student) => [
      student.firstName || '-',
      student.lastName || '-',
      student.code || '-',
      student.grade,
      isSchoolRole ? student.className || '-' : student.groupName || '-',
      <CopyButton
        key={`enter-${student.id}`}
        value={student.enter || ''}
        timeout={2000}
      >
        {({ copied, copy }) => (
          <Tooltip
            label={copied ? t('copied') : t('copy')}
            withArrow
            position="top"
          >
            <Flex
              onClick={copy}
              align="center"
              gap="xs"
              style={{ cursor: 'pointer' }}
            >
              <FaKey size={13} color="var(--color-turquoise)" />

              <Text
                type="body1"
                color="black"
                untranslatedText={student.enter}
              />
            </Flex>
          </Tooltip>
        )}
      </CopyButton>,
      <Checkbox
        value={student.id}
        key={student.id}
        isChecked={selectedStudents.some(
          (selectedStudent) => selectedStudent.id === student.id
        )}
        onChange={() => onIndividualTableSelection(student)}
      />,
      ...(userRoles.isSchoolTeacher
        ? []
        : [
            <StudentActionsMenu
              key={student.id}
              studentId={student.id}
              onOpenMenu={() => {
                const selectedStudentDetailsData =
                  students.find((s) => s.id === student.id) || null;

                setSelectedStudentDetails(selectedStudentDetailsData);
              }}
              onEditStudent={() => setIsViewModalOpen(true)}
            />,
          ]),
    ]),
  };

  const PAGINATION_COMPONENT =
    areStudentsEmptyAndNotFetching && !areTotalNumberOfStudentsZero ? null : (
      <Pagination
        total={totalNumberOfPages}
        value={activePage}
        hideWithOnePage
        withControls={false}
        onChange={(value) => setActivePage(value)}
        disabled={areStudentsFetching}
      />
    );

  const onCheckStudentInfoCard = (studentId: string) => {
    setSelectedStudents((prevStudents) => {
      const isStudentAlreadySelected = prevStudents.some(
        (prevStudent) => prevStudent.id === studentId
      );

      if (isStudentAlreadySelected) {
        return prevStudents.filter(
          (prevStudent) => prevStudent.id !== studentId
        );
      }

      const newStudent = students.find((stu) => stu.id === studentId);

      return [
        ...prevStudents,
        {
          displayedName: getStudentsDisplayName({
            firstName: newStudent?.firstName,
            lastName: newStudent?.lastName,
            code: newStudent?.code || '',
          }),
          id: newStudent?.id || '',
        },
      ];
    });
  };

  const CARDS_VIEW_COMPONENT = (
    <GridPlaceholder
      isPending={areStudentsPending}
      cardsComponent={students?.map((student) => {
        const studentFullName = getStudentsDisplayName({
          firstName: student.firstName,
          lastName: student.lastName,
          code: student.code || '',
        });

        return (
          <StudentInfoCard
            key={student.id}
            isChecked={selectedStudentsByIds.includes(student.id)}
            fullName={studentFullName}
            grade={student.grade}
            className={student.className}
            groupName={student.groupName}
            value={student.id}
            isDisabled={areStudentsFetching || areFiltersGettingCleared}
            isAnonymous={student.anonymous}
            enterCode={student.enter}
            onChange={(studentId) => onCheckStudentInfoCard(studentId)}
            {...((userRoles.isSchoolAdmin ||
              userRoles.isIndependentTeacher ||
              userRoles.isResearcher) && {
              actionComponent: (
                <StudentActionsMenu
                  studentId={student.id}
                  onOpenMenu={() => {
                    const selectedStudentDetailsData =
                      students.find((stu) => stu.id === student.id) || null;

                    setSelectedStudentDetails(selectedStudentDetailsData);
                  }}
                  onEditStudent={() => setIsViewModalOpen(true)}
                />
              ),
            })}
          />
        );
      })}
    />
  );

  const ACTIONS_COMPONENT = (
    <>
      <ActionButton
        onClick={() => {
          setIsConductTestModalOpen(true);
        }}
        type="conductTest"
        isDisabled={
          selectedStudents.length === 0 ||
          Object.keys(userCertificates).length === 0
        }
        toolTip={
          Object.keys(userCertificates).length === 0
            ? t('no-purchased-certificates')
            : t('no-selected-students')
        }
      />

      {userRoles.isSchoolTeacher && (
        <ActionButton
          onClick={() => setIsUnclaimStudentsModalOpen(true)}
          type="unclaim"
          isDisabled={selectedStudents.length === 0}
          toolTip={t('no-selected-students')}
        />
      )}

      {(userRoles.isSchoolAdmin ||
        userRoles.isIndependentTeacher ||
        userRoles.isResearcher) && (
        <ActionButton
          onClick={() => setIsAssignModalOpen(true)}
          type={userRoles.isSchoolAdmin ? 'assignToClass' : 'assignToGroup'}
          isDisabled={selectedStudents.length === 0}
          toolTip={t('no-selected-students')}
        />
      )}

      {(userRoles.isIndependentTeacher ||
        userRoles.isResearcher ||
        userRoles.isSchoolAdmin) && (
        <ActionsMenu
          data={[
            {
              key: 'delete',
              action: (
                <ActionButton
                  onClick={() => setIsDeleteStudentsModalOpen(true)}
                  type="delete"
                  isDisabled={selectedStudents.length === 0}
                  toolTip={t('no-selected-students')}
                />
              ),
            },
          ]}
        />
      )}
    </>
  );

  const schoolRoleSelectedClass =
    (classesList.length > 0 &&
      classesList.find((c) => c.id === classOrGroupFilter)) ||
    classOrGroupFilter === 'unassigned'
      ? classOrGroupFilter
      : '';

  const FILTERS = (
    <>
      <div className={s.labelAndFieldWrapper}>
        <Text
          transKey="school-grades-capital"
          type="body2"
          isBold
          fw={600}
          className={s.filterLabel}
        />

        <div className={s.gradesWrapper}>
          {SUPPORTED_SCHOOL_GRADES.map((grade) => {
            const isSelected = gradesDisplayValues?.includes(grade);

            return (
              <Box
                id={grade}
                key={grade}
                className={`${s.gradeSelector} cursorPointer ${isSelected && s.selectedGrade} ${areStudentsFetching && s.disabledGrade} preventUserSelect`}
                onClick={() => {
                  if (areStudentsFetching) return;

                  const filteredGrades = isSelected
                    ? gradesDisplayValues.filter((g) => g !== grade)
                    : [...gradesDisplayValues, grade];

                  handleGradesChange(filteredGrades);
                  setGradesDisplayValues(filteredGrades);

                  if (
                    isSchoolRole &&
                    gradesDisplayValues.length > 0 &&
                    classOrGroupFilter !== 'unassigned'
                  ) {
                    // reset classes
                    setClassOrGroupFilter('');
                  }
                }}
              >
                {grade}
              </Box>
            );
          })}
        </div>
      </div>

      <div className={s.labelAndFieldWrapper}>
        <Text
          transKey={isSchoolRole ? 'class-capital' : 'group-capital'}
          type="body2"
          isBold
          fw={600}
        />

        <SelectDropdown
          value={
            (isSchoolRole ? schoolRoleSelectedClass : classOrGroupFilter) || ''
          }
          clearable
          data={
            isSchoolRole
              ? CLASSES_DROPDOWN_OPTIONS
              : groupsList.map((item) => ({
                  label: item.name,
                  value: item.id,
                }))
          }
          onChange={(value) => {
            setClassOrGroupFilter(value || null);
            setActivePage(1);
          }}
          placeholder={isSchoolRole ? 'Select class' : 'Select group'}
          isDisabled={areClassesListLoading}
          isFirstOptionSeparated={isSchoolRole}
          onClear={
            isSchoolRole
              ? () => {
                  setClassOrGroupFilter(null);
                }
              : undefined
          }
        />
      </div>
    </>
  );

  const TABLE_VIEW_COMPONENT = areStudentsFetching ? (
    <TableSkeleton numberOfColumns={tableData.head?.length} />
  ) : (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
      }}
    />
  );

  return (
    <>
      <SchoolLayout
        type="students"
        onClearFilters={() => {
          if (grades.length === 0 && classOrGroupFilter === null) return;

          setGrades([]);
          updateGradesList([]);
          setGradesDisplayValues([]);

          setClassOrGroupFilter(null);

          setAreFiltersGettingCleared(true);
          setTimeout(() => {
            setAreFiltersGettingCleared(false);
          }, 500);
        }}
        numberOfSearchResults={data?.count || 0}
        cardsContent={CARDS_VIEW_COMPONENT}
        listContent={TABLE_VIEW_COMPONENT}
        onSearch={(value) => {
          handleStudentInputSearch(value);
          setSearchWordInputValue(value);
        }}
        isNoResultsVisible={
          areStudentsEmptyAndNotFetching && !areTotalNumberOfStudentsZero
        }
        actionsContent={ACTIONS_COMPONENT}
        searchWord={searchWordInputValue}
        searchInputTransKey="search-students"
        isNoDataListDisplayed={
          areStudentsEmptyAndNotLoading && areTotalNumberOfStudentsZero
        }
        isInformationLoading={areStudentsLoading}
        paginationComponent={PAGINATION_COMPONENT}
        filters={FILTERS}
        numberOFSelectedFilters={
          gradesDisplayValues.length + (classOrGroupFilter ? 1 : 0)
        }
      />

      {(userRoles.isSchoolAdmin ||
        userRoles.isIndependentTeacher ||
        userRoles.isResearcher) &&
        selectedStudentDetails && (
          <PrimaryModal
            isOpen={isEditViewModalOpen}
            content={
              <UpdateStudentPanel
                studentIdToUpdate={selectedStudentDetails.id}
                onClosePanel={() => setIsViewModalOpen(false)}
              />
            }
          />
        )}

      {(userRoles.isSchoolAdmin ||
        userRoles.isIndependentTeacher ||
        userRoles.isResearcher) && (
        <AlertDialog
          isOpen={isDeleteStudentsModalOpen}
          onConfirmAction={() => {
            if (
              userRoles.isSchoolAdmin ||
              userRoles.isIndependentTeacher ||
              userRoles.isResearcher
            )
              deleteStudentsMutation.mutate(selectedStudentsByIds);
          }}
          title="delete-students"
          description={
            selectedStudents.length > 0
              ? 'delete-students-confirmation'
              : 'no-selected-students'
          }
          onCancel={() => setIsDeleteStudentsModalOpen(false)}
          isActionInProgress={deleteStudentsMutation.isPending}
          variant="danger"
          isDisabled={selectedStudents.length === 0}
        />
      )}

      {userRoles.isSchoolTeacher && (
        <AlertDialog
          isOpen={isUnclaimStudentsModalOpen}
          onConfirmAction={() =>
            unclaimStudentsMutation.mutate(selectedStudentsByIds)
          }
          isDisabled={selectedStudents.length === 0}
          title="unclaim-students"
          description={
            selectedStudents.length > 0
              ? 'unclaim-students-confirmation'
              : 'no-selected-students'
          }
          onCancel={() => setIsUnclaimStudentsModalOpen(false)}
          isActionInProgress={unclaimStudentsMutation.isPending}
          variant="danger"
        />
      )}

      <PrimaryModal
        isOpen={isAssignModalOpen}
        content={
          selectedStudents ? (
            <AssignStudentsToClassOrGroupModal
              onClose={() => setIsAssignModalOpen(false)}
              classesList={classesList}
              selectedStudents={selectedStudentsByIds}
              onCompleteAssign={() => {
                setSelectedStudents([]);
                queryClient.invalidateQueries({
                  queryKey: [QUERY_KEYS.CLASSES_LIST],
                });
              }}
              groupsList={groupsList || []}
            />
          ) : null
        }
      />

      {selectedStudents.length > 0 && (
        <ConductTestModal
          key={selectedStudents.length}
          isOpen={isConductTestModalOpen}
          onCloseModal={() => {
            setIsConductTestModalOpen(false);
          }}
          selectedStudents={selectedStudents}
        />
      )}

      <ConductSubTestsModal />
    </>
  );
};

export default StudentsList;
