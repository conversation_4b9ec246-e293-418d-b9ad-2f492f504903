import { Box, Switch, TextInput } from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { JSX } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { SUPPORTED_GENDERS, SUPPORTED_SCHOOL_GRADES } from '@/common/consts';
import { mantineDateParser } from '@/common/helpers';
import Card from '@/components/Card/Card';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';

import { StudentFormValuesType } from '../types';
import s from './StudentForm.module.css';

type AddStudentFormPropsType = {
  onSubmitStudentForm: (
    values: StudentFormValuesType,
    resetForm: () => void
  ) => void;
  defaultValues: StudentFormValuesType;
  // isEditView?: boolean;
  isShoolAdminOrSchoolTeacher: boolean;
};

const StudentForm = ({
  defaultValues,
  // isEditView,
  isShoolAdminOrSchoolTeacher,
  onSubmitStudentForm,
}: AddStudentFormPropsType): JSX.Element => {
  const { t } = useTranslation();

  const { userRoles } = UserContext();

  const {
    clearErrors,
    control,
    formState: { errors },
    handleSubmit,
    register,
    reset,
    // resetField,
    watch,
  } = useForm<StudentFormValuesType>({
    defaultValues,
    mode: 'onSubmit',
  });

  const isAnonymous = watch('isAnonymous');

  const onSubmit = async (values: StudentFormValuesType) => {
    onSubmitStudentForm(values, reset);
  };

  return (
    <Card className={s.wrapper} size="xl">
      <form id="student-form" onSubmit={handleSubmit(onSubmit)}>
        {/* {isEditView ? (
          <Text transKey="basic-info" mb={8} />
        ) : (
          <Box className={s.header}>
            <Text transKey="basic-info" />

            <Controller
              name="isAnonymous"
              control={control}
              render={({ field }) => (
                <Switch
                  checked={isAnonymous}
                  label={t('anonymous-capital')}
                  labelPosition="left"
                  onChange={(v) => {
                    field.onChange(v);
                    clearErrors();
                    // resetField('firstName');
                    // resetField('lastName');
                  }}
                />
              )}
            />
          </Box>
        )} */}
        <Box className={s.header}>
          <Text transKey="basic-info" />

          <Controller
            name="isAnonymous"
            control={control}
            render={({ field }) => (
              <Switch
                checked={isAnonymous}
                label={t('anonymous-capital')}
                labelPosition="left"
                onChange={(v) => {
                  field.onChange(v);
                  clearErrors();
                  // resetField('firstName');
                  // resetField('lastName');
                }}
              />
            )}
          />
        </Box>
        {/* “visible only to you” */}
        <Box className={s.formElements}>
          <div className={s.formElementWrapper}>
            <TextInput
              label={
                <span className={`${isAnonymous && s.anonymousLabel}`}>
                  {isAnonymous ? t('first-name-anonymous') : t('first-name')}
                </span>
              }
              placeholder={isAnonymous ? '' : t('first-name-placeholder')}
              disabled={isAnonymous}
              required={!isAnonymous}
              {...register('firstName', {
                validate: {
                  firstName: (value) =>
                    isAnonymous
                      ? true
                      : (value?.trim()?.length || 0) > 0 ||
                        t('field-required-or-switch-to-anonymous'),
                },
              })}
              error={errors.firstName?.message}
            />
          </div>

          <div className={s.formElementWrapper}>
            <TextInput
              label={
                <span className={`${isAnonymous && s.anonymousLabel}`}>
                  {isAnonymous ? t('last-name-anonymous') : t('last-name')}
                </span>
              }
              placeholder={isAnonymous ? '' : t('last-name-placeholder')}
              disabled={isAnonymous}
              required={!isAnonymous}
              {...register('lastName', {
                validate: {
                  lastName: (value) =>
                    isAnonymous
                      ? true
                      : (value?.trim()?.length || 0) > 0 ||
                        t('field-required-or-switch-to-anonymous'),
                },
              })}
              error={errors.lastName?.message}
            />
          </div>

          <div className={s.groupInputs}>
            <div className={s.formElementWrapper}>
              <TextInput
                label={t('student-code')}
                placeholder={t('code-placeholder')}
                required={isAnonymous}
                mb={24}
                {...register('code', {
                  validate: {
                    code: (value) =>
                      isAnonymous
                        ? (value?.trim()?.length || 0) > 0 ||
                          t('field-required')
                        : true,
                  },
                })}
                error={errors.code?.message}
              />
            </div>

            <div className={s.formElementWrapper}>
              <Controller
                name="gender"
                control={control}
                rules={{
                  required: t('field-required'),
                }}
                render={({ field }) => (
                  <SelectDropdown
                    value={field.value || ''}
                    label={t('gender')}
                    required
                    data={SUPPORTED_GENDERS.map((item) => ({
                      value: item,
                      label: t(item),
                    }))}
                    onChange={(v) => {
                      field.onChange(v);
                    }}
                    placeholder={t('female')}
                    error={errors.gender?.message}
                  />
                )}
              />
            </div>
          </div>

          <div className={s.groupInputs}>
            <Controller
              name="grade"
              control={control}
              rules={{
                required: t('field-required'),
              }}
              render={({ field }) => (
                <SelectDropdown
                  label={t('grade')}
                  placeholder={t('grade-placeholder')}
                  value={field.value || ''}
                  data={
                    SUPPORTED_SCHOOL_GRADES.map((item) => ({
                      value: item,
                      label: item,
                    })) || []
                  }
                  required
                  onChange={(v) => {
                    field.onChange(v);
                  }}
                  error={errors.grade?.message}
                />
              )}
            />

            <div className={s.formElementWrapper}>
              <Controller
                name="dateOfBirth"
                control={control}
                rules={{
                  ...(userRoles.isSchoolAdmin && {
                    required: t('field-required'),
                  }),
                }}
                render={({ field }) => (
                  <DateInput
                    value={field.value ? new Date(field.value) : null}
                    label={t('date-of-birth')}
                    placeholder={t('dob-placeholder')}
                    required={userRoles.isSchoolAdmin}
                    highlightToday
                    dateParser={mantineDateParser}
                    valueFormat="DD/MM/YYYY"
                    onChange={(v) => {
                      field.onChange(v);
                    }}
                    clearable
                    allowDeselect
                    error={errors.dateOfBirth?.message}
                    maxDate={new Date(new Date().getFullYear() - 4, 12, 31)}
                    defaultDate={new Date(new Date().getFullYear() - 4, 12, 31)}
                    minDate={new Date(new Date().getFullYear() - 50, 12, 31)}
                  />
                )}
              />
            </div>
          </div>

          <TextInput
            placeholder={t('school-name-example')}
            disabled={isShoolAdminOrSchoolTeacher}
            label={t('school-name')}
            mb={16}
            {...register('schoolName')}
          />

          {userRoles.isSchoolAdmin && (
            <TextInput
              label={t('neurodiversity')}
              placeholder={t('neurodiversity-placeholder')}
              {...register('neurodiversity')}
            />
          )}

          {!userRoles.isSchoolAdmin && (
            <TextInput
              label={t('hometown')}
              placeholder={t('hometown-example')}
              {...register('residence')}
            />
          )}
        </Box>
      </form>
    </Card>
  );
};

export default StudentForm;
