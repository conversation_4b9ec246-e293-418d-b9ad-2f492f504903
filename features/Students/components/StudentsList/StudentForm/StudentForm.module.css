.wrapper {
  /* max-width: 460px; */
  min-width: 416px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-smd);
}

.formElements {
  display: flex;
  flex-direction: column;
}

.formElementWrapper {
  width: 100%;
  height: 100px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.groupInputs {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.fullNameWrapper {
  height: 174px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-smd);
  transition: height 0.3s ease;
}

.hideFullName {
  height: 0;
  overflow: hidden;
}

.anonymousLabel {
  opacity: 0.3;
}
