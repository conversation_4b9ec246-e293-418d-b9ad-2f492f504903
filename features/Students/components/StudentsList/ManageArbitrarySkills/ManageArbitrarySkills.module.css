.wrapper {
  min-width: 464px;
  width: 100%;
}

.accordionControl {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.dottedRow {
  border-bottom: 1px dotted var(--color-gray300);
  margin-bottom: var(--spacing-lg);
}

.collapsibleItem {
  border: 1px solid var(--color-gray300);
  border-radius: 5px;
  padding: 0 var(--spacing-lg);
}

.arbitrarySkills {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.labelWrapper {
  max-width: 80%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  overflow: hidden;
}

.supplementarySkillsWrapper {
  display: flex;
  flex-direction: column;
  padding-bottom: var(--spacing-lg);
}

.supplementarySkills {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.supplementarySkill {
  display: flex;
  justify-content: space-between;
}

.customSkill {
  display: flex;
  align-items: center;
}

.customSkillInput {
  border: none;
  color: var(--color-gray700);
  font-size: 14px;
  color: var(--color-blue);

  &:focus {
    outline: none;
  }
}

.addSkillButton {
  margin-top: 24px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  align-self: flex-end;
}
