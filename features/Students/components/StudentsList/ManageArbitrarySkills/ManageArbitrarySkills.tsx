import { Box, Collapse, FocusTrap, Rating, ScrollArea } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import baseTheme from '@/styles/baseTheme';
import { ArbitrarySkillType } from '@/types/common';

import s from './ManageArbitrarySkills.module.css';

type ManageArbitrarySkillsPropsType = {
  value: ArbitrarySkillType[];
  onChangeArbitrarySkills: (arbitrarySkills: ArbitrarySkillType[]) => void;
};

const SCORE_STAR_COLOR = 'rgba(222, 184, 65, 1)';
const SCORE_STAR_COLOR_OPACITY = 'rgba(222, 184, 65, 0.6)';

const ManageArbitrarySkills = ({
  onChangeArbitrarySkills,
  value,
}: ManageArbitrarySkillsPropsType): JSX.Element => {
  const [open, setOpen] = useState<number | null>(null);
  const { t } = useTranslation();
  const [active, { toggle }] = useDisclosure(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const anchor = useRef<any>(null);

  return (
    <Card className={s.wrapper} size="xl">
      <Text transKey="learning-profile" mb={32} />

      <Box className={s.arbitrarySkills}>
        {value.map((skill, basicSkillIndex) => {
          const isOtherSection = skill.default === 11;

          return (
            <div className={s.collapsibleItem} key={skill.default}>
              <Box className={s.accordionControl}>
                <Box
                  className={s.labelWrapper}
                  onClick={() => {
                    if (open === skill.default) setOpen(null);
                    else setOpen(skill.default);
                  }}
                >
                  <span>{t(skill.name)}</span>

                  <Icon name="ArrowDown" color="gray500" w={12} h={7} />
                </Box>

                <Rating
                  value={skill.score}
                  onChange={(v) => {
                    const newSkills = value.map((item) => ({ ...item }));
                    const skillIndex = newSkills.findIndex(
                      (item) => item.default === skill.default
                    );
                    newSkills[skillIndex].score = v;
                    onChangeArbitrarySkills(newSkills);
                  }}
                  color={SCORE_STAR_COLOR}
                />
              </Box>

              <Collapse
                in={open === skill.default}
                transitionDuration={400}
                transitionTimingFunction="ease"
              >
                <div className={s.dottedRow} />

                <div className={s.supplementarySkillsWrapper}>
                  <Text
                    transKey="specific-skills"
                    type="body3"
                    color="darkBg1"
                    fw={600}
                    mb={20}
                  />
                  <ScrollArea
                    type="never"
                    h={skill.subskills.length <= 2 ? 120 : 130}
                    scrollbarSize={4}
                    id="scroll-area"
                  >
                    <div className={s.supplementarySkills}>
                      {skill.subskills.map((subItem, subSkillIndex) => {
                        const isSubSkillCustom = !subItem?.default;

                        return (
                          <div
                            // eslint-disable-next-line react/no-array-index-key
                            key={`${subItem.default}-${subSkillIndex}`}
                            className={s.supplementarySkill}
                          >
                            {isSubSkillCustom ? (
                              <FocusTrap active={active}>
                                <div className={s.customSkill}>
                                  <input
                                    id={`custom-skill${subSkillIndex}`}
                                    type="text"
                                    placeholder="Custom skill"
                                    value={subItem.name}
                                    className={s.customSkillInput}
                                    maxLength={20}
                                    onChange={(e) => {
                                      const newSkills = value.map((item) => ({
                                        ...item,
                                        subskills: item.subskills.map(
                                          (sub) => ({
                                            ...sub,
                                          })
                                        ),
                                      }));

                                      newSkills[basicSkillIndex].subskills[
                                        subSkillIndex
                                      ].name = e.target.value;

                                      onChangeArbitrarySkills(newSkills);
                                    }}
                                  />

                                  <Text
                                    transKey="custom-capital"
                                    type="label"
                                    color="blue"
                                    ml={32}
                                    mr={24}
                                  />

                                  <RiDeleteBin5Fill
                                    color={baseTheme.colors.blue}
                                    onClick={() => {
                                      const newSkills = value.map((item) => ({
                                        ...item,
                                        subskills: item.subskills.map(
                                          (sub) => ({
                                            ...sub,
                                          })
                                        ),
                                      }));

                                      newSkills[
                                        basicSkillIndex
                                      ].subskills.splice(subSkillIndex, 1);

                                      onChangeArbitrarySkills(newSkills);
                                    }}
                                  />
                                </div>
                              </FocusTrap>
                            ) : (
                              <Text
                                untranslatedText={t(subItem.name)}
                                type="body2"
                                mb={8}
                              />
                            )}

                            <Rating
                              value={subItem.score}
                              onChange={(v) => {
                                const newSkills = value.map((item) => ({
                                  ...item,
                                  subskills: item.subskills.map((sub) => ({
                                    ...sub,
                                  })),
                                }));

                                newSkills[basicSkillIndex].subskills[
                                  subSkillIndex
                                ].score = v;

                                onChangeArbitrarySkills(newSkills);
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                              color={SCORE_STAR_COLOR_OPACITY}
                            />
                          </div>
                        );
                      })}
                    </div>

                    {isOtherSection && (
                      <div
                        ref={anchor}
                        style={{
                          height: 40,
                          width: '100%',
                          backgroundColor: 'transparent',
                        }}
                      />
                    )}
                  </ScrollArea>

                  {isOtherSection && (
                    <button
                      type="button"
                      className={s.addSkillButton}
                      onClick={() => {
                        const newSkills = value.map((item) => ({
                          ...item,
                          subskills: [...item.subskills],
                        }));

                        const newSupplementarySkills = [
                          ...newSkills[basicSkillIndex].subskills,
                          {
                            name: '',
                            score: 0,
                          },
                        ];

                        newSkills[basicSkillIndex].subskills =
                          newSupplementarySkills;

                        onChangeArbitrarySkills(newSkills);

                        setTimeout(() => {
                          toggle();
                        }, 300);

                        if (anchor.current) {
                          anchor.current.scrollIntoView({
                            behavior: 'smooth',
                            block: 'end',
                            inline: 'nearest',
                          });
                        }
                      }}
                    >
                      <Text
                        transKey="add-skill-capital"
                        type="body3"
                        color="blue"
                      />
                    </button>
                  )}
                </div>
              </Collapse>
            </div>
          );
        })}
      </Box>
    </Card>
  );
};

export default ManageArbitrarySkills;
