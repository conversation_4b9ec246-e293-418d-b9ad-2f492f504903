import React from 'react';
import { useTranslation } from 'react-i18next';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';

import { BUTTON_ACTIONS_TRANSLATION_KEYS } from '../../consts';
import { ShoolTabsType } from '../../types';
import styles from './ZeroListData.module.css';

type ZeroListDataProps = {
  type: ShoolTabsType;
};

const ZeroListData = ({ type }: ZeroListDataProps) => {
  const { t } = useTranslation();
  const { userRoles } = UserContext();

  return (
    <div className={styles.wrapper}>
      <div className={styles.titleWrapper}>
        <div className={styles.titleText}>
          <Text
            transKey="no-type-yet"
            type="h4"
            transVariables={{
              type: t(type),
            }}
            fw={400}
          />

          <div className={styles.dummyButtonWrapper}>
            <div className={styles.dummyButton}>
              <Text
                transKey={
                  BUTTON_ACTIONS_TRANSLATION_KEYS[
                    userRoles.isSchoolTeacher ? 'claim-students' : type
                  ]
                }
                type="button"
                color="blue"
              />
            </div>

            <Text transKey="to-add-some" type="h4" fw={400} />
          </div>
        </div>

        <Icon
          name="PointingArrowSvg"
          color="blue"
          className={styles.arrowIcon}
        />
      </div>

      <div className={styles.dummyCardsWrapper}>
        {[...Array(8)].map((_, index) => (
          // TODO : Fix this
          // eslint-disable-next-line react/no-array-index-key
          <div className={styles.dummyCard} key={index}>
            <div className={styles.topSkeleton} />

            <div className={styles.dummyCardBottomRow}>
              <div className={styles.smallSkeleton} />
              <div className={styles.smallSkeleton} />
              <div className={styles.smallSkeleton} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ZeroListData;
