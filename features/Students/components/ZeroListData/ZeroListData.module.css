.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-3xl);
}

.titleWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-4xl);
  margin-bottom: var(--spacing-2xl);
}

.dummyButton {
  background-color: var(--color-gray50);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-md);
  border-radius: var(--radius-2xs);
  color: var(--color-white);
  margin-right: var(--spacing-smd);
}

.titleText {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-smd);
}

.dummyButtonWrapper {
  display: flex;
  align-items: center;
}

.arrowIcon {
  margin-top: -20px;
  margin-left: var(--spacing-mdl);
}

.dummyCardsWrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-mdl);
  max-width: 906px;

  @media screen and (max-width: 1257px) {
    justify-content: center;
  }
}

.dummyCard {
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 2px solid var(--color-gray50);
  border-radius: var(--radius-xs);
  padding: var(--spacing-xl);

  width: 100%;
  max-width: 443px;
  height: 110px;

  @media screen and (max-width: 1257px) {
    max-width: 350px;
  }
}

.dummyCardBottomRow {
  margin-top: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
}

.topSkeleton {
  width: 265px;
  height: 15px;
  background-color: var(--color-gray50);
  border-radius: var(--radius-2xs);
}

.smallSkeleton {
  width: 58px;
  height: 11px;
  background-color: var(--color-gray50);
  border-radius: var(--radius-2xs);
}
