/* eslint-disable react-hooks/exhaustive-deps */
import { Menu, Table, TableData } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaUsers } from 'react-icons/fa';
import { MdOutlinePersonAddDisabled } from 'react-icons/md';
import { RiDeleteBin5Fill, RiEditFill } from 'react-icons/ri';

import { GLOBAL_ERRORS } from '@/common/errors';
import { getStudentsDisplayName } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import ActionButton from '@/components/ActionButton/ActionButton';
import Checkbox from '@/components/Checkbox/Checkbox';
import ConductTestModal from '@/components/ConductTestModal/ConductTestModal';
import Icon from '@/components/Icon/Icon';
import GroupInfoCard from '@/components/InformationActionCard/GroupInfoCard/GroupInfoCard';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { GroupsContext } from '@/context/GroupsProvider';
import ConductSubTestsModal from '@/features/ConductSubTestsModal/ConductSubTestsModal';
import GROUPS from '@/services/students/groups';
import {
  SelectedEntityByIdAndDisplayedNameType,
  SingleGroupDetailsType,
} from '@/types/common';

import { TEACHER_ACTIONS_TRANSLATION_KEYS } from '../../consts';
import GridPlaceholder from '../GridPlaceholder/GridPlaceholder';
import SchoolLayout from '../SchoolLayout/SchoolLayout';
import AddGroupForm from './AddGroupForm';
import styles from './Groups.module.css';
import GroupStudentsModal from './GroupStudentsModal';

type ModalActionsTypes = 'view' | 'delete' | 'edit';

const Groups = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { areGroupsListLoading, groupsError, groupsList, isGroupsError } =
    GroupsContext();

  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const [selectedStudents, setSelectedStudents] = useState<
    SelectedEntityByIdAndDisplayedNameType[]
  >([]);

  const [isConductTestModalOpen, setIsConductTestModalOpen] = useState(false);

  const [selectedGroupIds, setSelectedGroupIds] = useState<string[]>([]);

  const [selectedGroupDetails, setSelectedGroupDetails] =
    useState<SingleGroupDetailsType | null>(null);
  const [alertModalType, setAlertModalType] =
    useState<ModalActionsTypes | null>(null);

  const [selectedMenuId, setSelectedMenuId] = useState<string | null>(null);
  const [searchWord, setSearchWord] = useState('');

  const filteredGroupsList =
    groupsList?.filter((item) =>
      item.name.toLowerCase().includes(searchWord.toLowerCase())
    ) || [];

  const areGroupsEmptyAndNotLoading =
    filteredGroupsList.length === 0 && !areGroupsListLoading;

  const handleCloseModalActions = (action: ModalActionsTypes) => {
    if (action === 'view') {
      setIsViewModalOpen(false);
    } else if (action === 'edit') {
      setIsEditModalOpen(false);
    } else {
      setAlertModalType(null);
    }
  };

  const actionsCallback = (action: ModalActionsTypes, revalidate: boolean) => {
    if (revalidate) {
      // invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUPS_LIST] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST],
      });
    }

    handleCloseModalActions(action);
  };

  const deleteGroupMutation = useMutation({
    mutationFn: GROUPS.DELETE_GROUP,
    onSuccess: () => {
      notifications.show({
        color: 'green',
        title: 'Success',
        message: t('delete-success'),
        autoClose: 2000,
      });

      actionsCallback('delete', true);
    },
    onError: (error) => {
      notifications.show({
        color: 'red',
        title: 'Error',
        message: t(error.message),
        autoClose: 2000,
      });
      actionsCallback('delete', false);
    },
  });

  const getStudentsByGroupIdMutation = useMutation({
    mutationFn: (id: string) => GROUPS.GET_GROUP_DETAILS(id),
    onSuccess: (res) => {
      if (res) {
        const studentsList = res.students.map((student) => {
          return {
            displayedName: getStudentsDisplayName({
              code: student.code || '',
              firstName: student.firstName,
              lastName: student.lastName,
            }),
            id: student.id,
          };
        });

        setSelectedStudents(studentsList);
        setIsConductTestModalOpen(true);
      }
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  const handleOpenModalActions = (
    action: ModalActionsTypes,
    selectedTeacherDetailsData: SingleGroupDetailsType | null
  ) => {
    setSelectedGroupDetails(selectedTeacherDetailsData);

    if (action === 'view') {
      setIsViewModalOpen(true);
    } else if (action === 'edit') {
      setIsEditModalOpen(true);
    } else {
      setAlertModalType(action);
    }
  };

  const handleMutationModalActions = (action: ModalActionsTypes) => {
    if (action === 'delete') {
      deleteGroupMutation.mutate({
        groupId: selectedGroupDetails?.id || '',
      });
    }
  };

  const menuTable = useMemo(() => {
    const selectedGroupDetailsData =
      filteredGroupsList?.find((el) => el.id === selectedMenuId) || null;

    return (
      <Menu.Dropdown className="menuDropdown">
        <Menu.Item
          onClick={() =>
            handleOpenModalActions('view', selectedGroupDetailsData)
          }
          leftSection={<FaUsers size={19} />}
        >
          {t('students-list')}
        </Menu.Item>

        <Menu.Item
          onClick={() => {
            setSelectedGroupDetails(selectedGroupDetailsData);
            handleOpenModalActions('edit', selectedGroupDetailsData);
          }}
          leftSection={<RiEditFill size={19} />}
        >
          {t('edit')}
        </Menu.Item>

        <Menu.Item
          onClick={() =>
            handleOpenModalActions('delete', selectedGroupDetailsData)
          }
          leftSection={
            <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
          }
        >
          {t(TEACHER_ACTIONS_TRANSLATION_KEYS.delete)}
        </Menu.Item>
      </Menu.Dropdown>
    );
  }, [selectedMenuId, t]);

  const getDropdownMenu = (el: SingleGroupDetailsType) => {
    return (
      <Menu
        opened={selectedMenuId === el.id}
        key={el.id}
        onChange={() => selectedMenuId === el.id && setSelectedMenuId(null)}
        shadow="md"
      >
        <Menu.Target>
          <div className={styles.dropDownWrapper}>
            <Icon
              name="MenuDotsSvg"
              onClick={() => {
                setSelectedMenuId(selectedMenuId === el.id ? null : el.id);
              }}
              color="turquoise"
              w={18}
              h={18}
            />
          </div>
        </Menu.Target>
        {menuTable}
      </Menu>
    );
  };

  const tableData: TableData = {
    head: [
      t('name-capital'),
      t('description-capital'),
      t('number-of-students-capital'),
      t('select-group-capital'),
      '',
    ],
    body: filteredGroupsList?.map((el) => [
      el.name,
      el.description,
      el.studentsCount,
      el.studentsCount < 1 ? (
        <MdOutlinePersonAddDisabled
          fontSize={18}
          color="var(--color-gray500)"
        />
      ) : (
        <Checkbox
          key={selectedGroupIds[0]}
          value={selectedGroupIds[0]}
          isChecked={selectedGroupIds.includes(el.id)}
          isDisabled={el.studentsCount < 1}
          onChange={() =>
            setSelectedGroupIds((prev) => (prev.includes(el.id) ? [] : [el.id]))
          }
        />
      ),
      getDropdownMenu(el),
    ]),
  };

  const CARDS_VIEW_COMPONENT = (
    <GridPlaceholder
      isPending={areGroupsListLoading}
      cardsComponent={filteredGroupsList?.map((el) => {
        const isCardChecked = selectedGroupIds.includes(el.id);

        return (
          <GroupInfoCard
            key={el.id}
            {...(el.studentsCount > 0 && { value: el.id })}
            title={el.name}
            studentsCount={el.studentsCount}
            actionComponent={getDropdownMenu(el)}
            isLoading={areGroupsListLoading}
            isChecked={isCardChecked}
            isDisabled={deleteGroupMutation.isPending}
            {...(el.studentsCount > 0 && {
              onChange: (v) =>
                setSelectedGroupIds((prev) =>
                  prev.includes(v) ? prev.filter((item) => item !== v) : [v]
                ),
            })}
          />
        );
      })}
    />
  );

  const TABLE_VIEW_COMPONENT = areGroupsListLoading ? (
    <TableSkeleton numberOfColumns={tableData.head?.length} />
  ) : (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
      }}
    />
  );

  const ACTIONS_COMPONENT = (
    <ActionButton
      onClick={() => {
        getStudentsByGroupIdMutation.mutate(selectedGroupIds[0]);
      }}
      type="conductTest"
      isDisabled={selectedGroupIds.length < 1}
      toolTip={t('no-selected-students')}
      isLoading={getStudentsByGroupIdMutation.isPending}
    />
  );

  if (
    isGroupsError &&
    groupsError?.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS
  ) {
    return (
      <div className={styles.errorWrapper}>
        <Text
          transKey="unauthorized-access"
          type="h3"
          fw={400}
          mt={60}
          align="center"
        />
      </div>
    );
  }

  return (
    <>
      <SchoolLayout
        type="groups"
        numberOfSearchResults={filteredGroupsList?.length || 0}
        cardsContent={CARDS_VIEW_COMPONENT}
        listContent={TABLE_VIEW_COMPONENT}
        onSearch={(value) => {
          setSearchWord(value);
        }}
        actionsContent={ACTIONS_COMPONENT}
        isNoResultsVisible={areGroupsEmptyAndNotLoading}
        searchWord={searchWord}
        searchInputTransKey="search-group"
        isNoDataListDisplayed={
          filteredGroupsList?.length === 0 &&
          !areGroupsListLoading &&
          searchWord.length === 0
        }
      />

      <AlertDialog
        isOpen={alertModalType !== null}
        title="delete-group"
        description="delete-group-confirmation"
        transDescriptionVariables={{
          name: selectedGroupDetails?.name || '',
        }}
        variant="danger"
        onCancel={() => handleCloseModalActions('delete')}
        onConfirmAction={() => handleMutationModalActions('delete')}
        isActionInProgress={deleteGroupMutation.isPending}
      />

      <PrimaryModal
        isOpen={isEditModalOpen}
        content={
          <AddGroupForm
            onClose={() => handleCloseModalActions('edit')}
            selectedGroup={selectedGroupDetails}
          />
        }
      />

      {selectedGroupDetails && (
        <GroupStudentsModal
          isModalVisible={isViewModalOpen}
          onClose={() => handleCloseModalActions('view')}
          selectedGroup={selectedGroupDetails}
        />
      )}

      {selectedStudents.length > 0 && (
        <ConductTestModal
          key={selectedStudents.length}
          isOpen={isConductTestModalOpen}
          onCloseModal={() => {
            setIsConductTestModalOpen(false);
          }}
          selectedStudents={selectedStudents}
          selectedGroupId={selectedGroupIds[0]}
        />
      )}

      <ConductSubTestsModal />
    </>
  );
};

export default Groups;
