import { Input, Table, TableData } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { getFormattedDate } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Checkbox from '@/components/Checkbox/Checkbox';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import GROUPS from '@/services/students/groups';
import { GroupDetailsType, SingleGroupDetailsType } from '@/types/common';

import styles from './GroupStudentsModal.module.css';

type GroupStudentsModalProps = {
  isModalVisible: boolean;
  onClose: () => void;
  selectedGroup: SingleGroupDetailsType;
};

const GroupStudentsModal = ({
  isModalVisible,
  onClose,
  selectedGroup,
}: GroupStudentsModalProps) => {
  const { i18n, t } = useTranslation();

  const queryClient = useQueryClient();

  const [selectedStudentsForRemoval, setSelectedStudentsForRemoval] = useState<
    string[]
  >([]);

  const { isSchoolRole } = UserContext();

  const [searchWord, setSearchWord] = useState<string>('');

  const areStudentsAssigned = selectedGroup && selectedGroup.studentsCount > 0;

  const { data, isLoading: isLoadingStudentsList } =
    useQuery<GroupDetailsType | null>({
      queryFn: () => GROUPS.GET_GROUP_DETAILS(selectedGroup.id),
      queryKey: [QUERY_KEYS.GROUP_BY_ID, selectedGroup.id],
      staleTime: 10000,
      enabled: Boolean(selectedGroup.id),
    });

  const handleClose = () => {
    setSelectedStudentsForRemoval([]);
    onClose();
  };

  const removeStudentsMutation = useMutation({
    mutationFn: GROUPS.REMOVE_STUDENTS_FROM_GROUP,
    onSuccess: () => {
      notifications.show({
        color: 'green',
        title: 'Success',
        message: t('delete-success'),
        autoClose: 2000,
      });

      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUPS_LIST] });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.GROUP_BY_ID, selectedGroup.id],
      });

      handleClose();
    },
    onError: (error) => {
      notifications.show({
        color: 'red',
        title: 'Error',
        message: t(error.message),
        autoClose: 2000,
      });
    },
  });

  const filteredData =
    data?.students?.filter((student) => {
      return (
        student.firstName?.toLowerCase().includes(searchWord.toLowerCase()) ||
        student.lastName?.toLowerCase().includes(searchWord.toLowerCase()) ||
        student.code?.toLowerCase().includes(searchWord.toLowerCase())
      );
    }) || [];

  const tableData: TableData = {
    head: [
      t('first-name-capital'),
      t('last-name-capital'),
      t('code-capital'),
      t('grade-capital'),
      // t('date-of-birth-capital'),
      ' ',
    ],
    body: filteredData.map((student) => [
      student.firstName || '-',
      student.lastName || '-',
      student.code || '-',
      student.grade || '-',
      // student.dateOfBirth
      //   ? getFormattedDate(i18n.language, student.dateOfBirth)
      //   : '-',
      !isSchoolRole ? (
        <Checkbox
          key={student.id}
          isChecked={selectedStudentsForRemoval.includes(student.id)}
          value={student.id}
          onChange={() => {
            setSelectedStudentsForRemoval((prev) => {
              if (prev.includes(student.id)) {
                return prev.filter((id) => id !== student.id);
              }
              return [...prev, student.id];
            });
          }}
        />
      ) : (
        ''
      ),
    ]),
  };

  return (
    <PrimaryModal
      isOpen={isModalVisible}
      content={
        <Card
          size="2xl"
          bg="gray50"
          hasDynamicHeight
          className={styles.wrapper}
        >
          <div className={styles.header}>
            <div className={styles.titleWrapper}>
              <Text untranslatedText={selectedGroup?.name} type="h3" />

              <div className={styles.row}>
                <Text
                  transKey="students-capital"
                  type="label"
                  fw={500}
                  mr={6}
                  mt={2}
                />
                <Text
                  untranslatedText={` ${selectedGroup?.studentsCount}`}
                  type="body2"
                  fw={700}
                />
              </div>
            </div>

            <div className={styles.buttonsWrapper}>
              <Button
                transKey="remove-from-group"
                type="button"
                variant="danger"
                onClick={() =>
                  removeStudentsMutation.mutate({
                    studentsList: selectedStudentsForRemoval,
                    groupId: selectedGroup?.id || '',
                  })
                }
                isDisabled={selectedStudentsForRemoval.length === 0}
                isLoading={removeStudentsMutation.isPending}
              />

              <CloseButton onClick={handleClose} variant="outlined" />
            </div>
          </div>
          <Card
            bg="white"
            className={`${styles.content} ${!areStudentsAssigned && styles.center}`}
          >
            {areStudentsAssigned ? (
              <div>
                <div className={styles.inputWrapper}>
                  <Input
                    value={searchWord}
                    variant="filled"
                    placeholder={t('search-students')}
                    onChange={(e) => setSearchWord(e.currentTarget.value)}
                    rightSection={
                      <div className={styles.inputRightSection}>
                        <Icon name="StudentSvg" color="black" size="sm" />

                        <Text
                          untranslatedText={`${filteredData.length || '0'}`}
                          type="body2"
                        />
                      </div>
                    }
                  />
                </div>

                <div className={styles.tableWrapper}>
                  {isLoadingStudentsList ? (
                    <TableSkeleton
                      numberOfColumns={tableData.head?.length}
                      numberOfRows={6}
                    />
                  ) : (
                    <Table data={tableData} highlightOnHover />
                  )}
                </div>

                {filteredData.length === 0 && (
                  <div>
                    <Text
                      transKey="no-search-results"
                      type="body1"
                      align="center"
                      color="gray500"
                    />
                  </div>
                )}
              </div>
            ) : (
              <div className={styles.noStudentsContent}>
                <Text
                  transKey="no-group-students"
                  type="body1"
                  align="center"
                  color="gray500"
                />
              </div>
            )}
          </Card>
        </Card>
      }
    />
  );
};

export default GroupStudentsModal;
