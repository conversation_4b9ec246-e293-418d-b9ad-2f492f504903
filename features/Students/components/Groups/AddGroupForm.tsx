import { Textarea, TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import GROUPS from '@/services/students/groups';
import { SingleGroupDetailsType } from '@/types/common';

import styles from './AddGroupForm.module.css';

type AddGroupFormProps = {
  onClose: () => void;
  selectedGroup?: SingleGroupDetailsType | null;
};

const AddGroupForm = ({ onClose, selectedGroup }: AddGroupFormProps) => {
  const { t } = useTranslation();

  const [groupName, setGroupName] = useState<string>(selectedGroup?.name || '');
  const [description, setDescription] = useState<string>(
    selectedGroup?.description || ''
  );
  const [errorMessage, setErrorMessage] = useState<'required' | null>(null);
  const queryClient = useQueryClient();

  const addGroupMutation = useMutation({
    mutationFn: GROUPS.ADD_GROUP,
    onSuccess: () => {
      notifications.show({
        message: t('group-added-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUPS_LIST] });

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const editGroupMutation = useMutation({
    mutationFn: GROUPS.UPDATE_GROUP,
    onSuccess: () => {
      notifications.show({
        message: t('group-changes-saved-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUPS_LIST] });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.INDEPENDENT_TEACHER_STUDENT_LIST],
      });

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const handleSubmitData = () => {
    if (!groupName) {
      setErrorMessage('required');
    } else if (selectedGroup) {
      editGroupMutation.mutate({
        groupId: selectedGroup.id,
        name: groupName,
        description,
      });
    } else
      addGroupMutation.mutate({
        name: groupName,
        description,
      });
  };

  return (
    <Card size="2xl" bg="gray50" hasDynamicHeight className={styles.wrapper}>
      <div className={styles.header}>
        <Text
          transKey={selectedGroup ? 'edit-group' : 'add-new-group'}
          type="h3"
        />

        <div className={styles.buttonsWrapper}>
          <Button
            transKey={selectedGroup ? 'save-capital' : 'add-group-capital'}
            type="button"
            onClick={handleSubmitData}
            isDisabled={errorMessage !== null}
            isLoading={
              addGroupMutation.isPending || editGroupMutation.isPending
            }
          />

          <CloseButton onClick={onClose} variant="outlined" />
        </div>
      </div>
      <Card bg="white">
        <TextInput
          defaultValue={groupName}
          placeholder={t('group-name')}
          mb={16}
          error={errorMessage && t(errorMessage)}
          onChange={(e) => {
            setGroupName(e.target.value);

            if (errorMessage) setErrorMessage(null);
          }}
        />

        <Textarea
          defaultValue={description}
          placeholder={t('group-description')}
          mb={16}
          autosize
          minRows={3}
          maxRows={5}
          onChange={(e) => {
            setDescription(e.target.value);

            if (errorMessage) setErrorMessage(null);
          }}
        />
      </Card>
    </Card>
  );
};

export default AddGroupForm;
