import Router from 'next/router';

// import { useTranslation } from 'react-i18next';
import { GLOBAL_ERRORS } from '@/common/errors';
import Button from '@/components/Button/Button';
import request from '@/modules/api';

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const Assistant = (): JSX.Element => {
  // const { t } = useTranslation();

  const redirect = async () => {
    try {
      const response = await request(
        `${BASE_API_URL}/integrations/mathproresearch/url-link/login`
      );

      if (response.hasError) {
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }

      const { url } = response.data;

      Router.push(url);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        fontSize: '28px',
        color: 'var(--color-blue)',
        fontWeight: 'bold',
      }}
    >
      {/* {t('Under construction..')} */}
      <Button untranslatedText="redirect" size="md" onClick={redirect} />
    </div>
  );
};

export default Assistant;
