.wrapper {
  height: calc(100vh - 224px);
  overflow-y: scroll;
  overflow-x: hidden;
}

.profile-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.profileNameAndAffiliation {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.segmented-wrapper {
  max-width: 1000px;
  width: 100%;
  display: flex;
  gap: var(--spacing-4xl);
  justify-content: space-between;
}
