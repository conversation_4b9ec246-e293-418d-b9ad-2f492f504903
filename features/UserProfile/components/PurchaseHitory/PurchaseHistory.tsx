import { Pagination, Table, TableData } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
// import axios from 'axios';
// import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { CURRENCY_SYMBOLS, PRODUCTS } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import { getFormattedDate, getFormattedPrice } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import PURCHASES from '@/services/purchases';
import { PurchasesHistoryType } from '@/types/common';

import s from './PurchaseHistory.module.css';

const FETCH_DATA_LIMIT = 100;

const PurchaseHistory = ({ userId }: { userId?: string }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { i18n } = useTranslation();
  const { userRoles } = UserContext();
  const [activePage, setActivePage] = useState(1);

  const isAllowedToFetchPurchaseHistory =
    userRoles.isSchoolAdmin || userRoles.isIndependentTeacher;

  const { data, error, isError, isLoading } =
    useQuery<PurchasesHistoryType | null>({
      queryFn: () =>
        PURCHASES.GET_PURCHASES_HISTORY({
          limit: FETCH_DATA_LIMIT,
          page: activePage,
          ...(userId && { userId }),
        }),
      queryKey: [QUERY_KEYS.PURCHASE_HISTORY_LIST, activePage, userId],
      staleTime: 1000 * 60 * 60 * 24,
      enabled: Boolean(router.isReady) && isAllowedToFetchPurchaseHistory,
    });

  const purchaseHistoryList = data?.results || [];

  const downloadReceipt = async (receiptUrl: string) => {
    window.open(receiptUrl, '_blank');
  };

  const tableData: TableData = {
    head: [
      t('product-capital'),
      t('date-capital'),
      t('price-capital'),
      t('receipt-capital'),
    ],
    body: purchaseHistoryList.map((purchaseHistoryItem) => {
      const { product, purchaseDate, receiptUrl } = purchaseHistoryItem;

      return [
        PRODUCTS[product.metadata.type].typeName,
        getFormattedDate(i18n.language, purchaseDate),
        `${CURRENCY_SYMBOLS[product.price.currency]}${getFormattedPrice(product.price.amount)}`,
        <div key={product.id} className="cursorPointer hoverOpacity">
          <Text
            transKey="view"
            color="darkerBlue"
            onClick={() => downloadReceipt(receiptUrl)}
          />
        </div>,
      ];
    }),
  };

  const displayedContent =
    purchaseHistoryList.length < 1 ||
    (isError && error.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS) ? (
      <div
        className={`${s.noPurchaseHistory} ${userId && s.modalDisplayNoPurchase}`}
      >
        <Text transKey="no-purchase-history" type="h3" color="turquoise" />
      </div>
    ) : (
      <div className={`${s.tableWrapper} ${userId && s.modalDisplayTable}`}>
        <Table
          data={tableData}
          stickyHeader
          stickyHeaderOffset={-1}
          highlightOnHover
          styles={{
            thead: {
              border: 'none',
            },
          }}
        />

        <Pagination
          total={Math.ceil((data?.count || 1) / FETCH_DATA_LIMIT)}
          value={activePage}
          hideWithOnePage
          withControls={false}
          onChange={(value) => setActivePage(value)}
        />
      </div>
    );

  return isLoading ||
    (isError && error.message !== GLOBAL_ERRORS.UNAUTHORIZED_ACCESS) ? (
    <div className={s.tableWrapper}>
      <TableSkeleton
        numberOfColumns={tableData?.head?.length}
        numberOfRows={userId ? 9 : undefined}
      />
    </div>
  ) : (
    displayedContent
  );
};

export default PurchaseHistory;
