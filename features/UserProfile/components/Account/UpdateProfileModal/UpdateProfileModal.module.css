.wrapper {
  width: 100%;
  height: 100%;
  transition: all 0.2s ease;
}

.infoWrapper {
  max-width: 1000px;
  height: 641px;
  max-height: 700px;
}

.billingWrapper {
  max-width: 940px;
  height: 585px;
  max-height: 640px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  min-height: 50px;
}

.header-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.body {
  display: flex;
  gap: var(--spacing-2xl);
}

.photoWrapper {
  max-width: 350px;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-mdl);
}

.uploadHeaderWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xs);
  position: relative;
}

.profile-photo-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-xl);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: none;

  &:hover {
    background-color: transparent;

    & p,
    & svg,
    & img {
      opacity: 0.7;
    }
  }
}

.imageWrapper {
  position: relative;
}

.deleteButton {
  position: absolute;
  right: calc(50% - 70px);
  top: 170px;
  z-index: 1;
  cursor: pointer;
  border-radius: 50%;
  background-color: var(--color-gray50);
  outline: 2px solid var(--color-white);

  &:hover {
    background-color: var(--color-gray200);
  }
}

.placeholderIcon {
  cursor: pointer !important;
  width: 120px;
  height: 120px;
}

.imageAvatar {
  border-radius: 50%;
  overflow: hidden;
}

.row {
  display: flex;
  gap: var(--spacing-md);
}
