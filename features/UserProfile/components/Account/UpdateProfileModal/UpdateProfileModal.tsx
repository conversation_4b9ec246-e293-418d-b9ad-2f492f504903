/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { zodResolver } from '@hookform/resolvers/zod';
import { ActionIcon, TextInput } from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import Image from 'next/image';
import { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import { mantineDateParser } from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import PROFILES from '@/services/profiles';
import { ProfileFormValuesType } from '@/types/common';
import { PROFILE_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import s from './UpdateProfileModal.module.css';

type UpdateProfileModalPropsType = {
  isOpen: boolean;
  onClose: () => void;
};

const UpdateProfileModal = ({
  isOpen,
  onClose,
}: UpdateProfileModalPropsType): JSX.Element => {
  const { isSchoolRole, user, userRoles } = UserContext();
  const { t } = useTranslation();
  const { updateUserProfile } = UserContext();

  const { profilePicture } = user.profile;

  const defaultImageToUpload = profilePicture || null;

  const [imageToUpload, setImageToUpload] = useState<
    | {
        imageFile: File;
        fileName: string;
      }
    | string
    | null
  >(defaultImageToUpload);

  const [imageImportError, setImageImportError] = useState<
    'file-too-large' | 'file-invalid-type' | null
  >(null);

  const dropZoneRef = useRef<() => void>(null);

  const DEFAULT_FORM_VALUES = {
    firstName: user.profile.firstName,
    lastName: user.profile.lastName,
    dateOfBirth: user.profile.dob ? new Date(user.profile.dob) : null,
    phoneNumber: user.profile.phoneNumber || '',
    organisation: isSchoolRole
      ? user.school?.name || ''
      : user.profile.organisation || '',
    affiliation: user.profile.affiliation || '',
  };

  const {
    control,
    formState: { errors },
    getValues,
    handleSubmit,
    register,
    reset,
    watch,
  } = useForm<ProfileFormValuesType>({
    resolver: zodResolver(PROFILE_FORM_SCHEMA),
    defaultValues: DEFAULT_FORM_VALUES,
    mode: 'onSubmit',
  });

  const updateProfileMutation = useMutation({
    mutationFn: PROFILES.UPDATE_USER_PROFILE,
    onError: () => {
      // TODO : handle error messages
      //   if (error.message === 'invalid-username-or-password') {
      //     setShowErrorAlert(true);
      //     return;
      //   }
      //   notifications.show({
      //     title: t(error.message),
      //     message: '',
      //     color: 'red',
      //   });
    },
    onSuccess: (res) => {
      if (res) {
        updateUserProfile(res);
        onClose();
        setImageImportError(null);
      }
    },
  });

  const uploadProfilePicture = useMutation({
    mutationFn: PROFILES.UPLOAD_PROFILE_PICTURE,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });
    },
    onSuccess: (res) => {
      if (res) {
        // Get the current form values
        const formData = getValues();

        updateProfileMutation.mutate({
          ...formData,
          profilePicture: res,
        });
        updateUserProfile(res);
        onClose();
        setImageImportError(null);
      }
    },
  });

  const onSubmit = async (values: ProfileFormValuesType) => {
    if (imageToUpload && typeof imageToUpload !== 'string') {
      uploadProfilePicture.mutate({ profileImage: imageToUpload });
    } else {
      if (imageToUpload === null) {
        values.profilePicture = null;
      }

      updateProfileMutation.mutate(values);
    }
  };

  const displayedAvatarImage = () => {
    if (imageToUpload) {
      if (typeof imageToUpload === 'string') {
        return imageToUpload;
      }
      return URL.createObjectURL(imageToUpload.imageFile as File);
    }
    return defaultImageToUpload;
  };

  return (
    <PrimaryModal
      isOpen={isOpen}
      content={
        <Card
          size="2xl"
          bg="gray50"
          isLoading={updateProfileMutation.isPending}
          className={s.wrapper}
          hasDynamicHeight
        >
          <div className={s.header}>
            <Text transKey="edit-your-profile" type="h3" />

            <div className={s['header-actions']}>
              <Button
                transKey="save-capital"
                type="submit"
                onClick={handleSubmit(onSubmit)}
                isLoading={
                  updateProfileMutation.isPending ||
                  uploadProfilePicture.isPending
                }
              />

              <CloseButton
                onClick={() => {
                  onClose();
                  reset(DEFAULT_FORM_VALUES);
                  setImageToUpload(defaultImageToUpload);
                  setImageImportError(null);
                }}
                variant="outlined"
              />
            </div>
          </div>

          <div className={s.body}>
            <Card size="xl" className={s.photoWrapper}>
              <div className={s.uploadHeaderWrapper}>
                <Text transKey="photo" type="h4" color="black" fw={300} />

                {imageToUpload && (
                  <ActionIcon
                    variant="filled"
                    color="transparent"
                    size="lg"
                    aria-label="Delete"
                    className={s.deleteButton}
                    onClick={(e) => {
                      e.stopPropagation();
                      setImageToUpload(null);
                    }}
                  >
                    <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
                  </ActionIcon>
                )}
              </div>

              <Dropzone
                className={s['profile-photo-wrapper']}
                onDrop={(file) => {
                  const fileName = file[0].name;

                  if (imageImportError) setImageImportError(null);

                  setImageToUpload({ fileName, imageFile: file[0] });
                }}
                accept={IMAGE_MIME_TYPE}
                openRef={dropZoneRef}
                activateOnClick
                useFsAccessApi
                maxFiles={1}
                maxSize={5 * 1024 * 1024}
                onReject={(file) => {
                  setImageImportError(
                    file[0].errors[0].code === 'file-too-large'
                      ? 'file-too-large'
                      : 'file-invalid-type'
                  );
                }}
                disabled={
                  updateProfileMutation.isPending ||
                  uploadProfilePicture.isPending
                }
                styles={{
                  inner: {
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  },
                  root: {
                    paddingTop: '24px',
                    paddingBottom: '24px',
                  },
                }}
              >
                {imageToUpload ? (
                  <Image
                    src={displayedAvatarImage() || ''}
                    alt="profile photo"
                    width={120}
                    height={120}
                    className={s.imageAvatar}
                  />
                ) : (
                  <Icon
                    name="ProfilePlaceholderSvg"
                    color="turquoise"
                    className={s.placeholderIcon}
                  />
                )}

                {imageImportError && (
                  <Text
                    transKey={imageImportError}
                    type="label"
                    color="danger"
                    mt={12}
                  />
                )}

                <Text
                  transKey="upload-photo"
                  type="button"
                  color="blue"
                  mt={42}
                />
              </Dropzone>
            </Card>

            <Card size="xl">
              <Text
                transKey="basic-info"
                type="h4"
                color="black"
                fw={300}
                mb={16}
              />

              <form id="profile-form" className={s['profile-form']}>
                <TextInput
                  placeholder={t('name')}
                  label={t('name')}
                  error={t(errors.firstName?.message || '')}
                  {...register('firstName')}
                />

                <TextInput
                  label={t('surname')}
                  placeholder={t('surname')}
                  error={t(errors.lastName?.message || '')}
                  {...register('lastName')}
                />

                <Controller
                  name="dateOfBirth"
                  control={control}
                  render={({ field }) => (
                    <DateInput
                      label={t('date-of-birth')}
                      placeholder={t('date-of-birth')}
                      clearable
                      allowDeselect
                      highlightToday
                      dateParser={mantineDateParser}
                      value={watch('dateOfBirth')}
                      valueFormat="DD/MM/YYYY"
                      onChange={(v) => {
                        field.onChange(new Date(v as Date));
                      }}
                    />
                  )}
                />

                <TextInput
                  label={t('phone')}
                  placeholder={t('phone')}
                  {...register('phoneNumber')}
                />

                <TextInput
                  label={t('affiliation')}
                  placeholder={t('affiliation')}
                  {...register('affiliation')}
                  maxLength={40}
                />

                <TextInput
                  label={isSchoolRole ? t('school') : t('organisation')}
                  placeholder={isSchoolRole ? t('school') : t('organisation')}
                  disabled={
                    userRoles.isSchoolAdmin || userRoles.isSchoolTeacher
                  }
                  {...register('organisation')}
                />
              </form>
            </Card>
          </div>
        </Card>
      }
    />
  );
};

export default UpdateProfileModal;
