import { Box } from '@mantine/core';
import { JSX } from 'react';

import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import Text from '@/components/Text/Text';

import s from './AccountFooter.module.css';

type AccountFooterPropsType = {
  customerId: string;
  isDeleteAccountModalOpen: boolean;
  onDeleteAccount: () => void;
  onConfirmDeletion: () => void;
  onClose: () => void;
  isActionInProgress?: boolean;
};

const AccountFooter = ({
  customerId,
  isActionInProgress = false,
  isDeleteAccountModalOpen,
  onClose,
  onConfirmDeletion,
  onDeleteAccount,
}: AccountFooterPropsType): JSX.Element => {
  return (
    <div>
      <AlertDialog
        isOpen={isDeleteAccountModalOpen}
        onConfirmAction={onConfirmDeletion}
        title="delete-account-confirmation"
        description="delete-account-warning"
        onCancel={onClose}
        isActionInProgress={isActionInProgress}
        variant="danger"
        confirmLabel="delete-account-capital"
      />

      <div className={s.footer}>
        <div className={s['customer-id']}>
          <Text transKey="customer-id-capital" type="label" />
          <Text untranslatedText={customerId} type="label" isBold />
        </div>

        <Box className={s['delete-account']} onClick={onDeleteAccount}>
          <Text
            transKey="delete-account-capital"
            type="body2"
            isBold
            color="danger"
          />
        </Box>
      </div>
    </div>
  );
};

export default AccountFooter;
