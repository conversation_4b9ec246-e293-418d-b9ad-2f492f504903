import { LoadingOverlay } from '@mantine/core';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import { SUPPORTED_LANGUAGES } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import PROFILES from '@/services/profiles';

const LanguageCard = (): JSX.Element => {
  const { i18n, t } = useTranslation();
  const { updateUserProfile } = UserContext();
  const queryClient = useQueryClient();
  const currentLanguage = i18n.language;

  const updateLanguageProfileMutation = useMutation({
    mutationFn: PROFILES.UPDATE_USER_LANGUAGE,

    onError: (error) => {},
    onSuccess: (res, lang) => {
      if (res) {
        i18n.changeLanguage(lang);

        updateUserProfile({
          language: lang,
        });

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.CERTIFICATION_PRODUCTS],
        });

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.CERTIFICATION_TEST_DETAILS_BY_TEST_TYPE],
        });
      }
    },
  });

  const onChangeLanguage = (lang: string) => {
    updateLanguageProfileMutation.mutate(lang);
  };

  return (
    <Card size="2xl" bg="gray50" overflow="visible">
      <LoadingOverlay
        visible={updateLanguageProfileMutation.isPending}
        zIndex={10}
        overlayProps={{ radius: 'sm', blur: 2 }}
        pos="fixed"
      />

      <div>
        <Text transKey="language" type="h4" />

        <br />

        <SelectDropdown
          value={currentLanguage}
          data={Array.from(
            Object.entries(SUPPORTED_LANGUAGES),
            ([key, value]) => ({
              label: value.name,
              value: value.shortCode,
            })
          )}
          onChange={(v) => onChangeLanguage(v)}
          placeholder="Language"
        />
      </div>
    </Card>
  );
};

export default LanguageCard;
