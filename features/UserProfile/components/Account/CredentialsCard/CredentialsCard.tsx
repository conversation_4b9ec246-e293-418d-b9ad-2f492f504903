import { Box, HoverCard, TextInput } from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';

import ChangePassword from './ChangePassword/ChangePassword';
import s from './CredentialsCard.module.css';

type CredentialsCardPropsType = {
  email: string;
  contactEmail: string;
  onChangeEmail: () => void;
};

const CredentialsCard = ({
  contactEmail,
  email,
  onChangeEmail,
}: CredentialsCardPropsType): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Card size="2xl" bg="gray50">
      <div className={s.wrapper}>
        <div className={s['input-wrapper']}>
          <Box>
            <TextInput label={t('email')} defaultValue={email} disabled />
          </Box>
          {/* <HoverCard width={300} shadow="md">
            <HoverCard.Target>
              <Box>
                <TextInput label={t('email')} defaultValue={email} disabled />
              </Box>
            </HoverCard.Target>

            <HoverCard.Dropdown>
              <Text
                transKey="contact-to-change-email"
                type="subTitle2"
                transVariables={{ email: contactEmail }}
              />
            </HoverCard.Dropdown>
          </HoverCard> */}

          <Box
            style={{
              alignSelf: 'flex-end',
              cursor: 'pointer',
            }}
            onClick={onChangeEmail}
          >
            <Text transKey="change-email-capital" type="body2" color="blue" />
          </Box>
        </div>

        <ChangePassword />
      </div>
    </Card>
  );
};

export default CredentialsCard;
