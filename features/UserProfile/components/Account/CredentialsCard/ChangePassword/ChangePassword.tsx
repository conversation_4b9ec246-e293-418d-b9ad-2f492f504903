import { Box, TextInput } from '@mantine/core';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Text from '@/components/Text/Text';

import s from './ChangePassword.module.css';
import ChangePasswordModal from './ChangePasswordModal/ChangePasswordModal';

const ChangePassword = (): JSX.Element => {
  const { t } = useTranslation();
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =
    useState<boolean>(false);

  return (
    <>
      <div className={s.inputWrapper}>
        <TextInput
          label={t('password')}
          defaultValue="*****************"
          disabled
        />

        <Box
          onClick={() => setIsChangePasswordModalOpen(true)}
          className={s.changePasswordWrapper}
          style={{
            alignSelf: 'flex-end',
            userSelect: 'none',
            cursor: 'pointer',
          }}
        >
          <Text transKey="change-password-capital" type="body2" color="blue" />
        </Box>
      </div>

      <ChangePasswordModal
        isOpen={isChangePasswordModalOpen}
        onClose={() => setIsChangePasswordModalOpen(false)}
      />
    </>
  );
};

export default ChangePassword;
