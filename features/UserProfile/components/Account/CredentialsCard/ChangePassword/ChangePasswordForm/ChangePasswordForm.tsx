import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { PASSWORD_REQUIREMENTS } from '@/common/consts';
import Button from '@/components/Button/Button';
import PasscodeInput from '@/components/PasscodeInput/PasscodeInput';
import AUTH from '@/services/auth';
import { ResetUserPasswordFormValuesType } from '@/types/common';
import { RESET_USER_PASSWORD_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import s from './ChangePasswordForm.module.css';

type ChangePasswordFormProps = {
  onClose: () => void;
};

const DEFAULT_FORM_VALUES: ResetUserPasswordFormValuesType = {
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
};

const ChangePasswordForm = ({
  onClose,
}: ChangePasswordFormProps): JSX.Element => {
  const { t } = useTranslation();
  const [arePasswordRequirementsMet, setArePasswordRequirementsMet] =
    useState(false);

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<ResetUserPasswordFormValuesType>({
    resolver: zodResolver(RESET_USER_PASSWORD_FORM_SCHEMA),
    defaultValues: DEFAULT_FORM_VALUES,
    mode: 'onSubmit',
  });

  const isSubmitButtonDisabled =
    !arePasswordRequirementsMet ||
    watch('newPassword') !== watch('confirmPassword') ||
    !watch('newPassword') ||
    !watch('confirmPassword');

  const changePasswordMutation = useMutation({
    mutationFn: AUTH.CHANGE_USER_PASSWORD,
    onError: (error) => {
      if (error.message) {
        notifications.show({
          title: t(error.message),
          message: '',
          color: 'red',
        });
      }
    },
    onSuccess: () => {
      notifications.show({
        title: t('password-changed-successfully'),
        message: '',
        color: 'green',
      });
      onClose();
    },
  });

  const onSubmit = async (values: ResetUserPasswordFormValuesType) => {
    const { currentPassword, newPassword } = values;

    if (currentPassword && newPassword) {
      changePasswordMutation.mutate({ currentPassword, newPassword });
    }
  };

  return (
    <div>
      <div className={s.formElement}>
        <TextInput
          placeholder={t('type-your-current-password')}
          type="password"
          error={t(errors.currentPassword?.message || '')}
          {...register('currentPassword')}
        />
      </div>

      <div className={s.formElement}>
        <Controller
          name="newPassword"
          control={control}
          rules={{ required: t('new-password-required') }}
          render={({ field }) => (
            <PasscodeInput
              placeholderText={t('new-password')}
              value={field.value}
              onChange={(v, meetsRequirements) => {
                field.onChange(v);
                setArePasswordRequirementsMet(meetsRequirements);
              }}
              requirements={PASSWORD_REQUIREMENTS}
              error={t(errors.newPassword?.message || '')}
            />
          )}
        />
      </div>

      <div className={s.formElement}>
        <Controller
          name="confirmPassword"
          control={control}
          rules={{ required: t('confirm-new-password-required') }}
          render={({ field }) => (
            <PasscodeInput
              placeholderText={t('confirm-new-password')}
              value={field.value}
              type="confirm-password"
              onChange={(v) => {
                field.onChange(v);
              }}
              requirements={[
                {
                  re: new RegExp(
                    `^${watch('newPassword')?.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') || '#'}$`
                  ),
                  label: 'matches-new-password',
                },
              ]}
              error={t(errors.confirmPassword?.message || '')}
            />
          )}
        />
      </div>

      <div className={s.actions}>
        <Button
          transKey="cancel-capital"
          type="submit"
          onClick={onClose}
          isDisabled={changePasswordMutation.isPending}
        />

        <Button
          transKey="change-capital"
          type="submit"
          onClick={handleSubmit(onSubmit)}
          isLoading={changePasswordMutation.isPending}
          isDisabled={isSubmitButtonDisabled}
        />
      </div>
    </div>
  );
};

export default ChangePasswordForm;
