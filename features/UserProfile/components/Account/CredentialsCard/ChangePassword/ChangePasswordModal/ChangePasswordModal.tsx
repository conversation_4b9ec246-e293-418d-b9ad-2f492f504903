import { JSX } from 'react';

import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';

import ChangePasswordForm from '../ChangePasswordForm/ChangePasswordForm';
import s from './ChangePasswordModal.module.css';

type ChangePasswordModal = {
  isOpen: boolean;
  onClose: () => void;
};

const ChangePasswordModal = ({
  isOpen,
  onClose,
}: ChangePasswordModal): JSX.Element => {
  return (
    <PrimaryModal
      isOpen={isOpen}
      content={
        <Card
          size="2xl"
          bg="gray50"
          isLoading={false}
          className={s.wrapper}
          hasDynamicHeight
        >
          <div className={s.header}>
            <Text transKey="reset-your-password" type="h3" />

            <div className={s['header-actions']}>
              <CloseButton onClick={onClose} variant="outlined" />
            </div>
          </div>

          <ChangePasswordForm onClose={onClose} />
        </Card>
      }
    />
  );
};

export default ChangePasswordModal;
