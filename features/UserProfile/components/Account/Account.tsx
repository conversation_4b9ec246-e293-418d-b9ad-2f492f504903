import { em, Grid } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { COMPANY_DETAILS, MONTHS_BY_NUMBER } from '@/common/consts';
import { getDateDetails, padZero } from '@/common/helpers';
import DetailedCertificationsCard from '@/components/DetailedCertificationsCard/DetailedCertificationsCard';
import LicensesCard from '@/components/LicensesCard/LicensesCard';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PersonalInfoCard from '@/components/PersonalInfoCard/PersonalInfoCard';
import { UserContext } from '@/context/UserProvider';

import s from './Account.module.css';
import AccountFooter from './AccountFooter/AccountFooter';
import CredentialsCard from './CredentialsCard/CredentialsCard';
import LanguageCard from './LanguageCard/LanguageCard';

const Account = (): JSX.Element => {
  const { user, userCertificates, userRoles } = UserContext();
  const { t } = useTranslation();
  const [isDeleteAccountModalOpen, setIsDeleteAccountModalOpen] =
    useState(false);
  const isBreakingAt1300 = useMediaQuery(`(max-width: ${em(1300)})`);

  const [isChangePasswordVisible, setIsChangePasswordVisible] = useState(false);

  const completedCertificates = Object.values(userCertificates)
    .filter((certificate) => certificate.isCompleted)
    .map((certificate) => certificate.type);

  const formattedDate =
    user?.profile?.dob && getDateDetails(new Date(user.profile.dob));

  const displayedDate =
    formattedDate &&
    `${padZero(formattedDate.day)} ${t(MONTHS_BY_NUMBER[formattedDate.month].long)} ${formattedDate.year}`.toUpperCase();

  return (
    <div className={s.wrapper}>
      <Grid grow gutter="xl">
        <Grid.Col span={isBreakingAt1300 ? 6 : 4} mih={340} order={1}>
          <PersonalInfoCard
            dateOfBirth={displayedDate || ''}
            phoneNumber={user.profile.phoneNumber || ''}
            organisation={user.profile.organisation?.toUpperCase() || ''}
            theming="gray"
          />
        </Grid.Col>

        <Grid.Col
          span={isBreakingAt1300 ? 6 : 4}
          mih={340}
          order={isBreakingAt1300 ? 4 : 2}
        >
          {/* TODO Dynamically select from all tests render the licenses remaining number */}
          <LicensesCard
            licenses={user.profile.licenses}
            headerTransKey="your-licenses"
            hasUnlimitedLicenses={userRoles.isResearcher}
            hasRenewButton={
              !userRoles.isResearcher && !userRoles.isSchoolTeacher
            }
          />
        </Grid.Col>

        <Grid.Col span={isBreakingAt1300 ? 6 : 4} mih={340} order={3}>
          <DetailedCertificationsCard
            completedCertifications={completedCertificates}
            headerTransKey="your-certifications"
            hasEarnMoreButton={
              !userRoles.isResearcher && !userRoles.isSchoolTeacher
            }
          />
        </Grid.Col>

        <Grid.Col
          span={isBreakingAt1300 ? 6 : 2}
          mih={240}
          order={isBreakingAt1300 ? 2 : 4}
        >
          <LanguageCard />
        </Grid.Col>

        <Grid.Col span={6} mih={240} order={5}>
          <CredentialsCard
            email={user.profile.email}
            contactEmail={COMPANY_DETAILS.email}
            onChangeEmail={() => setIsChangePasswordVisible(true)}
          />
        </Grid.Col>
      </Grid>

      <AccountFooter
        isDeleteAccountModalOpen={isDeleteAccountModalOpen}
        onClose={() => setIsDeleteAccountModalOpen(false)}
        onDeleteAccount={() => setIsDeleteAccountModalOpen(true)}
        onConfirmDeletion={() => {
          setIsDeleteAccountModalOpen(false);
        }}
        customerId={user.id}
      />

      <AlertDialog
        isOpen={isChangePasswordVisible}
        onConfirmAction={() => {
          setIsChangePasswordVisible(false);
        }}
        title="change-email"
        description="change-email-description"
        variant="info"
        confirmLabel="ok"
      />
    </div>
  );
};

export default Account;
