import { Box, SegmentedControl } from '@mantine/core';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import { TranslationKeysType } from '@/types/common';

import { getRole } from '../../common/helpers';
import Account from './components/Account/Account';
import UpdateProfileModal from './components/Account/UpdateProfileModal/UpdateProfileModal';
import PurchaseHistory from './components/PurchaseHitory/PurchaseHistory';
import s from './UserProfile.module.css';

const TABS = [
  {
    label: 'account-capital',
    value: 'account',
  },
  {
    label: 'purchases-capital',
    value: 'purchase-history',
  },
];

const UserProfile = (): JSX.Element => {
  const router = useRouter();
  const { t } = useTranslation();
  const { user, userRoles } = UserContext();
  const tabQueryParam = (router.query.tab as string) || 'account';

  const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);

  const isAccountSelected = tabQueryParam === 'account';
  const isPurchaseHistorySelected = tabQueryParam === 'purchase-history';

  const isAllowedToSeePurchaseHistory =
    userRoles.isSchoolAdmin || userRoles.isIndependentTeacher;

  return (
    <div className="routeWrapper">
      <div className="pageHeaderWrapper" style={{ minHeight: '80px' }}>
        <div className={`${s['profile-details']} textPageIndicator`}>
          {user?.profile?.profilePicture ? (
            <Image
              src={user?.profile.profilePicture}
              width={56}
              height={56}
              alt={user?.profile.firstName}
              style={{
                borderRadius: '50%',
              }}
            />
          ) : (
            <Icon name="PersonSvg" color="turquoise" />
          )}

          <div className={s.profileNameAndAffiliation}>
            <Text
              untranslatedText={`${user?.profile.firstName} ${user?.profile.lastName}`}
              type="h3"
              lineClamp={2}
            />

            {user?.roles && (
              <Text
                transKey={getRole(user?.roles) as TranslationKeysType}
                type="body1"
                color="gray500"
                lineClamp={2}
              />
            )}
          </div>
        </div>

        {isAllowedToSeePurchaseHistory && (
          <SegmentedControl
            // w={400}
            value={tabQueryParam}
            onChange={(v) => {
              router.push(`${FULL_DASHBOARD_ROUTES.PROFILE}?tab=${v}`);
            }}
            withItemsBorders={false}
            data={TABS.map((item) => ({
              value: item.value,
              label: t(item.label),
            }))}
          />
        )}

        {isAccountSelected && (
          <Box className="actionButtonIndicator">
            <Button
              transKey="edit-profile-capital"
              onClick={() => setIsEditProfileModalOpen(true)}
            />
          </Box>
        )}
      </div>

      <div className={`${s.wrapper} hideScrollBar`}>
        {isAccountSelected && <Account />}

        {isPurchaseHistorySelected && <PurchaseHistory />}
      </div>

      <UpdateProfileModal
        isOpen={isEditProfileModalOpen}
        onClose={() => setIsEditProfileModalOpen(false)}
      />
    </div>
  );
};

export default UserProfile;
