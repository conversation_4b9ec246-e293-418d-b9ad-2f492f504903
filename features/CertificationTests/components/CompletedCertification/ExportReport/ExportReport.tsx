import { Box, Tooltip } from '@mantine/core';
import { showNotification } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import { downloadFile } from '@/common/helpers';
import ActionButton from '@/components/ActionButton/ActionButton';
import { ContentFilterType } from '@/features/CertificationTests/types';
import TESTS_SESSIONS_LISTING from '@/services/tests/sessions-listing';
import { ProductsType } from '@/types/common';

import s from './ExportReport.module.css';

export type ExportReportPropsType = {
  testType: ProductsType;
  grades: string[];
  classId?: string;
  from?: string;
  to?: string;
  isDisabled?: boolean;
  status?: ContentFilterType;
};

const ExportReport = ({
  classId,
  from,
  grades,
  isDisabled,
  status,
  testType,
  to,
}: ExportReportPropsType): JSX.Element => {
  const { t } = useTranslation();

  const exportReportMutation = useMutation({
    mutationFn: TESTS_SESSIONS_LISTING.DOWNLOAD_TESTS_REPORT,
    onSuccess: async (res) => {
      if (res) {
        downloadFile(res.data, `${testType}_report`, 'pdf');
      }
    },
    onError: (error) => {
      showNotification({
        title: t('Error'),
        message: t(error.message),
        color: 'red',
      });
    },
  });

  return (
    <Tooltip
      maw={360}
      inline={false}
      multiline
      style={{
        textAlign: 'center',
      }}
      label={t('export-report-disabled-tooltip')}
      disabled={!isDisabled}
    >
      <Box
        pos="relative"
        // className={`${isDisabled && s.disabled}`}
      >
        <ActionButton
          onClick={() => {
            exportReportMutation.mutate({
              testType,
              grades,
              classId,
              from,
              to,
              status,
            });
          }}
          type="exportTestsReport"
          isDisabled={isDisabled}
          isLoading={exportReportMutation.isPending}
        />
      </Box>
    </Tooltip>
  );
};

export default ExportReport;
