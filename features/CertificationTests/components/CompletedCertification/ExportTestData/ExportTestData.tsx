import { Progress } from '@mantine/core';

import ActionButton from '@/components/ActionButton/ActionButton';
import { ExportTestDataContext } from '@/context/ExportTestDataProvider';
import { ExportTestDataQueryParameters } from '@/types/tests/session-listing';

import s from './ExportTestData.module.css';

const ExportTestData = (
  exportDataParams: ExportTestDataQueryParameters
): JSX.Element => {
  const { isExporting, progress, startExport } = ExportTestDataContext();

  return (
    <div className={`${s.wrapper} ${isExporting && s.isDisabled}`}>
      <div className={`${s.progressWrapper} ${isExporting && s.isExporting}`}>
        <Progress animated value={progress} color="var(--color-blue)" />
      </div>

      <ActionButton
        onClick={() => {
          startExport(exportDataParams);
        }}
        type="exportTestResults"
        isDisabled={isExporting || exportDataParams.isDisabled}
        isLoading={isExporting}
      />
    </div>
  );
};

export default ExportTestData;
