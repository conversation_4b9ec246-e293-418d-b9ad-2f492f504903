import { Progress } from '@mantine/core';
import { JSX } from 'react';

import ActionButton from '@/components/ActionButton/ActionButton';
import { ExportTestDataContext } from '@/context/ExportTestDataProvider';
import { ExportTestDataQueryParameters } from '@/types/tests/session-listing';

import s from './ExportTestData.module.css';

// Export Test Data Should be used for the admin of the app and Study users. Meaning for Study admin and researchers

const ExportTestData = (
  exportDataParams: ExportTestDataQueryParameters
): JSX.Element => {
  const { isExporting, progress, startExport } = ExportTestDataContext();

  return (
    <div className={`${s.wrapper} ${isExporting && s.isDisabled}`}>
      <div className={`${s.progressWrapper} ${isExporting && s.isExporting}`}>
        <Progress animated value={progress} color="var(--color-blue)" />
      </div>

      <ActionButton
        onClick={() => {
          startExport(exportDataParams);
        }}
        type="exportTestResults"
        isDisabled={isExporting || Boolean(exportDataParams?.isDisabled)}
        isLoading={isExporting}
      />
    </div>
  );
};

export default ExportTestData;
