import { Pagination, ScrollArea } from '@mantine/core';
import { useDebouncedCallback } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { GLOBAL_PAGINATION_FETCH_LIMIT, PRODUCTS } from '@/common/consts';
import { getPayloadDate, getSelectedFiltersLength } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import { APP_LAYOUT_ROUTES } from '@/common/routes';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import ConductSubTestsModal from '@/features/ConductSubTestsModal/ConductSubTestsModal';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import TESTS_SESSIONS_LISTING from '@/services/tests/sessions-listing';
import { ProductsType, TestSessionsListType } from '@/types/common';

import { TEST_RESULTS_ID_QUERY_KEY } from '../../consts';
import { ContentFilterType, FiltersType } from '../../types';
import TestResultsModal from '../TestResultsModal/TestResultsModal';
import styles from './CompletedCertification.module.css';
import TestsFilters from './TestsFilters/TestsFilters';
import TestsListHeader from './TestsListHeader/TestsListHeader';
import TestsTable from './TestsTable/TestsTable';
import ZeroConductedTestsList from './ZeroConductedTestsList/ZeroConductedTestsList';

type CompletedCertificationPropsType = {
  testType: ProductsType;
};

const FILTERS_INITIAL_VALUES: FiltersType = {
  classOrGroup: null,
  from: null,
  to: null,
  study: null,
  grades: [],
};

const CompletedCertification = ({
  testType,
}: CompletedCertificationPropsType): JSX.Element => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { isSchoolRole, userRoles } = UserContext();
  const { t } = useTranslation();

  const [searchWord, setSearchWord] = useState('');
  const [searchWordInputValue, setSearchWordInputValue] = useState('');

  const [areFiltersVisible, setAreFiltersVisible] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const [isResultsOnlyVisible, setIsResultsOnlyVisible] = useState(false);
  const [isStudentLabelHidden, setIsStudentLabelHidden] =
    useState<boolean>(false);

  const [displayedFilterType, setDisplayedFilterType] =
    useState<ContentFilterType>('all');

  const [scoreSortedBy, setScoreSortedBy] = useState<
    'score:asc' | 'score:desc' | null
  >(null);

  const [selectedFilters, setSelectedFilters] = useState<FiltersType>(
    FILTERS_INITIAL_VALUES
  );

  const [activePage, setActivePage] = useState<number>(1);

  const [areStudiesChecked, setAreStudiesChecked] = useState<boolean>(false);
  const [isLiveEnabled, setIsLiveEnabled] = useState<boolean>(false);

  const isToDateBeforeFrom =
    (selectedFilters.to &&
      new Date(selectedFilters.to || 0) <
        new Date(selectedFilters.from || 0)) ||
    false;

  const toDatePayload = isToDateBeforeFrom
    ? selectedFilters.from
    : selectedFilters.to;

  const searchParams = {
    page: activePage,
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
    query: searchWord,
    from: selectedFilters.from ? getPayloadDate(selectedFilters.from) : null,
    to: toDatePayload ? getPayloadDate(toDatePayload) : null,
    ...(areStudiesChecked && {
      study: selectedFilters.study,
    }),
    ...(!areStudiesChecked && {
      className: isSchoolRole ? selectedFilters.classOrGroup : null,
      groupName: !isSchoolRole ? selectedFilters.classOrGroup : null,
    }),
    testType,
    ...(displayedFilterType !== 'all' && { displayedFilterType }),
    grades: selectedFilters.grades,
    report: isResultsOnlyVisible,
    sort: scoreSortedBy,
  };

  const isLiveOptionDisplayed = useMemo(
    () => userRoles.isStudyAdmin || (userRoles.isAdmin && areStudiesChecked),
    [userRoles, areStudiesChecked]
  );

  const { data, isFetching, isLoading } = useQuery<TestSessionsListType | null>(
    {
      enabled: Boolean(testType),
      queryFn: () =>
        areStudiesChecked
          ? TESTS_SESSIONS_LISTING.GET_TEST_SESSIONS_STUDY(searchParams)
          : TESTS_SESSIONS_LISTING.GET_TEST_SESSIONS(searchParams),
      queryKey: [
        areStudiesChecked
          ? QUERY_KEYS.ALL_CREATED_SESSION_TESTS_STUDY
          : QUERY_KEYS.ALL_CREATED_SESSION_TESTS,
        activePage,
        searchWord,
        testType,
        selectedFilters,
        displayedFilterType,
        isResultsOnlyVisible,
        scoreSortedBy,
      ],
      placeholderData: keepPreviousData,
      staleTime: 1000 * 60 * 60,
      refetchInterval: isLiveEnabled ? 5000 : false,
    }
  );

  const isInformationLoading = isLoading || isFetching;

  const conductedSessionsTestList = data?.results || [];
  const numberOfSearchResults = data?.count || 0;

  useEffect(() => {
    setIsResultsOnlyVisible(false);
    setDisplayedFilterType('all');
    setAreStudiesChecked(false);
    setScoreSortedBy(null);
  }, [router.query.tab]);

  const onClearFilters = () => {
    setSelectedFilters(FILTERS_INITIAL_VALUES);

    setActivePage(1);
  };

  const onSearch = useDebouncedCallback(async (query: string) => {
    setActivePage(1);
    setSearchWord(query);
  }, 500);

  const handleFilterUpdate = ({
    key,
    value,
  }: {
    key: keyof FiltersType;
    value: FiltersType[keyof FiltersType];
  }) => {
    setActivePage(1);

    setSelectedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const totalNumberOfPages = Math.ceil(
    (data?.count || 1) / GLOBAL_PAGINATION_FETCH_LIMIT
  );

  const selectedFiltersLength = getSelectedFiltersLength(selectedFilters);

  // DUPLICATE FUNCTION UNDER CONDUCT TEST MODULE WHERE IF THIS DEVICE IS SELECTED IMMEDIATELY WILL START THE SESSION AND OPEN THE MODAL
  const addSessionIdToURl = (sessionId: string) => {
    // It has a bug that if for some reason the open will be clicked again session will be writing its self on the url
    const path = `${router.asPath.includes('?') ? `${router.asPath}&session=${sessionId}` : `${router.asPath}?session=${sessionId}`}`;
    router.push(path);
  };

  const startSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.START_TEST_SESSION,
    onSuccess: (res, sessionId) => {
      addSessionIdToURl(sessionId);

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });

      // Need to also update the specific session from the list to change the state from active to in-progress
    },
    onError: (error) => {},
  });

  const continueSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.CONTINUE_TEST_SESSION,
    onSuccess: (res, sessionId) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });
    },
    onError: (error) => {},
  });

  const deleteSession = useMutation({
    mutationFn: SESSIONS_ACTIONS.DELETE_SESSION,
    onSuccess: () => {
      setSessionToDelete(null);

      notifications.show({
        message: t('session-deleted-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS_STUDY],
      });
    },
    onError: (error) => {
      setSessionToDelete(null);

      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const onOpenSession = (sessionId: string, isSessionInProgress: boolean) => {
    if (isSessionInProgress) {
      // This will trigger the modal to open cause the modal it self is checking if it find session in the url to open
      addSessionIdToURl(sessionId);
      continueSession.mutate(sessionId);
      return;
    }

    startSession.mutate(sessionId);
  };

  const onDeleteSessionClick = (sessionId: string) => {
    setSessionToDelete(sessionId);
  };

  const onShowResultsClick = (sessionId: string) => {
    if (router.asPath.includes(TEST_RESULTS_ID_QUERY_KEY)) {
      const path = router.asPath.replace(
        /${TEST_RESULTS_ID_QUERY_KEY}=[^&]*/,
        `${TEST_RESULTS_ID_QUERY_KEY}=${sessionId}`
      );
      router.push(path);
    } else {
      router.push(
        `${APP_LAYOUT_ROUTES.CERTIFICATION_TESTS}?tab=${testType}&${TEST_RESULTS_ID_QUERY_KEY}=${sessionId}${isStudentLabelHidden && (userRoles.isAdmin || userRoles.isStudyAdmin) ? '&isStudentLabelHidden=true' : ''}`
      );
    }
  };

  const isZeroConductedTestsListVisible =
    data &&
    conductedSessionsTestList.length === 0 &&
    data.totalCount === 0 &&
    !isFetching;

  const isNoResultsVisible =
    Boolean(data) &&
    conductedSessionsTestList.length === 0 &&
    !isZeroConductedTestsListVisible &&
    !isFetching;

  const handleScoreClick = () => {
    if (scoreSortedBy === null) {
      setScoreSortedBy('score:desc');
      return;
    }

    if (scoreSortedBy === 'score:desc') {
      setScoreSortedBy('score:asc');
      return;
    }

    if (scoreSortedBy === 'score:asc') {
      setScoreSortedBy(null);
    }
  };

  return (
    <div className={styles.pageWrapper}>
      {!isLoading && (
        <div className={styles.wrapper}>
          <TestsListHeader
            areFiltersVisible={areFiltersVisible}
            displayedFilterType={displayedFilterType}
            isLiveOptionDisplayed={isLiveOptionDisplayed}
            isLiveOptionChecked={isLiveEnabled}
            exportDataParams={{
              testType,
              isStudy: areStudiesChecked,
              selectedStudy: selectedFilters.study || '',
              from: selectedFilters.from
                ? getPayloadDate(selectedFilters.from)
                : '',
              to: selectedFilters.to ? getPayloadDate(selectedFilters.to) : '',
              grades: selectedFilters.grades || [],
              group: selectedFilters.classOrGroup || '',
              status:
                displayedFilterType !== 'all' ? displayedFilterType : undefined,
            }}
            onLiveOptionChange={() => {
              setIsLiveEnabled((prev) => !prev);
            }}
            isInformationLoading={isLoading}
            numberOfSearchResults={numberOfSearchResults}
            onChangeFilterType={(filterType: ContentFilterType) => {
              setDisplayedFilterType(filterType);
              setActivePage(1);
            }}
            onClearFilters={onClearFilters}
            onSearch={(value) => {
              setSearchWordInputValue(value);
              onSearch(value);
            }}
            searchWord={searchWordInputValue}
            selectedFilters={selectedFilters}
            selectedFiltersLength={selectedFiltersLength}
            setAreFiltersVisible={setAreFiltersVisible}
            areStudiesSwitchChecked={areStudiesChecked}
            onStudiesSwitchChanged={() => {
              setIsResultsOnlyVisible(false);
              setActivePage(1);
              setScoreSortedBy(null);
              setSelectedFilters(FILTERS_INITIAL_VALUES);
              setAreStudiesChecked((prev) => !prev);
            }}
            isResultsOnlyVisible={isResultsOnlyVisible}
            onResultsOnlyChanged={() => {
              setActivePage(1);
              setIsResultsOnlyVisible((prev) => !prev);
            }}
            isResultsOnlyDisplayed={testType === PRODUCTS['mathpro-s'].id}
            areNoTestsCreated={Boolean(isZeroConductedTestsListVisible)}
          />

          <div className={styles.filtersAndContainerWrapper}>
            {/* SIDE BAR FILTERS */}
            <div
              className={`${styles.filters} ${areFiltersVisible && styles.open}`}
            >
              <TestsFilters
                selectedFilters={selectedFilters}
                handleFilterUpdate={handleFilterUpdate}
                isToDateBeforeFrom={isToDateBeforeFrom}
                areTestsFetching={
                  isLiveEnabled ? false : isLoading || isFetching
                }
                areStudiesSwitchChecked={areStudiesChecked}
                onGradesChange={(value) => {
                  handleFilterUpdate({
                    key: 'grades',
                    value,
                  });
                }}
              />
            </div>

            {/* SCROLLABLE CONTENT AT THE RIGHT SIDE OF THE FILTERS */}
            {!isNoResultsVisible &&
              !isZeroConductedTestsListVisible &&
              !isLoading && (
                <ScrollArea
                  type={isResultsOnlyVisible ? 'auto' : 'never'}
                  className={styles.scrollAreaWrapper}
                >
                  <TestsTable
                    conductedTestList={conductedSessionsTestList}
                    isLoading={startSession.isPending}
                    isInformationLoading={isLoading}
                    isDisabled={isLiveEnabled ? false : isLoading || isFetching}
                    isStudentLabelHidden={isStudentLabelHidden}
                    onOpenSession={onOpenSession}
                    onDeleteSessionClick={onDeleteSessionClick}
                    onShowResultsClick={onShowResultsClick}
                    onHideStudentLabel={() => {
                      setIsStudentLabelHidden((prev) => !prev);
                    }}
                    isResultsModeEnabled={isResultsOnlyVisible}
                    isScoreClicked={scoreSortedBy !== null}
                    onScoreClick={handleScoreClick}
                  />

                  {isInformationLoading &&
                  (numberOfSearchResults || 0) > 0 ? null : (
                    <div className={styles.paginationWrapper}>
                      <Pagination
                        total={totalNumberOfPages}
                        value={activePage}
                        hideWithOnePage
                        withControls={false}
                        onChange={(value) => {
                          setActivePage(value);
                        }}
                        disabled={isInformationLoading}
                      />
                    </div>
                  )}
                </ScrollArea>
              )}

            {isZeroConductedTestsListVisible && (
              <ZeroConductedTestsList isStudies={areStudiesChecked} />
            )}

            {isNoResultsVisible && (
              <div className={styles.noResultsWrapper}>
                <Text
                  transKey="no-search-results"
                  type="h4"
                  color="gray"
                  fw={400}
                  mt={40}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {isLoading && (
        <div
          className={styles.wrapper}
          style={{
            userSelect: 'none',
          }}
        >
          <TestsListHeader
            areFiltersVisible={areFiltersVisible}
            displayedFilterType={displayedFilterType}
            isLiveOptionDisplayed={isLiveOptionDisplayed}
            isLiveOptionChecked={isLiveEnabled}
            onLiveOptionChange={() => {
              setIsLiveEnabled((prev) => !prev);
            }}
            isInformationLoading={isLoading}
            numberOfSearchResults={numberOfSearchResults}
            onChangeFilterType={(filterType: ContentFilterType) =>
              setDisplayedFilterType(filterType)
            }
            onClearFilters={onClearFilters}
            onSearch={(value) => {
              setSearchWordInputValue(value);
              onSearch(value);
            }}
            searchWord={searchWordInputValue}
            selectedFilters={selectedFilters}
            selectedFiltersLength={selectedFiltersLength}
            setAreFiltersVisible={setAreFiltersVisible}
            exportDataParams={{
              testType,
              isStudy: areStudiesChecked,
              selectedStudy: selectedFilters.study || '',
              from: selectedFilters.from
                ? getPayloadDate(selectedFilters.from)
                : '',
              to: selectedFilters.to ? getPayloadDate(selectedFilters.to) : '',
              grades: selectedFilters.grades || [],
              status:
                displayedFilterType !== 'all' ? displayedFilterType : undefined,
            }}
            areStudiesSwitchChecked={areStudiesChecked}
            onStudiesSwitchChanged={() => {
              setAreStudiesChecked((prev) => !prev);
            }}
            isResultsOnlyVisible={isResultsOnlyVisible}
            onResultsOnlyChanged={() => {
              setIsResultsOnlyVisible((prev) => !prev);
            }}
            isResultsOnlyDisplayed={testType === PRODUCTS['mathpro-s'].id}
            areNoTestsCreated={Boolean(isZeroConductedTestsListVisible)}
          />

          <TableSkeleton numberOfColumns={5} />
        </div>
      )}

      <ConductSubTestsModal />

      {(userRoles.isAdmin || userRoles.isDeveloper) && (
        <AlertDialog
          isOpen={sessionToDelete !== null}
          onConfirmAction={() => {
            deleteSession.mutate(sessionToDelete || '');
          }}
          title="session-delete-confirmation"
          description="session-delete-confirmation-description"
          onCancel={() => setSessionToDelete(null)}
          isActionInProgress={deleteSession.isPending}
          variant="danger"
        />
      )}

      <TestResultsModal
        onCloseModal={() => {
          const [basePath] = router.asPath.split('?');
          const params = new URLSearchParams(window.location.search);

          params.delete(TEST_RESULTS_ID_QUERY_KEY);
          params.delete('isStudentLabelHidden');

          const queryString = params.toString();

          router.push(queryString ? `${basePath}?${queryString}` : basePath);
        }}
      />

      {/* <TestResultsModal
        onCloseModal={() => {
          const [basePath, currentQuery] = router.asPath.split('?');

          if (!currentQuery) {
            router.push(basePath);
            return;
          }

          // Manually process the query string to preserve formatting
          const queryParts = currentQuery.split('&').filter((part) => {
            const [key] = part.split('=');
            return (
              key !== TEST_RESULTS_ID_QUERY_KEY &&
              key !== 'isStudentLabelHidden'
            );
          });

          const queryString = queryParts.join('&');
          router.push(queryString ? `${basePath}?${queryString}` : basePath);
        }}
      /> */}
    </div>
  );
};

export default CompletedCertification;
