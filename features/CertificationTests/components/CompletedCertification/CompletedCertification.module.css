.wrapper {
  height: 100%;
}

.pageWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-height: 100%;
}

.noResults {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-height: 100%;
}

.filters {
  max-width: 0;
  width: 100%;
  overflow-x: hidden;
  z-index: 1;
  opacity: 0;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.06);
  clip-path: inset(0 -15px 0 0);
  transition: all 0.4s ease-in-out;

  &.open {
    max-width: 180px;
    margin-right: var(--spacing-xl);
    padding-right: var(--spacing-sm);
    opacity: 1;
  }
}

.filtersAndContainerWrapper {
  display: flex;
  width: 100%;
  height: 100%;
}

.noResultsWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  padding: var(--spacing-3xl);
}

.scrollAreaWrapper {
  width: 100%;
  /*
  292px reflects to the occupied spacings from the elements
  above the cards and the table that are being displayed in the school
  Layout. For example the header with the search etc.
  */

  max-height: calc(100vh - 320px);
}

.paginationWrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
  position: sticky;
  left: 50%;
}

.gradesWrapper {
  width: 110px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-smd);
}

.gradeSelector {
  min-width: 30px;
  max-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--color-gray50);
  border-radius: var(--radius-roundedFull);
  transition: background-color 0.2s ease;
}

.selectedGrade {
  background-color: var(--color-turquoise);
  color: var(--color-white);
}

.disabledGrade {
  opacity: 0.5;
  pointer-events: none;
}
