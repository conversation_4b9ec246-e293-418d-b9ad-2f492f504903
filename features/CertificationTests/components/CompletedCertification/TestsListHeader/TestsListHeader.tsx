import {
  Button,
  Combobox,
  Flex,
  Indicator,
  SegmentedControl,
  Skeleton,
  Switch,
  useCombobox,
} from '@mantine/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import ActionButton from '@/components/ActionButton/ActionButton';
import Checkbox from '@/components/Checkbox/Checkbox';
import Icon from '@/components/Icon/Icon';
import Input from '@/components/Inputs/Input/Input';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import {
  ContentFilterType,
  FiltersType,
} from '@/features/CertificationTests/types';
import { ExportTestDataQueryParameters } from '@/types/tests/session-listing';

import ExportTestData from '../ExportTestData/ExportTestData';
import styles from './TestsListHeader.module.css';

type TestsListHeaderProps = {
  areFiltersVisible: boolean;
  setAreFiltersVisible: (value: boolean) => void;
  selectedFilters: FiltersType;
  selectedFiltersLength: number;
  searchWord: string;
  onSearch: (value: string) => void;
  isInformationLoading: boolean;
  onClearFilters?: () => void;
  onChangeFilterType: (filterType: ContentFilterType) => void;
  numberOfSearchResults: number | null;
  displayedFilterType: ContentFilterType;
  areStudiesSwitchChecked: boolean;
  onStudiesSwitchChanged: () => void;
  isLiveOptionDisplayed: boolean;
  isLiveOptionChecked: boolean;
  onLiveOptionChange: () => void;
  isResultsOnlyVisible: boolean;
  onResultsOnlyChanged: () => void;
  isResultsOnlyDisplayed: boolean;
  exportDataParams: ExportTestDataQueryParameters;
  areNoTestsCreated: boolean;
};

const TestsListHeader = ({
  areFiltersVisible,
  areNoTestsCreated,
  areStudiesSwitchChecked,
  displayedFilterType,
  exportDataParams,
  isInformationLoading,
  isLiveOptionChecked,
  isLiveOptionDisplayed,
  isResultsOnlyDisplayed,
  isResultsOnlyVisible,
  numberOfSearchResults,
  onChangeFilterType,
  onClearFilters,
  onLiveOptionChange,
  onResultsOnlyChanged,
  onSearch,
  onStudiesSwitchChanged,
  searchWord,
  selectedFilters,
  selectedFiltersLength,
  setAreFiltersVisible,
}: TestsListHeaderProps) => {
  const { t } = useTranslation();
  const {
    userRoles: { isAdmin, isStudyAdmin },
  } = UserContext();

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const displayedFilterTypes = ['all', 'ready', 'completed', 'uncompleted'];

  return (
    <div className={styles.header}>
      <div className={styles.searchWrapper}>
        <Indicator
          variant="filters"
          disabled={
            !selectedFilters || selectedFiltersLength === 0 || areFiltersVisible
          }
          inline
          label={selectedFiltersLength}
          size={20}
          offset={7}
          withBorder
        >
          <button
            type="button"
            className={`${styles.filterButton} ${areFiltersVisible && styles.activeButton}`}
            onClick={() => setAreFiltersVisible(!areFiltersVisible)}
            disabled={areNoTestsCreated}
          >
            <Icon
              name="FiltersSvg"
              color={areFiltersVisible ? 'white' : 'black'}
            />
          </button>
        </Indicator>

        <Input
          value={searchWord}
          variant="filled"
          placeholder={t('search-students')}
          onChange={(value) => onSearch(value)}
          rightSectionWidth={80}
          rightSection={
            isInformationLoading ? (
              <Skeleton />
            ) : (
              <div className={styles.inputRightSection}>
                <Text
                  untranslatedText={`${numberOfSearchResults || '0'}`}
                  type="body2"
                />
              </div>
            )
          }
          isDisabled={areNoTestsCreated}
        />

        {isAdmin && (
          <Flex align="center" gap="sm">
            <Switch
              checked={areStudiesSwitchChecked}
              onChange={onStudiesSwitchChanged}
              color="var(--color-blue)"
              styles={{
                track: {
                  cursor: 'pointer',
                },
              }}
            />
            <Text transKey="studies" color="darkerBlue" />
          </Flex>
        )}

        {selectedFiltersLength > 0 && (
          <ActionButton
            type="clearFilters"
            onClick={() => {
              setAreFiltersVisible(false);
              setTimeout(() => {
                onClearFilters?.();
              }, 400);
            }}
          />
        )}
      </div>

      <div className={styles.rightSection}>
        {isLiveOptionDisplayed && (
          <div className={styles.flashingWrapper}>
            <div
              className={
                isLiveOptionChecked
                  ? styles.greenFlashingCircle
                  : styles.offline
              }
            />

            <Text untranslatedText="LIVE" type="button" color="blue" />

            <Checkbox
              value="live"
              onChange={onLiveOptionChange}
              isChecked={isLiveOptionChecked}
              variant="primary"
              isRequired
              isDisabled={!isLiveOptionDisplayed || areNoTestsCreated}
            />
          </div>
        )}

        {/* SHOW RESULTS ONLY */}
        {isResultsOnlyDisplayed && (
          <Flex align="center" gap="sm">
            <Text transKey="show-results-only-capital" color="gray600" />

            <Switch
              checked={isResultsOnlyVisible}
              onChange={onResultsOnlyChanged}
              color="var(--color-blue)"
              styles={{
                track: {
                  cursor: areStudiesSwitchChecked ? 'not-allowed' : 'pointer',
                },
              }}
              disabled={areStudiesSwitchChecked || areNoTestsCreated}
            />
          </Flex>
        )}

        <Combobox
          store={combobox}
          width={250}
          position="bottom-end"
          onOptionSubmit={(val) => {
            onChangeFilterType(val as ContentFilterType);
            combobox.closeDropdown();
          }}
          disabled={areNoTestsCreated}
        >
          <Combobox.Target>
            <Button
              onClick={() => combobox.toggleDropdown()}
              pl={12}
              pr={12}
              bg="transparent"
              c="var(--color-turquoise)"
              rightSection={<Combobox.Chevron c="var(--color-turquoise)" />}
              styles={{
                inner: {
                  display: 'flex',
                  justifyContent: 'space-between',
                },
                root: {
                  cursor: 'pointer',
                  border: '1px dashed var(--color-gray200)',
                },
              }}
              disabled={areNoTestsCreated}
            >
              <p>{t(`${displayedFilterType}-capital`)}</p>
            </Button>
          </Combobox.Target>

          <Combobox.Dropdown
            maw="max-content"
            style={{
              boxShadow: 'var(--shadow-default)',
            }}
          >
            {displayedFilterTypes.map((filterType) => (
              <Combobox.Option key={filterType} value={filterType}>
                {t(`${filterType}-capital`)}
              </Combobox.Option>
            ))}
          </Combobox.Dropdown>
        </Combobox>

        {(isAdmin || isStudyAdmin) && (
          <ExportTestData
            testType={exportDataParams.testType}
            isStudy={exportDataParams.isStudy}
            selectedStudy={exportDataParams.selectedStudy}
            from={exportDataParams.from}
            to={exportDataParams.to}
            grades={exportDataParams.grades}
            group={exportDataParams.group}
            status={exportDataParams.status}
            isDisabled={
              areNoTestsCreated ||
              numberOfSearchResults === 0 ||
              exportDataParams.status === 'ready'
            }
          />
        )}
      </div>
    </div>
  );
};

export default TestsListHeader;
