.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  height: 42px;
  min-height: 42px;
  margin-top: 5px;
}

.searchWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);

  & input {
    max-width: 360px;
  }
}

.filterButton {
  background-color: transparent;
  border-radius: var(--radius-2xs);
  border: none;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;

  & svg {
    cursor: pointer;
  }

  &:hover {
    opacity: 0.8;
  }
}

.inputRightSection {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.activeButton {
  background-color: var(--color-yellow);
}

.rightSection {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.flashingWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

@keyframes greenFlash {
  0% {
    background-color: var(--color-white);
  }

  100% {
    background-color: var(--color-yellow);
  }
}

.greenFlashingCircle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: greenFlash 1.5s infinite;
  transform: translateY(1px);
}

.offline {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--color-gray);
}
