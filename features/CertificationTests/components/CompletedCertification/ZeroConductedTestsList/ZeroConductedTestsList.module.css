.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: var(--spacing-3xl);
  width: 100%;
}

.titleWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-4xl);
  margin-bottom: var(--spacing-2xl);
  gap: var(--spacing-md);
}

.dummyButton {
  background-color: var(--color-gray50);
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-xl);
  border-radius: var(--radius-2xs);
  color: var(--color-white);
  margin-right: var(--spacing-smd);
}

.titleText {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-smd);
}

.dummyButtonWrapper {
  display: flex;
  align-items: center;
}

.arrowIcon {
  transform: translateY(-10px);
}

.rowsWrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 906px;
  border: 2px solid var(--color-gray50);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xs);
}

.row {
  height: 24px;
  display: flex;
  gap: var(--spacing-md);
  justify-content: space-between;
  border-bottom: 1px solid var(--color-gray50);
}

.baseSkeleton {
  width: 150px;
  height: 11px;
  overflow: hidden;
}

.largeSkeleton {
  width: 100%;
  height: 100%;
  background-color: var(--color-gray50);
  border-radius: 6px;
}

.minSkeleton {
  width: 80%;
  height: 100%;
  background-color: var(--color-gray50);
  border-radius: 6px;
}
