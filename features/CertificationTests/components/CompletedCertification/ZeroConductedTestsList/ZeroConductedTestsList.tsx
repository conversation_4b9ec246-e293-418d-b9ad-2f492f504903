// TODO : FIX USING INDEX AS KEY. React can actually handle it, but it's not recommended. It's better to use a unique key.
/* eslint-disable react/no-array-index-key */

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import s from './ZeroConductedTestsList.module.css';

type ZeroConductedTestsListProps = {
  isStudies: boolean;
};

const ZeroConductedTestsList = ({
  isStudies,
}: ZeroConductedTestsListProps): JSX.Element => {
  return (
    <div className={s.wrapper}>
      <div className={s.titleWrapper}>
        <Text
          transKey={isStudies ? 'no-created-studies' : 'no-created-tests'}
          type="h4"
          fw={400}
        />

        {!isStudies && (
          <div className={s.titleText}>
            <Text transKey="whenever-you-are-ready" type="h4" fw={400} />

            <div className={s.dummyButton}>
              <Text
                transKey="create-test-capital"
                type="label"
                isBold
                color="blue"
              />
            </div>
            <Icon
              name="PointingArrowSvg"
              color="blue"
              className={s.arrowIcon}
            />
          </div>
        )}
      </div>

      <div className={s.rowsWrapper}>
        {[...Array(7)].map((_, index) => {
          const isRowOdd = (index + 1) % 2 !== 0;

          return (
            <div key={index} className={s.row}>
              {[...Array(4)].map((__, i) => {
                const isItemOdd = (i + 1) % 2 === 0;

                // eslint-disable-next-line no-nested-ternary
                const appliedClass = isRowOdd
                  ? isItemOdd
                    ? s.minSkeleton
                    : s.largeSkeleton
                  : isItemOdd
                    ? s.largeSkeleton
                    : s.minSkeleton;

                return (
                  <div key={i} className={s.baseSkeleton}>
                    <div className={appliedClass} />
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ZeroConductedTestsList;
