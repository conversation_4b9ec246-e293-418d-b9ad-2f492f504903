.labelWrapper {
  padding: var(--spacing-sm) var(--spacing-smd);
  width: fit-content;
  border-radius: var(--radius-2xs);
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scoreWrapper {
  padding: var(--spacing-xs) var(--spacing-smd);
  border-radius: var(--radius-2xs);
  width: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scoreListWrapper {
  padding: var(--spacing-xs) var(--spacing-smd);
  border-radius: var(--radius-2xs);
  height: 29px;
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.scoreRed {
  background-color: var(--color-danger);
}

.scoreYellow {
  background-color: var(--color-yellow);
}

.scoreGreen {
  background-color: var(--color-green);
}

.scoreGray {
  background-color: var(--color-gray);
}

.active {
  background-color: var(--color-blue);
}

.in-progress {
  background-color: var(--color-yellow);
}

.completed {
  background-color: var(--color-green);
}

.expired {
  background-color: var(--color-danger);
}

.scheduled {
  background-color: var(--color-turquoise);
}

.headRow {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);

  & svg {
    cursor: pointer;
  }

  &:hover {
    cursor: pointer;
  }
}

.code {
  display: flex;
  align-items: center;
  gap: var(--spacing-smd);
  width: fit-content;
  cursor: pointer;
}

.infoButton {
  background-color: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  gap: var(--spacing-smd);
  align-items: center;
}

.studentLabelColumnHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.disabledMenuItem {
  pointer-events: none;
  opacity: 0.2;
}

.myTable th:first-of-type,
.myTable td:first-of-type {
  position: sticky;
  left: 0;
  position: -webkit-sticky;
  background-color: var(--color-white);
}

.myTable tr:hover td {
  background-color: #f1f3f5;
}
