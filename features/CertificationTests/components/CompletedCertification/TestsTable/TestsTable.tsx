/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import {
  Box,
  CopyButton,
  Menu,
  Table,
  TableData,
  Tooltip,
} from '@mantine/core';
import { getLangNameFromCode } from 'language-name-map';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BsEyeFill } from 'react-icons/bs';
import { FaEye, FaEyeSlash, FaKey, FaLaptop, FaSort } from 'react-icons/fa';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import Icon from '@/components/Icon/Icon';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import {
  ColorsType,
  TestSessionsListResponseType,
  TranslationKeysType,
} from '@/types/common';

import { SESSION_STATE } from '../consts';
import styles from './TestsTable.module.css';

type TestsTableProps = {
  conductedTestList: TestSessionsListResponseType;
  isInformationLoading: boolean;
  isLoading: boolean;
  isDisabled?: boolean;
  isResultsModeEnabled: boolean;
  isStudentLabelHidden: boolean;
  onOpenSession: (
    sessionId: TestSessionsListResponseType[0]['id'],
    isInProgress: boolean
  ) => void;
  onDeleteSessionClick: (
    sessionId: TestSessionsListResponseType[0]['id']
  ) => void;
  onShowResultsClick: (
    sessionId: TestSessionsListResponseType[0]['id']
  ) => void;
  onHideStudentLabel?: () => void;
  isScoreClicked?: boolean;
  onScoreClick?: () => void;
};

// const getTestResultsDisplay = (test: TestSessionsListResponseType[0]) => {
//   if (test?.results) {
//     return (test.results || 0) >= 0 ? `${test.results || 0}%` : '-';
//   }

//   return '-';
// };

const getScoreColor = (score: number) => {
  switch (true) {
    case score > 0 && score <= 15:
      return styles.scoreRed;

    case score > 15 && score <= 30:
      return styles.scoreYellow;

    case score > 30 && score <= 70:
      return styles.scoreGray;

    case score > 70 && score <= 100:
      return styles.scoreGreen;

    default:
      return '';
  }
};

const TestsTable = ({
  conductedTestList,
  isDisabled = false,
  isInformationLoading,
  isLoading,
  isResultsModeEnabled,
  isScoreClicked,
  isStudentLabelHidden,
  onDeleteSessionClick,
  onHideStudentLabel,
  onOpenSession,
  onScoreClick,
  onShowResultsClick,
}: TestsTableProps) => {
  const { t } = useTranslation();
  const { userRoles } = UserContext();

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${t(month)} ${day}  ‘${year} ${hoursAndMinutes}`;
  };

  const getInfoContent = (test: TestSessionsListResponseType[number]) => {
    switch (test.info.type) {
      case 'date':
        return (
          <Text
            type="body1"
            color="black"
            untranslatedText={getListDateTime(test.info.value)}
          />
        );
      case 'code':
        return (
          <CopyButton value={test.info.value} timeout={2000}>
            {({ copied, copy }) => (
              <Tooltip
                label={copied ? t('copied') : t('copy')}
                withArrow
                position="top"
              >
                <div onClick={copy} className={styles.code}>
                  <FaKey size={13} color="var(--color-turquoise)" />

                  <Text
                    type="body1"
                    color="black"
                    untranslatedText={test.info.value}
                  />
                </div>
              </Tooltip>
            )}
          </CopyButton>
        );
      case 'score':
        return (
          <Text
            type="body1"
            color="blue"
            untranslatedText={`${test.info.value}%`}
          />
        );
      case 'text':
        return (
          <Text
            type="body1"
            color="black"
            untranslatedText={test.info.value ? `${test.info.value}` : '-'}
          />
        );
      case 'action':
        return (
          <button
            key={test.id}
            className={styles.infoButton}
            onClick={() =>
              onOpenSession(test.id, test.state === SESSION_STATE.IN_PROGRESS)
            }
            type="button"
          >
            <FaLaptop size={15} color="var(--color-blue)" />

            <Text type="button" color="blue" transKey="open-capital" />
          </button>
        );
      default:
        return '-';
    }
  };

  const getUnTranslatedStatus = (test: TestSessionsListResponseType[0]) => {
    switch (test.state) {
      case 'in-progress':
        return `${Math.round((test.progress || 0) * 100)}%`;
      case 'completed':
        return '100%';
      default:
        return undefined;
    }
  };

  const subTestsLabels = useMemo(() => {
    const allTitles = conductedTestList.flatMap((test) =>
      test?.report?.map((r) => r.title)
    );
    const uniqueTitles = Array.from(new Set(allTitles));
    return uniqueTitles;
  }, [conductedTestList]);

  // comment to trigger fake PR
  const headTitles = isResultsModeEnabled
    ? subTestsLabels
    : [
        t('grade-capital'),
        t('test-grade-capital'),
        t('language-capital'),
        t('monitor-capital'),
        t('start-date-capital'),
        t('last-date-capital'),
        t('progress-capital'),
        t('info-capital'),
        <Box
          key="score-head"
          className={styles.headRow}
          style={{ maxWidth: 140 }}
          onClick={onScoreClick}
        >
          <Text
            transKey="score-capital"
            type="body2"
            isBold
            color={(isScoreClicked ? 'turquoise' : 'gray900') as ColorsType}
          />

          <FaSort
            color={isScoreClicked ? 'var(--color-turquoise)' : undefined}
          />
        </Box>,
        t('results-capital'),
      ];

  const DISPLAYED_LIST: TableData = {
    head: [
      <Box
        key="student-header-column"
        className={styles.studentLabelColumnHeader}
      >
        {t('student-capital')}

        {(userRoles.isAdmin || userRoles.isStudyAdmin) && (
          <Box
            style={{
              height: '15px',
            }}
          >
            {isStudentLabelHidden ? (
              <FaEyeSlash
                size={15}
                color="var(--color-blue)"
                onClick={onHideStudentLabel}
              />
            ) : (
              <FaEye
                size={15}
                color="var(--color-blue)"
                onClick={onHideStudentLabel}
              />
            )}
          </Box>
        )}
      </Box>,
      ...headTitles,
    ],
    body: isResultsModeEnabled
      ? conductedTestList.map((test) => [
          <Text
            key={test.id}
            untranslatedText={
              isStudentLabelHidden
                ? `${test.student.label
                    .split('')
                    .map((word) => '*')
                    .join('')}`
                : test.student.label
            }
          />,
          ...subTestsLabels.map((title) => {
            const score = test.report?.find((r) => r.title === title)?.score;

            return score ? (
              <div
                key={`${test.id}-${title}`}
                className={`${styles.scoreListWrapper} ${getScoreColor(score)}`}
              >
                <Text
                  type="body3"
                  color="white"
                  untranslatedText={String(score)}
                  isBold
                />
              </div>
            ) : (
              '-'
            );
          }),
        ])
      : conductedTestList.map((test) => [
          <Text
            key={test.id}
            untranslatedText={
              isStudentLabelHidden
                ? `${test.student.label
                    .split('')
                    .map((word) => '*')
                    .join('')}`
                : test.student.label
            }
          />,
          test.student.grade || '-',
          test.grade || '-',
          test.language ? getLangNameFromCode(test.language)?.native : '-',
          test.teacher,
          // getTestResultsDisplay(test),
          // TO DO THIS IS A TAG COMPONENT
          test.started ? getListDateTime(test.started) : '-',
          getListDateTime(test.updated),
          <div
            key={test.id}
            className={`${styles.labelWrapper} ${styles[test.state]}`}
          >
            <Text
              type="body3"
              color="white"
              transKey={
                test.state === 'in-progress' || test.state === 'completed'
                  ? undefined
                  : (`${test.state}-capital` as TranslationKeysType)
              }
              untranslatedText={getUnTranslatedStatus(test)}
              isBold
            />
          </div>,
          <div key={test.id}>{getInfoContent(test)}</div>,
          test?.score ? (
            <div
              key={`${test.id}`}
              className={`${styles.scoreListWrapper} ${getScoreColor(test?.score)}`}
            >
              <Text
                type="body3"
                color="white"
                untranslatedText={String(test?.score)}
                isBold
              />
            </div>
          ) : (
            '-'
          ),
          <Menu key={test.id} shadow="md">
            <Menu.Target>
              <Box>
                <Icon
                  name="MenuDotsSvg"
                  color="turquoise"
                  w={18}
                  h={18}
                  hasCursor
                />
              </Box>
            </Menu.Target>

            <Menu.Dropdown className="menuDropdown">
              {(userRoles.isAdmin ||
                userRoles.isDeveloper ||
                userRoles.isResearcher ||
                userRoles.isStudyAdmin) && (
                <Menu.Item
                  onClick={() => onShowResultsClick(test.id)}
                  leftSection={<BsEyeFill size={19} />}
                  className={!test?.analysed ? styles.disabledMenuItem : ''}
                >
                  {t('results')}
                </Menu.Item>
              )}

              {(userRoles.isAdmin || userRoles.isDeveloper) && (
                <Menu.Item
                  onClick={() => onDeleteSessionClick(test.id)}
                  leftSection={
                    <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
                  }
                >
                  {t('delete')}
                </Menu.Item>
              )}
            </Menu.Dropdown>
          </Menu>,
        ]),
  };

  return isInformationLoading ? (
    <TableSkeleton numberOfColumns={DISPLAYED_LIST.head?.length} />
  ) : (
    <Table
      data={DISPLAYED_LIST}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      classNames={isResultsModeEnabled ? styles : {}}
      styles={{
        thead: {
          border: 'none',
        },
        table: {
          opacity: isDisabled ? 0.5 : 1,
          pointerEvents: isDisabled ? 'none' : 'auto',
        },
        th: isResultsModeEnabled
          ? {
              minWidth: 'fit-content',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }
          : {},

        td: isResultsModeEnabled
          ? {
              minWidth: 'fit-content',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }
          : {},
      }}
    />
  );
};

export default TestsTable;
