import {
  <PERSON>,
  CopyButton,
  Flex,
  Menu,
  Table,
  TableData,
  Tooltip,
  Transition,
} from '@mantine/core';
import { getLangNameFromCode } from 'language-name-map';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BsEyeFill } from 'react-icons/bs';
import { FaEye, FaEyeSlash, FaKey, FaLaptop, FaSort } from 'react-icons/fa';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import Icon from '@/components/Icon/Icon';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import {
  ColorsType,
  TestSessionsListResponseType,
  TranslationKeysType,
} from '@/types/common';

import { SESSION_STATE } from '../consts';
import styles from './TestsTable.module.css';

type TestsTableProps = {
  conductedTestList: TestSessionsListResponseType;
  isInformationLoading: boolean;
  isLoading: boolean;
  isDisabled?: boolean;
  isStudentLabelHidden: boolean;
  onOpenSession: (
    sessionId: TestSessionsListResponseType[0]['id'],
    isInProgress: boolean
  ) => void;
  onDeleteSessionClick: (
    sessionId: TestSessionsListResponseType[0]['id']
  ) => void;
  onShowResultsClick: (
    sessionId: TestSessionsListResponseType[0]['id']
  ) => void;
  onHideStudentLabel?: () => void;
  isScoreClicked?: boolean;
  onScoreClick?: () => void;
};

const getScoreColor = (score: number) => {
  switch (true) {
    case score > 0 && score <= 15:
      return styles.scoreRed;

    case score > 15 && score <= 30:
      return styles.scoreYellow;

    case score > 30 && score <= 70:
      return styles.scoreGray;

    case score > 70 && score <= 100:
      return styles.scoreGreen;

    default:
      return '';
  }
};

const TestsTable = ({
  conductedTestList,
  isDisabled = false,
  isInformationLoading,
  isLoading,
  isScoreClicked,
  isStudentLabelHidden,
  onDeleteSessionClick,
  onHideStudentLabel,
  onOpenSession,
  onScoreClick,
  onShowResultsClick,
}: TestsTableProps) => {
  const { i18n, t } = useTranslation();
  const { isSchoolRole, userRoles } = UserContext();

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${t(month)} ${day}  ‘${year} ${hoursAndMinutes}`;
  };

  const getInfoContent = (test: TestSessionsListResponseType[number]) => {
    switch (test.info.type) {
      case 'date':
        return (
          <Text
            type="body1"
            color="black"
            untranslatedText={getListDateTime(test.info.value)}
            align="center"
          />
        );
      case 'code':
        return (
          <CopyButton value={test.info.value} timeout={2000}>
            {({ copied, copy }) => (
              <Tooltip
                label={copied ? t('copied') : t('copy')}
                withArrow
                position="top"
              >
                <Box onClick={copy} className={styles.code}>
                  <FaKey size={13} color="var(--color-turquoise)" />

                  <Text
                    type="body1"
                    color="black"
                    untranslatedText={test.info.value}
                    align="center"
                  />
                </Box>
              </Tooltip>
            )}
          </CopyButton>
        );
      case 'score':
        return (
          <Text
            type="body1"
            color="blue"
            untranslatedText={`${test.info.value}%`}
            align="center"
          />
        );
      case 'text':
        return (
          <Text
            type="body1"
            color="black"
            untranslatedText={test.info.value ? `${test.info.value}` : '-'}
            align="center"
          />
        );
      case 'action':
        return (
          <button
            key={test.id}
            className={styles.infoButton}
            onClick={() =>
              onOpenSession(test.id, test.state === SESSION_STATE.IN_PROGRESS)
            }
            type="button"
          >
            <FaLaptop size={15} color="var(--color-blue)" />

            <Text type="button" color="blue" transKey="open-capital" />
          </button>
        );
      default:
        return '-';
    }
  };

  const getUnTranslatedStatus = (test: TestSessionsListResponseType[0]) => {
    switch (test.state) {
      case 'in-progress':
        return `${Math.round((test.progress || 0) * 100)}%`;
      case 'completed':
        return '100%';
      default:
        return undefined;
    }
  };

  const subTestsLabels = useMemo(() => {
    const allTitles = conductedTestList.flatMap((test) =>
      test?.report?.map((reportItem) => {
        const hasCodeTranslation = i18n.exists(
          (reportItem?.code?.includes('sub')
            ? `${reportItem.code}-reports-name`
            : reportItem?.code || '') as TranslationKeysType
        );

        if (hasCodeTranslation) {
          return t(
            reportItem?.code?.includes('sub')
              ? `${reportItem.code}-reports-name`
              : (reportItem?.code as TranslationKeysType)
          );
        }

        return reportItem.title;
      })
    );
    const uniqueTitles = Array.from(new Set(allTitles));

    return uniqueTitles;
  }, [conductedTestList, i18n, t]);

  const headTitles = [
    <Flex key="student and grade" gap={24}>
      <Box w={260} className={styles.studentLabelColumnHeader}>
        {t('student-capital')}

        {(userRoles.isAdmin ||
          userRoles.isStudyAdmin ||
          userRoles.isSchoolAdmin) && (
          <Box
            style={{
              height: '15px',
              cursor: 'pointer',
            }}
          >
            {isStudentLabelHidden ? (
              <FaEyeSlash
                size={16}
                color="var(--color-blue)"
                onClick={onHideStudentLabel}
              />
            ) : (
              <FaEye
                size={16}
                color="var(--color-blue)"
                onClick={onHideStudentLabel}
              />
            )}
          </Box>
        )}
      </Box>

      <Box w={50}>{t('grade-capital')}</Box>
    </Flex>,
    t('test-grade-capital'),
    isSchoolRole || userRoles.isAdmin ? t('class-capital') : null,
    t('language-capital'),
    t('monitor-capital'),
    t('start-date-capital'),
    t('last-date-capital'),
    t('progress-capital'),
    t('info-capital'),
    ...subTestsLabels,
    <Box
      key="mathpro-score"
      className={styles.headRow}
      style={{ maxWidth: 140 }}
      onClick={onScoreClick}
    >
      <Text
        untranslatedText="MathPro Score"
        type="body2"
        isBold
        color={(isScoreClicked ? 'turquoise' : 'gray900') as ColorsType}
      />

      <FaSort color={isScoreClicked ? 'var(--color-turquoise)' : undefined} />
    </Box>,
    t('results-capital'),
  ].filter(Boolean);

  const DISPLAYED_LIST: TableData = {
    head: headTitles,
    body: conductedTestList.map((test) =>
      [
        <Flex key={test.id} gap={24}>
          <Box w={260}>
            <Transition
              mounted={isStudentLabelHidden}
              transition="fade"
              duration={400}
              timingFunction="ease"
            >
              {(s) => (
                <div
                  style={{
                    ...s,
                    height: 0,
                    transform: 'translateY(5px)',
                  }}
                >
                  <Text
                    untranslatedText="******************"
                    isBold
                    lts={2}
                    hasTextTransition={false}
                  />
                </div>
              )}
            </Transition>

            <Transition
              mounted={!isStudentLabelHidden}
              transition="fade"
              duration={400}
              timingFunction="ease"
            >
              {(s) => (
                <div style={s}>
                  <Text
                    untranslatedText={test.student.label}
                    hasTextTransition={false}
                  />
                </div>
              )}
            </Transition>
          </Box>

          <Flex w={50} align="center" justify="center">
            <Text untranslatedText={test.studentGrade || '-'} />
          </Flex>
        </Flex>,
        test.grade || '-',
        isSchoolRole || userRoles.isAdmin ? test?.studentClass || '-' : null,
        test.language ? getLangNameFromCode(test.language)?.native : '-',
        test.teacher,
        <Box key="start-date" miw="150px">
          {test.started ? getListDateTime(test.started) : '-'}
        </Box>,
        <Box key="start-date" miw="150px">
          {getListDateTime(test.updated)}
        </Box>,
        <div
          key={test.id}
          className={`${styles.labelWrapper} ${styles[test.state]}`}
        >
          <Text
            type="body3"
            color="white"
            transKey={
              test.state === 'in-progress' || test.state === 'completed'
                ? undefined
                : (`${test.state}-capital` as TranslationKeysType)
            }
            untranslatedText={getUnTranslatedStatus(test)}
            isBold
          />
        </div>,
        getInfoContent(test),
        ...subTestsLabels.map((title) => {
          const matchedReport = test.report?.find((r) => {
            if (!r) return false;

            // direct title match (covers non-localized cases)
            if (r.title === title) return true;

            // try matching the translated name derived from the report code
            const translationKey = r.code?.includes('sub')
              ? `${r.code}-reports-name`
              : (r.code as TranslationKeysType | undefined);

            if (
              translationKey &&
              i18n.exists(translationKey as TranslationKeysType)
            ) {
              return t(translationKey as TranslationKeysType) === title;
            }

            return false;
          });

          const score = matchedReport?.score;

          return typeof score === 'number' ? (
            <Box
              key={`${test.id}-${title}`}
              className={`${styles.scoreListWrapper} ${getScoreColor(score)}`}
            >
              <Text
                type="body3"
                color="white"
                untranslatedText={String(score)}
                isBold
              />
            </Box>
          ) : (
            '-'
          );
        }),

        test?.score ? (
          <Box
            key={`${test.id}-mathpro-score`}
            className={`${styles.scoreListWrapper} ${getScoreColor(test?.score)}`}
          >
            <Text
              type="body3"
              color="white"
              untranslatedText={String(test?.score)}
              isBold
            />
          </Box>
        ) : (
          '-'
        ),
        <Flex key={`menu-${test.id}`} justify="center">
          <Menu shadow="md">
            <Menu.Target>
              <Box>
                <Icon
                  name="MenuDotsSvg"
                  color="turquoise"
                  w={18}
                  h={18}
                  hasCursor
                />
              </Box>
            </Menu.Target>

            <Menu.Dropdown className="menuDropdown">
              <Menu.Item
                onClick={() => onShowResultsClick(test.id)}
                leftSection={<BsEyeFill size={19} />}
                className={!test?.analysed ? styles.disabledMenuItem : ''}
              >
                {t('results')}
              </Menu.Item>

              {(userRoles.isAdmin ||
                userRoles.isDeveloper ||
                userRoles.isSchoolAdmin) && (
                <Menu.Item
                  onClick={() => onDeleteSessionClick(test.id)}
                  disabled={userRoles.isSchoolAdmin ? !!test?.started : false}
                  leftSection={
                    <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
                  }
                >
                  {t('delete')}
                </Menu.Item>
              )}
            </Menu.Dropdown>
          </Menu>
        </Flex>,
      ].filter(Boolean)
    ),
  };

  return isInformationLoading ? (
    <TableSkeleton numberOfColumns={DISPLAYED_LIST.head?.length} />
  ) : (
    <Table
      data={DISPLAYED_LIST}
      highlightOnHover
      className={styles.myTable}
      styles={{
        thead: {
          border: 'none',
          position: 'sticky',
          top: 0,
          backgroundColor: 'var(--color-white)',
        },
        table: {
          opacity: isDisabled ? 0.5 : 1,
          pointerEvents: isDisabled ? 'none' : 'auto',
        },
        td: {
          textAlign: 'center',
        },
        th: {
          textAlign: 'center',
          whiteSpace: 'nowrap',
        },
      }}
    />
  );
};

export default TestsTable;
