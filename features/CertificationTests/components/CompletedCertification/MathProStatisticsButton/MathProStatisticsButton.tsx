import { Box, Tooltip } from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import ActionButton from '@/components/ActionButton/ActionButton';

type MathProStatisticsButtonPropsType = {
  isDisabled: boolean;
  onClick: () => void;
};

const MathProStatisticsButton = ({
  isDisabled = false,
  onClick,
}: MathProStatisticsButtonPropsType): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Tooltip
      maw={360}
      inline={false}
      multiline
      style={{
        textAlign: 'center',
      }}
      label={t('mathpro-score-statistics-disabled-tooltip')}
      disabled={!isDisabled}
    >
      <Box pos="relative">
        <ActionButton
          onClick={onClick}
          type="mathproScoreStatistics"
          isDisabled={isDisabled}
        />
      </Box>
    </Tooltip>
  );
};

export default MathProStatisticsButton;
