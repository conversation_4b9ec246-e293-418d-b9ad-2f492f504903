.labelAndFieldWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-smd);
  margin-bottom: 24px;
}

.filterLabel {
  text-wrap: nowrap !important;
  white-space: nowrap !important;
}

.gradesWrapper {
  width: 110px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-smd);
}

.gradeSelector {
  min-width: 30px;
  max-width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--color-gray50);
  border-radius: var(--radius-roundedFull);
  transition: background-color 0.2s ease;
}

.selectedGrade {
  background-color: var(--color-turquoise);
  color: var(--color-white);
}

.disabledGrade {
  opacity: 0.5;
  pointer-events: none;
}
