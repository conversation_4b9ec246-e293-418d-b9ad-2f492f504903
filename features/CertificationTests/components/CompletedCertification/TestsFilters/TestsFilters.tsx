import { Box } from '@mantine/core';
import { DateInput, DateInputProps } from '@mantine/dates';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { SUPPORTED_SCHOOL_GRADES } from '@/common/consts';
import { mantineDateParser } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { ClassesContext } from '@/context/ClassesProvider';
import { GroupsContext } from '@/context/GroupsProvider';
import { UserContext } from '@/context/UserProvider';
import {
  FiltersType,
  UpdateTestListFilterType,
} from '@/features/CertificationTests/types';
import STUDIES from '@/services/studies';
import { StudiesListType } from '@/types/common';

import styles from './TestsFilters.module.css';

type TestsFiltersProps = {
  areStudiesSwitchChecked: boolean;
  selectedFilters: FiltersType;
  handleFilterUpdate: UpdateTestListFilterType;
  isToDateBeforeFrom: boolean;
  areTestsFetching?: boolean;
  onGradesChange: (value: string[]) => void;
};

const TestsFilters = ({
  areStudiesSwitchChecked,
  areTestsFetching = false,
  handleFilterUpdate,
  isToDateBeforeFrom,
  onGradesChange,
  selectedFilters,
}: TestsFiltersProps) => {
  const { t } = useTranslation();
  const { isSchoolRole, userRoles } = UserContext();

  const { classesList } = ClassesContext();
  const { groupsList } = GroupsContext();

  const {
    data: studiesList,
    isError,
    isLoading,
  } = useQuery<StudiesListType | null>({
    queryFn: STUDIES.GET_STUDIES,
    queryKey: [QUERY_KEYS.STUDIES_LIST],
    staleTime: 1000 * 60 * 60 * 24,
    enabled: userRoles.isAdmin,
  });

  const defaultToDate = isToDateBeforeFrom
    ? selectedFilters.from
    : selectedFilters.to;

  const selectedGrades = selectedFilters.grades || [];

  return (
    <>
      <div className={styles.labelAndFieldWrapper}>
        <Text transKey="grade-capital" type="body2" isBold fw={600} />

        <div className={styles.gradesWrapper}>
          {SUPPORTED_SCHOOL_GRADES.map((grade) => {
            const isSelected = selectedFilters.grades?.includes(grade);

            return (
              <Box
                id={grade}
                key={grade}
                className={`${styles.gradeSelector} cursorPointer ${isSelected && styles.selectedGrade} ${areTestsFetching && styles.disabledGrade} preventUserSelect`}
                onClick={() => {
                  if (areTestsFetching) return;

                  const filteredGrades = isSelected
                    ? selectedGrades.filter((g) => g !== grade)
                    : [...selectedGrades, grade];

                  onGradesChange(filteredGrades);
                  // setGradesDisplayValues(filteredGrades);
                }}
              >
                {grade}
              </Box>
            );
          })}
        </div>
      </div>

      {!areStudiesSwitchChecked && (
        <div className={styles.labelAndFieldWrapper}>
          <Text
            transKey={isSchoolRole ? 'class-capital' : 'group-capital'}
            type="body2"
            isBold
            fw={600}
          />

          <SelectDropdown
            value={selectedFilters.classOrGroup || ''}
            clearable
            data={
              (isSchoolRole ? classesList : groupsList).map((item) => ({
                label: item.name,
                value: item.id,
              })) || []
            }
            onChange={(value) => {
              handleFilterUpdate({
                key: 'classOrGroup',
                value,
              });
            }}
            isDisabled={isLoading || areTestsFetching}
            placeholder={t(isSchoolRole ? 'select-class' : 'select-group')}
          />
        </div>
      )}

      {areStudiesSwitchChecked && userRoles.isAdmin && !isError && (
        <div className={styles.labelAndFieldWrapper}>
          <Text transKey="study-capital" type="body2" isBold fw={600} />

          <SelectDropdown
            value={selectedFilters.study || ''}
            clearable
            isDisabled={isLoading || areTestsFetching}
            data={
              studiesList?.map((item) => ({
                label: item.name,
                value: item.id,
              })) || []
            }
            onChange={(value) => {
              handleFilterUpdate({
                key: 'study',
                value,
              });
            }}
            placeholder={t('select-study')}
          />
        </div>
      )}

      <div className={styles.labelAndFieldWrapper}>
        <Text
          transKey="period-of-time-capital"
          type="body2"
          isBold
          fw={600}
          className={styles.filterLabel}
        />

        <DateInput
          placeholder={t('from')}
          required
          highlightToday
          disabled={isLoading || areTestsFetching}
          allowDeselect
          valueFormat="DD/MM/YYYY"
          value={selectedFilters.from}
          clearable
          dateParser={mantineDateParser}
          onChange={(v) =>
            handleFilterUpdate({
              key: 'from',
              value: v,
            })
          }
        />

        <DateInput
          placeholder={t('to')}
          required
          clearable
          highlightToday
          allowDeselect
          disabled={isLoading || areTestsFetching}
          valueFormat="DD/MM/YYYY"
          value={defaultToDate}
          dateParser={mantineDateParser}
          onChange={(v) =>
            handleFilterUpdate({
              key: 'to',
              value: v,
            })
          }
          minDate={new Date(selectedFilters.from || 0)}
        />
      </div>
    </>
  );
};

export default TestsFilters;
