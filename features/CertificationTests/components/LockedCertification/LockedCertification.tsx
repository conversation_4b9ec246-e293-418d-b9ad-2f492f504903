import { Flex, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { BLUR_IMAGE_SVG, PRODUCTS } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import CertificationBuyCard from '@/components/CertificationCards/CertificationBuyCard/CertificationBuyCard';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import TESTS_DETAILS from '@/services/tests/details';
import { ProductsType, TranslationKeysType } from '@/types/common';

import { TestDetailsType } from '../../types';
import styles from './LockedCertification.module.css';

type LockedCertificationProps = {
  testType: ProductsType;
};

const MAPPED_TESTS_TRANSLATIONS = {
  'mathpro-s': {
    subtitle: 's-type-page-description-subtitle',
    description: 's-type-page-description',
  },
  'mathpro-d': {
    subtitle: 'd-type-page-description-subtitle',
    description: 'd-type-page-description',
  },
  'mathpro-r': {
    subtitle: 'r-type-page-description-subtitle',
    description: 'r-type-page-description',
  },
};

const LockedCertification = ({ testType }: LockedCertificationProps) => {
  const router = useRouter();
  const { userRoles } = UserContext();
  const { t } = useTranslation();

  // from the router and query tab get the Product name
  // const certificateName = router.query.tab;

  const {
    data: testDetails,
    error,
    isFetching: isFetchingProductDetails,
  } = useQuery<TestDetailsType | null>({
    enabled: router.isReady && Boolean(testType),
    queryFn: () => TESTS_DETAILS.GET_TEST_DETAILS(testType),
    queryKey: [QUERY_KEYS.CERTIFICATION_TEST_DETAILS_BY_TEST_TYPE, testType],
    staleTime: 1000 * 60 * 60 * 24,
  });

  const isTestNotFound = error?.message === 'test-not-found';

  // if (error?.message === 'test-not-found') {
  //   return (
  //     <Flex justify="center" align="center" h="100%">
  //       <Text transKey="no-certificate-found" type="h3" fw={300} color="blue" />
  //     </Flex>
  //   );
  // }

  // const associatedProductsText =
  //   testDetails?.products && testDetails?.products.length > 0 ? (
  //     <Text
  //       transKey="associated-products"
  //       type="h4"
  //       color="turquoise"
  //       fw={600}
  //       mb={30}
  //       mt={30}
  //     />
  //   ) : null;

  return (
    <div className={styles.wrapper}>
      <Card
        size="sm"
        radius="xs"
        borderSize={2}
        borderColor="gray50"
        shadow="none"
      >
        <div className={styles.cardWrapper}>
          <div className={styles.testContent}>
            {isFetchingProductDetails ? (
              <Skeleton visible height={110} />
            ) : (
              <Image
                src={`/images/tests/${PRODUCTS[testType].testImage}`}
                alt="product"
                width={110}
                height={110}
                placeholder="blur"
                blurDataURL={BLUR_IMAGE_SVG}
              />
            )}

            <div className={styles.descriptionWrapper}>
              {isFetchingProductDetails ? (
                <Skeleton visible height={40} />
              ) : (
                <Text
                  untranslatedText={
                    testDetails?.name || PRODUCTS[testType].typeName
                  }
                  type="h3"
                  fw={300}
                />
              )}

              {isFetchingProductDetails ? (
                <Skeleton visible height={40} />
              ) : (
                <Text
                  transKey={
                    MAPPED_TESTS_TRANSLATIONS[testType]
                      .subtitle as TranslationKeysType
                  }
                  type="subTitle2"
                  transVariables={{
                    productType: PRODUCTS[testType].typeName || '',
                  }}
                  fw={400}
                />
              )}
            </div>
          </div>

          <Text
            transKey="description"
            type="h3"
            transVariables={{
              productType: testDetails?.name || '',
            }}
            fw={300}
            mb={20}
          />

          {isFetchingProductDetails ? (
            <Skeleton visible height={100} />
          ) : (
            <Text
              transKey={
                MAPPED_TESTS_TRANSLATIONS[testType]
                  .description as TranslationKeysType
              }
              type="subTitle1"
              fw={400}
            />
          )}
        </div>
      </Card>

      {(userRoles.isDeveloper || userRoles.isIndependentTeacher) && (
        <>
          {isFetchingProductDetails ? (
            <Skeleton visible height={26} mt={30} mb={30} />
          ) : (
            <Text
              transKey="associated-products"
              type="h4"
              color="turquoise"
              fw={600}
              mb={30}
              mt={30}
            />
          )}

          {isFetchingProductDetails && <Skeleton visible height={150} />}

          {!isFetchingProductDetails &&
            testDetails &&
            !isTestNotFound &&
            testDetails?.products &&
            testDetails?.products.length > 0 && (
              <div className={styles.productsWrapper}>
                {testDetails?.products.map((product) => (
                  <CertificationBuyCard
                    key={product.stripeId}
                    productDetails={product}
                    isDisabled={userRoles.isSchoolTeacher}
                    tooltipText={t('contact-admin-to-buy')}
                  />
                ))}
              </div>
            )}

          {!isFetchingProductDetails && isTestNotFound && (
            <Text
              transKey="no-certificate-found"
              type="h3"
              fw={300}
              color="blue"
              mt={30}
            />
          )}
        </>
      )}
    </div>
  );
};

export default LockedCertification;
