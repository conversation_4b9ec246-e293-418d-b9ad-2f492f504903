import { Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { BLUR_IMAGE_SVG, PRODUCTS } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import CertificationBuyCard from '@/components/CertificationCards/CertificationBuyCard/CertificationBuyCard';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import TESTS_DETAILS from '@/services/tests/details';
import { ProductsType } from '@/types/common';

import { TestDetailsType } from '../../types';
import styles from './LockedCertification.module.css';

type LockedCertificationProps = {
  testType: ProductsType;
};

const LockedCertification = ({ testType }: LockedCertificationProps) => {
  const router = useRouter();
  const { userRoles } = UserContext();
  const { t } = useTranslation();

  const {
    data: testDetails,
    error,
    isFetching: isFetchingProductDetails,
  } = useQuery<TestDetailsType | null>({
    enabled: router.isReady && Boolean(testType),
    queryFn: () => TESTS_DETAILS.GET_TEST_DETAILS(testType),
    queryKey: [QUERY_KEYS.CERTIFICATION_TEST_DETAILS_BY_TEST_TYPE, testType],
    staleTime: 1000 * 60 * 60 * 24,
  });

  if (error?.message === 'test-not-found') {
    return (
      <div className={styles.wrapper}>
        <Text transKey="test-not-found" type="h3" fw={300} />
      </div>
    );
  }

  const associatedProductsText =
    testDetails?.products && testDetails?.products.length > 0 ? (
      <Text
        transKey="associated-products"
        type="h4"
        color="turquoise"
        fw={600}
        mb={30}
        mt={30}
      />
    ) : null;

  return (
    <div className={styles.wrapper}>
      <Card
        size="sm"
        radius="xs"
        borderSize={2}
        borderColor="gray50"
        shadow="none"
      >
        <div className={styles.cardWrapper}>
          <div className={styles.testContent}>
            {isFetchingProductDetails || !testDetails ? (
              <Skeleton visible height={110} />
            ) : (
              <Image
                src={`/images/tests/${PRODUCTS[testType].testImage}`}
                alt="product"
                width={110}
                height={110}
                placeholder="blur"
                blurDataURL={BLUR_IMAGE_SVG}
              />
            )}

            <div className={styles.descriptionWrapper}>
              {isFetchingProductDetails || !testDetails ? (
                <Skeleton visible height={40} />
              ) : (
                <Text untranslatedText={testDetails.name} type="h3" fw={300} />
              )}

              {isFetchingProductDetails || !testDetails ? (
                <Skeleton visible height={40} />
              ) : (
                <Text
                  transKey="typeCardDescription"
                  type="subTitle2"
                  transVariables={{
                    productType: PRODUCTS[testType].typeName || '',
                  }}
                  fw={400}
                />
              )}
            </div>
          </div>

          <Text
            transKey="description"
            type="h3"
            transVariables={{
              productType: testDetails?.name || '',
            }}
            fw={300}
            mb={20}
          />

          {isFetchingProductDetails || !testDetails ? (
            <Skeleton visible height={100} />
          ) : (
            <Text
              untranslatedText={testDetails?.description || ''}
              type="subTitle1"
              fw={400}
            />
          )}
        </div>
      </Card>

      {isFetchingProductDetails || !testDetails ? (
        <Skeleton visible height={26} mt={30} mb={30} />
      ) : (
        associatedProductsText
      )}

      {isFetchingProductDetails || !testDetails ? (
        <Skeleton visible height={150} />
      ) : (
        testDetails?.products &&
        testDetails?.products.length > 0 && (
          <div className={styles.productsWrapper}>
            {testDetails?.products.map((product) => (
              <CertificationBuyCard
                key={product.stripeId}
                productDetails={product}
                isDisabled={userRoles.isSchoolTeacher}
                tooltipText={t('contact-admin-to-buy')}
              />
            ))}
          </div>
        )
      )}
    </div>
  );
};

export default LockedCertification;
