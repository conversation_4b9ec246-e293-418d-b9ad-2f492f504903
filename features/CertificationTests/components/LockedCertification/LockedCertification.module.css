.wrapper {
  padding: var(--spacing-2xl) var(--spacing-xl);
}

.cardWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-height: 125px;
  background-color: var(--color-white);
  padding: var(--spacing-xl);
}

.testContent {
  width: 100%;
  display: flex;
  margin-bottom: var(--spacing-xl);
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 var(--spacing-smd);
  margin: 0 var(--spacing-xs);
  gap: var(--spacing-mdl);
}

.productsWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-mdl);
}
