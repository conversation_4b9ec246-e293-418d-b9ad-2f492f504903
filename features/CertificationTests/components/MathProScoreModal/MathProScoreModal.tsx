import { useQuery } from '@tanstack/react-query';
import { JSX } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TESTS_SESSIONS_LISTING from '@/services/tests/sessions-listing';
import {
  ProductsType,
  TestsResultsMathproScoreAveragesType,
} from '@/types/common';

import ResultsAggregate from './ResultsAggregate/ResultsAggregate';
import { AcceptedQueryParamsType } from './types';

type MathProScoreModalProps = {
  areStudiesEnabled: boolean;
  selectedStudyId?: string;
  testType: ProductsType;
  isOpen: boolean;
  onClose: () => void;
  acceptedQueryParams: AcceptedQueryParamsType;
};

const MathProScoreModal = ({
  acceptedQueryParams,
  areStudiesEnabled,
  isOpen,
  onClose,
  selectedStudyId,
  testType,
}: MathProScoreModalProps): JSX.Element => {
  const { data, error, isError, isFetching } =
    useQuery<TestsResultsMathproScoreAveragesType | null>({
      queryFn: () =>
        TESTS_SESSIONS_LISTING.GET_RESULTS_MATHPRO_SCORE({
          isStudy: areStudiesEnabled,
          testType,
          grades: acceptedQueryParams.grades,
          classId: acceptedQueryParams.classId,
          groupId: acceptedQueryParams.groupId,
          from: acceptedQueryParams.from,
          to: acceptedQueryParams.to,
          status: acceptedQueryParams.status,
          studyId: selectedStudyId,
        }),
      queryKey: [
        QUERY_KEYS.MATH_PRO_SCORE_MODAL,
        testType,
        acceptedQueryParams,
      ],
      enabled: Boolean(testType) && isOpen,
    });

  const groups = data?.groups || [];
  const reports = data?.report || [];
  const header = data?.header || {
    students: 0,
    duration: '',
    grades: [],
    study: '',
  };

  return (
    <div>
      <PrimaryModal
        isOpen={isOpen}
        content={
          <ResultsAggregate
            testType={testType}
            groups={groups}
            reports={reports}
            areStudiesEnabled={areStudiesEnabled}
            selectedStudyId={selectedStudyId}
            onClose={onClose}
            isLoading={isFetching}
            header={header}
          />
        }
      />
    </div>
  );
};

export default MathProScoreModal;
