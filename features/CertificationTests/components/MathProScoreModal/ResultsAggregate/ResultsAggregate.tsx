import {
  <PERSON>,
  Close<PERSON>utton,
  Di<PERSON>r,
  Flex,
  ScrollArea,
  Skeleton,
  Table,
  TableData,
} from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import { PRODUCTS } from '@/common/consts';
import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';
import {
  ProductsType,
  TestsResultsMathproScoreAveragesType,
} from '@/types/common';

import styles from './ResultsAggregate.module.css';

type ResultsAggregateProps = {
  groups: TestsResultsMathproScoreAveragesType['groups'];
  reports: TestsResultsMathproScoreAveragesType['report'];
  isLoading?: boolean;
  testType: ProductsType;
  areStudiesEnabled: boolean;
  selectedStudyId?: string;
  header: TestsResultsMathproScoreAveragesType['header'];
  onClose: () => void;
};

const TEXT_WIDTH = 80;

const DISTRIBUTIONS = [
  {
    key: 'lowest',
    label: 'very-low-equal-or-less-15',
    color: 'red',
  },
  {
    key: 'low',
    label: 'low-equal-or-less-30',
    color: 'yellow',
  },
  {
    key: 'average',
    label: 'average-equal-or-less-70',
    color: 'gray',
  },
  {
    key: 'high',
    label: 'high-more-than-70',
    color: 'green',
  },
] as const;

const DISTRIBUTION_HEADER_COLORS = {
  red: 'var(--color-red)',
  yellow: 'var(--color-yellow)',
  gray: 'var(--color-gray)',
  green: 'var(--color-green)',
} as const;

const TABLE_HEAD = DISTRIBUTIONS.map((item) => (
  <Flex
    key={item.key}
    align="center"
    justify="center"
    style={{
      backgroundColor: DISTRIBUTION_HEADER_COLORS[item.color],
    }}
    h={44}
  >
    <Text transKey={item.label} type="body2" isBold color="white" />
  </Flex>
));

const ResultsAggregate = ({
  areStudiesEnabled,
  groups,
  header,
  isLoading,
  onClose,
  reports,
  selectedStudyId,
  testType,
}: ResultsAggregateProps): JSX.Element => {
  const { t } = useTranslation();

  const TABLE_BODY = DISTRIBUTIONS.map((item) => {
    const group = groups.find((groupItem) => groupItem.group === item.key);
    const displayedValue = group?.count || 0;

    return (
      <Text
        key={item.key}
        untranslatedText={`${displayedValue}`}
        align="center"
        isBold
      />
    );
  });

  const RESULTS_DISTRIBUTION_TABLE_DATA: TableData = {
    head: TABLE_HEAD,
    body: [TABLE_BODY],
  };

  const SUBJECT_REPORTS_HEAD = reports.map((report) => report.title);

  const SUBJECT_REPORTS_BODY = reports.map((report) => (
    <Text
      key="subject-reports"
      untranslatedText={`${report?.score || '-'}`}
      fw={600}
    />
  ));

  const SUBJECT_REPORTS_TABLE_DATA: TableData = {
    head: SUBJECT_REPORTS_HEAD,
    body: [SUBJECT_REPORTS_BODY],
  };

  return (
    <Card size="xl" className={styles.wrapper} bg="gray50">
      <Flex justify="space-between">
        <Text transKey="summary-statistics" type="h3" fw={500} mb={32} />

        <CloseButton onClick={onClose} variant="outlined" />
      </Flex>

      <Flex direction="column" gap={12}>
        {isLoading ? (
          <Skeleton height={98} radius={12} />
        ) : (
          <Card bg="white">
            <Flex wrap="wrap">
              <Box>
                {/* Test Type will always be visible */}
                <Flex>
                  <Box w={TEXT_WIDTH}>
                    <Text transKey="type" isBold />
                  </Box>
                  :
                  <Text
                    untranslatedText={PRODUCTS[testType].typeName}
                    ml={20}
                  />
                </Flex>

                {/* Test Grade will always be visible */}
                <Flex>
                  <Box w={TEXT_WIDTH}>
                    <Text transKey="grade" isBold />
                  </Box>
                  :
                  <Text
                    untranslatedText={
                      (header?.grades || []).join(', ') || t('all-capital')
                    }
                    ml={20}
                  />
                </Flex>

                {areStudiesEnabled && (
                  <Flex>
                    <Box w={TEXT_WIDTH}>
                      <Text transKey="study" mr={20} isBold />
                    </Box>
                    :
                    <Text
                      untranslatedText={
                        header?.study === true
                          ? t('all-capital')
                          : header?.study || '-'
                      }
                      fw={250}
                      ml={16}
                    />
                  </Flex>
                )}

                {header?.group && (
                  <Flex>
                    <Box w={TEXT_WIDTH}>
                      <Text transKey="group" isBold />
                    </Box>
                    :
                    <Text untranslatedText={header?.group || '-'} ml={20} />
                  </Flex>
                )}

                {header?.class && (
                  <Flex>
                    <Box w={TEXT_WIDTH}>
                      <Text transKey="class" isBold />
                    </Box>
                    :
                    <Text untranslatedText={header?.class || '-'} ml={20} />
                  </Flex>
                )}
              </Box>

              <Divider orientation="vertical" ml={32} mr={32} />

              <Box>
                <Flex>
                  <Box mr={16}>
                    <Text transKey="sample" isBold />
                  </Box>
                  :
                  <Text
                    untranslatedText={
                      header?.students ? `${header.students}` : '-'
                    }
                    ml={20}
                  />
                </Flex>

                <Flex>
                  <Box mr={16}>
                    <Text transKey="average-duration" isBold />
                  </Box>
                  :
                  <Text
                    untranslatedText={
                      header?.duration ? `${header.duration}` : '-'
                    }
                    ml={20}
                  />
                </Flex>
              </Box>
            </Flex>
          </Card>
        )}

        {isLoading ? (
          <Skeleton height={225} radius={12} />
        ) : (
          <Card size="xl">
            <Text
              transKey="mathpro-score-level-distribution"
              type="h4"
              fw={250}
              mb={32}
            />

            <Flex w="100%" justify="center">
              <Table
                data={RESULTS_DISTRIBUTION_TABLE_DATA}
                maw={1000}
                styles={{
                  td: {
                    padding: 16,
                  },
                  th: {
                    padding: 0,
                  },
                }}
              />
            </Flex>
          </Card>
        )}

        {isLoading ? (
          <Skeleton height={237} radius={12} />
        ) : (
          <Card size="xl">
            <Text
              transKey="domain-average-percentile-scores"
              type="h4"
              fw={250}
            />

            <Flex w="100%" justify="center">
              <ScrollArea type="auto" p="24px 0" w="100%">
                <Table
                  data={SUBJECT_REPORTS_TABLE_DATA}
                  stickyHeader
                  stickyHeaderOffset={-1}
                  // classNames={styles}
                  styles={{
                    thead: {
                      border: 'none',
                    },
                    table: {
                      pointerEvents: 'auto',
                    },
                    th: {
                      minWidth: 'fit-content',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    },
                    td: {
                      minWidth: 'fit-content',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    },
                  }}
                />
              </ScrollArea>
            </Flex>
          </Card>
        )}
      </Flex>
    </Card>
  );
};

export default ResultsAggregate;
