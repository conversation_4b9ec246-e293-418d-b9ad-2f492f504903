import { useDisclosure } from '@mantine/hooks';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useTranslation } from 'react-i18next';

import { PRODUCTS } from '@/common/consts';
import { CERTIFICATE_PROGRESS_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import CertificationProgress from '@/components/CertificationCards/CertificationProgress/CertificationProgress';
import CollapsibleDescription from '@/components/CollapsibleCards/CollapsibleDescription/CollapsibleDescription';
import Text from '@/components/Text/Text';
import WelcomeBoard from '@/components/WelcomeBoard/WelcomeBoard';
import { UserContext } from '@/context/UserProvider';
import CollapsibleModule from '@/features/CertificationTests/components/IncompleteCertification/CollapsibleModule/CollapsibleModule';
import CERTIFICATES_PROGRESS from '@/services/certificate-progress';
import {
  CertificateProgressType,
  CertificationsProductsType,
  ProductsType,
} from '@/types/common';

import CertificationsProcessModal from './CertificationProcessModal/CertificationProcessModal';
import s from './IncompleteCertification.module.css';

const getProgressStatus = (
  isModuleInProgress: boolean,
  isModuleCompleted: boolean
) => {
  if (isModuleInProgress) return 'in-progress';

  if (isModuleCompleted) return 'completed';

  return 'locked';
};

const IncompleteCertification = ({
  testType,
}: {
  testType: ProductsType;
}): JSX.Element => {
  const router = useRouter();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { isUserFetching } = UserContext();

  const {
    data: certificationProgressDetails,
    error,
    isLoading: isCertificationProgressLoading,
  } = useQuery<CertificateProgressType | null>({
    enabled: Boolean(testType),
    queryFn: () =>
      CERTIFICATES_PROGRESS.GET_CERTIFICATE_PROGRESS_BY_TYPE(testType),
    queryKey: [QUERY_KEYS.CERTIFICATE_PROGRESS_BY_TEST_TYPE, testType],
    staleTime: 1000 * 60 * 60 * 24,
  });

  const currentActiveModule = certificationProgressDetails?.modules.find(
    (module) => module.active
  );

  const [opened, { close, open }] = useDisclosure(Boolean(router.query?.order));
  const isInformationLoading = isUserFetching || isCertificationProgressLoading;

  const isProgressReportMissing =
    error?.message ===
    CERTIFICATE_PROGRESS_ERRORS.PROGRESS_REPORT_FOR_USER_NOT_FOUND;

  const isTestTypeNotSupported =
    error?.message === CERTIFICATE_PROGRESS_ERRORS.NOT_SUPPORTED_TEST_TYPE;

  // TODO make it a common function use under the card certification progress as well
  const modulesProgress =
    certificationProgressDetails?.modules.map((module) => ({
      id: module.order,
      progress: Math.round(
        ((module?.answered || 0) / (module?.count || 1)) * 100
      ),
    })) || [];

  const numberOfCompletedModules =
    modulesProgress.filter((module) => module.progress === 100).length || 0;

  const onModuleCompletion = (
    activeTest: ProductsType,
    moduleNumber: CertificateProgressType['modules'][0]['order']
  ) => {
    queryClient.setQueryData<CertificateProgressType | null>(
      [QUERY_KEYS.CERTIFICATE_PROGRESS_BY_TEST_TYPE, activeTest],
      (prevData) => {
        if (!prevData) return null;

        const updatedModules = prevData.modules.map((module) => {
          if (module.order === moduleNumber) {
            return {
              ...module,
              completed: new Date().toISOString(),
              active: false,
            };
          }

          if (module.order === moduleNumber + 1) {
            return {
              ...module,
              active: true,
            };
          }

          return module;
        });

        return {
          ...prevData,
          modules: updatedModules,
        };
      }
    );
  };

  const updateProgressUnderIncompleteCertificate = (
    updatedAnsweredQuestions: number,
    moduleNumber: number
  ) => {
    queryClient.setQueryData<CertificateProgressType | null>(
      [QUERY_KEYS.CERTIFICATE_PROGRESS_BY_TEST_TYPE, testType],
      (prevData) => {
        if (!prevData) return null;

        const updatedModules = prevData.modules.map((module) => {
          if (module.order === moduleNumber) {
            return {
              ...module,
              answered: updatedAnsweredQuestions,
            };
          }
          return module;
        });

        return {
          ...prevData,
          modules: updatedModules,
        };
      }
    );
  };

  const updateProgressUnderCertificatesProducts = (
    updatedAnsweredQuestions: number,
    moduleNumber: number
  ) => {
    queryClient.setQueryData<CertificationsProductsType | null>(
      [`certificates-products`],
      // TODO : FIX THE ANY TYPE BELOW
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (prevData: any) => {
        if (!prevData) return null;

        // base on the schema above update the progress of the certificate
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const updatedProducts = prevData.map((product: any) => {
          if (product.type === testType) {
            return {
              ...product,
              progress: {
                ...product.progress,
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                modules: product.progress?.modules.map((module: any) => {
                  if (module.order === moduleNumber) {
                    return {
                      ...module,
                      answered: updatedAnsweredQuestions,
                    };
                  }
                  return module;
                }),
              },
            };
          }
          return product;
        });

        return updatedProducts;
      }
    );
  };

  const updateCertificateModuleAnsweredQuestions = (
    updatedAnsweredQuestions: number,
    moduleNumber: number
  ) => {
    updateProgressUnderIncompleteCertificate(
      updatedAnsweredQuestions,
      moduleNumber
    );

    // We are checking if the certificates-products is already in the cache
    const certificatedProducts =
      queryClient.getQueryData<CertificationsProductsType | null>([
        `certificates-products`,
      ]);

    // If the certificates-products is in the cache, we update the progress of the certificate otherwise we ignore it.
    // It will be fetched when the user navigates to the actual page
    if (certificatedProducts)
      updateProgressUnderCertificatesProducts(
        updatedAnsweredQuestions,
        moduleNumber
      );
  };

  return (
    <>
      {!currentActiveModule ? null : (
        <CertificationsProcessModal
          isOpened={opened}
          onClose={() => {
            close();
            router.replace(
              `${FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS}?tab=${testType}`
            );
          }}
          certificationType={testType}
          onUpdateCertificateModuleAnsweredQuestions={(
            updatedAnsweredQuestions
          ) =>
            updateCertificateModuleAnsweredQuestions(
              updatedAnsweredQuestions,
              currentActiveModule.order
            )
          }
          onModuleCompletion={(test, moduleNumb) =>
            onModuleCompletion(test, moduleNumb)
          }
        />
      )}

      {isProgressReportMissing && (
        <Text
          transKey="certification-progress-report--missing"
          isBold
          align="center"
        />
      )}

      {isTestTypeNotSupported && (
        <Text
          untranslatedText="The test type you are trying to access is not supported."
          isBold
          align="center"
        />
      )}

      {!isProgressReportMissing && !isTestTypeNotSupported && (
        <div className={s.wrapper}>
          <WelcomeBoard />

          <CertificationProgress
            title={certificationProgressDetails?.name || ''}
            description={t('certification-in-progress')}
            image={`/images/tests/${PRODUCTS[testType].certificateImage}`}
            steps={modulesProgress}
            numberOfCompletedSteps={numberOfCompletedModules}
            isLoading={isInformationLoading}
          />

          <CollapsibleDescription
            title={t('description')}
            isLoading={isInformationLoading}
            // TODO : Do not forget to remove the dummy text
            // description={certificationProgressDetails?.description || ''}
            description={t('dummy-lorem-ipsum')}
          />

          {certificationProgressDetails?.modules
            .sort((a, b) => a.order - b.order)
            .map((module) => {
              const isModuleInProgress = Boolean(module.active);
              const isModuleCompleted = Boolean(module.completed);

              const progressStatus = getProgressStatus(
                isModuleInProgress,
                isModuleCompleted
              );

              return (
                <CollapsibleModule
                  key={progressStatus}
                  moduleId={`${module.order}`}
                  isExpanded={isModuleInProgress}
                  title={module.title}
                  description={module.description}
                  feedback={module.startDate}
                  progressStatus={progressStatus}
                  answeredQuestions={module.answered}
                  {...(module.lesson?.url &&
                    module.lesson?.format === 'Video' && {
                      videoSection: {
                        title: module.title,
                        description: module.description,
                        video: module.lesson.url,
                      },
                    })}
                  {...(module.lesson?.url &&
                    module.lesson?.format === 'PDF' && {
                      pdfSection: {
                        title: module.title,
                        description: module.description,
                        pdf: module.lesson.url,
                      },
                    })}
                  testSection={{
                    title: module.title,
                    description: module.description,
                    onTakeTest: () => {
                      open();

                      if (module.active) {
                        router.push(
                          `${FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS}?tab=${testType}&order=${module.order}`
                        );
                      }
                    },
                    toolTipDescription: isModuleCompleted
                      ? t('completed-module-tooltip')
                      : `${t('locked-module-tooltip')} ${currentActiveModule?.title || ''}`,
                  }}
                  isLoading={isInformationLoading}
                />
              );
            })}
        </div>
      )}
    </>
  );
};

export default IncompleteCertification;
