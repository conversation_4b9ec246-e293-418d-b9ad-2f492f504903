import {
  AspectRatio,
  Box,
  Collapse,
  Skeleton,
  Space,
  Tooltip,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { JSX } from 'react';

import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { ColorsType, IconType, TranslationKeysType } from '@/types/common';

import s from './CollapsibleModule.module.css';

type ProgressType = 'in-progress' | 'completed' | 'locked';

type CollapsibleModulePropsType = {
  moduleId: string;
  title: string;
  description?: string;
  progressStatus: ProgressType;
  feedback?: string;
  isExpanded?: boolean;
  isLoading?: boolean;
  answeredQuestions: number;
  videoSection?: {
    title: string;
    description: string;
    video: string;
  };
  pdfSection?: {
    title: string;
    description: string;
    pdf: string;
  };
  testSection?: {
    title: string;
    description: string;
    toolTipDescription?: string;
    onTakeTest: () => void;
  };
};

type ProgressStatusDetailsType = {
  [key: string]: {
    icon: IconType;
    label: TranslationKeysType;
    color: ColorsType;
  };
};

const PROGRESS_STATUS_DETAILS: ProgressStatusDetailsType = {
  'in-progress': {
    icon: 'InProgressSvg',
    label: 'in-progress-capital',
    color: 'turquoise',
  },
  completed: {
    icon: 'CheckMarkWholeSvg',
    label: 'completed-capital',
    color: 'green',
  },
  locked: {
    icon: 'LockedSvg',
    label: 'locked-capital',
    color: 'danger',
  },
};

const CollapsibleModule = ({
  answeredQuestions,
  description,
  feedback = '',
  isExpanded,
  isLoading = false,
  moduleId,
  pdfSection,
  progressStatus,
  testSection,
  title,
  videoSection,
}: CollapsibleModulePropsType): JSX.Element => {
  const [opened, { toggle }] = useDisclosure(isExpanded);

  return (
    <div className={s.wrapper}>
      <Card
        size="xl"
        shadow="none"
        radius="sm"
        borderSize={2}
        borderColor="gray50"
      >
        <div className={s['action-title']}>
          <div className={s['module-details-wrapper']}>
            {isLoading ? (
              <Skeleton height={16} w={160} />
            ) : (
              <Text untranslatedText={title} type="subTitle" isBold />
            )}

            {isLoading ? (
              <Skeleton height={24} w={260} />
            ) : (
              <Text untranslatedText={description} type="h3" />
            )}

            {isLoading ? (
              <Skeleton height={32} width={400} />
            ) : (
              <div className={s['module-progress-details']}>
                <Icon
                  name={PROGRESS_STATUS_DETAILS[progressStatus].icon}
                  color={PROGRESS_STATUS_DETAILS[progressStatus].color}
                  w={30}
                  h={30}
                />

                <Text
                  transKey={PROGRESS_STATUS_DETAILS[progressStatus].label}
                  color={PROGRESS_STATUS_DETAILS[progressStatus].color}
                  type="subTitle2"
                  fw={600}
                />

                {feedback && (
                  <>
                    |
                    <Text untranslatedText={feedback} type="subTitle2" />
                  </>
                )}
              </div>
            )}
          </div>

          {isLoading ? (
            <Skeleton height={20} width={20} />
          ) : (
            <Icon
              name="ArrowDown"
              color="gray500"
              onClick={toggle}
              className={`${s['toggle-icon']} ${opened ? s.rotate : s['rotate-reset']}`}
            />
          )}
        </div>

        <Collapse
          in={opened}
          transitionDuration={400}
          transitionTimingFunction="ease-in-out"
        >
          <Space pb={24} />

          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '120px',
            }}
          >
            {videoSection && (
              <div className={s['content-wrapper']}>
                {isLoading ? (
                  <Skeleton height={400} radius={24} />
                ) : (
                  <div
                    className={s['left-side']}
                    style={{
                      maxWidth: 600,
                      width: '100%',
                      maxHeight: 400,
                      overflow: 'hidden',
                    }}
                  >
                    <Text
                      untranslatedText={videoSection.title}
                      type="subTitle"
                      isBold
                      mb={24}
                    />

                    {/* <Text untranslatedText={videoSection.description} type="h4" /> */}
                    <Text
                      untranslatedText="Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolorem voluptatibus magni consequuntur quibusdam necessitatibus consequatur at suscipit. Nihil nostrum repellat tempora ab dolor dolores recusandae iusto rerum dolorem molestiae saepe, eveniet libero quos quisquam molestias possimus numquam necessitatibus odio quaerat. Nihil temporibus labore laborum illum eum autem, aperiam quae reprehenderit, placeat inventore optio nesciunt unde corporis magni? Possimus eligendi repudiandae excepturi saepe recusandae voluptatibus ipsum sequi, odit, ipsa impedit repellendus a inventore eos quod corrupti amet adipisci consequatur praesentium ducimus illo! Dicta architecto, accusantium, veritatis ad fuga officia, cupiditate sed sapiente praesentium minima laboriosam fugit. Consectetur quisquam ducimus perspiciatis modi facere amet, praesentium sint earum a vitae, illum deleniti minus voluptates, et suscipit commodi aliquam molestiae. Vero similique quas provident?"
                      type="subTitle1"
                      lh={1.5}
                    />
                  </div>
                )}

                {isLoading ? (
                  <Skeleton height={400} radius={24} />
                ) : (
                  <div
                    style={{
                      maxWidth: 600,
                      width: '100%',
                      height: 400,
                      overflow: 'hidden',
                      borderRadius: 24,
                    }}
                  >
                    <AspectRatio ratio={1} w="100%" h="100%">
                      <iframe
                        src="https://www.youtube.com/embed/mzJ4vCjSt28"
                        // src={videoSection.video}
                        width="100%"
                        height="100%"
                        title="YouTube video player"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        style={{
                          border: 'none',
                        }}
                      />
                    </AspectRatio>
                  </div>
                )}
              </div>
            )}

            {testSection && (
              <div className={s['content-wrapper']}>
                {isLoading ? (
                  <Skeleton height={400} radius={24} />
                ) : (
                  <div
                    className={s['left-side']}
                    style={{
                      maxWidth: 600,
                      width: '100%',
                    }}
                  >
                    <Text
                      untranslatedText={testSection.title}
                      type="subTitle"
                      isBold
                      mb={24}
                    />

                    {/* <Text untranslatedText={videoSection.description} type="h4" /> */}
                    <Text
                      untranslatedText="Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolorem voluptatibus magni consequuntur quibusdam necessitatibus consequatur at suscipit. Nihil nostrum repellat tempora ab dolor dolores recusandae iusto rerum dolorem molestiae saepe, eveniet libero quos quisquam molestias possimus numquam necessitatibus odio quaerat. Nihil temporibus labore laborum illum eum autem, aperiam quae reprehenderit, placeat inventore optio nesciunt unde corporis magni? Possimus eligendi repudiandae excepturi saepe recusandae voluptatibus ipsum sequi, odit, ipsa impedit repellendus a inventore eos quod corrupti amet adipisci consequatur praesentium ducimus illo! Dicta architecto, accusantium, veritatis ad fuga officia, cupiditate sed sapiente praesentium minima laboriosam fugit. Consectetur quisquam ducimus perspiciatis modi facere amet, praesentium sint earum a vitae, illum deleniti minus voluptates, et suscipit commodi aliquam molestiae. Vero similique quas provident?"
                      type="subTitle1"
                      lh={1.5}
                    />
                  </div>
                )}

                {isLoading ? (
                  <Skeleton height={400} radius={24} />
                ) : (
                  <div
                    style={{
                      maxWidth: 600,
                      width: '100%',
                      height: 400,
                      overflow: 'hidden',
                    }}
                  >
                    <Card borderSize={1} borderColor="gray400">
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 24,
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '100%',
                        }}
                      >
                        <Icon name="CertificationTestSvg" color="blue" />

                        <Tooltip
                          label={testSection.toolTipDescription}
                          disabled={
                            progressStatus === 'in-progress' ||
                            !testSection.toolTipDescription
                          }
                        >
                          <Box>
                            <Button
                              isDisabled={progressStatus !== 'in-progress'}
                              transKey={
                                answeredQuestions > 0 &&
                                progressStatus === 'in-progress'
                                  ? 'continue-test-capital'
                                  : 'take-test-capital'
                              }
                              onClick={testSection.onTakeTest}
                              id={`take-test-${moduleId}`}
                            />
                          </Box>
                        </Tooltip>
                      </div>
                    </Card>
                  </div>
                )}
              </div>
            )}
          </div>
        </Collapse>
      </Card>
    </div>
  );
};

export default CollapsibleModule;
