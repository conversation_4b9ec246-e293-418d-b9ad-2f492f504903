.wrapper {
  width: 100%;
}

.action-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-icon {
  cursor: pointer;
  transition: all 0.4s ease;
  margin-right: 24px;
}

.rotate {
  transform: rotate(180deg);
}

.rotate-reset {
  transform: rotate(0deg);
}

.module-details-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.module-progress-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-smd);
}

.module-completion {
  display: flex;
  align-items: center;
}

.preview-wrapper {
  width: 600px;
  height: 400px;
}

.content-wrapper {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-2xl);
}
