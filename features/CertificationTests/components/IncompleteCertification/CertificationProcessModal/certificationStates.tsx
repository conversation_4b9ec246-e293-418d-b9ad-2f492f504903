import SuccessfulCertification from './SuccessfulCertification/SuccessfulCertification';
import { ModuleCertificationStateType } from './types';
import UnSuccessfulCertification from './UnSuccessfulCertification/UnSuccessfulCertification';
import WelcomeCertification from './WelcomeCertification/WelcomeCertification';

export const getDisplayedCertificationState = (
  certificationState: ModuleCertificationStateType,
  moduleNumber: number,
  moduleCompletionScore: number
) => {
  switch (certificationState) {
    case 'ModuleNotStarted':
      return <WelcomeCertification moduleName={`Module ${moduleNumber}`} />;
    case 'ModuleFailed':
      return <UnSuccessfulCertification minimumMark={moduleCompletionScore} />;
    case 'ModuleComplete':
      return (
        <SuccessfulCertification
          moduleName={`Module ${moduleNumber}`}
          successPercentage={moduleCompletionScore}
        />
      );
    case 'CertificateComplete':
      return (
        <SuccessfulCertification
          moduleName={`Module ${moduleNumber}`}
          successPercentage={moduleCompletionScore}
        />
      );
    default:
      return null;
  }
};
