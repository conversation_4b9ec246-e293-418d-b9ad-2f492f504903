import React from 'react';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import styles from './WelcomeCertification.module.css';

type WelcomeCertificationProps = {
  moduleName: string;
};

const WelcomeCertification = ({ moduleName }: WelcomeCertificationProps) => {
  return (
    <div className={styles.wrapper}>
      <div className={styles.titleWrapper}>
        <Icon name="LogoColorfulSvg" color="turquoise" />

        <div className={styles.verticalDivider} />

        <Text
          transKey="practice-test-for"
          type="h2"
          color="turquoise"
          fw={400}
          transVariables={{ moduleName }}
          className={styles.moduleName}
        />
      </div>

      <div className={styles.descriptionWrapper}>
        <Text
          transKey="minimum-mark"
          type="h4"
          mb={30}
          transVariables={{ minimumMark: '80' }}
        />

        <Text transKey="good-luck" type="h4" />
      </div>
    </div>
  );
};

export default WelcomeCertification;
