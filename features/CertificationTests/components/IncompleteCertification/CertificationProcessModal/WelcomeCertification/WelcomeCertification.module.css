.wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg2);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);

  @media (max-width: 429px) {
    & h4 {
      text-align: center !important;
    }

    & h2 {
      text-align: center !important;
    }
  }
}

.titleWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 468px;
  gap: var(--spacing-xl);

  @media (max-width: 429px) {
    flex-direction: column;
    max-width: fit-content;
  }
}

.verticalDivider {
  height: 100%;
  width: 1px;
  background-color: rgba(163, 165, 164, 0.4);

  @media (max-width: 429px) {
    width: 100%;
    height: 1px;
  }
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-2xl);
}

.moduleName {
  max-width: 264px;
}
