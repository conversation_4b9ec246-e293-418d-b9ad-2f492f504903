.wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
}

.completed-certification-wrapper {
  max-width: 1100px;
  width: 100%;
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.display-screen {
  position: relative;
  width: 900px;
  height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white);
  border-radius: var(--radius-sm);
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: var(--spacing-xl);

  scrollbar-width: none;
  -ms-overflow-style: none;
}

.actions-wrapper {
  display: flex;
  gap: var(--spacing-3xl);
  justify-content: center;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 var(--color-blue);
  }
  70% {
    transform: scale(1.02);
    box-shadow: 0 0 0 16px rgba(0, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

.pulse {
  animation-name: pulse;
  animation-duration: 1.4s;
  animation-iteration-count: 10;
}

/* ----------------------------------- */

.root {
  background-color: transparent;
}

.content {
  background-color: rgba(0, 0, 0, 0.92);
}

.overlay {
  background-color: transparent;
}

.inner {
}

.body {
  width: 100%;
  height: 100%;
  padding: 0;
}

.progressHeaderSkeleton {
  color: red;
}
