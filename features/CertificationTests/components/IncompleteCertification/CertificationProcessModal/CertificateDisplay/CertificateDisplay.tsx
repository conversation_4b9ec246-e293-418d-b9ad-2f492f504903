import Image from 'next/image';
import React from 'react';

import { BLUR_IMAGE_SVG, PRODUCTS } from '@/common/consts';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { ProductsType, UserDetailsType } from '@/types/common';

import styles from './CertificateDisplay.module.css';

type CertificateDisplayProps = {
  certificateType: ProductsType;
  fullName: UserDetailsType['profile']['firstName'];
  // affiliation?: UserDetailsType['profile']['affiliation'];
  onClose: () => void;
};

const CertificateDisplay = ({
  // affiliation,
  certificateType,
  fullName,
  onClose,
}: CertificateDisplayProps) => {
  return (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.certificateInfoWrapper}>
          <Image
            src={`/images/tests/${PRODUCTS[certificateType].certificateImage}`}
            alt="product"
            width={110}
            height={110}
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />

          <div className={styles.certificateTypeDetailsWrapper}>
            <Text
              untranslatedText={PRODUCTS[certificateType].typeName}
              type="h3"
              fw={300}
              align="center"
            />

            <Text
              transKey="typeCardDescription"
              type="subTitle2"
              transVariables={{
                productType: PRODUCTS[certificateType].typeName,
              }}
              fw={400}
              align="center"
            />
          </div>
        </div>

        <div className={styles.actionsWrapper}>
          <div className={styles.completionWrapper}>
            <Icon name="CheckMarkSvg" color="darkerGreen" />

            <Text
              transKey="completed-capital"
              type="subTitle2"
              fw={600}
              color="darkerGreen"
              align="center"
            />
          </div>

          <CloseButton onClick={onClose} variant="outlined" />
        </div>
      </div>

      <Card shadow="none" hasDynamicHeight className={styles.card}>
        <div className={`${styles.cardContent} specialBackground`}>
          <div className={styles.titleWrapper}>
            <Icon name="LogoColorfulSvg" color="turquoise" w={144} h={60} />

            <div className={styles.verticalDivider} />

            <Text
              transKey="type-certificate"
              type="h1"
              fw={250}
              transVariables={{
                certificateType: PRODUCTS[certificateType].typeName,
              }}
              className={styles.moduleName}
              align="center"
            />
          </div>

          <Text
            transKey="mathpro-certifies"
            type="h4"
            fw={300}
            mt={40}
            mb={40}
            transVariables={{
              certificateType: PRODUCTS[certificateType].typeName,
            }}
            align="center"
          />

          <div className={styles.fullNameWrapper}>
            <Text
              type="h4"
              fw={600}
              untranslatedText={fullName}
              align="center"
            />

            <div className={styles.verticalDividerSmall} />

            <Text
              type="body1"
              fw={400}
              transKey="mathematician-capital"
              align="center"
            />
          </div>

          <Text
            transKey="with-certification"
            type="h4"
            fw={300}
            mt={40}
            mb={40}
            transVariables={{
              certificateName: PRODUCTS[certificateType].typeName,
            }}
            align="center"
          />

          <Image
            src="/images/tests/signature.png"
            alt="signature"
            width={232}
            height={127}
          />
        </div>
      </Card>
    </div>
  );
};

export default CertificateDisplay;
