.wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg3);
  flex-direction: column;
  padding: var(--spacing-2xl);
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-2xl);
}

.certificateInfoWrapper {
  display: flex;
  gap: var(--spacing-md);

  @media screen and (max-width: 637px) {
    flex-direction: column;
    align-items: center;
  }
}

.certificateTypeDetailsWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--spacing-mdl);
}

.actionsWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.completionWrapper {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;

  & div:nth-child(1) {
    margin-bottom: -4px;
  }
}

.card {
  padding: var(--spacing-md);
}

.cardContent {
  outline: 1px solid var(--color-gray);
  border: 1px dashed var(--color-turquoise);
  outline-offset: var(--spacing-xs);
  height: 100%;
  min-height: 568px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);

  & h4 {
    line-height: normal !important;
  }
}

.titleWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xl);

  @media (max-width: 637px) {
    flex-direction: column;
    max-width: fit-content;
  }
}

.verticalDivider {
  height: 65px;
  width: 1px;
  background-color: rgba(163, 165, 164, 0.4);

  @media (max-width: 637px) {
    width: 100%;
    height: 1px;
  }
}

.fullNameWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);

  @media (max-width: 530px) {
    flex-direction: column;
  }
}

.verticalDividerSmall {
  height: 22px;
  width: 1px;
  background-color: rgba(163, 165, 164, 0.4);

  @media (max-width: 530px) {
    width: 100%;
    height: 1px;
  }
}
