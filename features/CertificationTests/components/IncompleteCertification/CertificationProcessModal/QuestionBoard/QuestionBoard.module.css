.image-wrapper {
  position: relative;
  width: 100%;
  height: 330px;
  margin-bottom: var(--spacing-md);
}

.questions-wrapper {
  position: relative;
  padding: 16px var(--spacing-5xl) 24px var(--spacing-5xl);
}

.align-items-start {
  align-self: flex-start;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-right {
  animation: slideInFromRight 0.4s ease;
}

.options-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.option-wrapper {
  display: flex;
  gap: var(--spacing-lg);
}
