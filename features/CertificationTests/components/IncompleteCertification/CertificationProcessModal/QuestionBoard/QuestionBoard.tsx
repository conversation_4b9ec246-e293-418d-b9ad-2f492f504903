import { Box, LoadingOverlay, Skeleton } from '@mantine/core';
import Image from 'next/image';

import { BLUR_IMAGE_SVG } from '@/common/consts';
import Checkbox from '@/components/Checkbox/Checkbox';
import Radio from '@/components/Radio/Radio';
import Text from '@/components/Text/Text';
import { ModuleProgressSchemaByCertificateAndOrderType } from '@/types/common';

import s from './QuestionBoard.module.css';

const QUESTION_TYPES = {
  SINGLE: 'single',
  MULTIPLE: 'multiple',
} as const;

type QuestionTypesType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];

type QuestionBoardPropsType = {
  moduleQuestions: ModuleProgressSchemaByCertificateAndOrderType['questions'];
  currentQuestion: number;
  selectedAnswers: number[];
  typeOfQuestion: QuestionTypesType;
  onOptionSelection: (selectedAnswer: number) => void;
  isLoading?: boolean;
  isAnswerInProgress?: boolean;
};

const QuestionBoard = ({
  currentQuestion,
  isAnswerInProgress = false,
  isLoading = false,
  moduleQuestions,
  onOptionSelection,
  selectedAnswers,
  typeOfQuestion,
}: QuestionBoardPropsType): JSX.Element => {
  const displayedImage = moduleQuestions[currentQuestion - 1]?.image;

  return isLoading ? (
    <Box pos="relative" w="100%" h="100%">
      <LoadingOverlay visible overlayProps={{ radius: 'sm', blur: 0 }} />
      <Skeleton w="100%" h="100%" />
    </Box>
  ) : (
    <div
      key={currentQuestion}
      className={`${s['questions-wrapper']} ${currentQuestion && s['animate-right']} ${displayedImage && s['align-items-start']}`}
    >
      {displayedImage && (
        <div className={s['image-wrapper']}>
          <Image
            src={`${displayedImage}`}
            alt="an image relative to the question"
            fill
            placeholder="blur"
            blurDataURL={BLUR_IMAGE_SVG}
          />
        </div>
      )}

      {typeOfQuestion === QUESTION_TYPES.MULTIPLE && (
        <Text
          lh={1}
          transKey="prompt-for-more-correct-answers"
          color="darkerBlue"
          isBold
          type="subTitle2"
          align="center"
        />
      )}

      <Text
        lh={1.5}
        untranslatedText={`${moduleQuestions[currentQuestion - 1].content}`}
        mb={16}
        mt={16}
      />

      <div className={s['options-wrapper']}>
        {moduleQuestions[currentQuestion - 1].options.map((option) => {
          return (
            <div key={option.order} className={`${s['option-wrapper']}`}>
              {typeOfQuestion === QUESTION_TYPES.MULTIPLE && (
                <Checkbox
                  value={`${option.order}`}
                  onChange={() => onOptionSelection(option.order)}
                  isChecked={selectedAnswers.includes(option.order) || false}
                  variant="primary"
                  isRequired
                  isDisabled={isAnswerInProgress}
                />
              )}

              {typeOfQuestion === QUESTION_TYPES.SINGLE && (
                <Radio
                  value={option.order}
                  isChecked={selectedAnswers.includes(option.order)}
                  onChange={() => onOptionSelection(option.order)}
                  isDisabled={isAnswerInProgress}
                />
              )}

              <Text
                untranslatedText={`${option.content}`}
                onClick={() => onOptionSelection(option.order)}
                hasUserSelect={false}
                className="cursorPointer"
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default QuestionBoard;
