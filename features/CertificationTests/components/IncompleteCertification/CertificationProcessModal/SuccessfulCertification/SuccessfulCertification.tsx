import React from 'react';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import styles from './SuccessfulCertification.module.css';

type SuccessfulCertificationProps = {
  moduleName: string;
  successPercentage: number;
};

const SuccessfulCertification = ({
  moduleName,
  successPercentage,
}: SuccessfulCertificationProps) => {
  return (
    <div className={styles.wrapper}>
      <Icon name="CheckMarkWholeSvg" color="yellow" />

      <div className={styles.descriptionWrapper}>
        <Text
          transKey="congrats"
          type="h1"
          mb={30}
          color="white"
          className={styles.heading}
        />

        <Text
          transKey="certifications-complete"
          type="h4"
          color="white"
          transVariables={{
            moduleName,
            successPercentage: `${successPercentage}`,
          }}
          align="center"
        />
      </div>
    </div>
  );
};

export default SuccessfulCertification;
