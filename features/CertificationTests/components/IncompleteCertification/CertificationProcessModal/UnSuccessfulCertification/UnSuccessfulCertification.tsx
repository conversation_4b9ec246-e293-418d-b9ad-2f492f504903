import React from 'react';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import styles from './UnSuccessfulCertification.module.css';

type UnSuccessfulCertificationProps = {
  minimumMark: number;
};

const UnSuccessfulCertification = ({
  minimumMark,
}: UnSuccessfulCertificationProps) => {
  return (
    <div className={styles.wrapper}>
      <Icon name="FailIconSvg" color="turquoise" />

      <Text
        transKey="you-did-not-make-it"
        type="h1"
        mb={30}
        color="white"
        className={styles.heading}
      />

      <Text
        transKey="failure-reason"
        type="h4"
        mb={30}
        color="white"
        transVariables={{ minimumMark: `${minimumMark}` }}
        align="center"
        className={styles.explanation}
      />

      <div className={styles.descriptionWrapper}>
        <Text
          transKey="whats-next"
          type="body1"
          mb={20}
          fw={600}
          color="white"
          align="center"
        />

        <Text
          transKey="review-answers"
          type="body1"
          mb={10}
          color="white"
          align="center"
        />

        <Text
          transKey="brush-up-content"
          type="body1"
          mb={10}
          color="white"
          align="center"
        />

        <Text
          transKey="try-again"
          type="body1"
          mb={10}
          color="white"
          align="center"
        />
      </div>
    </div>
  );
};

export default UnSuccessfulCertification;
