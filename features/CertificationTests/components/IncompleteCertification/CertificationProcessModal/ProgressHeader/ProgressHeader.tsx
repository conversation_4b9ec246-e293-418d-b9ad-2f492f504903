import { Box, CloseButton, Progress } from '@mantine/core';

import { PRODUCTS } from '@/common/consts';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { ProductsType } from '@/types/common';

import s from './ProgressHeader.module.css';

type ProgressHeaderPropsType = {
  certificationType: ProductsType;
  moduleNumber: number;
  currentQuestion: number;
  totalNumberOfQuestions: number;
  currentProgress: number;
  isModuleInProgress: boolean;
  onClose: () => void;
};

const ProgressHeader = ({
  certificationType,
  currentProgress,
  currentQuestion,
  isModuleInProgress,
  moduleNumber,
  onClose,
  totalNumberOfQuestions,
}: ProgressHeaderPropsType): JSX.Element => {
  return (
    <Box className={s['progress-header-wrapper']}>
      <div className={s['progress-header']}>
        <Icon
          name={PRODUCTS[certificationType].certificateIcon}
          color="white"
          w={24}
          h={30}
        />

        <Text
          untranslatedText={PRODUCTS[certificationType].typeName}
          color="white"
        />

        <Text transKey="typeof-certification" color="white" />

        <Text untranslatedText="|" color="white" />

        <Text
          transKey="module-number"
          transVariables={{
            moduleNumber: `${moduleNumber}`,
          }}
          color="white"
        />

        {isModuleInProgress && <Text untranslatedText="|" color="white" />}

        {isModuleInProgress && (
          <Text
            transKey="certification-process-question"
            color="white"
            transVariables={{
              activeQuestion: `${currentQuestion}`,
              totalQuestions: `${totalNumberOfQuestions}`,
            }}
          />
        )}
      </div>

      <div className={s['progress-button-wrapper']}>
        <Progress value={currentProgress} color="green" w={150} h={6} />

        <CloseButton
          onClick={() => {
            onClose();
          }}
          variant="subtle"
          autoFocus={false}
        />
      </div>
    </Box>
  );
};

export default ProgressHeader;
