/* eslint-disable react-hooks/exhaustive-deps */
import { LoadingOverlay, Modal, Skeleton, Tooltip } from '@mantine/core';
import {
  useIsFetching,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { CERTIFICATE_PROGRESS_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import CERTIFICATES_PROGRESS from '@/services/certificate-progress';
import {
  ModuleProgressSchemaByCertificateAndOrderType,
  ProductsType,
} from '@/types/common';

import CertificateDisplay from './CertificateDisplay/CertificateDisplay';
import s from './CertificationProcessModal.module.css';
import { getDisplayedCertificationState } from './certificationStates';
import { MODULE_PROGRESS_STATES } from './consts';
import ProgressHeader from './ProgressHeader/ProgressHeader';
import QuestionBoard from './QuestionBoard/QuestionBoard';
import { ModuleCertificationStateType } from './types';

type CertificationsProcessModalPropsType = {
  isOpened: boolean;
  certificationType: ProductsType;
  onClose: () => void;
  onUpdateCertificateModuleAnsweredQuestions: (
    updatedAnsweredQuestions: number
  ) => void;
  onModuleCompletion: (test: ProductsType, moduleNumb: number) => void;
};

const CertificationsProcessModal = ({
  certificationType,
  isOpened,
  onClose,
  onModuleCompletion,
  onUpdateCertificateModuleAnsweredQuestions,
}: CertificationsProcessModalPropsType): JSX.Element => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const router = useRouter();
  const selectedModule = Number(router.query.order as string);
  const activeModuleNumber =
    typeof selectedModule === 'number' ? selectedModule : 0;

  const { invalidateUser, user } = UserContext();
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [moduleCertificationState, setModuleCertificationState] =
    useState<ModuleCertificationStateType>('ModuleNotStarted');
  const [moduleCompletionScore, setModuleCompletionScore] = useState<number>(0);
  const isFetching = useIsFetching();
  const [scale, setScale] = useState(1);

  const { data: moduleDetails, error } =
    useQuery<ModuleProgressSchemaByCertificateAndOrderType | null>({
      enabled:
        Boolean(certificationType) && Boolean(activeModuleNumber) && isOpened,
      queryFn: () =>
        CERTIFICATES_PROGRESS.GET_MODULE_BY_CERTIFICATION_AND_ORDER(
          certificationType,
          activeModuleNumber
        ),
      queryKey: [
        QUERY_KEYS.MODULE_DETAILS,
        certificationType,
        activeModuleNumber,
      ],
      staleTime: 1000 * 60 * 60 * 24,
    });

  const areInformationLoading = moduleDetails === undefined || !router.isReady;

  const isUserProgressReportMissing =
    error?.message ===
    CERTIFICATE_PROGRESS_ERRORS.PROGRESS_REPORT_FOR_USER_NOT_FOUND;

  const updateAnsweredQuestions = (updatedAnsweredQuestions: number) => {
    queryClient.setQueryData<ModuleProgressSchemaByCertificateAndOrderType | null>(
      [QUERY_KEYS.MODULE_DETAILS, certificationType, activeModuleNumber],
      (prevData) => {
        if (!prevData) return null;

        return {
          ...prevData,
          answered: updatedAnsweredQuestions,
        };
      }
    );
  };

  const answeredQuestions = moduleDetails?.answered || 0;
  const currentQuestion = answeredQuestions + 1;
  const totalNumberOfQuestions = moduleDetails?.count || 1;

  const isModuleNotStarted =
    answeredQuestions === 0 &&
    moduleCertificationState === MODULE_PROGRESS_STATES.ModuleNotStarted;

  const moduleQuestions = (moduleDetails?.questions || []).sort(
    (a, b) => a.order - b.order
  );
  const typeOfQuestion = moduleDetails?.questions[currentQuestion - 1]?.type;

  const isModuleCompleted = Boolean(moduleDetails?.completed);
  const isModalOpen = isModuleCompleted ? false : isOpened;

  const isQuestionBoardDisplayed =
    MODULE_PROGRESS_STATES.ModuleActive === moduleCertificationState ||
    answeredQuestions > 0;

  const currentProgress = (currentQuestion * 100) / (moduleDetails?.count || 1);
  const displayedProgress = !isQuestionBoardDisplayed ? 0 : currentProgress;

  const answerQuestionMutation = useMutation({
    mutationFn: CERTIFICATES_PROGRESS.ANSWER_QUESTION_FROM_CERTIFICATE_MODULE,
    onError: () => {
      // TODO - use CERTIFICATE_PROGRESS_ERRORS under errors.ts to handle each case separately if you want
      document.location.reload();
    },
    onSuccess: async (res) => {
      if (!res) return;

      if (res.results === MODULE_PROGRESS_STATES.ModuleActive) {
        updateAnsweredQuestions(currentQuestion);
        onUpdateCertificateModuleAnsweredQuestions(currentQuestion);
        setSelectedAnswers([]);
        return;
      }

      if (res.results === MODULE_PROGRESS_STATES.ModuleComplete) {
        setModuleCertificationState(MODULE_PROGRESS_STATES.ModuleComplete);
        onUpdateCertificateModuleAnsweredQuestions(currentQuestion);
      }

      if (res.results === MODULE_PROGRESS_STATES.ModuleFailed) {
        onUpdateCertificateModuleAnsweredQuestions(0);
        setModuleCertificationState(MODULE_PROGRESS_STATES.ModuleFailed);
      }

      if (res.results === MODULE_PROGRESS_STATES.CertificateComplete) {
        setModuleCertificationState(MODULE_PROGRESS_STATES.CertificateComplete);
      }

      updateAnsweredQuestions(0);
      setModuleCompletionScore(res?.score || 0);
      setSelectedAnswers([]);
    },
  });

  const onCompletedModule = async () => {
    onClose();

    const segment = document.getElementById(
      'certification-tests-segmented-control'
    );

    const element = document.getElementById(`take-test-${activeModuleNumber}`);

    if (segment) {
      segment.scrollIntoView({
        behavior: 'instant',
      });
    }

    onModuleCompletion(certificationType, activeModuleNumber);

    setTimeout(() => {
      setModuleCertificationState(MODULE_PROGRESS_STATES.ModuleNotStarted);
      element?.scrollIntoView({
        behavior: 'smooth',
      });

      element?.classList.add(s.pulse);
    }, 1000);
  };

  const onModuleFailure = () => {
    onClose();
    setTimeout(() => {
      setModuleCertificationState(MODULE_PROGRESS_STATES.ModuleNotStarted);
    }, 500);
  };

  const onCloseModuleModal = () => {
    onClose();

    switch (moduleCertificationState) {
      case MODULE_PROGRESS_STATES.ModuleFailed:
        onModuleFailure();
        break;
      case MODULE_PROGRESS_STATES.ModuleComplete:
        onCompletedModule();
        break;
      case MODULE_PROGRESS_STATES.CertificateComplete:
        invalidateUser();
        break;
      default:
        break;
    }
  };

  // TODO - This can be moved up to the parent and passed as a prop and remove the useEffect from here
  useEffect(() => {
    if (
      moduleCertificationState === MODULE_PROGRESS_STATES.ModuleActive &&
      answeredQuestions === 0
    ) {
      setModuleCertificationState(MODULE_PROGRESS_STATES.ModuleNotStarted);
    }
    setSelectedAnswers([]);
  }, [certificationType]);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      if (width > 1000 && height > 900) {
        setScale(1);
      } else {
        const scaleFactor = Math.min(width / 1000, height / 900);
        setScale(scaleFactor);
      }
    };

    window.addEventListener('resize', handleResize);

    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <Modal
      opened={isModalOpen}
      onClose={() => {}}
      fullScreen
      withCloseButton={false}
      classNames={s}
      autoFocus={false}
      trapFocus
    >
      <div className={s.wrapper}>
        {isUserProgressReportMissing && (
          <Text
            untranslatedText="User progress report is missing, please refresh the page."
            isBold
            align="center"
          />
        )}

        {!isUserProgressReportMissing &&
          moduleCertificationState === 'ClaimCertificate' && (
            <div className={s['completed-certification-wrapper']}>
              <CertificateDisplay
                certificateType={certificationType}
                fullName={`${user.profile.firstName} ${user.profile.lastName}`}
                onClose={() => {
                  onClose();
                  invalidateUser();
                }}
              />
            </div>
          )}

        {!isUserProgressReportMissing &&
          moduleCertificationState !== 'ClaimCertificate' && (
            <div
              className={s['content-wrapper']}
              style={{
                transform: `scale(${scale})`,
              }}
            >
              {areInformationLoading ? (
                <Skeleton h={60} opacity={0.05} mb={16} />
              ) : (
                <ProgressHeader
                  certificationType={certificationType}
                  moduleNumber={activeModuleNumber}
                  currentQuestion={currentQuestion}
                  totalNumberOfQuestions={totalNumberOfQuestions}
                  currentProgress={displayedProgress}
                  onClose={onCloseModuleModal}
                  isModuleInProgress={isQuestionBoardDisplayed}
                />
              )}

              <div className={s['display-screen']}>
                {areInformationLoading && (
                  <LoadingOverlay
                    visible
                    overlayProps={{ blur: isModalOpen ? 30 : 7 }}
                  />
                )}

                {isQuestionBoardDisplayed && typeOfQuestion && (
                  <QuestionBoard
                    moduleQuestions={moduleQuestions}
                    currentQuestion={currentQuestion}
                    selectedAnswers={selectedAnswers}
                    typeOfQuestion={typeOfQuestion}
                    isLoading={false}
                    isAnswerInProgress={answerQuestionMutation.isPending}
                    onOptionSelection={(selectedAnswer) => {
                      if (typeOfQuestion === 'multiple') {
                        setSelectedAnswers((prev) => {
                          if (prev.includes(selectedAnswer)) {
                            return prev.filter(
                              (item) => item !== selectedAnswer
                            );
                          }
                          return [...prev, selectedAnswer];
                        });
                      }

                      if (typeOfQuestion === 'single') {
                        setSelectedAnswers([selectedAnswer]);
                      }
                    }}
                  />
                )}

                {!isQuestionBoardDisplayed &&
                  getDisplayedCertificationState(
                    moduleCertificationState,
                    activeModuleNumber,
                    moduleCompletionScore
                  )}
              </div>

              {isModuleNotStarted && (
                <Button
                  transKey="start-capital"
                  hasFullWidth
                  onClick={() => {
                    setModuleCertificationState(
                      MODULE_PROGRESS_STATES.ModuleActive
                    );
                  }}
                  isDisabled={areInformationLoading}
                />
              )}

              {isQuestionBoardDisplayed && (
                <Tooltip
                  label={t('select-answer-tooltip')}
                  disabled={selectedAnswers.length > 0}
                >
                  <div className={s['actions-wrapper']}>
                    <Button
                      transKey="submit-capital"
                      isLoading={answerQuestionMutation.isPending}
                      isDisabled={!selectedAnswers.length}
                      onClick={() => {
                        if (selectedAnswers) {
                          answerQuestionMutation.mutate({
                            certificationType,
                            module: activeModuleNumber,
                            question: currentQuestion,
                            selectedAnswers,
                          });
                        }
                      }}
                    />
                  </div>
                </Tooltip>
              )}

              {moduleCertificationState ===
                MODULE_PROGRESS_STATES.ModuleFailed && (
                <Button
                  untranslatedText={`${t('go-back-to-module')} ${activeModuleNumber}`}
                  hasFullWidth
                  onClick={onModuleFailure}
                />
              )}

              {moduleCertificationState ===
                MODULE_PROGRESS_STATES.ModuleComplete && (
                <Button
                  untranslatedText={`${t('go-to-module')} ${activeModuleNumber + 1}`}
                  hasFullWidth
                  isLoading={Boolean(isFetching)}
                  onClick={onCompletedModule}
                />
              )}

              {moduleCertificationState ===
                MODULE_PROGRESS_STATES.CertificateComplete && (
                <Button
                  untranslatedText={t('claim-certificate-capital')}
                  hasFullWidth
                  onClick={() => {
                    setModuleCertificationState('ClaimCertificate');
                  }}
                />
              )}
            </div>
          )}
      </div>
    </Modal>
  );
};

export default CertificationsProcessModal;
