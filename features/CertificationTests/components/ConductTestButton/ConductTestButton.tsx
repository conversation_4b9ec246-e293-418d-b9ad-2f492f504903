import { useState } from 'react';

import Button from '@/components/Button/Button';
import ConductTestModal from '@/components/ConductTestModal/ConductTestModal';

type ConductTestButtonPropsType = {
  isDisabled: boolean;
};

const ConductTestButton = ({
  isDisabled,
}: ConductTestButtonPropsType): JSX.Element => {
  const [isConductTestModalOpen, setIsConductTestModalOpen] = useState(false);

  return (
    <>
      <Button
        transKey="create-test-capital"
        type="submit"
        isDisabled={isDisabled}
        isLocked={isDisabled}
        onClick={() => setIsConductTestModalOpen(true)}
      />

      <ConductTestModal
        selectedStudents={[]}
        isOpen={isConductTestModalOpen}
        onCloseModal={() => {
          setIsConductTestModalOpen(false);
        }}
      />
    </>
  );
};

export default ConductTestButton;
