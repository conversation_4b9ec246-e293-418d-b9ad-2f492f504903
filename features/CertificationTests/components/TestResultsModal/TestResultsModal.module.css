.modalContent {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  max-width: 1340px;

  margin: 0 auto;
}

.root {
  background-color: transparent;
}

.content {
  background-color: transparent;
}

.overlay {
  background-color: var(--color-gray50);
  margin: 0;
  padding: 0;
}

.body {
  max-width: 1440px;
  min-width: 1000px;
  width: 100%;
  min-height: 100%;
  height: fit-content;
  padding: var(--spacing-xl);
  background-color: var(--color-gray50);
  margin: 0 auto;
}
