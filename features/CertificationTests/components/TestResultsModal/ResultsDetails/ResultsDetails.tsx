import React from 'react';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';
import { TestScoresResultsType, TranslationKeysType } from '@/types/common';

import s from './ResultsDetails.module.css';

type ResultsDetailsProps = {
  data: TestScoresResultsType | null;
  isLoading?: boolean;
};

const ResultsDetails = ({ data, isLoading }: ResultsDetailsProps) => {
  const { i18n, t } = useTranslation();

  const formatDateToLongForm = (dateString: string): string => {
    const date = new Date(dateString);

    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    };

    const selectedLanguage = i18n.language === 'en' ? 'en-GB' : i18n.language;

    return date.toLocaleDateString(selectedLanguage, options).replace(/,/g, '');
  };

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${day} ${t(month)} ‘${year} ${hoursAndMinutes}`;
  };

  return (
    !isLoading &&
    data && (
      <Card size="xl" className={s.wrapper}>
        <div className={s.column}>
          <Text transKey="school" type="h4" fw={300} mb={12} />

          {data?.student?.school && (
            <div className={s.row}>
              <Text transKey="name-capital" type="label" fw={500} mt={2} />

              <Text
                untranslatedText={data?.student?.school || ''}
                type="body2"
                fw={700}
              />
            </div>
          )}

          {data?.session?.examiner && (
            <div className={s.row}>
              <Text transKey="examiner-capital" type="label" fw={500} mt={2} />

              <Text
                untranslatedText={data?.session?.examiner || ''}
                type="body2"
                fw={700}
              />
            </div>
          )}
        </div>

        <div className={s.column}>
          <Text transKey="student-info" type="h4" fw={300} mb={12} />

          {data?.student?.gender && (
            <div className={s.row}>
              <Text transKey="gender-capital" type="label" fw={500} mt={2} />

              <Text
                transKey={data.student.gender as TranslationKeysType}
                type="body2"
                fw={700}
              />
            </div>
          )}

          {data?.student?.age && (
            <div className={s.row}>
              <Text transKey="age-capital" type="label" fw={500} mt={2} />

              <Text
                untranslatedText={data.student.age || ' '}
                type="body2"
                fw={700}
              />
            </div>
          )}

          {data?.student.dob && (
            <div className={s.studentInfoWrapper}>
              <div className={s.row}>
                <Text
                  transKey="birthday-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={formatDateToLongForm(
                    data?.student.dob || ''
                  )}
                  type="body2"
                  fw={700}
                />
              </div>
            </div>
          )}

          <div className={s.row}>
            {data?.student.hometown && (
              <div className={s.row}>
                <Text
                  transKey="hometown-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data?.student.hometown || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}
          </div>
        </div>

        <div className={s.column}>
          <Text transKey="test-stats" type="h4" fw={300} mb={12} />

          {data?.session?.started && (
            <div className={s.row}>
              <Text transKey="started-capital" type="label" fw={500} mt={2} />

              <Text
                untranslatedText={getListDateTime(data?.session?.started || '')}
                type="body2"
                fw={700}
              />
            </div>
          )}

          {data?.session?.completed && (
            <div className={s.row}>
              <Text transKey="completed-capital" type="label" fw={500} mt={2} />

              <Text
                untranslatedText={getListDateTime(
                  data?.session?.completed || ''
                )}
                type="body2"
                fw={700}
              />
            </div>
          )}

          {data?.session?.duration && (
            <div className={s.row}>
              <Text transKey="duration-capital" type="label" fw={500} mt={2} />

              <Text
                untranslatedText={data?.session?.duration || ''}
                type="body2"
                fw={700}
              />
            </div>
          )}
        </div>
      </Card>
    )
  );
};

export default ResultsDetails;
