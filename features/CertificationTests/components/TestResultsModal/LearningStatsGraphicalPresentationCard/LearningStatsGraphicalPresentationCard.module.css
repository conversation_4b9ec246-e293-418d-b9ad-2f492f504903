.statisticsWrapper {
  position: relative;
  width: 100%;
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-4xl);
  transition: all 0.4s ease;
}

.graphWrapper {
  max-width: 1100px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.metricsWrapper {
  max-width: 150px;
  min-width: 150px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-mdl);
}

.row {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.cube {
  width: 12px;
  height: 12px;
}

.very-low {
  background-color: rgba(229, 203, 203, 1);
}

.low {
  background-color: rgba(243, 228, 228, 1);
}

.average {
  background-color: rgba(248, 242, 242, 1);
}

.high {
  background-color: rgba(181, 214, 183, 1);
}

.labelWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-smd);
}

.valuesWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.valueWrapper {
  width: 25px;
}
