import { Flex, <PERSON>ading<PERSON><PERSON>lay, Skeleton, Switch } from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import LearningStatsGraph from '@/components/LearningStatsGraph/LearningStatsGraph';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import { TestScoresResultsType, TranslationKeysType } from '@/types/common';

import s from './LearningStatsGraphicalPresentationCard.module.css';

type LearningStatsGraphicalPresentationCardProps = {
  isChecked: boolean;
  onChange: () => void;
  domains: TestScoresResultsType['results']['domains'];
  isLoading?: boolean;
};

const METRICS = [
  {
    key: 'very-low',
    value: '15',
  },
  {
    key: 'low',
    value: '30',
  },
  {
    key: 'average',
    value: '70',
  },
  {
    key: 'high',
    value: '100',
  },
] as const;

const LearningStatsGraphicalPresentationCard = ({
  domains,
  isChecked,
  isLoading = false,
  onChange,
}: LearningStatsGraphicalPresentationCardProps): JSX.Element => {
  const { t } = useTranslation();
  const { userRoles } = UserContext();

  return (
    <Card size="xl">
      {isLoading ? (
        <>
          <Text
            transKey="learning-stats-graphical-presentation"
            type="h3"
            fw={250}
            mb={64}
          />

          <div
            style={{
              maxWidth: 1024,
              display: 'flex',
              position: 'relative',
              margin: '0 auto',
              marginBottom: 124,
            }}
          >
            <LoadingOverlay
              visible={isLoading}
              zIndex={1000}
              overlayProps={{ radius: 'sm', blur: 2 }}
            />

            <Skeleton h={640} w={780} mr={120} />
            <Skeleton h={240} w={250} />
          </div>
        </>
      ) : (
        <>
          <Flex justify="space-between">
            <Text
              transKey="learning-stats-graphical-presentation"
              type="h3"
              fw={250}
              mb={64}
            />

            {userRoles.isAdmin && (
              <Flex gap="sm" mt={8}>
                <Switch
                  checked={isChecked}
                  onChange={onChange}
                  color="var(--color-blue)"
                  mt={2}
                />

                <Text transKey="display-subject-name" color="darkerBlue" />
              </Flex>
            )}
          </Flex>

          <div className={s.statisticsWrapper}>
            <div className={s.graphWrapper}>
              <LearningStatsGraph
                domains={domains}
                xAxisRange={{
                  min: 0,
                  max: 100,
                  step: 10,
                }}
                hasLocalizedTitle={isChecked}
              />
            </div>

            <div className={s.metricsWrapper}>
              {METRICS.map((metric) => {
                return (
                  <div className={s.row} key={metric.value}>
                    <div className={s.labelWrapper}>
                      <div className={`${s.cube} ${s[metric.key]}`} />

                      <Text
                        transKey={t(metric.key) as TranslationKeysType}
                        type="subTitle2"
                        fw={600}
                      />
                    </div>

                    <div className={s.valuesWrapper}>
                      <Text untranslatedText="≤" type="subTitle2" />

                      <Text
                        untranslatedText={metric.value}
                        type="subTitle2"
                        className={s.valueWrapper}
                        align="right"
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </>
      )}
    </Card>
  );
};

export default LearningStatsGraphicalPresentationCard;
