/* eslint-disable react-hooks/exhaustive-deps */
import { Modal } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { GLOBAL_ERRORS, TEST_SESSION_REPORT_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import RESULTS from '@/services/results';
import { TestScoresResultsType } from '@/types/common';

import ErrorResults from './ErrorResults/ErrorResults';
import LearningStatsGraphicalPresentationCard from './LearningStatsGraphicalPresentationCard/LearningStatsGraphicalPresentationCard';
import LearningStatsTablePresentationCard from './LearningStatsTablePresentationCard/LearningStatsTablePresentationCard';
import ResultsHeader from './ResultsHeader/ResultsHeader';
import s from './TestResultsModal.module.css';

type TestResultsModalProps = {
  onCloseModal: () => void;
};

const TestResultsModal = ({ onCloseModal }: TestResultsModalProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const testResultsId = (router.query.testResultsId || '') as string;
  const isStudentLabelHidden = router.query.isStudentLabelHidden === 'true';

  const { data, error, isError, isLoading } =
    useQuery<TestScoresResultsType | null>({
      enabled: Boolean(testResultsId),
      queryFn: () =>
        RESULTS.GET_TEST_SCORES_REPORT({ testId: testResultsId || '' }),
      queryKey: [QUERY_KEYS.TEST_RESULTS_BY_ID, testResultsId],
    });

  useEffect(() => {
    const noSessionIdFound =
      error?.message === TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND;

    const badRequest = error?.message === GLOBAL_ERRORS.BAD_REQUEST;

    if (noSessionIdFound || badRequest) {
      notifications.show({
        message: t('no-results-found'),
        color: 'red',
      });

      onCloseModal();
    }
  }, [error]);

  return (
    <Modal
      opened={Boolean(testResultsId)}
      onClose={onCloseModal}
      withCloseButton={false}
      autoFocus={false}
      trapFocus
      classNames={s}
      closeOnClickOutside={false}
      fullScreen
      transitionProps={{
        duration: 400,
        transition: 'pop',
      }}
    >
      <ResultsHeader
        isSkeletonVisible={isLoading || !testResultsId || !data}
        data={data || null}
        isStudentLabelHidden={isStudentLabelHidden}
        onCloseModal={onCloseModal}
      />

      <div className={s.modalContent}>
        {/* <ResultsDetails data={data || null} isLoading={isLoading} /> */}

        <LearningStatsGraphicalPresentationCard
          domains={data?.results?.domains || []}
          isLoading={isLoading}
        />

        <LearningStatsTablePresentationCard
          domains={data?.results?.domains || []}
          total={data?.results?.total}
          isLoading={isLoading}
        />

        <ErrorResults
          testId={isLoading || isError ? '' : testResultsId || ''}
        />
      </div>
    </Modal>
  );
};

export default TestResultsModal;
