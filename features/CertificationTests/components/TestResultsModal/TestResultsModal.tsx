/* eslint-disable react-hooks/exhaustive-deps */
import { Modal } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { GLOBAL_ERRORS, TEST_SESSION_REPORT_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import RESULTS from '@/services/results';
import { TestScoresResultsType } from '@/types/common';

import ErrorResults from './ErrorResults/ErrorResults';
import LearningStatsGraphicalPresentationCard from './LearningStatsGraphicalPresentationCard/LearningStatsGraphicalPresentationCard';
import LearningStatsTablePresentationCard from './LearningStatsTablePresentationCard/LearningStatsTablePresentationCard';
import ResultsHeader from './ResultsHeader/ResultsHeader';
import s from './TestResultsModal.module.css';

type TestResultsModalProps = {
  onCloseModal: () => void;
};

const TestResultsModal = ({ onCloseModal }: TestResultsModalProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const testResultsId = (router.query.testResultsId || '') as string;
  const isStudentLabelHidden = router.query.isStudentLabelHidden === 'true';
  const [isChecked, setIsChecked] = useState(false);

  const { data, error, isError, isFetching } =
    useQuery<TestScoresResultsType | null>({
      enabled: Boolean(testResultsId),
      queryFn: () =>
        RESULTS.GET_TEST_SCORES_REPORT({
          testId: testResultsId || '',
          refresh: false,
        }),
      queryKey: [QUERY_KEYS.TEST_RESULTS_BY_ID, testResultsId],
    });

  useEffect(() => {
    const noSessionIdFound =
      error?.message === TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND;

    const badRequest = error?.message === GLOBAL_ERRORS.BAD_REQUEST;

    const testReportIsNotReady =
      error?.message === TEST_SESSION_REPORT_ERRORS.TEST_REPORT_IS_NOT_READY;

    if (noSessionIdFound || badRequest || testReportIsNotReady) {
      onCloseModal();

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });

      setTimeout(() => {
        notifications.show({
          message: t('no-results-found'),
          color: 'red',
        });
      }, 1000);
    }
  }, [error]);

  return (
    <Modal
      opened={Boolean(testResultsId)}
      onClose={onCloseModal}
      withCloseButton={false}
      autoFocus={false}
      trapFocus
      classNames={s}
      closeOnClickOutside={false}
      fullScreen
      transitionProps={{
        duration: 400,
        transition: 'pop',
      }}
    >
      <ResultsHeader
        isSkeletonVisible={isFetching || !testResultsId || !data}
        data={data || null}
        isStudentLabelHidden={isStudentLabelHidden}
        onCloseModal={onCloseModal}
        isRefreshing={isRefreshing}
        onRefresh={async () => {
          setIsRefreshing(true);

          const newData = await RESULTS.GET_TEST_SCORES_REPORT({
            testId: testResultsId || '',
            refresh: true,
          });

          queryClient.setQueryData(
            [QUERY_KEYS.TEST_RESULTS_BY_ID, testResultsId],
            newData
          );

          setIsRefreshing(false);
        }}
      />

      <div className={s.modalContent}>
        <LearningStatsGraphicalPresentationCard
          isChecked={isChecked}
          onChange={() => setIsChecked((prev) => !prev)}
          domains={data?.results?.domains || []}
          isLoading={isFetching}
        />

        <LearningStatsTablePresentationCard
          hasLocalizedTitle={isChecked}
          domains={data?.results?.domains || []}
          total={data?.results?.total}
          isLoading={isFetching}
        />

        <ErrorResults
          testId={isFetching || isError ? '' : testResultsId || ''}
          hasLocalizedTitle={isChecked}
        />
      </div>
    </Modal>
  );
};

export default TestResultsModal;
