.exerciseWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.headRow {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);

  & svg {
    cursor: pointer;
  }

  &:hover {
    cursor: pointer;
  }
}

.image {
  border-radius: var(--radius-xs);
  border: 1px solid var(--color-gray300);
  overflow: hidden;
}

.audioPlayer {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;

  margin: var(--spacing-sm) 0;
  color: var(--color-gray700) !important;
  transition: all 0.2s ease;
}

.rowItemWrapper {
  /* margin-left: var(--spacing-mdl); */
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  justify-content: center;
}

.fetchingNewPage {
  user-select: none;
  opacity: 0.5;
}

.box {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-gray300);
  border-radius: var(--radius-xs);
  padding: var(--spacing-sm) var(--spacing-smd);
  max-width: 300px;
}

.table {
  margin: 0 auto;
  padding: 0 96px;
}
