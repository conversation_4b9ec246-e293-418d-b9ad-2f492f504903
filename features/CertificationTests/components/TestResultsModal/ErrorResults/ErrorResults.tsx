/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/no-array-index-key */
import { Pagination, Skeleton, Table, TableData } from '@mantine/core';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import React, { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
// import { FaSort } from 'react-icons/fa';
import { PiSpeakerHigh } from 'react-icons/pi';

import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import Text from '@/components/Text/Text';
import RESULTS from '@/services/results';
import { TestErrorsResultsType } from '@/types/common';

import s from './ErrorResults.module.css';

const FETCH_DATA_LIMIT = 50;

type ErrorResultsProps = {
  testId: string;
};

const ErrorResults = ({ testId }: ErrorResultsProps) => {
  const { t } = useTranslation();
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [errorsPage, setErrorsPage] = useState<number>(1);
  const [previousErrorPage, setPreviousErrorPage] = useState(1);

  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { data, isFetching, isLoading } =
    useQuery<TestErrorsResultsType | null>({
      enabled: Boolean(testId),

      queryFn: () =>
        RESULTS.GET_TEST_REPORT_ERRORS({
          testId: testId || '',
          page: errorsPage,
          limit: FETCH_DATA_LIMIT,
        }),
      queryKey: [
        `${QUERY_KEYS.TEST_RESULTS_ERRORS_BY_ID}-${testId}-${errorsPage}`,
      ],
    });

  const previousData = useQueryClient().getQueryData([
    `${QUERY_KEYS.TEST_RESULTS_ERRORS_BY_ID}-${testId}-${previousErrorPage}`,
  ]) as TestErrorsResultsType | undefined;

  const originalData = useMemo(
    () => [...(data?.results || previousData?.results || [])],
    [data]
  );

  const getExerciseDisplay = (
    subtest: TestErrorsResultsType['results'][number]['exhibits'][0],
    index: number
  ) => {
    switch (subtest?.type || '') {
      case 'audio':
        return (
          <div
            key={`audio-${index}-${subtest.value}`}
            className={s.audioPlayer}
            onClick={() => {
              setAudioUrl(subtest.value);
            }}
          >
            <PiSpeakerHigh size={20} />

            <Text transKey="play-capital" type="button" color="blue" />
          </div>
        );

      case 'text':
        return (
          <div key={`text-${index}-${subtest.value}`} className={s.box}>
            <Text
              untranslatedText={subtest.value || ''}
              type="body2"
              fw={400}
              align="center"
            />
          </div>
        );

      case 'number':
        return (
          <div key={`number-${index}-${subtest.value}`} className={s.box}>
            <Text
              untranslatedText={subtest.value || ''}
              type="body2"
              fw={400}
              align="center"
            />
          </div>
        );

      case 'image':
        return (
          <Image
            key={`image-${index}-${subtest.value}`}
            src={subtest.value || ''}
            alt="image"
            width={80}
            height={80}
            className={s.image}
          />
        );

      default:
        break;
    }

    return null;
  };

  const getTableBodyData = () => {
    const grouped = originalData.reduce(
      (acc, item) => {
        let group = acc.find((g) => g.subtest === item.subtest);

        if (!group) {
          group = { subtest: item.subtest, items: [] };
          acc.push(group);
        }

        group.items.push(item);
        return acc;
      },
      [] as { subtest: string; items: typeof originalData }[]
    );

    const augmentedData = grouped
      .map((group) => group.items)
      .flat()
      .reduce(
        (acc, item) => {
          if (
            acc.length === 0 ||
            acc[acc.length - 1].subtest !== item.subtest
          ) {
            acc.push({
              subtest: item.subtest,
              title: '',
              answer: '',
              given: '',
              order: 0,
              exhibits: [],
            });
          }

          return [...acc, item];
        },
        [] as typeof originalData
      );

    const tableBodyData = augmentedData.map((subtest, index) => {
      return subtest.answer && subtest.given
        ? [
            <div key={index} className={s.rowItemWrapper}>
              {subtest?.title || ''}
            </div>,
            <div key={index} className={s.exerciseWrapper}>
              {subtest.exhibits.map((exhibit) =>
                getExerciseDisplay(exhibit, index)
              )}
            </div>,
            subtest.answer === 'left' || subtest.answer === 'right' ? (
              <Text key="subtest" transKey={subtest.answer} />
            ) : (
              subtest.answer
            ),
            subtest.given === 'left' || subtest.given === 'right' ? (
              <Text key="subtest" transKey={subtest.given} />
            ) : (
              subtest.given
            ),
          ]
        : [
            <div key={index} className={s.rowItemWrapper}>
              <Text untranslatedText={subtest.subtest} isBold mb={8} mt={8} />
            </div>,
            <div key={index} className={s.rowItemWrapper}>
              <Text transKey="exercise" isBold mb={8} mt={8} />
            </div>,
            <div key={index} className={s.rowItemWrapper}>
              <Text transKey="correct-answer" isBold mb={8} mt={8} />
            </div>,
            <div key={index} className={s.rowItemWrapper}>
              <Text transKey="student-answer" isBold mb={8} mt={8} />
            </div>,
          ];
    });

    return tableBodyData;
  };

  const tableData: TableData = {
    // head: [
    //   <Text key="subtest" transKey="subtest-capital" type="body2" isBold />,
    //   ,
    // ],
    body: getTableBodyData(),
  };

  if (!isLoading && data?.results.length === 0) {
    return null;
  }

  return isLoading && errorsPage === 1 ? (
    <Skeleton height={400} radius="lg" />
  ) : (
    <div className={s.wrapper}>
      <Card
        size="xl"
        radius="sm"
        borderSize={2}
        borderColor="gray50"
        shadow="none"
        className={s.card}
      >
        <Text transKey="errors-per-subtest" type="h3" mb={64} fw={250} />

        <div
          className={`${s.table} ${isLoading && Boolean(previousData) && s.fetchingNewPage}`}
        >
          <Table data={tableData} mb={20} w="100%" />
        </div>

        <audio
          ref={audioRef}
          src={audioUrl || ''}
          style={{ display: 'none' }}
          loop={false}
          autoPlay
          onEnded={() => {
            setAudioUrl(null);
          }}
        />
      </Card>

      <Pagination
        total={Math.ceil((data?.totalCount || 0) / FETCH_DATA_LIMIT)}
        value={errorsPage}
        hideWithOnePage
        withControls={false}
        onChange={(value) => {
          setPreviousErrorPage(errorsPage);
          setErrorsPage(value);
        }}
        disabled={isFetching}
        m="0 auto"
      />
    </div>
  );
};

export default ErrorResults;
