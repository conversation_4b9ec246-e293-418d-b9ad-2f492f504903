import { Flex, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { JSX, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import { InfoModal } from '@/components/Modals/InformationModal/InfoModal';
import Text from '@/components/Text/Text';
import TextEditor from '@/components/TextEditor/TextEditor';
import RESULTS from '@/services/results';

type DownloadReportModalProps = {
  isOpen: boolean;
  sessionId: string;
  onClose: () => void;
  isMutationInProgress: boolean;
  downloadReport: (summary: string) => void;
};

const DownloadReportModal = ({
  downloadReport,
  isMutationInProgress,
  isOpen,
  onClose,
  sessionId,
}: DownloadReportModalProps): JSX.Element => {
  const { t } = useTranslation();
  const [summary, setSummary] = useState<string>('');

  const { data, isFetching } = useQuery<{
    summary?: string;
  } | null>({
    queryFn: () => RESULTS.GET_TEST_SUMMARY(sessionId),
    queryKey: [QUERY_KEYS.SESSION_SUMMARY, sessionId],
    enabled: isOpen,
  });

  useEffect(() => {
    if (data?.summary) {
      setSummary(data.summary || '');
    }
  }, [data?.summary]);

  return (
    <InfoModal
      size="xl"
      opened={isOpen}
      onClose={() => {
        onClose();
        setSummary('');
      }}
      title={t('download-report-download-modal-title')}
      content={
        <Flex direction="column" gap={24} p={10}>
          <Text
            transKey="download-report-textarea-placeholder"
            type="body1"
            color="darkerBlue"
          />

          {isFetching ? (
            <Skeleton h={300} />
          ) : (
            <TextEditor
              value={summary}
              onChange={setSummary}
              isDisabled={isMutationInProgress}
            />
          )}

          <Button
            transKey="download-capital"
            variant="primary"
            onClick={() => {
              downloadReport(summary.trim());
            }}
            isLoading={isMutationInProgress}
          />
        </Flex>
      }
    />
  );
};

export default DownloadReportModal;
