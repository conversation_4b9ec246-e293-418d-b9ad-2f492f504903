import { Flex, Textarea } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import Button from '@/components/Button/Button';
import { InfoModal } from '@/components/Modals/InformationModal/InfoModal';
import Text from '@/components/Text/Text';

type DownloadReportModalProps = {
  isOpen: boolean;
  onClose: () => void;
  isMutationInProgress: boolean;
  downloadReport: (summary: string) => void;
};

const DownloadReportModal = ({
  downloadReport,
  isMutationInProgress,
  isOpen,
  onClose,
}: DownloadReportModalProps): JSX.Element => {
  const { t } = useTranslation();
  const textAreaRef = useRef<HTMLTextAreaElement | null>(null);

  return (
    <InfoModal
      size="xl"
      opened={isOpen}
      onClose={onClose}
      title={t('download-report-download-modal-title')}
      content={
        <Flex direction="column" gap={24} p={10}>
          <Text
            transKey="download-report-textarea-placeholder"
            type="body1"
            color="darkerBlue"
          />

          <Textarea
            ref={textAreaRef}
            // placeholder={t('download-report-textarea-placeholder')}
            placeholder={t('summary-placeholder')}
            autosize
            minRows={6}
            maxRows={10}
            data-autofocus
            disabled={isMutationInProgress}
          />

          <Button
            transKey="download-capital"
            variant="primary"
            onClick={() => {
              downloadReport(textAreaRef?.current?.value.trim() || '');
            }}
            isLoading={isMutationInProgress}
          />
        </Flex>
      }
    />
  );
};

export default DownloadReportModal;
