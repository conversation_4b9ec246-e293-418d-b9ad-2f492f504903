.header {
  width: 100%;
  max-width: 1340px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 auto;
  margin-bottom: var(--spacing-xl);
}

.headerInfoWrapper {
  display: flex;
  align-self: center;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-right: var(--spacing-lg);
}

.headerInfo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.headerActionsWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-mdl);
}

.verticalDivider {
  display: flex;
  min-height: 100%;
  width: 1px;
  background-color: rgba(163, 165, 164, 1);
}

.studentInfoWrapper {
  display: flex;
  gap: var(--spacing-smd);
}

.row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.label {
  width: 60px;
  overflow: hidden;
}

.gradeRow {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  border-left: 1px solid rgba(163, 165, 164, 1);
  padding-left: var(--spacing-smd);
  margin-left: var(--spacing-sm);
}
