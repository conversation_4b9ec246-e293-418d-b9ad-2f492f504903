import { Box, CopyButton, Flex, Skeleton, Tooltip } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { getLangNameFromCode } from 'language-name-map';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { IoMdCopy } from 'react-icons/io';

import Button from '@/components/Button/Button';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import RESULTS from '@/services/results';
import { TestScoresResultsType, TranslationKeysType } from '@/types/common';

import DownloadReportModal from '../DownloadReportModal/DownloadReportModal';
import s from './ResultsHeader.module.css';

type ResultsHeaderProps = {
  isSkeletonVisible: boolean;
  data: TestScoresResultsType | null;
  isStudentLabelHidden: boolean;
  onCloseModal: () => void;
};

const ResultsHeader = ({
  data,
  isSkeletonVisible,
  isStudentLabelHidden,
  onCloseModal,
}: ResultsHeaderProps) => {
  const { i18n, t } = useTranslation();
  const { isSchoolRole, userRoles } = UserContext();

  const [isDownloadReportModalOpen, setIsDownloadReportModalOpen] =
    React.useState(false);

  const formatDateToLongForm = (dateString: string): string => {
    const date = new Date(dateString);

    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    };

    const selectedLanguage = i18n.language === 'en' ? 'en-GB' : i18n.language;

    return date.toLocaleDateString(selectedLanguage, options).replace(/,/g, '');
  };

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${day} ${t(month)} ‘${year} ${hoursAndMinutes}`;
  };

  const downloadReportMutation = useMutation({
    mutationFn: (summary: string) =>
      RESULTS.GET_SCORES_PDF({ sessionId: data?.session.id || '', summary }),
    onSuccess: (response) => {
      if (response) {
        const blob = new Blob([response], { type: 'application/pdf' });

        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${data?.student.label}_report.pdf`);
        document.body.appendChild(link);

        link.click();

        link.remove();
        window.URL.revokeObjectURL(url);
        setIsDownloadReportModalOpen(false);
      }
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  return (
    <div className={s.header}>
      <div className={s.headerInfoWrapper}>
        <Image src="/images/logo.svg" alt="logo" width={100} height={60} />

        {isSkeletonVisible ? (
          <Skeleton height={102} w={150} />
        ) : (
          <div className={s.headerInfo}>
            <div className={s.row}>
              <Text untranslatedText={data?.session.test} type="h3" fw={250} />

              {data?.session?.grade && (
                <div className={s.gradeRow}>
                  <Text transKey="grade-capital" type="label" fw={500} mt={2} />

                  <Text
                    untranslatedText={data?.session?.grade || ''}
                    type="body2"
                    fw={700}
                  />
                </div>
              )}
            </div>

            {data?.session?.started && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="started-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={getListDateTime(
                    data?.session?.started || ''
                  )}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.completed && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="completed-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={getListDateTime(
                    data?.session?.completed || ''
                  )}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.duration && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="duration-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data?.session?.duration || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.examiner && !isStudentLabelHidden && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="examiner-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data?.session?.examiner || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.examiner && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="language-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={
                    getLangNameFromCode(data?.session?.language || 'en')
                      ?.native || ''
                  }
                  type="body2"
                  fw={700}
                />
              </div>
            )}
          </div>
        )}

        <div className={s.verticalDivider} />

        {isSkeletonVisible && <Skeleton height={102} w={150} />}

        {!isSkeletonVisible && (
          <div className={s.headerInfo}>
            {!isStudentLabelHidden && (
              <div className={s.row}>
                <Text
                  untranslatedText={data?.student?.label || ''}
                  type="h3"
                  fw={250}
                />

                {data?.student?.grade && (
                  <div className={s.gradeRow}>
                    <Text
                      transKey="grade-capital"
                      type="label"
                      fw={500}
                      mt={2}
                    />

                    <Text
                      untranslatedText={data?.student.grade || ''}
                      type="body2"
                      fw={700}
                    />
                  </div>
                )}
              </div>
            )}

            {isStudentLabelHidden && data?.student?.grade && (
              <div className={s.row}>
                <Text transKey="grade-capital" type="label" fw={500} mt={2} />

                <Text
                  untranslatedText={data.student.grade || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            <div className={s.studentInfoWrapper}>
              {data?.student?.age && (
                <>
                  <div className={s.row}>
                    <Text transKey="age-capital" type="label" fw={500} mt={2} />

                    <Text
                      untranslatedText={data.student.age || ' '}
                      type="body2"
                      fw={700}
                    />
                  </div>

                  <div className={s.verticalDivider} />
                </>
              )}

              {data?.student?.gender && (
                <div className={s.row}>
                  <Text
                    transKey="gender-capital"
                    type="label"
                    fw={500}
                    mt={2}
                  />

                  <Text
                    transKey={data.student.gender as TranslationKeysType}
                    type="body2"
                    fw={700}
                  />
                </div>
              )}

              {/* <div className={s.verticalDivider} />

              {data?.student?.grade && (
                <div className={s.row}>
                  <Text transKey="grade-capital" type="label" fw={500} mt={2} />

                  <Text
                    untranslatedText={data.student.grade || ''}
                    type="body2"
                    fw={700}
                  />
                </div>
              )} */}
            </div>

            <div className={s.studentInfoWrapper}>
              {data?.student.school && (
                <>
                  <div className={s.row}>
                    <Text
                      transKey="school-capital"
                      type="label"
                      fw={500}
                      mt={2}
                    />

                    <Text
                      untranslatedText={data?.student.school || ''}
                      type="body2"
                      fw={700}
                    />
                  </div>

                  <div className={s.verticalDivider} />
                </>
              )}

              {data?.student.hometown && (
                <div className={s.row}>
                  <Text
                    transKey="hometown-capital"
                    type="label"
                    fw={500}
                    mt={2}
                  />

                  <Text
                    untranslatedText={data?.student.hometown || ''}
                    type="body2"
                    fw={700}
                  />
                </div>
              )}
            </div>

            {!isStudentLabelHidden && data?.student.dob && (
              <div className={s.studentInfoWrapper}>
                <div className={s.row}>
                  <Text
                    transKey="birthday-capital"
                    type="label"
                    fw={500}
                    mt={2}
                  />

                  <Text
                    untranslatedText={formatDateToLongForm(
                      data?.student.dob || ''
                    )}
                    type="body2"
                    fw={700}
                  />
                </div>
              </div>
            )}

            {data?.student?.group && (
              <div className={s.row}>
                <Text
                  transKey={isSchoolRole ? 'class-capital' : 'group-capital'}
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data.student.group || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}
          </div>
        )}
      </div>

      <div className={s.headerActionsWrapper}>
        {userRoles.isAdmin && (
          <CopyButton value={window.location.href}>
            {({ copied, copy }) => (
              <Tooltip
                label={copied ? t('copied') : t('copy')}
                withArrow
                position="bottom"
              >
                <Box style={{ cursor: 'pointer' }}>
                  <IoMdCopy
                    onClick={copy}
                    size={24}
                    color="var(--color-blue)"
                  />
                </Box>
              </Tooltip>
            )}
          </CopyButton>
        )}

        <Button
          transKey="download-report-capital"
          variant="primaryOutlined"
          onClick={() => setIsDownloadReportModalOpen(true)}
          isLoading={downloadReportMutation.isPending}
        />

        <CloseButton onClick={onCloseModal} />
      </div>

      <DownloadReportModal
        isOpen={isDownloadReportModalOpen}
        onClose={() => setIsDownloadReportModalOpen(false)}
        isMutationInProgress={downloadReportMutation.isPending}
        downloadReport={(summary) => downloadReportMutation.mutate(summary)}
      />
    </div>
  );
};

export default ResultsHeader;

// import { Skeleton } from '@mantine/core';
// import { notifications } from '@mantine/notifications';
// import { useMutation } from '@tanstack/react-query';
// import Image from 'next/image';
// import React from 'react';
// import { useTranslation } from 'react-i18next';

// import Button from '@/components/Button/Button';
// import CloseButton from '@/components/CloseButton/CloseButton';
// import Text from '@/components/Text/Text';
// import { UserContext } from '@/context/UserProvider';
// import RESULTS from '@/services/results';
// import { TestScoresResultsType } from '@/types/common';

// import s from './ResultsHeader.module.css';

// type ResultsHeaderProps = {
//   isSkeletonVisible: boolean;
//   data: TestScoresResultsType | null;
//   onCloseModal: () => void;
// };

// const ResultsHeader = ({
//   data,
//   isSkeletonVisible,
//   onCloseModal,
// }: ResultsHeaderProps) => {
//   const { t } = useTranslation();
//   const { isSchoolRole } = UserContext();

//   const downloadReport = useMutation({
//     mutationFn: () =>
//       RESULTS.GET_SCORES_PDF({ sessionId: data?.session.id || '' }),
//     onSuccess: (response) => {
//       if (response) {
//         const url = window.URL.createObjectURL(new Blob([response]));
//         const link = document.createElement('a');
//         link.href = url;
//         link.setAttribute('download', `${data?.student.label}_report.pdf`);
//         document.body.appendChild(link);
//         link.click();
//       }
//     },
//     onError: (error) => {
//       notifications.show({
//         message: t(error.message),
//         color: 'red',
//       });
//     },
//   });

//   return (
//     <div className={s.header}>
//       <div className={s.headerInfoWrapper}>
//         <Image src="/images/logo.svg" alt="logo" width={100} height={60} />

//         {isSkeletonVisible ? (
//           <Skeleton height={60} w={150} />
//         ) : (
//           <div className={s.headerInfo}>
//             <div className={s.row}>
//               <Text untranslatedText={data?.session.test} type="h3" fw={250} />
//             </div>

//             {data?.session?.grade && (
//               <div className={s.row}>
//                 <Text transKey="grade-capital" type="label" fw={500} mt={2} />

//                 <Text
//                   untranslatedText={data?.session?.grade || ''}
//                   type="body2"
//                   fw={700}
//                 />
//               </div>
//             )}
//           </div>
//         )}

//         <div className={s.verticalDivider} />

//         {isSkeletonVisible ? (
//           <Skeleton height={60} w={150} />
//         ) : (
//           <div className={s.headerInfo}>
//             <div className={s.row}>
//               <Text untranslatedText={data?.student.label} type="h3" fw={250} />
//             </div>

//             <div className={s.studentInfoWrapper}>
//               {(data?.student.group || data?.student.class) && (
//                 <div className={s.row}>
//                   <Text
//                     transKey={isSchoolRole ? 'class-capital' : 'group-capital'}
//                     type="label"
//                     fw={500}
//                     mt={2}
//                   />

//                   <Text
//                     untranslatedText={
//                       data?.student.group || data?.student.class || ' '
//                     }
//                     type="body2"
//                     fw={700}
//                   />
//                 </div>
//               )}

//               {(data?.student.group || data?.student.class) &&
//                 data?.student?.grade && <div className={s.verticalDivider} />}

//               {data?.student?.grade && (
//                 <div className={s.row}>
//                   <Text transKey="grade-capital" type="label" fw={500} mt={2} />

//                   <Text
//                     untranslatedText={data.student.grade || ' '}
//                     type="body2"
//                     fw={700}
//                   />
//                 </div>
//               )}
//             </div>
//           </div>
//         )}
//       </div>

//       <div className={s.headerActionsWrapper}>
//         <Button
//           transKey="download-report-capital"
//           variant="primaryOutlined"
//           onClick={downloadReport.mutate}
//           isLoading={downloadReport.isPending}
//           isDisabled={isSkeletonVisible}
//         />

//         <CloseButton onClick={onCloseModal} />
//       </div>
//     </div>
//   );
// };

// export default ResultsHeader;
