import { Box, CopyButton, Flex, Skeleton, Tooltip } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { getLangNameFromCode } from 'language-name-map';
import Image from 'next/image';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { IoMdCopy } from 'react-icons/io';
import { IoRefreshCircleOutline } from 'react-icons/io5';

import { downloadFile } from '@/common/helpers';
import Button from '@/components/Button/Button';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';
import RESULTS from '@/services/results';
import { TestScoresResultsType, TranslationKeysType } from '@/types/common';

import DownloadReportModal from '../DownloadReportModal/DownloadReportModal';
import s from './ResultsHeader.module.css';

type ResultsHeaderProps = {
  isSkeletonVisible: boolean;
  data: TestScoresResultsType | null;
  isRefreshing: boolean;
  isStudentLabelHidden: boolean;
  onCloseModal: () => void;
  onRefresh: () => void;
};

const ResultsHeader = ({
  data,
  isRefreshing,
  isSkeletonVisible,
  isStudentLabelHidden,
  onCloseModal,
  onRefresh,
}: ResultsHeaderProps) => {
  const { i18n, t } = useTranslation();
  const { isSchoolRole, userRoles } = UserContext();

  const [isDownloadReportModalOpen, setIsDownloadReportModalOpen] =
    React.useState(false);

  const formatDateToLongForm = (dateString: string): string => {
    const date = new Date(dateString);

    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    };

    const selectedLanguage = i18n.language === 'en' ? 'en-GB' : i18n.language;

    return date.toLocaleDateString(selectedLanguage, options).replace(/,/g, '');
  };

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${day} ${t(month)} ‘${year} ${hoursAndMinutes}`;
  };

  const downloadReportMutation = useMutation({
    mutationFn: (summary: string) =>
      RESULTS.GET_SCORES_PDF({ sessionId: data?.session.id || '', summary }),
    onSuccess: (response) => {
      if (response) {
        downloadFile(
          response,
          `${data?.student.label}_${data?.session.test}_report`,
          'pdf'
        );

        setIsDownloadReportModalOpen(false);
      }
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const yearsText = t('age.years', { count: data?.student?.age?.years || 0 });
  const monthsText = t('age.months', {
    count: data?.student?.age?.months || 0,
  });

  return (
    <div className={s.header}>
      <div className={s.headerInfoWrapper}>
        <Image src="/images/logo.svg" alt="logo" width={100} height={60} />

        {isSkeletonVisible ? (
          <Skeleton height={102} w={150} />
        ) : (
          <div className={s.headerInfo}>
            <div className={s.row}>
              <Text untranslatedText={data?.session.test} type="h3" fw={250} />

              {data?.session?.grade && (
                <div className={s.gradeRow}>
                  <Text transKey="grade-capital" type="label" fw={500} mt={2} />

                  <Text
                    untranslatedText={data?.session?.grade || ''}
                    type="body2"
                    fw={700}
                  />
                </div>
              )}
            </div>

            {data?.session?.started && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="started-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={getListDateTime(
                    data?.session?.started || ''
                  )}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.completed && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="completed-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={getListDateTime(
                    data?.session?.completed || ''
                  )}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.duration && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="duration-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data?.session?.duration || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.examiner && !isStudentLabelHidden && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="examiner-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data?.session?.examiner || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            {data?.session?.examiner && (
              <div className={s.row}>
                <Text
                  className={s.label}
                  transKey="language-capital"
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={
                    getLangNameFromCode(data?.session?.language || 'en')
                      ?.native || ''
                  }
                  type="body2"
                  fw={700}
                />
              </div>
            )}
          </div>
        )}

        <div className={s.verticalDivider} />

        {isSkeletonVisible && <Skeleton height={102} w={150} />}

        {!isSkeletonVisible && (
          <div className={s.headerInfo}>
            {!isStudentLabelHidden && (
              <div className={s.row}>
                <Text
                  untranslatedText={data?.student?.label || ''}
                  type="h3"
                  fw={250}
                />

                {data?.student?.grade && (
                  <div className={s.gradeRow}>
                    <Text
                      transKey="grade-capital"
                      type="label"
                      fw={500}
                      mt={2}
                    />

                    <Text
                      untranslatedText={data?.student.grade || ''}
                      type="body2"
                      fw={700}
                    />
                  </div>
                )}
              </div>
            )}

            {isStudentLabelHidden && data?.student?.grade && (
              <div className={s.row}>
                <Text transKey="grade-capital" type="label" fw={500} mt={2} />

                <Text
                  untranslatedText={data.student.grade || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}

            <div className={s.studentInfoWrapper}>
              {data?.student?.age && (
                <>
                  <div className={s.row}>
                    <Text transKey="age-capital" type="label" fw={500} mt={2} />

                    <Text
                      transKey="testReportStudentAge"
                      type="body2"
                      fw={700}
                      transVariables={{
                        yearsText,
                        monthsText,
                      }}
                    />
                  </div>

                  <div className={s.verticalDivider} />
                </>
              )}

              {data?.student?.gender && (
                <div className={s.row}>
                  <Text
                    transKey="gender-capital"
                    type="label"
                    fw={500}
                    mt={2}
                  />

                  <Text
                    transKey={data.student.gender as TranslationKeysType}
                    type="body2"
                    fw={700}
                  />
                </div>
              )}

              {/* <div className={s.verticalDivider} />

              {data?.student?.grade && (
                <div className={s.row}>
                  <Text transKey="grade-capital" type="label" fw={500} mt={2} />

                  <Text
                    untranslatedText={data.student.grade || ''}
                    type="body2"
                    fw={700}
                  />
                </div>
              )} */}
            </div>

            <div className={s.studentInfoWrapper}>
              {data?.student.school && (
                <>
                  <div className={s.row}>
                    <Text
                      transKey="school-capital"
                      type="label"
                      fw={500}
                      mt={2}
                    />

                    <Text
                      untranslatedText={data?.student.school || ''}
                      type="body2"
                      fw={700}
                    />
                  </div>

                  <div className={s.verticalDivider} />
                </>
              )}

              {data?.student.hometown && (
                <div className={s.row}>
                  <Text
                    transKey="hometown-capital"
                    type="label"
                    fw={500}
                    mt={2}
                  />

                  <Text
                    untranslatedText={data?.student.hometown || ''}
                    type="body2"
                    fw={700}
                  />
                </div>
              )}
            </div>

            {!isStudentLabelHidden && data?.student.dob && (
              <div className={s.studentInfoWrapper}>
                <div className={s.row}>
                  <Text
                    transKey="birthday-capital"
                    type="label"
                    fw={500}
                    mt={2}
                  />

                  <Text
                    untranslatedText={formatDateToLongForm(
                      data?.student.dob || ''
                    )}
                    type="body2"
                    fw={700}
                  />
                </div>
              </div>
            )}

            {data?.student?.group && (
              <div className={s.row}>
                <Text
                  transKey={isSchoolRole ? 'class-capital' : 'group-capital'}
                  type="label"
                  fw={500}
                  mt={2}
                />

                <Text
                  untranslatedText={data.student.group || ''}
                  type="body2"
                  fw={700}
                />
              </div>
            )}
          </div>
        )}
      </div>

      <Flex direction="column">
        <div className={s.headerActionsWrapper}>
          {userRoles.isAdmin && (
            <CopyButton value={window.location.href}>
              {({ copied, copy }) => (
                <Tooltip
                  label={copied ? t('copied') : t('copy')}
                  withArrow
                  position="bottom"
                >
                  <Box style={{ cursor: 'pointer' }}>
                    <IoMdCopy
                      onClick={copy}
                      size={24}
                      color="var(--color-blue)"
                    />
                  </Box>
                </Tooltip>
              )}
            </CopyButton>
          )}

          <Button
            transKey="download-report-capital"
            variant="primaryOutlined"
            onClick={() => setIsDownloadReportModalOpen(true)}
            isLoading={downloadReportMutation.isPending}
          />

          <CloseButton onClick={onCloseModal} />
        </div>

        {(userRoles.isAdmin || userRoles.isDeveloper) && data?.cached && (
          <Flex
            mt={16}
            align="center"
            p="0px 8px"
            style={{
              opacity: isRefreshing ? 0.6 : 1,
              pointerEvents: isRefreshing ? 'none' : 'all',
            }}
          >
            <Text
              untranslatedText={
                data.cached !== true
                  ? `${t('cached')} : ${getListDateTime(data.cached)}`
                  : ''
              }
              type="subTitle2"
              mr={16}
              ml={36}
            />

            <IoRefreshCircleOutline
              color="var(--color-blue)"
              size={28}
              onClick={onRefresh}
              cursor="pointer"
            />
          </Flex>
        )}
      </Flex>

      <DownloadReportModal
        sessionId={data?.session.id || ''}
        isOpen={isDownloadReportModalOpen}
        onClose={() => setIsDownloadReportModalOpen(false)}
        isMutationInProgress={downloadReportMutation.isPending}
        downloadReport={(summary) => downloadReportMutation.mutate(summary)}
      />
    </div>
  );
};

export default ResultsHeader;
