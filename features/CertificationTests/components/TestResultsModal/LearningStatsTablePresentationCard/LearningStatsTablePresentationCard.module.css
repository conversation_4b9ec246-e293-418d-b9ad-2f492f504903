.statisticsWrapper {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: var(--spacing-3xl);

  @media (max-width: 1350px) {
    justify-content: space-between;
  }

  transition: all 0.4s ease;
}

.graphWrapper {
  max-width: 860px;
  width: 100%;
}

.metricsWrapper {
  position: absolute;
  top: 0;
  right: 0;
  max-width: 150px;
  min-width: 130px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-mdl);

  @media (max-width: 1350px) {
    position: relative;
  }
}

.row {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.cube {
  width: 12px;
  height: 12px;
}

.very-low {
  background-color: rgba(229, 203, 203, 1);
}

.low {
  background-color: rgba(243, 228, 228, 1);
}

.average {
  background-color: rgba(248, 242, 242, 1);
}

.high {
  background-color: rgba(181, 214, 183, 1);
}

.labelWrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-smd);
}

.valuesWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.valueWrapper {
  width: 25px;
}

.headRow {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);

  /* & svg {
    cursor: pointer;
  } */

  /* &:hover {
    cursor: pointer;
  } */
}

.dummyLevelPresentation {
  width: 140px;
}

.levelPresentation {
  min-width: 180px;
  width: fit-content;
  padding: var(--spacing-xs) var(--spacing-xl);
  border-radius: var(--radius-2xs);
  text-align: center;
}

.levelPresentationAverage {
  font-weight: 500;
}

.separatorLine {
  width: 160%;
  height: 1px;
  background-color: var(--mantine-color-gray-3);
}

.separatedMetricsWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.totalAveragesWrapper {
  max-width: 1084px;
  display: flex;
  justify-content: center;
  background-color: var(--color-blue);
  gap: var(--spacing-2xl);
  margin: 0 auto;
  margin-top: var(--spacing-3xl);
  padding: var(--spacing-xl);
  border-radius: var(--radius-sm);
}

.totalAveragesNoTotalScoreBg {
  background-color: var(--color-gray400);
}

.averageWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.subtestNameWrapper {
  min-width: 280px;
  max-width: 280px;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.lowAccuracyTableText {
  max-width: 100px;
  color: #ff9188;
  font-weight: 600;
  font-size: 16px;
  transform: translateY(2px);
  letter-spacing: 0.2px;
  text-wrap: nowrap;
}

.hideMetric {
  opacity: 0;
}
