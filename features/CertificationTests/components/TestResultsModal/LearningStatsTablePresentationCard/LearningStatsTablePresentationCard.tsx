/* eslint-disable no-nested-ternary */
/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Flex, Table, TableData } from '@mantine/core';
import { JSX, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { TestScoresResultsType } from '@/types/common';

import s from './LearningStatsTablePresentationCard.module.css';

type LearningStatsTablePresentationCardProps = {
  hasLocalizedTitle: boolean;
  domains: TestScoresResultsType['results']['domains'];
  total: TestScoresResultsType['results']['total'];
  isLoading?: boolean;
};

const LEVELS_TRANSLATIONS_KEYS: Record<string, string> = {
  lowest: 'very-low',
  low: 'low',
  average: 'average',
  high: 'high',
};

const getLevelByNumberRange = (number: number) => {
  if (number <= 0.15) return 'lowest';

  if (number <= 0.3) return 'low';

  if (number <= 0.7) return 'average';
  return 'high';
};

const generateTableHead = (t: (key: string) => string): TableData['head'] => {
  return [
    t('subtest-capital'),
    <Box key="score-head" className={s.headRow}>
      <Text
        transKey="initial-score-capital"
        type="body2"
        isBold
        className={s.clickableLabel}
      />
    </Box>,
    <Box key="percentile-head" className={s.headRow} style={{ maxWidth: 140 }}>
      <Text
        transKey="percentilen-capital"
        type="body2"
        isBold
        className={s.clickableLabel}
      />
    </Box>,
    t('level-capital'),
  ];
};

const LearningStatsTablePresentationCard = ({
  domains,
  hasLocalizedTitle,
  isLoading = false,
  total,
}: LearningStatsTablePresentationCardProps): JSX.Element => {
  const { t } = useTranslation();

  const hasMoreThanOneDomains = (domains.length || 0) > 1;

  const getSeparatedRows = useCallback(
    (
      column: 'subtest' | 'score' | 'percentile' | 'level',
      metricA: string | number,
      metricB: string | number,
      hasLowAccuracy = false,
      hideMetric = false,
      type: 'text-metric' | 'level-metric' = 'text-metric',
      subjectTitle: string = '',
      hasSpecifiedWidth = false
    ) => {
      return (
        <Flex align="center" gap={16}>
          {subjectTitle && (
            <p
              style={{
                textWrap: 'wrap',
                minWidth: hasSpecifiedWidth ? 232 : 'auto',
              }}
            >
              {subjectTitle}
            </p>
          )}

          <div className={s.separatedMetricsWrapper}>
            <div />

            <p
              className={
                type === 'level-metric'
                  ? `${s[LEVELS_TRANSLATIONS_KEYS[metricA] || 'low']} ${s.levelPresentation}`
                  : ''
              }
            >
              {type === 'level-metric'
                ? t(LEVELS_TRANSLATIONS_KEYS[metricA])
                : typeof metricA === 'number'
                  ? metricA
                  : t('score-acronym-capital')}
            </p>

            <div className={s.separatorLine} />

            {!hasLowAccuracy && type === 'text-metric' && (
              <p className={hideMetric ? s.hideMetric : ''}>
                {`${typeof metricB === 'number' ? metricB : t('response-time-acronym-capital')} ${column === 'score' ? t('milliseconds-short') : ''}`}
              </p>
            )}

            {hasLowAccuracy && type === 'text-metric' && (
              <p className={s.lowAccuracyTableText}>
                {t('unavailable-low-accuracy')}
              </p>
            )}

            {type === 'level-metric' && (
              <p
                className={`${hasLowAccuracy ? s.hideMetric : s[LEVELS_TRANSLATIONS_KEYS[metricB] || 'low']} ${s.levelPresentation}`}
              >
                {hasLowAccuracy ? '-' : t(LEVELS_TRANSLATIONS_KEYS[metricB])}
              </p>
            )}
          </div>
        </Flex>
      );
    },
    [t]
  );

  const generatedTables = useMemo(() => {
    if (!domains.length) return [];

    return domains.map((domain) => {
      const isDomainWithCRT = domain?.subjects.some((subject) => {
        return subject.metrics?.some((metric) => metric.type === 'CRT');
      });

      const tableHead = generateTableHead(t);

      const body = domain?.subjects.flatMap((subject) => {
        if (!subject.metrics?.length) {
          return [
            [
              <Box
                key={`subtest-name-${subject.code}`}
                className={s.subtestNameWrapper}
              >
                {subject[hasLocalizedTitle ? 'name' : 'title']}
              </Box>,
              '-',
              '-',
              <div
                key={`fake-${subject.code}`}
                className={s.dummyLevelPresentation}
              >
                -
              </div>,
            ],
          ];
        }

        // We return a TABLE ROW with split metrics design
        if ((subject.metrics?.length || 0) > 1) {
          const hasLowAccAccuracy = subject?.metrics[0].score < 70;

          const accuracyMetric = subject?.metrics.find(
            (metric) => metric.type === 'ACC'
          );

          const reactionTimeMetric = subject?.metrics.find(
            (metric) => metric.type === 'CRT'
          );

          return [
            [
              getSeparatedRows(
                'subtest',
                accuracyMetric?.type || '',
                reactionTimeMetric?.type || '',
                false,
                false,
                'text-metric',
                subject[hasLocalizedTitle ? 'name' : 'title'],
                true
              ),
              getSeparatedRows(
                'score',
                accuracyMetric?.score || 0,
                reactionTimeMetric?.score || 0,
                hasLowAccAccuracy
              ),
              getSeparatedRows(
                'percentile',
                accuracyMetric?.percentile || 0,
                reactionTimeMetric?.percentile || 0,
                false,
                hasLowAccAccuracy
              ),
              getSeparatedRows(
                'level',
                accuracyMetric?.level || '',
                reactionTimeMetric?.level || '',
                hasLowAccAccuracy,
                hasLowAccAccuracy,
                'level-metric'
              ),
            ],
          ];
        }

        // WE return a TABLE ROW with the necessary details
        return [
          [
            (
              <Box
                key={`subject-title-${subject.code}`}
                className={s.subtestNameWrapper}
              >
                {subject[hasLocalizedTitle ? 'name' : 'title']}
              </Box>
            ) as any,
            subject?.metrics[0].score ?? 0,
            subject?.metrics[0].percentile ?? 0,
            <p
              key={`lvl-${subject.code}`}
              className={`${s[LEVELS_TRANSLATIONS_KEYS[subject?.metrics[0].level] || 'low']} ${s.levelPresentation}`}
            >
              {subject?.metrics[0].level
                ? t(LEVELS_TRANSLATIONS_KEYS[subject?.metrics[0].level])
                : t('low')}
            </p>,
          ],
        ];
      });

      // Adding total score if there is more than one domain
      if (domains.length !== 1) {
        body.push([
          <Text
            key={`domain-total-score-text-${domain.title}`}
            transKey="total-score"
            type="body2"
            isBold
          />,
          domain?.total?.score === undefined ? (
            '-'
          ) : (
            <Text
              key={`domain-total-score-${domain.title}`}
              untranslatedText={`${domain?.total?.score ?? '-'}`}
              type="body1"
              fw={600}
            />
          ),
          domain?.total?.percentile === undefined ? (
            '-'
          ) : (
            <Text
              key={`domain-total-percentile-${domain.title}`}
              untranslatedText={`${domain?.total?.percentile ?? '-'}`}
              type="body1"
              fw={600}
            />
          ),
          domain?.total?.percentile === undefined ? (
            '-'
          ) : (
            <div
              key={`domain-total-level-${domain.title}`}
              className={`${
                s[
                  LEVELS_TRANSLATIONS_KEYS[
                    getLevelByNumberRange(
                      Math.round(domain.total?.percentile || 0) / 100
                    )
                  ] || 'low'
                ]
              } ${s.levelPresentation} ${s.levelPresentationAverage}`}
            >
              {t(
                LEVELS_TRANSLATIONS_KEYS[
                  getLevelByNumberRange(
                    Math.round(domain?.total?.percentile || 0) / 100
                  )
                ]
              )}
            </div>
          ),
        ]);
      }

      return {
        domainTitle: `${hasLocalizedTitle ? domain?.name : domain?.title || ''}`,
        isDomainWithCRT,
        head: tableHead,
        body,
      };
    });
  }, [domains, hasLocalizedTitle, t]);

  return (
    <Card size="xl">
      <Text
        transKey="learning-stats-table-presentation"
        type="h3"
        fw={250}
        mb={64}
      />

      {isLoading ? (
        <div
          style={{ margin: '0 auto', padding: '0 96px', marginBottom: '64px' }}
        >
          <TableSkeleton numberOfColumns={5} />
        </div>
      ) : (
        <>
          {generatedTables.map((tableData, index) => (
            <div
              key={tableData.domainTitle}
              style={{
                margin: '0 auto',
                padding: '0 96px',
                marginBottom:
                  index === generatedTables.length - 1 ? '24px' : '64px',
              }}
            >
              {hasMoreThanOneDomains && (
                <Text
                  untranslatedText={tableData.domainTitle}
                  type="h4"
                  fw={300}
                  mb={16}
                />
              )}

              <Table
                data={tableData}
                styles={{
                  table: {
                    overflow: 'hidden',
                  },
                  td: {
                    userSelect: 'none',
                  },
                  thead: { userSelect: 'none' },
                }}
              />

              {tableData?.isDomainWithCRT && (
                <Text
                  transKey="acc-crt-explanation"
                  type="body1"
                  fw={300}
                  color="gray800"
                  mt={2}
                />
              )}
            </div>
          ))}

          <Box
            className={`${s.totalAveragesWrapper} ${!total && s.totalAveragesNoTotalScoreBg}`}
          >
            <div className={s.averageWrapper}>
              <Text
                color="white"
                type="h3"
                fw={700}
                untranslatedText={`${total?.score ?? '-'}`}
              />

              <Text
                color="white"
                type="body1"
                transKey="summary-initial-score"
              />
            </div>

            <div className={s.averageWrapper}>
              <Text
                color="white"
                type="h3"
                fw={700}
                untranslatedText={`${total?.percentile ?? '-'}`}
              />

              <Text color="white" type="body1" transKey="summary-percentilen" />
            </div>

            <div className={s.averageWrapper}>
              <Text
                color="white"
                type="h3"
                fw={700}
                untranslatedText={
                  total?.percentile !== undefined
                    ? t(
                        LEVELS_TRANSLATIONS_KEYS[
                          getLevelByNumberRange(
                            Math.round(total.percentile) / 100
                          )
                        ]
                      )
                    : '-'
                }
              />

              <Text color="white" transKey="summary-level" type="body1" />
            </div>
          </Box>
        </>
      )}
    </Card>
  );
};

export default LearningStatsTablePresentationCard;
