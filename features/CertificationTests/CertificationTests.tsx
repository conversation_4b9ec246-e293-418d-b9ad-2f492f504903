import { Box, Flex, SegmentedControl } from '@mantine/core';
import { useRouter } from 'next/router';
import { JSX, useMemo } from 'react';
import { FaLock } from 'react-icons/fa';

import { PRODUCTS } from '@/common/consts';
import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';
import { UserContext } from '@/context/UserProvider';

import s from './CertificationTests.module.css';
import CompletedCertification from './components/CompletedCertification/CompletedCertification';
import ConductTestButton from './components/ConductTestButton/ConductTestButton';
import IncompleteCertification from './components/IncompleteCertification/IncompleteCertification';
import LockedCertification from './components/LockedCertification/LockedCertification';

const CertificationTests = (): JSX.Element => {
  const router = useRouter();
  const { allTests } = TestsContext();
  const tabQueryParam = (router.query.tab as string) || allTests[0]?.type;

  const { userCertificates } = UserContext();

  const TABS = useMemo(() => {
    return allTests.map((test) => {
      const certificate = userCertificates[test.type];

      if (!certificate) {
        return {
          label: PRODUCTS[test.type].testName,
          value: test.type,
          isLocked: true,
          isCompleted: false,
          isIncomplete: false,
        };
      }

      return {
        label: PRODUCTS[test.type].testName,
        value: test.type,
        isLocked: certificate.isNotBought,
        isCompleted: certificate.isCompleted,
        isIncomplete: certificate.isInProgress && !certificate.isCompleted,
      };
    });
  }, [allTests, userCertificates]);

  const activeTab = TABS.find((item) => item.value === tabQueryParam);

  return (
    <div className="routeWrapper">
      <div className="pageHeaderWrapper">
        <Text transKey="tests" type="h3" className="textPageIndicator" />

        <SegmentedControl
          id="certification-tests-segmented-control"
          value={tabQueryParam}
          onChange={(v) => {
            // setTabQueryParam(v);
            router.replace(
              `${FULL_DASHBOARD_ROUTES.CERTIFICATION_TESTS}?tab=${v}`
            );
          }}
          withItemsBorders={false}
          data={TABS.map((item) => ({
            value: item.value,
            label: (
              <Flex gap={12} align="center">
                <span>{item.label}</span>
                {item.isLocked && <FaLock fontSize={12} color="lightGray" />}
              </Flex>
            ),
          }))}
          // its added just to visually make the segmented control more centered into human eye
          mr={60}
        />

        <Box className="actionButtonIndicator">
          <ConductTestButton isDisabled={!activeTab?.isCompleted} />
        </Box>
      </div>

      <div className={`${s.wrapper} hideScrollBar`}>
        {/* Certification is not bought */}
        {activeTab?.isLocked && (
          <LockedCertification testType={activeTab.value} />
        )}
        {/* Certification is bought, either not started or in progress but not completed */}
        {activeTab?.isIncomplete && (
          <IncompleteCertification testType={activeTab.value} />
        )}

        {activeTab?.isCompleted && (
          <CompletedCertification testType={activeTab.value} />
        )}
      </div>
    </div>
  );
};

export default CertificationTests;
