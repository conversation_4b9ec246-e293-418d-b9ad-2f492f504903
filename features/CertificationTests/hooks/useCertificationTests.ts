import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import { ClassesContext } from '@/context/ClassesProvider';
import { UserContext } from '@/context/UserProvider';
import TESTS_SESSIONS_LISTING from '@/services/tests/sessions-listing';
import { ProductsType, TestSessionsListType } from '@/types/common';

type SearchParamsType = {
  grades: string[];
  sort: 'score:asc' | 'score:desc' | null;
  displayedFilterType?: 'completed' | 'uncompleted' | 'ready' | undefined;
  testType: ProductsType;
  className?: string | null | undefined;
  groupName?: string | null | undefined;
  study?: string | null | undefined;
  page: number;
  limit: number;
  query: string;
  from: string | null;
  to: string | null;
};

const useCertificationTests = (
  searchParams: SearchParamsType,
  areStudiesChecked: boolean,
  isLiveEnabled?: boolean
) => {
  const { isSchoolRole } = UserContext();

  const { areClassesListLoading } = ClassesContext();

  const { data, error, isFetching, isLoading } =
    useQuery<TestSessionsListType | null>({
      enabled:
        Boolean(searchParams.testType) &&
        (isSchoolRole ? !areClassesListLoading : true),
      queryFn: () =>
        areStudiesChecked
          ? TESTS_SESSIONS_LISTING.GET_TEST_SESSIONS_STUDY(searchParams)
          : TESTS_SESSIONS_LISTING.GET_TEST_SESSIONS(searchParams),
      queryKey: [
        areStudiesChecked
          ? QUERY_KEYS.ALL_CREATED_SESSION_TESTS_STUDY
          : QUERY_KEYS.ALL_CREATED_SESSION_TESTS,
        searchParams,
        isLiveEnabled,
      ],
      placeholderData: keepPreviousData,
      // staleTime: 1000 * 60 * 10, // Ten minutes
      refetchInterval: isLiveEnabled ? 5000 : false,
    });

  return {
    conductedSessionsTestList: data?.results || [],
    numberOfSearchResults: data?.count || 0,
    isInformationLoading: isLoading || isFetching,
    isFetching,
    isLoading,
    hasAtLeastOneCreatedEntry: data?.totalCount === 0,
    totalNumberOfPages: Math.ceil(
      (data?.count || 1) / GLOBAL_PAGINATION_FETCH_LIMIT
    ),
    errors: {
      hasUserForbiddenAccess:
        error?.message === GLOBAL_ERRORS.UNAUTHORIZED_ACCESS,
      isBadRequest: error?.message === GLOBAL_ERRORS.BAD_REQUEST,
    },
  };
};

export default useCertificationTests;
