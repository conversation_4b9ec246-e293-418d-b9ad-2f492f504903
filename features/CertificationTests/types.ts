import { z } from 'zod';

import { TEST_DETAILS_SCHEMA } from '@/zod/responseSchemas/tests/details';

export type TestDetailsType = z.infer<typeof TEST_DETAILS_SCHEMA>;

export type ContentFilterType = 'all' | 'completed' | 'uncompleted' | 'ready';

export type FiltersType = {
  classOrGroup: string | null;
  from: Date | null;
  to: Date | null;
  study: string | null;
  grades: string[];
};

export type UpdateTestListFilterType = ({
  key,
  value,
}: {
  key: keyof FiltersType;
  value: FiltersType[keyof FiltersType];
}) => void;
