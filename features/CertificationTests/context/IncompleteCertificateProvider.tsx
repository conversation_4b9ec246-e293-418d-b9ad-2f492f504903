/* eslint-disable react/jsx-no-constructed-context-values */
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createContext, useContext, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import CERTIFICATES_PROGRESS from '@/services/certificate-progress';
import {
  CertificateProgressType,
  CertificationsProductsType,
  ProductsType,
} from '@/types/common';

type DefaultContextDataType = {
  isCertificationProgressLoading: boolean;
  certificationProgressDetails: {
    name: CertificateProgressType['name'];
    description: CertificateProgressType['description'];
    modules: CertificateProgressType['modules'];
  };
  modulesProgress: {
    id: number;
    progress: number;
  }[];
  numberOfCompletedModules: number;
  moduleInProgressDetails: {
    title: CertificateProgressType['modules'][0]['title'];
    moduleNumber: CertificateProgressType['modules'][0]['order'];
    totalNumberOfQuestions: CertificateProgressType['modules'][0]['count'];
    answeredQuestions: CertificateProgressType['modules'][0]['answered'];
    progressStep: number;
    initialProgress: number;
  };
  errors: {
    isProgressReportMissing: boolean;
    isTestTypeNotSupported: boolean;
  };
  testType: ProductsType;
  updateCertificateModuleAnsweredQuestions: (
    updatedAnsweredQuestions: number
  ) => void;
};

const defaultContextData: DefaultContextDataType = {
  isCertificationProgressLoading: true,
  certificationProgressDetails: {
    name: '',
    description: '',
    modules: [],
  },
  modulesProgress: [],
  numberOfCompletedModules: 0,
  moduleInProgressDetails: {
    title: '',
    moduleNumber: 0,
    totalNumberOfQuestions: 0,
    answeredQuestions: 0,
    progressStep: 0,
    initialProgress: 0,
  },
  errors: {
    isProgressReportMissing: false,
    isTestTypeNotSupported: false,
  },
  // TODO WE NEED TO CHANGE THIS TO NULL
  testType: 'mathpro-d',
  updateCertificateModuleAnsweredQuestions: () => {},
};

const IncompleteCertificateContextData = createContext(defaultContextData);

export const IncompleteCertificateProvider = ({
  children,
  testType,
}: {
  children: React.ReactNode;
  testType: ProductsType;
}) => {
  const queryClient = useQueryClient();
  const { data, error, isLoading } = useQuery<CertificateProgressType | null>({
    enabled: Boolean(testType),
    queryFn: () =>
      CERTIFICATES_PROGRESS.GET_CERTIFICATE_PROGRESS_BY_TYPE(testType),
    queryKey: [QUERY_KEYS.CERTIFICATE_PROGRESS_BY_TEST_TYPE, testType],
    staleTime: 1000 * 60 * 60 * 24,
  });

  const isProgressReportMissing = error?.message === 'process-report-not-found';
  const isTestTypeNotSupported = error?.message === 'not-supported-test-type';

  // TODO :  make it a common function its being used under certification progress card
  const modulesProgress = useMemo(
    () =>
      data?.modules.map((module) => ({
        id: module.order,
        progress: Math.round(
          ((module?.answered || 0) / (module?.count || 1)) * 100
        ),
      })) || [],
    [data?.modules]
  );

  const numberOfCompletedModules = useMemo(
    () =>
      modulesProgress.filter((module) => module.progress === 100).length || 0,
    [modulesProgress]
  );

  const moduleInProgress = useMemo(
    () => data?.modules.find((module) => module.active),
    [data?.modules]
  );

  const moduleInProgressDetails = useMemo(
    () => ({
      title: moduleInProgress?.title || '',
      moduleNumber: moduleInProgress?.order || 1,
      totalNumberOfQuestions: moduleInProgress?.count || 0,
      answeredQuestions: moduleInProgress?.answered || 0,
      progressStep: 100 / (moduleInProgress?.count || 1),
      initialProgress:
        moduleInProgress?.answered === 0
          ? 0
          : Math.round(
              ((moduleInProgress?.answered || 0) + Number.EPSILON) * 100
            ) /
              (moduleInProgress?.count || 1) +
            100 / (moduleInProgress?.count || 1),
    }),
    [
      moduleInProgress?.title,
      moduleInProgress?.order,
      moduleInProgress?.count,
      moduleInProgress?.answered,
    ]
  );

  const certificationProgressDetails = useMemo(
    () => ({
      name: data?.name || '',
      description: data?.description || '',
      modules: data?.modules || [],
    }),
    [data?.name, data?.description, data?.modules]
  );

  const updateProgressUnderIncompleteCertificate = (
    updatedAnsweredQuestions: number
  ) => {
    queryClient.setQueryData<CertificateProgressType | null>(
      [QUERY_KEYS.CERTIFICATE_PROGRESS_BY_TEST_TYPE, testType],
      (prevData) => {
        if (!prevData) return null;

        const updatedModules = prevData.modules.map((module) => {
          if (module.order === moduleInProgressDetails.moduleNumber) {
            return {
              ...module,
              answered: updatedAnsweredQuestions,
            };
          }
          return module;
        });

        return {
          ...prevData,
          modules: updatedModules,
        };
      }
    );
  };

  // TODO : FIX THE ANY
  const updateProgressUnderCertificatesProducts = (
    updatedAnsweredQuestions: number
  ) => {
    queryClient.setQueryData<CertificationsProductsType | null>(
      [`certificates-products`],
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (prevData: any) => {
        if (!prevData) return null;

        // base on the schema above update the progress of the certificate

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const updatedProducts = prevData.map((product: any) => {
          if (product.type === testType) {
            return {
              ...product,
              progress: {
                ...product.progress,
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                modules: product.progress?.modules.map((module: any) => {
                  if (module.order === moduleInProgressDetails.moduleNumber) {
                    return {
                      ...module,
                      answered: updatedAnsweredQuestions,
                    };
                  }
                  return module;
                }),
              },
            };
          }
          return product;
        });

        return updatedProducts;
      }
    );
  };

  const updateCertificateModuleAnsweredQuestions = (
    updatedAnsweredQuestions: number
  ) => {
    updateProgressUnderIncompleteCertificate(updatedAnsweredQuestions);

    // We are checking if the certificates-products is already in the cache
    const certificatedProducts =
      queryClient.getQueryData<CertificationsProductsType | null>([
        `certificates-products`,
      ]);

    // If the certificates-products is in the cache, we update the progress of the certificate otherwise we ignore it.
    // It will be fetched when the user navigates to the actual page
    if (certificatedProducts)
      updateProgressUnderCertificatesProducts(updatedAnsweredQuestions);
  };

  const contextValue = {
    isCertificationProgressLoading: isLoading,
    certificationProgressDetails,
    modulesProgress,
    numberOfCompletedModules,
    moduleInProgressDetails,
    errors: {
      isProgressReportMissing,
      isTestTypeNotSupported,
    },
    testType,
    updateCertificateModuleAnsweredQuestions,
  };

  return (
    <IncompleteCertificateContextData.Provider value={contextValue}>
      {children}
    </IncompleteCertificateContextData.Provider>
  );
};

export const IncompleteCertificateContext = () => {
  return useContext(IncompleteCertificateContextData);
};
