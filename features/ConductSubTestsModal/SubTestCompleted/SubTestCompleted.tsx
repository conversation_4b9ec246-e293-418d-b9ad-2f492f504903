/* eslint-disable jsx-a11y/media-has-caption */
import { Progress } from '@mantine/core';
import React from 'react';
import { IoCheckmarkCircleOutline } from 'react-icons/io5';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { TestLocalesType } from '@/types/common';

import styles from './SubTestCompleted.module.css';

type SubTestCompletedProps = {
  onNextButtonClick: () => void;
  currentProgress: number;
  currentExecutedSubTest: number;
  totalNumberOfSubTests: number;
  congratsAudio: string;
  content: {
    ofTitle: TestLocalesType['ofTitle'];
    subtestCompleted: TestLocalesType['subtestCompleted'];
    nextButtonTitle: TestLocalesType['nextButtonTitle'];
  };
};

const SubTestCompleted = ({
  congratsAudio,
  content,
  currentExecutedSubTest,
  currentProgress,
  onNextButtonClick,
  totalNumberOfSubTests,
}: SubTestCompletedProps) => {
  return (
    <div className={styles.wrapper}>
      <IoCheckmarkCircleOutline fontSize={124} color="var(--color-blue)" />

      <Text
        untranslatedText={content.subtestCompleted}
        type="h1"
        fw={300}
        className={styles.title}
      />

      <div className={styles.percentageWrapper}>
        <Text
          type="subTitle1"
          untranslatedText={`${currentExecutedSubTest} ${content.ofTitle} ${totalNumberOfSubTests}`}
          isBold
          color="blue"
        />

        <Progress
          value={currentProgress}
          color="var(--color-blue)"
          w={314}
          h={32}
          radius={3}
        />
      </div>

      <Button
        untranslatedText={content.nextButtonTitle}
        onClick={onNextButtonClick}
        variant="success"
      />

      {congratsAudio && (
        <audio
          src={congratsAudio || ''}
          style={{ display: 'none' }}
          loop={false}
          autoPlay
        />
      )}
    </div>
  );
};

export default SubTestCompleted;
