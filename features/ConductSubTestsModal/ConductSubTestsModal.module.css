.wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
}

.container {
  min-width: 1100px;
  max-width: 1100px;
  height: 800px;
  display: flex;
  flex-direction: column;
  padding: 0 var(--spacing-md);
  scale: 1;
}

.displayScreen {
  height: 100%;
  min-height: 730px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  margin: var(--spacing-md) 0;
}

/* ----------------MODAL STYLES------------------- */

.root {
  background-color: transparent;
}

.content {
  height: 100%;
  background-color: rgba(0, 0, 0, 0.92);
}

.overlay {
  background-color: transparent;
}

.body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 var(--spacing-xl);
  user-select: none;
}
