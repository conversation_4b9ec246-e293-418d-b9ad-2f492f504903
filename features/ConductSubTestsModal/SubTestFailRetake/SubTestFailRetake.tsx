/* eslint-disable jsx-a11y/media-has-caption */
import React, { useMemo } from 'react';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import { TestLocalesType } from '@/types/common';

import EmojiDisplay from '../ExerciseBoard/components/EmojiDisplay/EmojiDisplay';
import styles from './SubTestFailRetake.module.css';

type SubTestFailRetakeProps = {
  onRetakeButtonClick: () => void;
  retakeAudio: string;
  retakeText: TestLocalesType['subtestRetake'];
  retakeButtonTitle: TestLocalesType['retakeButtonTitle'];
};

const SubTestFailRetake = ({
  onRetakeButtonClick,
  retakeAudio,
  retakeButtonTitle,
  retakeText,
}: SubTestFailRetakeProps) => {
  const DISPLAYED_PRACTICE_EMOJI = useMemo(
    () => <EmojiDisplay isEmojiDisplayed type="failure" />,
    []
  );

  return (
    <div className={styles.wrapper}>
      <div>{DISPLAYED_PRACTICE_EMOJI}</div>

      <Text
        untranslatedText={retakeText}
        type="h1"
        fw={300}
        className={styles.title}
        align="center"
      />

      <Button
        untranslatedText={retakeButtonTitle}
        onClick={onRetakeButtonClick}
        variant="primary"
      />

      {retakeAudio && (
        <audio
          src={retakeAudio || ''}
          style={{ display: 'none' }}
          loop={false}
          autoPlay
        />
      )}
    </div>
  );
};

export default SubTestFailRetake;
