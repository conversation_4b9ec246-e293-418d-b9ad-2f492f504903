import { useEffect, useState } from 'react';

const useFixation = (fixation: number, ISI: number) => {
  const [isEyeVisible, setIsEyeVisible] = useState(true);
  const [isFixationVisible, setIsFixationVisible] = useState(true);

  useEffect(() => {
    const eyeInterval = setTimeout(() => {
      setIsEyeVisible(false);
    }, fixation || 0);

    const fixationInterval = setTimeout(
      () => {
        setIsFixationVisible(false);
      },
      (ISI || 0) + (fixation || 0)
    );

    return () => {
      clearTimeout(eyeInterval);
      clearTimeout(fixationInterval);
      setIsFixationVisible(true);
      setIsEyeVisible(true);
    };
  }, [ISI, fixation]);

  return {
    isEyeVisible,
    isFixationVisible,
  };
};

export default useFixation;
