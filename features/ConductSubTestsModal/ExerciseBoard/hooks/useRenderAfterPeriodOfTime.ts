import { useEffect, useState } from 'react';

const useRenderAfterPeriodOfTime = (durationInMilliseconds: number) => {
  const [isRendered, setIsRendered] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (durationInMilliseconds) {
      timer = setTimeout(() => {
        setIsRendered(true);
      }, durationInMilliseconds);
    }

    return () => clearTimeout(timer);
  }, [durationInMilliseconds]);

  return {
    isRendered,
  };
};

export default useRenderAfterPeriodOfTime;
