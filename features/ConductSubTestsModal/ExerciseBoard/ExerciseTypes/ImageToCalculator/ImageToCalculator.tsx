import { Box } from '@mantine/core';
import { JSX } from 'react';
import { MdRemoveRedEye } from 'react-icons/md';

import { SQUARE_IMAGE } from '@/common/consts';
import useRenderAfterPeriodOfTime from '@/features/ConductSubTestsModal/ExerciseBoard/hooks/useRenderAfterPeriodOfTime';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { ImageToCalculatorType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './ImageToCalculator.module.css';

type ImageToCalculatorProps = {
  exhibits: ImageToCalculatorType['exhibits'];
  info: BaseInfoExerciseType;
};

const ImageToCalculator = ({
  exhibits,
  info,
}: ImageToCalculatorProps): JSX.Element => {
  const { isRendered } = useRenderAfterPeriodOfTime(info.disappear);
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const displayedImage = exhibits[0].value;

  return (
    <Box key={displayedImage} className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && exhibits[0].type === 'image' && (
        <img
          src={
            Boolean(info.answer.correctAnswer) || !isRendered
              ? displayedImage
              : SQUARE_IMAGE
          }
          alt="displays a value or nothing"
          sizes="310px"
          width="100%"
          height="100%"
          className={s.imgWrapper}
        />
      )}
    </Box>
  );
};

export default ImageToCalculator;
