/* eslint-disable jsx-a11y/media-has-caption */
import { Box } from '@mantine/core';
import { useRef, useState } from 'react';
import { MdOutlineHearing, MdRemoveRedEye } from 'react-icons/md';

import Text from '@/components/Text/Text';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { ProblemWithOperationType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './ProblemWithOperation.module.css';

type ProblemWithOperationProps = {
  exhibits: ProblemWithOperationType['exhibits'];
  info: BaseInfoExerciseType;
  repeatButtonTitle: string;
  onSoundEnd: () => void;
};

const ProblemWithOperation = ({
  exhibits,
  info,
  onSoundEnd,
  repeatButtonTitle,
}: ProblemWithOperationProps): JSX.Element => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const [isPlayingSound, setIsPlayingSound] = useState(true);

  const text = exhibits.find((exhibit) => exhibit.type === 'text')?.value || '';
  const audio =
    exhibits.find((exhibit) => exhibit.type === 'audio')?.value || '';

  // const displayedText = text.split('.');

  const onRepeatClick = () => {
    if (audioRef && !isPlayingSound) {
      setIsPlayingSound(true);
      audioRef.current?.play();
    }
  };

  return (
    <Box className={s.wrapper}>
      <Box className={s.fixationWrapper}>
        {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

        {!isFixationVisible && (
          <Box className={`${s.contentWrapper}`}>
            {/* {displayedText.map((sentence, index) => (
              <Text
                key={sentence}
                type="h4"
                untranslatedText={`${sentence}${index !== displayedText.length - 1 ? '.' : ''}`}
                align="center"
                fw={300}
                mt={index === displayedText.length - 1 ? 20 : undefined}
              />
            ))} */}

            <Text type="h4" untranslatedText={text} align="center" fw={300} />
          </Box>
        )}
      </Box>

      {!isFixationVisible && (
        <audio
          ref={audioRef}
          src={audio || ''}
          style={{ display: 'none' }}
          autoPlay
          loop={false}
          onEnded={() => {
            setIsPlayingSound(false);
            onSoundEnd();
          }}
          onError={() => {
            setIsPlayingSound(false);
            onSoundEnd();
          }}
          onPlay={() => setIsPlayingSound(true)}
        />
      )}

      {!isFixationVisible && (
        <button
          type="button"
          className={`${s.repeatButton} ${isPlayingSound ? s.buttonDisabled : ''}`}
          onClick={onRepeatClick}
        >
          <MdOutlineHearing size={24} color="white" />

          <Text
            untranslatedText={repeatButtonTitle}
            type="button"
            color="white"
          />
        </button>
      )}
    </Box>
  );
};

export default ProblemWithOperation;
