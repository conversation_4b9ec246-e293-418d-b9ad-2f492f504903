.wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
  align-items: center;
  user-select: none;
}

.fixationWrapper {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contentWrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  padding: var(--spacing-xl) var(--spacing-3xl);
  border-radius: var(--radius-xs);
  background-color: var(--color-white);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xs);
}

.repeatButton {
  background-color: transparent;
  border-radius: var(--radius-2xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-smd);
  border: 1px solid var(--color-white);
  outline: none;
  box-shadow: none;
  color: var(--color-white);
  cursor: pointer;
  align-self: center;
}

.repeatButton:hover {
  opacity: 0.5;
}

.buttonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}
