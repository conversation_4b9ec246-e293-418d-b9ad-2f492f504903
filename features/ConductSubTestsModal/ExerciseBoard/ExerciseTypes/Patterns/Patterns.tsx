import { Box } from '@mantine/core';
import { MdRemoveRedEye } from 'react-icons/md';

import Text from '@/components/Text/Text';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { PatternsType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './Patterns.module.css';

type PatternsProps = {
  exhibits: PatternsType['exhibits'];
  info: BaseInfoExerciseType;
};

const Patterns = ({ exhibits, info }: PatternsProps): JSX.Element => {
  const displayedText = exhibits[0].value;
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );
  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && exhibits[0].type === 'text' && (
        <Box className={`${s.textWrapper}`}>
          <Text untranslatedText={displayedText} fw={250} />
        </Box>
      )}
    </Box>
  );
};

export default Patterns;
