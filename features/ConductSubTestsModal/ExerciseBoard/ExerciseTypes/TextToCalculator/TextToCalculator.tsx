import { Box } from '@mantine/core';
import { JSX } from 'react';
import { MdRemoveRedEye } from 'react-icons/md';

import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { TextToCalculatorType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './TextToCalculator.module.css';

type TextToCalculatorProps = {
  exhibits: TextToCalculatorType['exhibits'];
  info: BaseInfoExerciseType;
};

const TextToCalculator = ({
  exhibits,
  info,
}: TextToCalculatorProps): JSX.Element => {
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const displayedText = exhibits[0].value;

  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && exhibits[0].type === 'text' && (
        <Box className={`${s.textWrapper}`}>
          <p>{displayedText}</p>
        </Box>
      )}
    </Box>
  );
};

export default TextToCalculator;
