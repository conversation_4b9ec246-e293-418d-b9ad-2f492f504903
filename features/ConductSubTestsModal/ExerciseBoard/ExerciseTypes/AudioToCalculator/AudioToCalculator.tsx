import { Box } from '@mantine/core';
import { LottieRefCurrentProps } from 'lottie-react';
import dynamic from 'next/dynamic';
import { JSX, useRef, useState } from 'react';
import { MdOutlineHearing, MdRemoveRedEye } from 'react-icons/md';

import Text from '@/components/Text/Text';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { AudioToCalculatorType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './AudioToCalculator.module.css';
import speakerLottie from './speaker.json';

type AudioToCalculatorProps = {
  exhibits: AudioToCalculatorType['exhibits'];
  info: BaseInfoExerciseType;
  onSoundEnd: () => void;
  repeatButtonTitle: string;
};

const DynamicLottie = dynamic(() => import('lottie-react'), { ssr: false });

const AudioToCalculator = ({
  exhibits,
  info,
  onSoundEnd,
  repeatButtonTitle,
}: AudioToCalculatorProps): JSX.Element => {
  const [isPlayingSound, setIsPlayingSound] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const lottieRef = useRef<LottieRefCurrentProps | null>(null);
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const stopLottie = () => {
    if (lottieRef.current) {
      lottieRef.current.stop();
    }
  };

  const startLottie = () => {
    if (lottieRef.current) {
      lottieRef.current.play();
    }
  };

  const onRepeatClick = () => {
    if (audioRef && !isPlayingSound) {
      setIsPlayingSound(true);
      audioRef.current?.play();
    }
  };

  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && (
        <div className={s.soundIconWrapper}>
          <DynamicLottie
            lottieRef={lottieRef}
            animationData={speakerLottie}
            className={s.player}
          />
        </div>
      )}

      {!isFixationVisible && exhibits[0].type === 'audio' && (
        <audio
          ref={audioRef}
          src={exhibits[0].value}
          style={{ display: 'none' }}
          onEnded={() => {
            stopLottie();
            onSoundEnd();
            setIsPlayingSound(false);
          }}
          autoPlay
          onPlayCapture={startLottie}
        />
      )}

      {!isFixationVisible && exhibits[0].type === 'audio' && (
        <button
          type="button"
          className={`${s.repeatButton} ${isPlayingSound ? s.buttonDisabled : ''}`}
          onClick={onRepeatClick}
        >
          <MdOutlineHearing size={24} color="white" />

          <Text
            untranslatedText={repeatButtonTitle}
            type="button"
            color="white"
          />
        </button>
      )}
    </Box>
  );
};

export default AudioToCalculator;
