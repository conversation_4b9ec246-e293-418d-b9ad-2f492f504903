.wrapper {
  height: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  user-select: none;
  gap: var(--spacing-xl);
}

.soundIconWrapper {
  width: 240px;
  height: 100%;
  overflow: hidden;
  border-radius: var(--radius-sm);
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.repeatButton {
  background-color: transparent;
  border-radius: var(--radius-2xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-smd);
  border: 1px solid var(--color-white);
  outline: none;
  box-shadow: none;
  color: var(--color-white);
  cursor: pointer;
  align-self: center;
}

.repeatButton:hover {
  opacity: 0.5;
}

.buttonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}
