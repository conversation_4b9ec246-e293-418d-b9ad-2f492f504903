import { Box } from '@mantine/core';
import { JSX } from 'react';
import { MdRemoveRedEye } from 'react-icons/md';

import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { ShapesType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './Shapes.module.css';

type ShapesProps = {
  exhibits: ShapesType['exhibits'];
  info: BaseInfoExerciseType;
};

const Shapes = ({ exhibits, info }: ShapesProps): JSX.Element => {
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const displayedShape = exhibits[0].value;

  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && exhibits[0].type === 'image' && (
        <img
          className={s.imageDisplay}
          src={displayedShape}
          alt="displays a value or nothing"
          sizes="280px"
        />
      )}
    </Box>
  );
};

export default Shapes;
