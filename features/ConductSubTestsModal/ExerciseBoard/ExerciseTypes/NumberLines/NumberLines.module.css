.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.sliderWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  user-select: none;
  align-items: center;
  justify-content: center;
  height: 286px;
}

.exhibitsWrapper {
  display: flex;
  gap: var(--spacing-xl);
  justify-content: center;
  margin-bottom: var(--spacing-2xl);
}

.exhibitWrapper {
  min-width: 120px;
  overflow: hidden;
  padding: var(--spacing-xl);
  border-radius: var(--radius-xs);
  background-color: var(--color-white);
  text-align: center;
}

.exhibitWrapper > p {
  font-weight: 300;
  font-size: 50px;
  line-height: 1;
}

.exhibitFraction {
  min-width: 140px;
  overflow: hidden;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-xs);
  background-color: var(--color-white);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.exhibitFraction > p {
  font-weight: 300;
  font-size: 40px;
  line-height: 1;
}

.fractionSymbol {
  width: 40px;
  height: 4px;
  background-color: var(--color-black);
}

.continueButtonWrapper {
  margin-top: var(--spacing-2xl);
}

.root {
  background-color: var(--color-white);
  max-width: 815px;
  width: 100%;
  height: 108px;
  padding: 40px;
  border-radius: var(--radius-xs);
  overflow: visible;
}

.track {
  height: 4px;
  background-color: var(--color-gray100);
}

.track::before {
  display: none;
}

.thumb {
  background-color: var(--color-black);
  border: none;
  width: 18px;
  height: 18px;
}

.thumb:active {
  width: 20px;
  height: 20px;
}

.thumb.notVisible {
  display: none;
}

.bar {
  background-color: var(--color-gray100);
  opacity: 0;
}

.mark {
  width: 6px;
  height: 20px;
  background-color: var(--color-gray400);
  transform: translate(0px, -8px);
  border-radius: 0;
  border: none;
}

.markLabel {
  font-size: 22px;
  font-weight: 300;
  color: var(--color-black);
  margin-top: -6px;
  margin-left: -1px;
}

@keyframes revealFromBottom {
  from {
    bottom: -220px;
    left: -600px;
  }
  to {
    bottom: -140px;
    left: -42px;
  }
}

.pointerArrow {
  position: absolute;
  transform: rotate(270deg);
  animation: revealFromBottom 1s ease forwards;
  z-index: 10;

  width: 100px;
  height: 100px;
}
