/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Slider } from '@mantine/core';
import { useMemo, useState } from 'react';
import { MdRemoveRedEye } from 'react-icons/md';

import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { TestLocalesType } from '@/types/common';
import { NumberLinesType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './NumberLines.module.css';

type NumberLinesProps = {
  exhibits: NumberLinesType['exhibits'];
  info: BaseInfoExerciseType;
  nextQuestionContent: TestLocalesType['nextQuestion'];
};

const convertStringToNumber = (numberAsString: string) => {
  try {
    const transformedStringToNumber = Number(numberAsString);
    return transformedStringToNumber;
  } catch (error) {
    return 0;
  }
};

const NumberLines = ({
  exhibits,
  info,
  nextQuestionContent,
}: NumberLinesProps): JSX.Element => {
  const minParamsValue = exhibits[0]?.params?.min || 0;
  const maxParamsValue = exhibits[0]?.params?.max || 0;
  const intervalValue = exhibits[0]?.params?.int || maxParamsValue;

  const [value, setValue] = useState(minParamsValue);
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );
  const [hasSliderBeenAdjusted, setHasSliderBeenAdjusted] = useState(false);
  const maxIntervalLength = Math.round(maxParamsValue / intervalValue);

  const generatedMarks = useMemo(
    () =>
      Array.from({ length: maxIntervalLength + 1 }, (_, i) => {
        if (i === 0)
          return {
            value: minParamsValue,
            label: `${minParamsValue}`,
          };

        if (i === maxIntervalLength)
          return {
            value: maxParamsValue,
            label: `${maxParamsValue}`,
          };

        const result = intervalValue * i;

        return {
          value: result,
          label: `${result}`,
        };
      }),
    [minParamsValue, maxParamsValue, intervalValue]
  );

  const isPracticeExerciseAnswered = Boolean(info.answer.correctAnswer);

  const hasFractionSeparator = exhibits[0].value.includes('/');

  const [firstFraction, secondFraction] = hasFractionSeparator
    ? exhibits[0].value.split('/')
    : [];

  return (
    <Box className={s.wrapper}>
      <Box className={s.sliderWrapper}>
        {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

        {!isFixationVisible && (
          <Box className={`${s.exhibitsWrapper}`}>
            {hasFractionSeparator && firstFraction && secondFraction ? (
              <Box key={exhibits[0].value} className={`${s.exhibitFraction}`}>
                <p>{firstFraction}</p>
                <div className={s.fractionSymbol} />
                <p>{secondFraction}</p>
              </Box>
            ) : (
              <Box key={exhibits[0].value} className={`${s.exhibitWrapper}`}>
                <p>{exhibits[0].value}</p>
              </Box>
            )}
          </Box>
        )}

        {!isFixationVisible && (
          <Slider
            min={minParamsValue}
            max={maxParamsValue}
            label={null}
            step={0.001}
            value={
              info.answer.correctAnswer &&
              typeof info.answer.correctAnswer === 'string'
                ? convertStringToNumber(info.answer.correctAnswer)
                : value
            }
            onChange={(v) => {
              if (!hasSliderBeenAdjusted) {
                setHasSliderBeenAdjusted(true);
              }
              setValue(v);
            }}
            marks={generatedMarks}
            classNames={{
              thumb: `${s.thumb} ${!hasSliderBeenAdjusted && s.notVisible}`,
              root: s.root,
              bar: s.bar,
              mark: s.mark,
              markLabel: s.markLabel,
              track: s.track,
            }}
            thumbChildren={
              isPracticeExerciseAnswered && info.isPracticeAnswerIncorrect ? (
                <Icon
                  name="CalculatorArrowSvg"
                  className={`${s.pointerArrow}`}
                />
              ) : null
            }
          />
        )}
      </Box>

      {!isFixationVisible &&
        hasSliderBeenAdjusted &&
        !info.answer.correctAnswer && (
          <Box className={s.continueButtonWrapper}>
            <Button
              variant="success"
              untranslatedText={nextQuestionContent}
              onClick={() => info.onSubmitAnswer(`${value}`)}
              size="default"
            />
          </Box>
        )}
    </Box>
  );
};

export default NumberLines;
