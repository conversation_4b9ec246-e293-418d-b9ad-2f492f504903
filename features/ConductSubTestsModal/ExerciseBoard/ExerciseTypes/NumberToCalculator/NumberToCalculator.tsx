import { Box } from '@mantine/core';
import { MdRemoveRedEye } from 'react-icons/md';

import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { NumberToCalculatorType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './NumberToCalculator.module.css';

type NumberToCalculatorProps = {
  exhibits: NumberToCalculatorType['exhibits'];
  info: BaseInfoExerciseType;
};

const NumberToCalculator = ({
  exhibits,
  info,
}: NumberToCalculatorProps): JSX.Element => {
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const displayedNumber = exhibits[0].value;
  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && (
        <Box key={displayedNumber} className={`${s.numberWrapper}`}>
          <p>{displayedNumber}</p>
        </Box>
      )}
    </Box>
  );
};

export default NumberToCalculator;
