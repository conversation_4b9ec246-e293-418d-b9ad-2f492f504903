import { Box } from '@mantine/core';
import { JSX } from 'react';
import { MdRemoveRedEye } from 'react-icons/md';

import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { TestLocalesType } from '@/types/common';
import { SolveSecondBasedOnFirstType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './SolveSecondBasedOnFirst.module.css';

type SolveSecondBasedOnFirstProps = {
  exhibits: SolveSecondBasedOnFirstType['exhibits'];
  info: BaseInfoExerciseType;
  content: {
    ifPredicate: TestLocalesType['ifPredicate'];
    whatIsPredicate: TestLocalesType['whatIsPredicate'];
    questionMark: TestLocalesType['questionMark'];
  };
};

const SolveSecondBasedOnFirst = ({
  content,
  exhibits,
  info,
}: SolveSecondBasedOnFirstProps): JSX.Element => {
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const firstExhibit = exhibits[0].value;
  const secondExhibit = exhibits[1].value;

  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && (
        <Box className={`${s.displayWrapper}`}>
          <div className={s.localsWrapper}>
            <span className={s.leftSpan}>{content?.ifPredicate || ' '}</span>

            <span className={s.leftSpan}>
              {content?.whatIsPredicate || ' '}
            </span>
          </div>

          <div className={s.questionsWrapper}>
            {firstExhibit}

            <p>
              {secondExhibit}
              <span className={s.rightSpan}>{content.questionMark}</span>
            </p>
          </div>
        </Box>
      )}
    </Box>
  );
};

export default SolveSecondBasedOnFirst;
