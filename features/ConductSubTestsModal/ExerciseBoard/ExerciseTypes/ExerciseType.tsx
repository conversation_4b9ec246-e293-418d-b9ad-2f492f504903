/* eslint-disable react/destructuring-assignment */
import { TestLocalesType } from '@/types/common';
import {
  ExerciseTypes,
  NarrowedSubTestQuestionType,
} from '@/types/currentSubTestExerciseResponse';

import { AnswerType, BaseInfoExerciseType } from '../../types';
import AudioToCalculator from './AudioToCalculator/AudioToCalculator';
import CompareImages from './CompareImages/CompareImages';
import CompareNumbers from './CompareNumbers/CompareNumbers';
import ImageToCalculator from './ImageToCalculator/ImageToCalculator';
import NumberLines from './NumberLines/NumberLines';
import NumberToCalculator from './NumberToCalculator/NumberToCalculator';
import Patterns from './Patterns/Patterns';
import ProblemWithOperation from './ProblemWithOperation/ProblemWithOperation';
import Shapes from './Shapes/Shapes';
import SolveSecondBasedOnFirst from './SolveSecondBasedOnFirst/SolveSecondBasedOnFirst';
import TextToCalculator from './TextToCalculator/TextToCalculator';

type ExerciseTypeProps = {
  currentExercise: NarrowedSubTestQuestionType;
  onSubmitAnswer: (answer: string) => void;
  isPracticeAnswerIncorrect: boolean;
  practiceModeCorrectAnswer: AnswerType['correctAnswer'];
  practiceModeAnswerType: AnswerType['type'];
  disappear: number;
  onSoundEnd: () => void;
  content: {
    hearTheProblem: TestLocalesType['hearTheProblem'];
    ifPredicate: TestLocalesType['ifPredicate'];
    whatIsPredicate: TestLocalesType['whatIsPredicate'];
    questionMark: TestLocalesType['questionMark'];
    nextQuestion: TestLocalesType['nextQuestion'];
  };
};

const ExerciseType = (props: ExerciseTypeProps): JSX.Element | null => {
  const infoDetails: BaseInfoExerciseType = {
    question: props.currentExercise.question,
    answer: {
      correctAnswer: props.practiceModeCorrectAnswer,
      type: props.practiceModeAnswerType,
    },
    isPracticeAnswerIncorrect: props.isPracticeAnswerIncorrect,
    onSubmitAnswer: props.onSubmitAnswer,
    disappear: props.disappear,
    ISI: props.currentExercise.ISI,
    fixation: props.currentExercise.fixation,
  };

  switch (props.currentExercise.type) {
    case ExerciseTypes.COMPARE_IMAGES:
      return (
        <CompareImages
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
        />
      );
    case ExerciseTypes.COMPARE_NUMBERS:
      return (
        <CompareNumbers
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
        />
      );
    case ExerciseTypes.NUMBER_TO_CALCULATOR:
      return (
        <NumberToCalculator
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
        />
      );
    case ExerciseTypes.TEXT_TO_CALCULATOR:
      return (
        <TextToCalculator
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
        />
      );
    case ExerciseTypes.AUDIO_TO_CALCULATOR:
      return (
        <AudioToCalculator
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
          onSoundEnd={props.onSoundEnd}
          repeatButtonTitle={props.content.hearTheProblem}
        />
      );
    case ExerciseTypes.IMAGE_TO_CALCULATOR:
      return (
        <ImageToCalculator
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
        />
      );
    case ExerciseTypes.NUMBER_LINES:
      return (
        <NumberLines
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
          nextQuestionContent={props.content.nextQuestion}
        />
      );
    case ExerciseTypes.PATTERNS:
      return (
        <Patterns
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
        />
      );
    case ExerciseTypes.PROBLEM_WITH_OPERATION:
      return (
        <ProblemWithOperation
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
          repeatButtonTitle={props.content.hearTheProblem}
          onSoundEnd={props.onSoundEnd}
        />
      );
    case ExerciseTypes.SHAPES:
      return (
        <Shapes info={infoDetails} exhibits={props.currentExercise.exhibits} />
      );
    case ExerciseTypes.SOLVE_SECOND_BASED_ON_FIRST:
      return (
        <SolveSecondBasedOnFirst
          info={infoDetails}
          exhibits={props.currentExercise.exhibits}
          content={{
            ifPredicate: props.content.ifPredicate,
            whatIsPredicate: props.content.whatIsPredicate,
            questionMark: props.content.questionMark,
          }}
        />
      );
    default:
      return null;
  }
};

export default ExerciseType;
