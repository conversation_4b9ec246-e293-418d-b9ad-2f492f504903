.wrapper {
  height: 240px;
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2xl);
  justify-content: center;
}

.imageWrapper {
  position: relative;
  height: 240px;
  width: 240px;
  border-radius: var(--radius-xs);
  cursor: pointer;
}

.imageWrapper.reduceOpacity {
  opacity: 0.5;
}

.imageWrapper.purpleBorder {
  border: 5px solid var(--color-purple);
}

.imageWrapper.ceramicBorder {
  border: 5px solid var(--color-danger);
}

.placeholderDiv {
  width: 100%;
  height: 100%;
  background-color: var(--color-white);
}

@keyframes revealFromLeftToRight {
  from {
    left: -280px;
  }
  to {
    left: -120px;
  }
}

@keyframes revealFromRightToLeft {
  from {
    right: -280px;
  }
  to {
    right: -120px;
  }
}

.pointerArrowLeft {
  position: absolute;
  top: 60px;
  animation: revealFromLeftToRight 1s ease forwards;
}

.pointerArrowRight {
  position: absolute;
  top: 60px;
  transform: rotate(180deg);
  animation: revealFromRightToLeft 1s ease forwards;
}
