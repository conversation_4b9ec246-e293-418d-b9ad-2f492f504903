/* eslint-disable @next/next/no-img-element */
import { Box } from '@mantine/core';
import { MdRemoveRedEye } from 'react-icons/md';

import Icon from '@/components/Icon/Icon';
import useRenderAfterPeriodOfTime from '@/features/ConductSubTestsModal/ExerciseBoard/hooks/useRenderAfterPeriodOfTime';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { CompareImagesType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './CompareImages.module.css';

type CompareImagesProps = {
  exhibits: CompareImagesType['exhibits'];
  info: BaseInfoExerciseType;
};

const CompareImages = ({ exhibits, info }: CompareImagesProps): JSX.Element => {
  const { isRendered } = useRenderAfterPeriodOfTime(info.disappear);
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );

  const isPracticeQuestionAnswered = Boolean(info.answer.correctAnswer);
  const isAnswerValid =
    isPracticeQuestionAnswered && info.answer.type === 'index';

  return (
    <Box className={s.wrapper}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && (
        <Box
          className={`${s.imageWrapper} ${isAnswerValid && info.answer.correctAnswer !== `0` && s.reduceOpacity} ${s.ceramicBorder}`}
          onClick={() => info.onSubmitAnswer(`0`)}
        >
          {isPracticeQuestionAnswered || !isRendered ? (
            <>
              <img
                src={exhibits[0].value}
                alt="displays a value or nothing"
                sizes="240px"
                width="100%"
                height="100%"
              />

              {info.isPracticeAnswerIncorrect &&
                info.answer.correctAnswer === '0' && (
                  <Icon
                    name="CalculatorArrowSvg"
                    className={`${s.pointerArrowLeft}`}
                  />
                )}
            </>
          ) : (
            <div className={s.placeholderDiv} />
          )}
        </Box>
      )}

      {!isFixationVisible && (
        <Box
          className={`${s.imageWrapper} ${isAnswerValid && info.answer.correctAnswer !== `1` && s.reduceOpacity} ${s.purpleBorder}`}
          onClick={() => info.onSubmitAnswer(`1`)}
        >
          {isPracticeQuestionAnswered || !isRendered ? (
            <>
              <img
                src={exhibits[1].value}
                alt="displays a value or nothing"
                sizes="240px"
                width="100%"
                height="100%"
              />

              {info.isPracticeAnswerIncorrect &&
                info.answer.correctAnswer === '1' && (
                  <Icon
                    name="CalculatorArrowSvg"
                    className={`${s.pointerArrowRight}`}
                  />
                )}
            </>
          ) : (
            <div className={s.placeholderDiv} />
          )}
        </Box>
      )}
    </Box>
  );
};

export default CompareImages;
