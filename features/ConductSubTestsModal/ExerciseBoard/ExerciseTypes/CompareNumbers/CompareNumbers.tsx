import { Box } from '@mantine/core';
import { MdRemoveRedEye } from 'react-icons/md';

import Icon from '@/components/Icon/Icon';
import { BaseInfoExerciseType } from '@/features/ConductSubTestsModal/types';
import { CompareNumbersType } from '@/types/currentSubTestExerciseResponse';

import useFixation from '../../hooks/useFixation';
import s from './CompareNumbers.module.css';

type CompareNumbersProps = {
  exhibits: CompareNumbersType['exhibits'];
  info: BaseInfoExerciseType;
};

const CompareNumbers = ({
  exhibits,
  info,
}: CompareNumbersProps): JSX.Element => {
  const { isEyeVisible, isFixationVisible } = useFixation(
    info.fixation,
    info.ISI
  );
  const isPracticeQuestionAnswered = Boolean(info.answer.correctAnswer);

  const isAnswerValid =
    isPracticeQuestionAnswered && info.answer.type === 'index';

  return (
    <Box className={`${s.wrapper}`}>
      {isEyeVisible && <MdRemoveRedEye color="white" size={40} />}

      {!isFixationVisible && (
        <Box
          key={exhibits[0].value}
          className={`${s.numberWrapper} ${isAnswerValid && info.answer.correctAnswer !== '0' && s.reduceOpacity}`}
          onClick={() => info.onSubmitAnswer('0')}
        >
          <p>{exhibits[0].value}</p>

          {info.isPracticeAnswerIncorrect &&
            info.answer.correctAnswer === '0' && (
              <Icon
                name="CalculatorArrowSvg"
                className={`${s.pointerArrowLeft}`}
              />
            )}
        </Box>
      )}

      {!isFixationVisible && (
        <Box
          key={exhibits[1].value}
          className={`${s.numberWrapper} ${isAnswerValid && info.answer.correctAnswer !== '1' && s.reduceOpacity}`}
          onClick={() => info.onSubmitAnswer('1')}
        >
          <p>{exhibits[1].value}</p>

          {info.isPracticeAnswerIncorrect &&
            info.answer.correctAnswer === '1' && (
              <Icon
                name="CalculatorArrowSvg"
                className={`${s.pointerArrowRight}`}
              />
            )}
        </Box>
      )}
    </Box>
  );
};

export default CompareNumbers;
