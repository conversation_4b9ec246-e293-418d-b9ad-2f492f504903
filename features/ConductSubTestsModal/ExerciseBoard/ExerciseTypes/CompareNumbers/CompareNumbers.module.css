.wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2xl);
  justify-content: center;
  align-items: center;
  height: 240px;
}

.wrapper.disabled {
  pointer-events: none;
}

.numberWrapper {
  position: relative;
  height: 160px;
  min-width: 180px;
  padding: 40px 64px;
  border-radius: var(--radius-xs);
  cursor: pointer;
  background-color: var(--color-white);
  text-align: center;
}

.numberWrapper > p {
  font-weight: 300;
  font-size: 70px;
  line-height: 1;
}

.numberWrapper.reduceOpacity {
  opacity: 0.5;
}

@keyframes revealFromLeftToRight {
  from {
    left: -280px;
  }
  to {
    left: -120px;
  }
}

@keyframes revealFromRightToLeft {
  from {
    right: -280px;
  }
  to {
    right: -120px;
  }
}

.pointerArrowLeft {
  position: absolute;
  top: 20px;
  animation: revealFromLeftToRight 1s ease forwards;
}

.pointerArrowRight {
  position: absolute;
  top: 20px;
  transform: rotate(180deg);
  animation: revealFromRightToLeft 1s ease forwards;
}
