/* eslint-disable no-useless-escape */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/media-has-caption */
import { Loader } from '@mantine/core';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useRef, useState } from 'react';

import { TEST_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import Text from '@/components/Text/Text';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import { TestLocalesType } from '@/types/common';
import {
  ExerciseTypes,
  NarrowedSubTestQuestionType,
} from '@/types/currentSubTestExerciseResponse';

import StartExercisePracticeEnded from '../StartExercisePracticeEnded/StartExercisePracticeEnded';
import { AnswerType } from '../types';
import Calculator from './components/Calculator/Calculator';
import EmojiDisplay from './components/EmojiDisplay/EmojiDisplay';
import ExerciseQuestion from './components/ExerciseQuestion/ExerciseQuestion';
import Header from './components/Header/Header';
import s from './ExerciseBoard.module.css';
import ExerciseType from './ExerciseTypes/ExerciseType';

type ExerciseBoardPropsType = {
  currentExercise: NarrowedSubTestQuestionType;
  testSessionId: string;
  isTimeChallenged: boolean;
  isCurrentExerciseFetching: boolean;
  onCompleteSessionTest: () => void;
  onCompleteSessionSubTest: (audioUrl?: string) => void;
  onRetakeSubTest: (audioUrl?: string) => void;
  content: {
    practiceStageLabel: TestLocalesType['practiceStageLabel'];
    nextQuestion: TestLocalesType['nextQuestion'];
    correct: TestLocalesType['correct'];
    incorrect: TestLocalesType['incorrect'];
    timeChallenge: TestLocalesType['timeChallenge'];
    hearTheProblem: TestLocalesType['hearTheProblem'];
    ifPredicate: TestLocalesType['ifPredicate'];
    whatIsPredicate: TestLocalesType['whatIsPredicate'];
    questionMark: TestLocalesType['questionMark'];
    decimalSymbol: TestLocalesType['decimalSymbol'];
    divisionSymbol: TestLocalesType['divisionSymbol'];
    nextButtonTitle: TestLocalesType['nextButtonTitle'];
    startButtonTitle: TestLocalesType['startButtonTitle'];
    timeCounts: TestLocalesType['timeCounts'];
    multiplySymbol: TestLocalesType['multiplySymbol'];
  };
};

const EXERCISE_TYPES_THAT_START_WITH_SOUND = [
  'audioToCalculator',
  'problemWOperation',
];

const INITIAL_CORRECT_ANSWER: AnswerType = {
  correctAnswer: '',
  type: 'index',
};

const PROBLEM_WITH_OPERATION_VALIDATOR_REGEX = /^\d+[\+\-\*/]\d+$/;

const ExerciseBoard = ({
  content,
  currentExercise,
  isCurrentExerciseFetching,
  isTimeChallenged,
  onCompleteSessionSubTest,
  onCompleteSessionTest,
  onRetakeSubTest,
  testSessionId,
}: ExerciseBoardPropsType): JSX.Element | null => {
  const queryClient = useQueryClient();
  const [questionStartedAt, setQuestionStartedAt] = useState<number>(0);
  const [hasReviewedSection, setHasReviewedSection] = useState(false);

  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isAlertOpen, setIsAlertOpen] = useState(false);

  // This and the state below practiceModeAnswer can be 1 state cause it reflects the state for practice responses
  const [practiceModeState, setPracticeModeState] = useState<
    'succeeded' | 'failed' | null
  >(null);

  const isAnswerSubmittedUnderPracticeExercise = Boolean(practiceModeState);

  const [practiceModeAnswer, setPracticeModeAnswer] = useState<AnswerType>(
    INITIAL_CORRECT_ANSWER
  );

  const [isPlayingSound, setIsPlayingSound] = useState(false);

  const audioRef = useRef<HTMLAudioElement | null>(null);

  const isCalculatorDisplayed = currentExercise.calculator !== 'occluded';

  const isPracticeMode = Boolean(currentExercise.practice);

  const totalDisappear = currentExercise?.disappear
    ? (currentExercise?.disappear || 0) +
      (currentExercise?.fixation || 0) +
      (currentExercise?.ISI || 0)
    : 0;

  const DISPLAYED_PRACTICE_EMOJI = useMemo(
    () => (
      <EmojiDisplay
        isEmojiDisplayed={isAnswerSubmittedUnderPracticeExercise}
        type={practiceModeState === 'succeeded' ? 'success' : 'failure'}
      />
    ),
    [practiceModeState, isAnswerSubmittedUnderPracticeExercise]
  );

  const answerQuestionMutation = useMutation({
    mutationFn: SESSIONS_ACTIONS.ANSWER_TEST_QUESTION,
    onSuccess: async (res) => {
      if (!res) return;

      if (isPracticeMode) {
        setPracticeModeState(res.result.correct ? 'succeeded' : 'failed');
        setAudioUrl(res.result.audio || null);

        setPracticeModeAnswer({
          correctAnswer: `${res.result.answer}`,
          type: res.result.type,
        });

        return;
      }

      if (res.completed === 'subtest') {
        if (res.retake) {
          onRetakeSubTest(res?.audio || '');
          return;
        }

        onCompleteSessionSubTest(res?.audio || '');
        return;
      }

      if (res.completed === 'test') {
        onCompleteSessionTest();
        return;
      }

      await queryClient.refetchQueries({
        queryKey: [QUERY_KEYS.TEST_SESSION_CURRENT_QUESTION, testSessionId],
      });

      setHasReviewedSection(false);
    },
    onError: (error) => {
      if (
        error.message ===
        TEST_ERRORS.SESSION_ANSWERED_QUESTION_IS_NOT_IN_SYNC_WITH_CURRENT
      )
        setIsAlertOpen(true);
    },
  });

  const onContinueToNextPracticeQuestion = async () => {
    await queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_CURRENT_QUESTION, testSessionId],
    });

    setPracticeModeState(null);
    setAudioUrl(null);
    setPracticeModeAnswer(INITIAL_CORRECT_ANSWER);
  };

  const handleExerciseTypeAnswerSubmission = (answer: string) => {
    if (isPracticeMode && !isAnswerSubmittedUnderPracticeExercise) {
      answerQuestionMutation.mutate({
        code: currentExercise.code,
        sessionId: testSessionId,
        questionAnswer: answer,
        time: Date.now() - questionStartedAt,
      });
    }

    if (!isPracticeMode) {
      answerQuestionMutation.mutate({
        code: currentExercise.code,
        sessionId: testSessionId,
        questionAnswer: answer,
        time: Date.now() - questionStartedAt,
      });
    }
  };

  const onSubmitCalculatorAnswer = (answer: string) => {
    if (currentExercise.type === ExerciseTypes.PROBLEM_WITH_OPERATION) {
      const isValid = PROBLEM_WITH_OPERATION_VALIDATOR_REGEX.test(answer);

      if (!isValid) return;
    }

    answerQuestionMutation.mutate({
      code: currentExercise.code,
      sessionId: testSessionId,
      questionAnswer: answer,
      time: Date.now() - questionStartedAt,
    });
  };

  useEffect(() => {
    if (currentExercise?.code) {
      setQuestionStartedAt(Date.now());
    }

    setPracticeModeState(null);
    setPracticeModeAnswer(INITIAL_CORRECT_ANSWER);

    if (
      EXERCISE_TYPES_THAT_START_WITH_SOUND.includes(currentExercise?.type || '')
    ) {
      setIsPlayingSound(true);
    } else {
      setIsPlayingSound(false);
    }
  }, [currentExercise?.code, currentExercise?.type]);

  return !hasReviewedSection && currentExercise?.section ? (
    <StartExercisePracticeEnded
      title={currentExercise.section?.head || ''}
      body={currentExercise.section?.body || ''}
      icon={currentExercise.section?.icon}
      startButtonTitle={content.startButtonTitle}
      audio={currentExercise.section?.audio || ''}
      onNext={() => setHasReviewedSection(true)}
    />
  ) : (
    <div className={s.wrapper}>
      <div className={`${s.questionScreen}`}>
        {(isCurrentExerciseFetching || answerQuestionMutation.isPending) && (
          <div className={s.loadingScreen}>
            <Loader color="blue" className={s.loader} />
          </div>
        )}

        <Header
          isPracticeDisplayed={isPracticeMode}
          practiceLabel={content.practiceStageLabel}
          isTimedChallengeDisplayed={isTimeChallenged}
          timedChallengeLabel={content.timeChallenge}
        />

        <ExerciseQuestion question={currentExercise?.question || ''} />

        <div
          className={`${isAnswerSubmittedUnderPracticeExercise && s.pointerNone}`}
        >
          {currentExercise && (
            <ExerciseType
              // key={`${currentExercise.order} ${currentExercise.practice}`}
              key={currentExercise.code}
              currentExercise={currentExercise}
              practiceModeCorrectAnswer={practiceModeAnswer.correctAnswer}
              practiceModeAnswerType={practiceModeAnswer.type}
              isPracticeAnswerIncorrect={practiceModeState === 'failed'}
              onSubmitAnswer={handleExerciseTypeAnswerSubmission}
              disappear={totalDisappear}
              onSoundEnd={() => setIsPlayingSound(false)}
              content={{
                hearTheProblem: content?.hearTheProblem || '',
                ifPredicate: content?.ifPredicate || '',
                whatIsPredicate: content?.whatIsPredicate || '',
                questionMark: content?.questionMark || '',
                nextQuestion: content?.nextQuestion || '',
              }}
            />
          )}
        </div>

        {DISPLAYED_PRACTICE_EMOJI}

        {practiceModeState !== null && (
          <Text
            untranslatedText={
              practiceModeState === 'succeeded'
                ? content.correct
                : content.incorrect
            }
            color="white"
            type="h3"
            align="center"
            mt={8}
          />
        )}

        {/* If the calculator is NOT displayed the continue button should appear inside the question Screen */}
        {isAnswerSubmittedUnderPracticeExercise && !isCalculatorDisplayed && (
          <div className={s.screenButtonWrapper}>
            <Button
              onClick={onContinueToNextPracticeQuestion}
              type="button"
              untranslatedText={content.nextQuestion}
              isDisabled={
                isCurrentExerciseFetching ||
                answerQuestionMutation.isPending ||
                isPlayingSound
              }
              variant="success"
            />
          </div>
        )}

        {isPracticeMode && (
          <div className={s.practiceBanner}>
            <Text
              untranslatedText={content.practiceStageLabel}
              color="white"
              type="h4"
              isBold
            />
          </div>
        )}
      </div>

      {/* Right Side of the layout with the calculator display */}
      <div
        className={`${s.calculatorMenu} ${isCalculatorDisplayed && s.openCalculator}`}
      >
        <Calculator
          // key={`${currentExercise?.order} ${currentExercise?.practice}`}
          key={currentExercise.code}
          value={practiceModeAnswer.correctAnswer}
          content={{
            decimalSymbol: content?.decimalSymbol || '',
            divisionSymbol: content?.divisionSymbol || '',
            multiplySymbol: content?.multiplySymbol || '',
          }}
          areDecimalsEnabled={currentExercise?.calculator === 'decimals'}
          areOperationsEnabled={currentExercise?.calculator === 'extended'}
          isDisabled={
            isCurrentExerciseFetching ||
            answerQuestionMutation.isPending ||
            isAnswerSubmittedUnderPracticeExercise ||
            isPlayingSound
          }
          onSubmitAnswer={onSubmitCalculatorAnswer}
        />

        {/* If the calculator is displayed the continue button should appear or the side of the calculator */}
        {isAnswerSubmittedUnderPracticeExercise && isCalculatorDisplayed && (
          <Button
            onClick={onContinueToNextPracticeQuestion}
            type="button"
            untranslatedText={content.nextQuestion}
            variant="success"
            isDisabled={
              isCurrentExerciseFetching ||
              answerQuestionMutation.isPending ||
              isPlayingSound
            }
          />
        )}
      </div>

      {isCalculatorDisplayed && practiceModeState === 'failed' && (
        <Icon name="CalculatorArrowSvg" className={`${s.calculatorArrow}`} />
      )}

      {practiceModeState !== null && (
        <audio
          ref={audioRef}
          src={
            practiceModeState === 'succeeded'
              ? '/success_1.mp3'
              : audioUrl || ''
          }
          style={{ display: 'none' }}
          loop={false}
          autoPlay
          onEnded={() => {
            setIsPlayingSound(false);
          }}
          onLoadStart={() => {
            setIsPlayingSound(true);
          }}
          onError={() => {
            setIsPlayingSound(false);
          }}
        />
      )}

      <AlertDialog
        isOpen={isAlertOpen}
        onConfirmAction={() => {
          document.location.reload();
        }}
        title="test-sync-error"
        confirmLabel="refresh-capital"
        description="test-sync-error-description"
        variant="danger"
      />
    </div>
  );
};

export default ExerciseBoard;
