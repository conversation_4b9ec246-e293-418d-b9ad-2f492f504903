.wrapper {
  width: 100%;
  height: 100%;
  padding: 40px;
  background-color: var(--color-gray200);
  border-radius: var(--radius-sm);
  display: flex;
  position: relative;
}

.questionScreen {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--color-turquoise);
  border-radius: var(--radius-xs);
  padding: var(--spacing-xl);
  overflow: hidden;
  transition: background-color 0.3s ease;
}

.pointerNone {
  pointer-events: none;
  max-height: 500px;
  overflow: visible;

  scrollbar-width: none;
  -ms-overflow-style: none;
}

.loadingScreen {
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  background-color: var(--color-white);
  opacity: 5%;

  z-index: 1;
}

.loader {
  position: absolute;
  bottom: var(--spacing-3xl);
  left: 50%;
}

.calculatorMenu {
  position: relative;
  max-width: 0;
  width: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.openCalculator {
  max-width: 240px;
  width: 100%;
  margin-left: 40px;
}

.screenButtonWrapper {
  position: absolute;
  right: 64px;
  bottom: 46px;
}

.fixation {
  width: 100%;
  position: absolute;
  top: 45%;
  left: 0;
  display: flex;
  justify-content: center;
}

@keyframes revealFromBottom {
  from {
    top: 120%;
  }
  to {
    top: 3.3%;
  }
}

.calculatorArrow {
  position: absolute;
  right: 240px;
  animation: revealFromBottom 1s ease forwards;
}

.practiceBanner {
  position: absolute;
  left: -210px;
  bottom: 60px;
  width: 600px;
  height: 80px;
  background-color: var(--color-black);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(45deg);
}
