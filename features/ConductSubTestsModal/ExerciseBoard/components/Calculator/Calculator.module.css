.wrapper {
  width: 240px;
  height: 400px;
  border: 1px solid var(--color-gray200);
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.displayScreen {
  height: 100px;
  background-color: rgba(70, 74, 73, 1);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
  border-bottom: 2px solid var(--color-gray200);
}

.padsWrapper {
  display: flex;
}

.padsWrapper.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.baseScreen {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  cursor: pointer;
  user-select: none;
}

@keyframes flash {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

.applyFlash {
  animation: flash 0.1s linear 1;
}

.keyPad {
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 2px solid var(--color-gray200);
}

.keyPad {
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 2px solid var(--color-gray200);
}

.keyPad.borderRight {
  border-right: 1px solid var(--color-gray200);
}

.keyPad.borderLeftRight {
  border-left: 1px solid var(--color-gray200);
  border-right: 1px solid var(--color-gray200);
}

.keyPad.borderLeft {
  border-left: 1px solid var(--color-gray200);
}

.keyPad.numeric {
  background-color: rgba(116, 119, 119, 0.8);
}

.keyPad.numeric:hover {
  background-color: rgba(116, 119, 119, 0.6);
}

.keyPad.deletion {
  background-color: var(--color-deletion);
}

.keyPad.deletion:hover {
  background-color: var(--color-deletionHover);
}

.keyPad.submission {
  background-color: var(--color-green);
}

.keyPad.submission:hover {
  background-color: var(--color-onHoverGreen);
}

.keyPad.arithmetic-operation {
  position: relative;
  background-color: var(--color-blue);
  font-size: 40px;
}

.keyPad.arithmetic-operation:hover {
  background-color: var(--color-onHoverBlue);
}

.keyPad.decimal-operation {
  background-color: var(--color-blue);
  font-size: 40px;
}

.keyPad.decimal-operation:hover {
  background-color: var(--color-onHoverBlue);
}

.keyPad:active {
  animation: flash 0.1s linear 1;
}

.keyPad.disabled {
  pointer-events: none;
}

.divisionOperation {
  font-family: 'Times New Roman', Times, serif;
}

.baseScreen div:nth-last-child(-n + 3) {
  border-bottom: none;
}

.operations {
  max-width: 0px;
  width: 0;
  overflow: hidden;
  cursor: pointer;
  user-select: none;
  color: white;
}

.operations.open {
  transition:
    max-width 0.4s ease,
    width 0.4s ease;

  max-width: 60px;
  width: 100%;
  border-left: 2px solid var(--color-gray200);
}

.operations div:last-child {
  border-bottom: none;
}
