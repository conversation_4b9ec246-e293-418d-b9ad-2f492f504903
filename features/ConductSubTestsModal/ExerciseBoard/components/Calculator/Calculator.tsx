import { Box } from '@mantine/core';
import {
  // useCallback,
  //  useEffect,
  useState,
} from 'react';
import { FaDeleteLeft } from 'react-icons/fa6';

import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { TestLocalesType } from '@/types/common';

import s from './Calculator.module.css';

type KeyPad = {
  label: string | React.ReactNode;
  value: string | number;
  type: 'numeric' | 'deletion' | 'submission';
};

type OperationKeyPad = {
  label: string | React.ReactNode;
  value: 'subtraction' | 'addition' | 'multiplication' | 'division';
  type: 'arithmetic-operation';
};

type DecimalsKeyPad = {
  label: string | React.ReactNode;
  value: 'dot' | 'placeholder';
  type: 'decimal-operation';
};

type CalculatorProps = {
  value?: string;
  isDisabled?: boolean;
  areOperationsEnabled?: boolean;
  areDecimalsEnabled?: boolean;
  content: {
    divisionSymbol: TestLocalesType['divisionSymbol'];
    decimalSymbol: TestLocalesType['decimalSymbol'];
    multiplySymbol: TestLocalesType['multiplySymbol'];
  };
  onSubmitAnswer: (value: string) => void;
};

const BASE_MENU: KeyPad[] = [
  {
    label: '7',
    value: 7,
    type: 'numeric',
  },
  {
    label: '8',
    value: 8,
    type: 'numeric',
  },
  {
    label: '9',
    value: 9,
    type: 'numeric',
  },
  {
    label: '4',
    value: 4,
    type: 'numeric',
  },
  {
    label: '5',
    value: 5,
    type: 'numeric',
  },
  {
    label: '6',
    value: 6,
    type: 'numeric',
  },
  {
    label: '1',
    value: 1,
    type: 'numeric',
  },
  {
    label: '2',
    value: 2,
    type: 'numeric',
  },
  {
    label: '3',
    value: 3,
    type: 'numeric',
  },
  {
    label: <FaDeleteLeft color="white" size={21} />,
    value: 'deletion',
    type: 'deletion',
  },
  {
    label: '0',
    value: 0,
    type: 'numeric',
  },
  {
    label: <Icon name="CheckMarkSvg" color="white" size="md" />,
    value: 'submission',
    type: 'submission',
  },
];

const GLOBAL_OPERATION_SYMBOLS = {
  DIVISION: '/',
  DOT_DECIMAL: '.',
  MULTIPLICATION: '*',
} as const;

// const SUPPORTED_KEYBOARD_OPERATIONS: {
//   [key: string]: KeyPad | OperationKeyPad;
// } = {
//   Enter: {
//     label: '',
//     value: 'submission',
//     type: 'submission',
//   },
//   Backspace: {
//     label: '',
//     value: 'deletion',
//     type: 'deletion',
//   },
//   '/': {
//     label: '/',
//     value: 'division',
//     type: 'arithmetic-operation',
//   },
//   '*': {
//     label: '*',
//     value: 'multiplication',
//     type: 'arithmetic-operation',
//   },
//   '-': {
//     label: '-',
//     value: 'subtraction',
//     type: 'arithmetic-operation',
//   },
//   '+': {
//     label: '+',
//     value: 'addition',
//     type: 'arithmetic-operation',
//   },
//   '1': {
//     label: '1',
//     value: 1,
//     type: 'numeric',
//   },
//   '2': {
//     label: '2',
//     value: 2,
//     type: 'numeric',
//   },
//   '3': {
//     label: '3',
//     value: 3,
//     type: 'numeric',
//   },
//   '4': {
//     label: '4',
//     value: 4,
//     type: 'numeric',
//   },
//   '5': {
//     label: '5',
//     value: 5,
//     type: 'numeric',
//   },
//   '6': {
//     label: '6',
//     value: 6,
//     type: 'numeric',
//   },
//   '7': {
//     label: '7',
//     value: 7,
//     type: 'numeric',
//   },
//   '8': {
//     label: '8',
//     value: 8,
//     type: 'numeric',
//   },
//   '9': {
//     label: '9',
//     value: 9,
//     type: 'numeric',
//   },
// };

const Calculator = ({
  areDecimalsEnabled = false,
  areOperationsEnabled = false,
  content,
  isDisabled = false,
  onSubmitAnswer,
  value = '',
}: CalculatorProps): JSX.Element => {
  const [keyPadActionsHistory, setKeyPadActionsHistory] = useState<
    (KeyPad | OperationKeyPad | DecimalsKeyPad)[]
  >([]);

  const OPERATIONS_MENU: OperationKeyPad[] = [
    {
      label: content.divisionSymbol,
      value: 'division',
      type: 'arithmetic-operation',
    },
    {
      label: content.multiplySymbol,
      value: 'multiplication',
      type: 'arithmetic-operation',
    },
    {
      label: '-',
      value: 'subtraction',
      type: 'arithmetic-operation',
    },
    {
      label: '+',
      value: 'addition',
      type: 'arithmetic-operation',
    },
  ];

  const DECIMALS_MENU: DecimalsKeyPad[] = [
    {
      label: content.decimalSymbol,
      value: 'dot',
      type: 'decimal-operation',
    },
    {
      label: '',
      value: 'placeholder',
      type: 'decimal-operation',
    },
    {
      label: '',
      value: 'placeholder',
      type: 'decimal-operation',
    },
    {
      label: '',
      value: 'placeholder',
      type: 'decimal-operation',
    },
  ];

  const screenResult =
    value ||
    (keyPadActionsHistory?.length
      ? keyPadActionsHistory.map((keyPad) => keyPad.label).join('')
      : null);

  const submissionResult = keyPadActionsHistory?.length
    ? keyPadActionsHistory
        .map((keyPad) => {
          if (keyPad.type === 'decimal-operation' && keyPad.value === 'dot') {
            return GLOBAL_OPERATION_SYMBOLS.DOT_DECIMAL;
          }

          if (
            keyPad.type === 'arithmetic-operation' &&
            keyPad.value === 'division'
          ) {
            return GLOBAL_OPERATION_SYMBOLS.DIVISION;
          }

          if (
            keyPad.type === 'arithmetic-operation' &&
            keyPad.value === 'multiplication'
          ) {
            return GLOBAL_OPERATION_SYMBOLS.MULTIPLICATION;
          }

          return keyPad.label;
        })
        .join('')
    : null;

  const handlePadClick = (item: KeyPad | OperationKeyPad | DecimalsKeyPad) => {
    if (item.type === 'numeric') {
      setKeyPadActionsHistory((prev) => [...prev, item]);
      return;
    }

    if (keyPadActionsHistory.length === 0) return;

    if (item.type === 'arithmetic-operation') {
      const hasAlreadyOneArithmeticOperation = keyPadActionsHistory.reduce(
        (acc, value) => (value.type === 'arithmetic-operation' ? acc + 1 : acc),
        0
      );

      const isLastValueOperation =
        keyPadActionsHistory[keyPadActionsHistory.length - 1].type ===
        'arithmetic-operation';

      if (isLastValueOperation && hasAlreadyOneArithmeticOperation) {
        setKeyPadActionsHistory((prev) => [...prev.slice(0, -1), item]);
      } else {
        if (hasAlreadyOneArithmeticOperation) return;

        setKeyPadActionsHistory((prev) => [...prev, item]);
      }

      return;
    }

    if (item.type === 'decimal-operation') {
      const isLastValueDecimalOperation =
        keyPadActionsHistory[keyPadActionsHistory.length - 1].type ===
        'decimal-operation';

      if (isLastValueDecimalOperation) return;

      const hasAlreadyOneDecimalOperation = keyPadActionsHistory.reduce(
        (acc, v) => (v.type === 'decimal-operation' ? acc + 1 : acc),
        0
      );

      if (hasAlreadyOneDecimalOperation) return;

      setKeyPadActionsHistory((prev) => [...prev, item]);

      return;
    }

    if (item.type === 'deletion') {
      setKeyPadActionsHistory((prev) => prev.slice(0, -1));
      return;
    }

    onSubmitAnswer(submissionResult || '');
  };

  // handle what happens on key press
  // const handleKeyPress = useCallback(
  //   (event: KeyboardEvent) => {
  //     const keyPressed = event.key;

  //     if (value) return;

  //     if (
  //       // eslint-disable-next-line no-prototype-builtins
  //       SUPPORTED_KEYBOARD_OPERATIONS.hasOwnProperty(keyPressed)
  //     ) {
  //       // console.log('i will ', SUPPORTED_KEYBOARD_OPERATIONS[keyPressed]);
  //       handlePadClick(SUPPORTED_KEYBOARD_OPERATIONS[keyPressed]);
  //     }
  //   },
  //   [value]
  // );

  // useEffect(() => {
  //   document.addEventListener('keydown', handleKeyPress);

  //   return () => {
  //     document.removeEventListener('keydown', handleKeyPress);
  //   };
  // }, [handleKeyPress]);

  return (
    <Box className={s.wrapper}>
      <Box className={s.displayScreen}>
        {screenResult ? (
          <Text
            untranslatedText={screenResult}
            type="h2"
            color="white"
            isBold
          />
        ) : null}
      </Box>

      <Box className={`${s.padsWrapper} ${isDisabled && s.disabled}`}>
        <Box className={s.baseScreen}>
          {BASE_MENU.map((item, i) => (
            <Box
              key={item.value}
              className={`
                ${s.keyPad} ${s[item.type]}
                ${i % 3 === 0 && s.borderRight}
                ${i % 3 === 1 && s.borderLeftRight}
                ${i % 3 === 2 && s.borderLeft}
              `}
              onClick={() =>
                keyPadActionsHistory.length > 8 && item.type === 'numeric'
                  ? {}
                  : handlePadClick(item)
              }
            >
              {item.type === 'numeric' ? (
                <Text
                  untranslatedText={`${item.label}`}
                  type="h4"
                  color="white"
                  fw={400}
                />
              ) : (
                item.label
              )}
            </Box>
          ))}
        </Box>

        <Box className={`${s.operations} ${areOperationsEnabled && s.open}`}>
          {OPERATIONS_MENU.map((item) => (
            <Box
              key={item.value}
              className={`${s.keyPad} ${s[item.type]} ${item.value === 'division' && s.divisionOperation}`}
              onClick={() =>
                keyPadActionsHistory.length > 8 ? {} : handlePadClick(item)
              }
            >
              <span>{item.label}</span>
            </Box>
          ))}
        </Box>

        <Box className={`${s.operations} ${areDecimalsEnabled && s.open}`}>
          {DECIMALS_MENU.map((item, index) => (
            <Box
              // eslint-disable-next-line react/no-array-index-key
              key={index}
              className={`${s.keyPad} ${s[item.type]} ${item.value === 'placeholder' && s.disabled}`}
              onClick={() =>
                keyPadActionsHistory.length > 8 ? {} : handlePadClick(item)
              }
            >
              {/* <span>{item.label}</span> */}

              {item.label}
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default Calculator;
