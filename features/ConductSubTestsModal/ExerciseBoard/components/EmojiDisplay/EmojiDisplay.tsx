import dynamic from 'next/dynamic';
import React from 'react';

import s from './EmojiDisplay.module.css';
import happyEmoji from './happy.json';
import sadEmoji from './sad.json';

type EmojiDisplayProps = {
  isEmojiDisplayed: boolean;
  type: 'success' | 'failure';
};

const EmojiDisplay = ({ isEmojiDisplayed, type }: EmojiDisplayProps) => {
  const DynamicLottie = dynamic(() => import('lottie-react'), { ssr: false });

  return isEmojiDisplayed ? (
    <div className={s.wrapper}>
      <DynamicLottie
        animationData={type === 'success' ? happyEmoji : sadEmoji}
        className={s.emoji}
        autoPlay
        loop
      />
    </div>
  ) : null;
};

export default EmojiDisplay;
