.wrapper {
  position: relative;
  height: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-smd);
}

.practiceStageWrapper {
  position: absolute;
  width: fit-content;
  height: 100%;
  top: -16px;
  background-color: var(--color-black);
  padding: var(--spacing-smd) var(--spacing-xl);
  border-radius: var(--radius-xs);
  display: flex;
  align-items: center;
}

.timeChallengeWrapper {
  position: absolute;
  top: -24px;
  left: -28px;
}
