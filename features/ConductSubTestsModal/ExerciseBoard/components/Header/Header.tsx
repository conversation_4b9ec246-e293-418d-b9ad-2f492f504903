/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { LuTimer } from 'react-icons/lu';

import Text from '@/components/Text/Text';

import s from './Header.module.css';

type HeaderProps = {
  isPracticeDisplayed: boolean;
  practiceLabel: string;
  isTimedChallengeDisplayed: boolean;
  timedChallengeLabel: string;
};

const Header = ({
  isPracticeDisplayed,
  isTimedChallengeDisplayed,
  practiceLabel,
  timedChallengeLabel,
}: HeaderProps) => {
  return (
    <div className={s.wrapper}>
      {/* {isPracticeDisplayed && (
        <div className={s.practiceStageWrapper}>
          <Text untranslatedText={practiceLabel} color="white" fw={500} />
        </div>
      )} */}

      {isTimedChallengeDisplayed && (
        <LuTimer size={86} color="white" className={s.timeChallengeWrapper} />
      )}
    </div>
  );
};

export default Header;
