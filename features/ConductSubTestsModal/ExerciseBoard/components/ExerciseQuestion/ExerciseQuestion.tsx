import { JSX } from 'react';

import Text from '@/components/Text/Text';

type ExerciseQuestionProps = {
  question: string;
};

const ExerciseQuestion = ({
  question,
}: ExerciseQuestionProps): JSX.Element | null => {
  return question ? (
    <Text
      untranslatedText={question}
      type="h2"
      color="white"
      align="center"
      fw={500}
      mb={32}
    />
  ) : null;
};

export default ExerciseQuestion;
