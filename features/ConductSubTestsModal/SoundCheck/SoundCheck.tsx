/* eslint-disable react-hooks/exhaustive-deps */

import { LoadingOverlay } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { LottieRefCurrentProps } from 'lottie-react';
import dynamic from 'next/dynamic';
import React, { JSX, useEffect, useRef, useState } from 'react';
import { FaArrowRightLong } from 'react-icons/fa6';

import { isUrl } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import { AdjustAudioDetailsType, TestLocalesType } from '@/types/common';

import headphonesLottie from './headphonesLottie.json';
import playerLottie from './playerLotieSpeaker.json';
import s from './SoundCheck.module.css';

type SoundCheckProps = {
  sessionId: string;
  onBegin: () => void;
  content: {
    playButtonTitle: TestLocalesType['playButtonTitle'];
    stopButtonTitle: TestLocalesType['stopButtonTitle'];
    skipButtonTitle: TestLocalesType['skipButtonTitle'];
    nextButtonTitle: TestLocalesType['nextButtonTitle'];
  };
};

const DynamicLottie = dynamic(() => import('lottie-react'), { ssr: false });

const SoundCheck = ({
  content,
  onBegin,
  sessionId,
}: SoundCheckProps): JSX.Element => {
  const [isPlayingSound, setIsPlayingSound] = useState<boolean>(false);
  const [isIntroPlayed, setIsIntroPlayed] = useState<boolean>(false);

  const soundRef = useRef<HTMLAudioElement | null>(null);

  const lottieRef = useRef<LottieRefCurrentProps | null>(null);
  const headphonesLottieRef = useRef<LottieRefCurrentProps | null>(null);

  const onPlayClick = () => {
    if (soundRef.current && !isPlayingSound) {
      setIsPlayingSound(true);
      soundRef.current.play();
    }
  };

  const onStopClick = () => {
    if (soundRef.current && isPlayingSound) {
      setIsPlayingSound(false);
      soundRef.current.pause();
      soundRef.current.currentTime = 0;
    }
  };

  // Synchronize Lottie animation with `isPlayingSound`
  useEffect(() => {
    if (lottieRef.current) {
      if (isPlayingSound) {
        lottieRef.current.play();
      } else {
        lottieRef.current.stop();
      }
    }
  }, [isPlayingSound]);

  const { data, isLoading } = useQuery<AdjustAudioDetailsType | null>({
    queryFn: () => SESSIONS_ACTIONS.ADJUST_AUDIO_DETAILS(sessionId),
    queryKey: [QUERY_KEYS.TEST_ADJUST_AUDIO_BY_ID, sessionId],
    enabled: Boolean(sessionId),
    staleTime: 1000 * 60 * 60 * 24,
  });

  useEffect(() => {
    if (isIntroPlayed && soundRef.current) {
      setIsPlayingSound(true);
      soundRef.current.play();
    }
  }, [isIntroPlayed]);

  const isAudioUrl = Boolean(data?.soundFile) && isUrl(data?.soundFile || '');

  return (
    <div className={s.wrapper}>
      <div className={s.content}>
        <LoadingOverlay visible={isLoading} overlayProps={{ blur: 1 }} />

        {!isLoading && (
          <>
            {isIntroPlayed ? (
              <>
                <Text
                  untranslatedText={data?.instructions || ''}
                  type="h3"
                  className={s.description}
                  align="center"
                  fw={500}
                />

                <div className={s.playerWrapper}>
                  {data && playerLottie && (
                    <DynamicLottie
                      lottieRef={lottieRef}
                      animationData={playerLottie}
                      className={s.player}
                      autoPlay={false}
                      loop={isPlayingSound}
                    />
                  )}
                </div>

                <audio
                  ref={soundRef}
                  src={isAudioUrl ? data?.soundFile : '/sound_test_norm.mp3'}
                  style={{ display: 'none' }}
                  onEnded={onStopClick}
                  autoPlay
                  onError={onStopClick}
                />

                <Button
                  id="stop"
                  untranslatedText={
                    isPlayingSound
                      ? content?.stopButtonTitle
                      : content?.playButtonTitle
                  }
                  onClick={isPlayingSound ? onStopClick : onPlayClick}
                />
              </>
            ) : (
              <>
                {data?.headphones && (
                  <Text
                    untranslatedText={data.headphones || ''}
                    type="h2"
                    align="center"
                  />
                )}

                <div>
                  {data && playerLottie && (
                    <DynamicLottie
                      lottieRef={headphonesLottieRef}
                      animationData={headphonesLottie}
                      className={s.headphones}
                      autoPlay
                      loop
                    />
                  )}
                </div>

                <audio
                  src={data?.audioFile}
                  style={{ display: 'none' }}
                  autoPlay
                  loop
                />
              </>
            )}
          </>
        )}
      </div>

      {/* {isIntroPlayed ? (
        <Button
          untranslatedText={content?.nextButtonTitle || ` `}
          onClick={onBegin}
          variant="success"
          isDisabled={isLoading}
        />
      ) : (
        <div className={s.arrowButton} onClick={() => setIsIntroPlayed(true)}>
          <FaArrowRightLong color="var(--color-darkerGreen)" size={40} />
        </div>
      )} */}

      <div
        className={s.arrowButton}
        onClick={isIntroPlayed ? onBegin : () => setIsIntroPlayed(true)}
      >
        <FaArrowRightLong color="var(--color-darkerGreen)" size={60} />
      </div>
    </div>
  );
};

export default SoundCheck;
