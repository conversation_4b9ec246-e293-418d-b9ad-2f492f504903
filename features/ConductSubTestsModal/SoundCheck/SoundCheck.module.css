.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xl);
  background-color: var(--color-white);
  border-radius: var(--radius-sm);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  width: 100%;
  height: 100%;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  position: relative;
}

.description {
  width: 100%;
  padding: 0 var(--spacing-md);
}

.player {
  width: 160px;
  height: 160px;
}

.headphones {
  width: 400px;
  height: 400px;
}

.playerWrapper {
  height: 160px;
}

.arrowButton {
  background-color: var(--color-white);
  min-height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-2xs);

  &:hover {
    cursor: pointer;
    opacity: 0.9;
  }

  &:active {
    opacity: 0.7;
  }
}
