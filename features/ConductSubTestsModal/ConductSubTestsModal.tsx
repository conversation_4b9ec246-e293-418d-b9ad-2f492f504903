/* eslint-disable react-hooks/exhaustive-deps */
import { Modal } from '@mantine/core';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { TEST_ERRORS } from '@/common/errors';
import QUERY_KEYS from '@/common/queryKeys';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import {
  ConductedTestSessionStatsType,
  SubTestIntroType,
  TestLocalesType,
} from '@/types/common';
import { NarrowedSubTestQuestionType } from '@/types/currentSubTestExerciseResponse';

import AdminSkipButtons from './AdminSkipButtons/AdminSkipButtons';
import s from './ConductSubTestsModal.module.css';
import ExerciseBoard from './ExerciseBoard/ExerciseBoard';
import Intro from './Intro/Intro';
import ProgressHeader from './ProgressHeader/ProgressHeader';
import SessionCompletion from './SessionCompletion/SessionCompletion';
import SoundCheck from './SoundCheck/SoundCheck';
import SubTestCompleted from './SubTestCompleted/SubTestCompleted';
import SubTestFailRetake from './SubTestFailRetake/SubTestFailRetake';

type ConductSubTestsModalProps = {
  hasCloseButton?: boolean;
  onCompleteTest?: () => void;
};

const SESSION_STATE = {
  SOUND_CHECK: 'sound_check',
  INTRO: 'intro',
  QUESTIONS: 'questions',
  COMPLETION_SCREEN: 'completion_screen',
  SUB_TEST_COMPLETION_SCREEN: 'sub_test_completion_screen',
  RETAKE_SUB_TEST_SCREEN: 'retake_sub_test_screen',
};

const ConductSubTestsModal = ({
  hasCloseButton = true,
  onCompleteTest,
}: ConductSubTestsModalProps): JSX.Element | null => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const testSessionId = (router.query.session || '') as string;
  const [sessionState, setSessionState] = useState(SESSION_STATE.SOUND_CHECK);
  const [congratsAudio, setCongratsAudio] = useState<string>('');
  const [retakeAudio, setRetakeAudio] = useState<string>('');
  const [scale, setScale] = useState(1);

  const isSessionIdValid = router.isReady && Boolean(testSessionId);

  const {
    data: sessionProgress,
    error: progressError,
    isError: hasProgressError,
    isLoading: isSessionProgressLoading,
  } = useQuery<ConductedTestSessionStatsType | null>({
    queryFn: () => SESSIONS_ACTIONS.GET_TEST_SESSION_DETAILS(testSessionId),
    queryKey: [QUERY_KEYS.TEST_SESSION_DETAILS_BY_ID, testSessionId],
    enabled: isSessionIdValid,
  });

  const isSessionProgressLoaded =
    !isSessionProgressLoading && Boolean(sessionProgress?.id);

  const { data: introData, isLoading: isIntroDataLoading } =
    useQuery<SubTestIntroType | null>({
      queryFn: () => SESSIONS_ACTIONS.GET_TEST_SESSION_INTRO(testSessionId),
      queryKey: [QUERY_KEYS.TEST_SESSION_INTRO_BY_ID, testSessionId],
      enabled: isSessionIdValid && isSessionProgressLoaded,
      staleTime: 1000 * 60 * 60 * 24,
    });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data: languageData, isLoading: isLanguageDataLoading } =
    useQuery<TestLocalesType | null>({
      queryFn: () => SESSIONS_ACTIONS.GET_TEST_LOCALS(testSessionId),
      queryKey: [QUERY_KEYS.TEST_TRANSLATIONS_BY_ID, sessionProgress?.language],
      staleTime: 1000 * 60 * 60 * 24,
      enabled: isSessionIdValid && isSessionProgressLoaded,
    });

  const { data: currentExercise, isFetching: isCurrentExerciseFetching } =
    useQuery<NarrowedSubTestQuestionType | null>({
      enabled: isSessionIdValid && isSessionProgressLoaded,
      queryFn: () =>
        SESSIONS_ACTIONS.GET_CURRENT_STATE_OF_SESSION(testSessionId),
      queryKey: [QUERY_KEYS.TEST_SESSION_CURRENT_QUESTION, testSessionId],
    });

  const isSubTestsModalOpen =
    router.isReady && router.asPath.includes('session');

  const isSessionTestNotRunning =
    progressError?.message === TEST_ERRORS.STUDENT_TEST_SESSION_IS_NOT_RUNNING;

  const currentProgress =
    ((sessionProgress?.currentSubtestsOrder || 1) * 100) /
    (sessionProgress?.totalSubtestsCount || 1);

  const isIntroMode = !sessionProgress?.hasWatchedIntro;

  // TODO : Content should be under a provider Single it will never change and be consumed by one single reference, Instead of creating new one by passing props
  const exerciseBoardContent = {
    practiceStageLabel: languageData?.practiceStageLabel || '',
    nextQuestion: languageData?.nextQuestion || '',
    correct: languageData?.correct || '',
    incorrect: languageData?.incorrect || '',
    timeChallenge: languageData?.timeChallenge || '',
    hearTheProblem: languageData?.hearTheProblem || '',
    ifPredicate: languageData?.ifPredicate || '',
    whatIsPredicate: languageData?.whatIsPredicate || '',
    questionMark: languageData?.questionMark || '',
    decimalSymbol: languageData?.decimalSymbol || '',
    multiplySymbol: languageData?.multiplySymbol || '',
    divisionSymbol: languageData?.divisionSymbol || '',
    nextButtonTitle: languageData?.nextButtonTitle || '',
    startButtonTitle: languageData?.startButtonTitle || '',
    timeCounts: languageData?.timeCounts || '',
  };

  const removeSessionFromUrl = (url: string) => {
    // Find the index of '?session=' or '&session='
    const sessionIndex = url.indexOf('?session=');

    // If '?session=' is found, remove everything after it
    if (sessionIndex !== -1) {
      return url.split('?session=')[0];
    }

    // Find '&session=' and remove everything after it
    const sessionParamIndex = url.indexOf('&session=');

    if (sessionParamIndex !== -1) {
      return url.split('&session=')[0];
    }

    // If no session parameter exists, return the original URL
    return url;
  };

  const moveToCompletedScreen = async () => {
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
    });

    setSessionState(SESSION_STATE.COMPLETION_SCREEN);
  };

  const moveToSubTestCompletionScreen = (url?: string) => {
    setSessionState(SESSION_STATE.SUB_TEST_COMPLETION_SCREEN);

    if (url) {
      setCongratsAudio(url);
    }
  };

  const moveToIntroScreen = async () => {
    await queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_CURRENT_QUESTION, testSessionId],
    });

    await queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_DETAILS_BY_ID, testSessionId],
    });

    await queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_INTRO_BY_ID, testSessionId],
    });

    setSessionState(SESSION_STATE.INTRO);
  };

  const onRetakeSubTest = async () => {
    await queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_CURRENT_QUESTION, testSessionId],
    });

    await queryClient.refetchQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_DETAILS_BY_ID, testSessionId],
    });

    setSessionState(SESSION_STATE.QUESTIONS);
  };

  const onClose = () => {
    const cleanedUrl = removeSessionFromUrl(router.asPath);
    router.push(cleanedUrl);

    if (
      sessionState === SESSION_STATE.SUB_TEST_COMPLETION_SCREEN ||
      sessionState === SESSION_STATE.COMPLETION_SCREEN
    ) {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ALL_CREATED_SESSION_TESTS],
      });
    }

    setTimeout(() => {
      setSessionState(SESSION_STATE.SOUND_CHECK);
    }, 500);
  };

  const getInfoMessage = () => {
    if (sessionState === SESSION_STATE.INTRO) {
      return introData?.instruction ? introData?.instruction : '';
    }

    if (sessionState === SESSION_STATE.QUESTIONS) {
      return sessionProgress?.info ? sessionProgress?.info : '';
    }

    return '';
  };

  useEffect(() => {
    if (isSessionTestNotRunning) {
      onClose();
    }
  }, [isSessionTestNotRunning]);

  useEffect(() => {
    const handleResize = () => {
      const scaleFactor = Math.min(
        window.innerWidth / 1100,
        window.innerHeight / 800
      );

      if (scaleFactor < 1) {
        setScale(scaleFactor);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once to set initial scale

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [window.innerHeight, window.innerWidth]);

  return (
    <Modal
      opened={isSubTestsModalOpen}
      onClose={() => {}}
      fullScreen
      withCloseButton={false}
      classNames={s}
      autoFocus={false}
      trapFocus
    >
      <div
        className={s.container}
        style={{
          transform: `scale(${scale})`,
        }}
      >
        {sessionProgress && languageData && !hasProgressError && (
          <ProgressHeader
            subTestName={
              sessionState === SESSION_STATE.QUESTIONS ||
              sessionState === SESSION_STATE.INTRO
                ? sessionProgress?.title
                : ''
            }
            sessionTestGrade={sessionProgress?.testGrade || '0'}
            studentName={sessionProgress.studentFullName || ''}
            onClose={hasCloseButton ? onClose : undefined}
            infoMessage={getInfoMessage()}
            content={{
              subtest: languageData?.subtest || '',
              grade: languageData?.grade || '',
              subject: languageData?.subject || '',
            }}
          />
        )}

        <div className={s.displayScreen}>
          {/* SOUND CHECK */}
          {sessionState === SESSION_STATE.SOUND_CHECK &&
            !isSessionProgressLoading &&
            !hasProgressError && (
              <SoundCheck
                sessionId={testSessionId}
                onBegin={() => {
                  if (isIntroMode) {
                    setSessionState(SESSION_STATE.INTRO);
                  } else {
                    setSessionState(SESSION_STATE.QUESTIONS);
                  }
                }}
                content={{
                  playButtonTitle: languageData?.playButtonTitle || '',
                  stopButtonTitle: languageData?.stopButtonTitle || '',
                  skipButtonTitle: languageData?.skipButtonTitle || '',
                  nextButtonTitle: languageData?.nextButtonTitle || '',
                }}
              />
            )}

          {/* INTRO */}
          {sessionState === SESSION_STATE.INTRO &&
            isIntroMode &&
            !hasProgressError &&
            !isIntroDataLoading && (
              <Intro
                testSessionId={testSessionId}
                onSubmitOption={() => setSessionState(SESSION_STATE.QUESTIONS)}
                introData={introData || null}
                isTimeChallenged={Boolean(sessionProgress?.displayClock)}
                content={{
                  ...exerciseBoardContent,
                  skipButtonTitle: languageData?.skipButtonTitle || '',
                  nextButtonTitle: languageData?.nextButtonTitle || '',
                }}
              />
            )}

          {/* EXERCISE BOARD */}
          {currentExercise &&
            sessionState === SESSION_STATE.QUESTIONS &&
            !hasProgressError &&
            !isSessionProgressLoading && (
              <ExerciseBoard
                testSessionId={testSessionId}
                currentExercise={currentExercise}
                isTimeChallenged={Boolean(sessionProgress?.displayClock)}
                isCurrentExerciseFetching={isCurrentExerciseFetching}
                onCompleteSessionTest={() => {
                  moveToCompletedScreen();
                  onCompleteTest?.();
                }}
                onCompleteSessionSubTest={moveToSubTestCompletionScreen}
                onRetakeSubTest={(audioUrl?: string) => {
                  setSessionState(SESSION_STATE.RETAKE_SUB_TEST_SCREEN);

                  if (audioUrl) {
                    setRetakeAudio(audioUrl);
                  }
                }}
                content={exerciseBoardContent}
              />
            )}

          {/* RETAKE SUB TEST */}
          {sessionState === SESSION_STATE.RETAKE_SUB_TEST_SCREEN &&
            languageData && (
              <SubTestFailRetake
                retakeAudio={retakeAudio}
                retakeText={languageData?.subtestRetake || ''}
                retakeButtonTitle={languageData?.retakeButtonTitle || ''}
                onRetakeButtonClick={onRetakeSubTest}
              />
            )}

          {/* COMPLETE SUB TEST */}
          {sessionState === SESSION_STATE.SUB_TEST_COMPLETION_SCREEN &&
            languageData && (
              <SubTestCompleted
                onNextButtonClick={moveToIntroScreen}
                currentProgress={currentProgress}
                congratsAudio={congratsAudio}
                currentExecutedSubTest={
                  sessionProgress?.currentSubtestsOrder || 0
                }
                totalNumberOfSubTests={sessionProgress?.totalSubtestsCount || 0}
                content={{
                  ofTitle: languageData?.ofTitle || '',
                  subtestCompleted: languageData?.subtestCompleted || '',
                  nextButtonTitle: languageData?.nextButtonTitle || '',
                }}
              />
            )}

          {/* COMPLETE SESSION */}
          {sessionState === SESSION_STATE.COMPLETION_SCREEN && (
            <SessionCompletion
              onClose={onClose}
              content={{
                congratulations: languageData?.congratulations || '',
                testCompleted: languageData?.testCompleted || '',
                doneButtonTitle: languageData?.doneButtonTitle || '',
              }}
            />
          )}
        </div>
      </div>

      {sessionState === SESSION_STATE.QUESTIONS && (
        <AdminSkipButtons
          testSessionId={testSessionId}
          handleShowIntro={moveToIntroScreen}
          isFirstTest={sessionProgress?.currentSubtestsOrder === 1}
          isLastTest={
            sessionProgress?.currentSubtestsOrder ===
            sessionProgress?.totalSubtestsCount
          }
        />
      )}
    </Modal>
  );
};

export default ConductSubTestsModal;
