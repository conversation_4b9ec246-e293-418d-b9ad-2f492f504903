import { Box, CloseButton, Popover } from '@mantine/core';
import { FaInfo } from 'react-icons/fa';

import Text from '@/components/Text/Text';
import { TestLocalesType } from '@/types/common';

import s from './ProgressHeader.module.css';

type ProgressHeaderPropsType = {
  subTestName: string;
  sessionTestGrade: string;
  studentName: string;
  onClose?: () => void;
  infoMessage?: string;
  content: {
    subtest: TestLocalesType['subtest'];
    grade: TestLocalesType['grade'];
    subject: TestLocalesType['subject'];
  };
};

const ProgressHeader = ({
  content,
  infoMessage,
  onClose,
  sessionTestGrade,
  studentName,
  subTestName,
}: ProgressHeaderPropsType): JSX.Element => {
  return (
    <Box className={s.wrapper}>
      <div className={s.textGrouping}>
        <Text
          untranslatedText={studentName}
          color="white"
          type="subTitle"
          fw={600}
          className={s.studentName}
        />
      </div>

      <div className={s.subTestName}>
        {subTestName && (
          <Text
            type="subTitle"
            untranslatedText={subTestName}
            color="white"
            isBold
            mr={24}
          />
        )}

        {infoMessage && (
          <Popover position="bottom" width={500} withArrow shadow="md">
            <Popover.Target>
              <div className={s.popoverIcon}>
                <FaInfo size={12} color="black" />
              </div>
            </Popover.Target>

            <Popover.Dropdown>
              <Text
                untranslatedText={infoMessage || ''}
                type="subTitle1"
                align="center"
              />
            </Popover.Dropdown>
          </Popover>
        )}
      </div>

      <div className={s.rightSection}>
        <div className={s.textGrouping}>
          <Text
            type="subTitle2"
            untranslatedText={content.grade}
            color="white"
          />

          <Text
            type="subTitle2"
            untranslatedText={`${sessionTestGrade}`}
            color="white"
            isBold
          />
        </div>

        {onClose && (
          <CloseButton
            onClick={() => {
              onClose();
            }}
            variant="subtle"
            autoFocus={false}
          />
        )}
      </div>
    </Box>
  );
};

export default ProgressHeader;
