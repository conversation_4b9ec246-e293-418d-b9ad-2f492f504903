.wrapper {
  position: relative;
  height: 52px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-md);
}

.textGrouping {
  display: flex;
  gap: var(--spacing-sm);
  height: 25px;
  align-items: center;
}

.leftSection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.testDetails {
  overflow: hidden;
  display: flex;
  gap: var(--spacing-lg);
}

.rightSection {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-smd);
  height: 25px;
}

.popoverIcon {
  cursor: pointer;
  background-color: var(--color-white);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 25px;
  height: 25px;
  color: var(--color-white);
  padding-bottom: 2px;
  padding-left: 1px;
  z-index: 1;
}

.subTestName {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 0;
}

.studentName {
  padding-right: var(--spacing-sm);
}
