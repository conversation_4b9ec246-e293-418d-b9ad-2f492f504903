import React, { JSX, useState } from 'react';
import { FaDivide, FaMinus, FaPlus, FaTimes } from 'react-icons/fa';
import { LuTimer } from 'react-icons/lu';

import Button from '@/components/Button/Button';
import Text from '@/components/Text/Text';
import {
  NonUndefined,
  SubTestQuestionType,
  TestLocalesType,
} from '@/types/common';

import s from './StartExercisePracticeEnded.module.css';

type StartExercisePracticeEndedProps = {
  onNext: () => void;
  title: string;
  body: string;
  audio?: string;
  icon?: NonUndefined<SubTestQuestionType['section']>['icon'];
  startButtonTitle: TestLocalesType['startButtonTitle'];
};

const SECTION_ICONS: { [key: string]: React.ReactNode } = {
  clock: <LuTimer size={132} color="white" />,
  over: <FaDivide size={132} color="white" />,
  minus: <FaMinus size={132} color="white" />,
  plus: <FaPlus size={132} color="white" />,
  times: <FaTimes size={132} color="white" />,
};

const StartExercisePracticeEnded = ({
  audio,
  body,
  icon,
  onNext,
  startButtonTitle,
  title,
}: StartExercisePracticeEndedProps): JSX.Element => {
  const [isPlayingSound, setIsPlayingSound] = useState(false);

  return (
    <div className={s.wrapper}>
      {audio && (
        <audio
          src={audio || ''}
          style={{ display: 'none' }}
          autoPlay
          loop={false}
          onEnded={() => setIsPlayingSound(false)}
          onError={() => setIsPlayingSound(false)}
          onPlay={() => setIsPlayingSound(true)}
        />
      )}

      <div className={s.content}>
        {title && (
          <Text
            untranslatedText={title}
            color="white"
            type="h1"
            fw={250}
            className={s.title}
            align="center"
          />
        )}

        {icon && icon !== 'clock' && (
          <div className={s.roundedCircle}>{SECTION_ICONS[icon]}</div>
        )}

        {icon && icon === 'clock' && SECTION_ICONS[icon]}

        {body && (
          <Text
            untranslatedText={body}
            color="white"
            type="h1"
            fw={250}
            className={s.title}
            align="center"
          />
        )}
      </div>

      <Button
        untranslatedText={startButtonTitle}
        onClick={onNext}
        variant="success"
        isDisabled={isPlayingSound}
      />
    </div>
  );
};

export default StartExercisePracticeEnded;
