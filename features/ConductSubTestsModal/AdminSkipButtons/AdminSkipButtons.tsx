/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { FaChevronCircleLeft, FaChevronCircleRight } from 'react-icons/fa';

import QUERY_KEYS from '@/common/queryKeys';
import { UserContext } from '@/context/UserProvider';
import SESSIONS_ADMIN_ACTIONS from '@/services/tests/sessions-admins';
import { TestAdminActionType } from '@/types/common';

import s from './AdminSkipButtons.module.css';

type AdminSkipButtonsType = {
  isFirstTest: boolean;
  isLastTest: boolean;
  testSessionId: string;
  handleShowIntro: () => void;
};

const AdminSkipButtons = ({
  handleShowIntro,
  isFirstTest,
  isLastTest,
  testSessionId,
}: AdminSkipButtonsType) => {
  const { userRoles } = UserContext();
  const queryClient = useQueryClient();

  const invalidationCallback = (showIntro = false) => {
    if (showIntro) {
      handleShowIntro();
    }

    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_DETAILS_BY_ID, testSessionId],
    });

    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.TEST_SESSION_CURRENT_QUESTION, testSessionId],
    });
  };

  const nextSubTestMutation = useMutation({
    mutationFn: SESSIONS_ADMIN_ACTIONS.NEXT_SESSION,

    onSuccess: async (res: TestAdminActionType | null) => {
      if (!res) return;

      invalidationCallback(res.skipped === 'subtest');
    },
  });

  const previousSubTestMutation = useMutation({
    mutationFn: SESSIONS_ADMIN_ACTIONS.PREVIOUS_SESSION,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onSuccess: async (res: TestAdminActionType | null) => {
      if (!res) return;

      invalidationCallback(res.skipped === 'subtest');
    },
  });

  return (
    userRoles.isAdmin && (
      <>
        {!isFirstTest && (
          <div
            className={s.skipSubTestLeftButton}
            onClick={
              !previousSubTestMutation.isPending
                ? () => previousSubTestMutation.mutate(testSessionId)
                : undefined
            }
          >
            <FaChevronCircleLeft color="white" size={30} />
          </div>
        )}

        {!isLastTest && (
          <div
            className={s.skipSubTestRightButton}
            onClick={
              !previousSubTestMutation.isPending
                ? () => nextSubTestMutation.mutate(testSessionId)
                : undefined
            }
          >
            <FaChevronCircleRight color="white" size={30} />
          </div>
        )}
      </>
    )
  );
};

export default AdminSkipButtons;
