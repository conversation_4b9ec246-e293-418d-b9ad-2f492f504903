/* eslint-disable jsx-a11y/media-has-caption */
import React, { useState } from 'react';

import Button from '@/components/Button/Button';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';
import { TestLocalesType } from '@/types/common';

import s from './SessionCompletion.module.css';

type SessionCompletionProps = {
  onClose: () => void;
  content: {
    congratulations: TestLocalesType['congratulations'];
    testCompleted: TestLocalesType['testCompleted'];
    doneButtonTitle: TestLocalesType['doneButtonTitle'];
  };
};

const SessionCompletion = ({
  content,
  onClose,
}: SessionCompletionProps): JSX.Element => {
  const [isPlayingSound, setIsPlayingSound] = useState(true);

  return (
    <div className={s.wrapper}>
      <div className={s.content}>
        <Icon name="CheckMarkWholeSvg" color="yellow" />

        <Text
          untranslatedText={content.congratulations}
          color="white"
          type="h1"
          fw={250}
          className={s.title}
        />

        <Text
          untranslatedText={content.testCompleted}
          color="white"
          type="h4"
          fw={300}
        />
      </div>

      <Button
        untranslatedText={content.doneButtonTitle}
        onClick={onClose}
        variant="success"
        isDisabled={isPlayingSound}
      />

      <audio
        src="/applause.mp3"
        style={{ display: 'none' }}
        autoPlay
        loop={false}
        onEnded={() => {
          setIsPlayingSound(false);
        }}
      />
    </div>
  );
};

export default SessionCompletion;
