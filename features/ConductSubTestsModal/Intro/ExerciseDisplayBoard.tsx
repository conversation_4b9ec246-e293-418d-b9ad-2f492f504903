import Icon from '@/components/Icon/Icon';
import { SubTestIntroType, TestLocalesType } from '@/types/common';
import { NarrowedSubTestQuestionType } from '@/types/currentSubTestExerciseResponse';

import Calculator from '../ExerciseBoard/components/Calculator/Calculator';
import ExerciseQuestion from '../ExerciseBoard/components/ExerciseQuestion/ExerciseQuestion';
import Header from '../ExerciseBoard/components/Header/Header';
import ExerciseType from '../ExerciseBoard/ExerciseTypes/ExerciseType';
import s from './ExerciseDisplayBoard.module.css';

type ExerciseBoardPropsType = {
  currentExercise: SubTestIntroType['sample'];
  isTimeChallenged: boolean;
  content: {
    practiceStageLabel: TestLocalesType['practiceStageLabel'];
    nextQuestion: TestLocalesType['nextQuestion'];
    correct: TestLocalesType['correct'];
    incorrect: TestLocalesType['incorrect'];
    timeChallenge: TestLocalesType['timeChallenge'];
    hearTheProblem: TestLocalesType['hearTheProblem'];
    ifPredicate: TestLocalesType['ifPredicate'];
    whatIsPredicate: TestLocalesType['whatIsPredicate'];
    questionMark: TestLocalesType['questionMark'];
    decimalSymbol: TestLocalesType['decimalSymbol'];
    divisionSymbol: TestLocalesType['divisionSymbol'];
    nextButtonTitle: TestLocalesType['nextButtonTitle'];
    timeCounts: TestLocalesType['timeCounts'];
    multiplySymbol: TestLocalesType['multiplySymbol'];
  };
};

const ExerciseDisplayBoard = ({
  content,
  currentExercise,
  isTimeChallenged,
}: ExerciseBoardPropsType): JSX.Element | null => {
  const isCalculatorDisplayed = currentExercise?.calculator !== 'occluded';

  const totalDisappear = currentExercise?.disappear
    ? (currentExercise?.disappear || 0) +
      (currentExercise?.fixation || 0) +
      (currentExercise?.ISI || 0)
    : 0;

  return (
    <div className={s.wrapper}>
      <div className={`${s.questionScreen}`}>
        <Header
          isPracticeDisplayed={false}
          practiceLabel={content.practiceStageLabel}
          isTimedChallengeDisplayed={isTimeChallenged}
          timedChallengeLabel={content.timeChallenge}
        />

        <ExerciseQuestion question={currentExercise?.question || ''} />

        <div>
          {currentExercise && (
            <ExerciseType
              currentExercise={currentExercise as NarrowedSubTestQuestionType}
              practiceModeCorrectAnswer={currentExercise?.answer?.value || ''}
              practiceModeAnswerType={currentExercise?.answer?.type || 'value'}
              isPracticeAnswerIncorrect
              onSubmitAnswer={() => {}}
              disappear={totalDisappear}
              onSoundEnd={() => {}}
              content={{
                hearTheProblem: content?.hearTheProblem || '',
                ifPredicate: content?.ifPredicate || '',
                whatIsPredicate: content?.whatIsPredicate || '',
                questionMark: content?.questionMark || '',
                nextQuestion: content?.nextButtonTitle || '',
              }}
            />
          )}
        </div>
      </div>

      {/* Right Side of the layout with the calculator display */}
      <div className={`${s.calculatorMenu} ${isCalculatorDisplayed && s.open}`}>
        <Calculator
          // key={`${currentExercise?.order} ${currentExercise?.practice}`}
          key={currentExercise?.code}
          content={{
            decimalSymbol: content?.decimalSymbol || '',
            divisionSymbol: content?.divisionSymbol || '',
            multiplySymbol: content?.multiplySymbol || '',
          }}
          areDecimalsEnabled={currentExercise?.calculator === 'decimals'}
          areOperationsEnabled={currentExercise?.calculator === 'extended'}
          isDisabled={false}
          onSubmitAnswer={() => {}}
          value={currentExercise?.answer?.value || ''}
        />
      </div>

      {isCalculatorDisplayed && (
        <Icon name="CalculatorArrowSvg" className={`${s.calculatorArrow}`} />
      )}
    </div>
  );
};

export default ExerciseDisplayBoard;
