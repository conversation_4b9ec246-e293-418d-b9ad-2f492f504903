.wrapper {
  width: 100%;
  height: 100%;
  max-height: 736px;
  display: flex;
  flex-direction: column;
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xl);
  border-radius: var(--radius-sm);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  position: relative;
  background-color: rgb(204, 204, 204);
}

.videoBG {
  background-color: rgb(214, 214, 214);
}

.imageBg {
  background-color: rgb(208, 208, 208);
}

.videoWrapper {
  clip-path: inset(3px 3px);
  height: 100%;
  max-height: 590px;
  max-width: 840px;
}

.replayOverlay {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  border-radius: var(--radius-sm);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;

  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 10px;
}

.audio {
  display: none;
}

.image {
  width: 1100px;
  height: 660px;
  object-fit: contain;
}

.replayButton {
  background-color: transparent;
  border-radius: var(--radius-2xs);
  border: none;
  width: 70px;
  height: 70px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
