import { useMutation } from '@tanstack/react-query';
import { JSX, useRef, useState } from 'react';

import Button from '@/components/Button/Button';
import IconButton from '@/components/IconButton/IconButton';
import SESSIONS_ACTIONS from '@/services/tests/sessions-action';
import { SubTestIntroType, TestLocalesType } from '@/types/common';

// import { NarrowedSubTestQuestionType } from '@/types/currentSubTestExerciseResponse';
import ExerciseDisplayBoard from './ExerciseDisplayBoard';
import s from './Intro.module.css';

type IntroProps = {
  testSessionId: string;
  onSubmitOption: () => void;
  introData: SubTestIntroType | null;
  isTimeChallenged: boolean;
  content: {
    skipButtonTitle: TestLocalesType['skipButtonTitle'];
    nextButtonTitle: TestLocalesType['nextButtonTitle'];
    startButtonTitle: TestLocalesType['startButtonTitle'];
    practiceStageLabel: TestLocalesType['practiceStageLabel'];
    nextQuestion: TestLocalesType['nextQuestion'];
    correct: TestLocalesType['correct'];
    incorrect: TestLocalesType['incorrect'];
    timeChallenge: TestLocalesType['timeChallenge'];
    hearTheProblem: TestLocalesType['hearTheProblem'];
    ifPredicate: TestLocalesType['ifPredicate'];
    whatIsPredicate: TestLocalesType['whatIsPredicate'];
    questionMark: TestLocalesType['questionMark'];
    decimalSymbol: TestLocalesType['decimalSymbol'];
    divisionSymbol: TestLocalesType['divisionSymbol'];
    timeCounts: TestLocalesType['timeCounts'];
    multiplySymbol: TestLocalesType['multiplySymbol'];
  };
};

const Intro = ({
  content,
  introData,
  isTimeChallenged,
  onSubmitOption,
  testSessionId,
}: IntroProps): JSX.Element => {
  const [isMediaEnded, setIsMediaEnded] = useState<boolean>(false);

  // under answerQuestionMutation.mutate() you provide the id
  const introWatchedMutation = useMutation({
    mutationFn: SESSIONS_ACTIONS.INTRO_WATCHED,
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);

  const videoRef = useRef<HTMLVideoElement | null>(null);

  const multimediaType = introData?.video ? 'video' : 'audio';

  const isVideoSkippable = Boolean(introData?.canSkipVideo);

  // const isSkipDisabled =
  //   multimediaType === 'video' && !isVideoSkippable && !isMediaEnded;

  const isSkipDisabled = !isVideoSkippable && !isMediaEnded;

  const onSkipClicked = () => {
    introWatchedMutation.mutate(testSessionId);

    if (multimediaType === 'video') {
      videoRef.current?.pause();
    } else if (multimediaType === 'audio') {
      audioRef.current?.pause();
    }

    onSubmitOption();
  };

  const onReplayClicked = () => {
    setIsMediaEnded(false);

    if (multimediaType === 'video') {
      videoRef.current?.play();
    } else if (multimediaType === 'audio') {
      audioRef.current?.play();
    }
  };

  // const getButtonText = () => {
  //   if (multimediaType === 'video' && !isVideoSkippable) {
  //     return content?.nextButtonTitle || ` `;
  //   }

  //   return isMediaEnded
  //     ? content?.nextButtonTitle || ` `
  //     : content?.skipButtonTitle || ` `;
  // };

  return (
    <div className={s.wrapper}>
      <div
        className={`${s.content} ${multimediaType === 'audio' ? s.imageBg : s.videoBG}`}
      >
        {multimediaType === 'video' && (
          <video
            src={introData?.video}
            ref={videoRef}
            autoPlay
            muted={false}
            onEnded={() => setIsMediaEnded(true)}
            loop={false}
            className={s.videoWrapper}
          />
        )}

        {multimediaType === 'audio' &&
          introData?.audio &&
          introData?.sample && (
            <>
              <ExerciseDisplayBoard
                currentExercise={introData?.sample}
                isTimeChallenged={isTimeChallenged}
                content={content}
              />

              <audio
                src={introData?.audio || ''}
                ref={audioRef}
                onEnded={() => {
                  setIsMediaEnded(true);
                }}
                controls={false}
                loop={false}
                autoPlay
                className={s.audio}
                onError={() => setIsMediaEnded(true)}
              >
                <track kind="captions" src={introData?.audio || ''} />
              </audio>
            </>
          )}

        {(introData?.video || introData?.audio) && isMediaEnded && (
          <div className={s.replayOverlay}>
            <IconButton
              iconName="RepeatSvg"
              onClick={onReplayClicked}
              className={s.replayButton}
              iconSize={60}
            />
          </div>
        )}
      </div>

      {isMediaEnded && (
        <audio
          src={introData?.repeat}
          style={{ display: 'none' }}
          autoPlay
          loop={false}
        />
      )}

      <Button
        untranslatedText={content?.startButtonTitle || ` `}
        onClick={onSkipClicked}
        variant="success"
        size="lg"
        isDisabled={isSkipDisabled}
      />
    </div>
  );
};

export default Intro;
