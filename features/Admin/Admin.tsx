import { SegmentedControl } from '@mantine/core';
import { useRouter } from 'next/router';
import { JSX, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FULL_DASHBOARD_ROUTES } from '@/common/routes';
import Text from '@/components/Text/Text';
import { UserContext } from '@/context/UserProvider';

import s from './Admin.module.css';
import AdminAddButtonVariants from './components/AdminAddButtonVariants/AdminAddButtonVariants';
import { ADMIN_TAB_OPTIONS, TABS } from './consts/adminTabs';
import SchoolsTab from './Tabs/Schools/SchoolsTab';
import StudiesTab from './Tabs/Studies/StudiesTab';
import TestsTab from './Tabs/Tests/TestsTab';
import UsersTab from './Tabs/Users/<USER>';

export type ActiveTabType = (typeof TABS)[number]['value'];

const Admin = (): JSX.Element => {
  const router = useRouter();
  const tabQueryParam = (router.query.tab as string) || 'users';
  const { t } = useTranslation();
  const { user, userRoles } = UserContext();

  const TABS_VALUES = TABS.map((tab) => tab.value);

  // In case the user tries to access a tab that does not exist, we default to the first tab
  const isValidQueryParam = TABS_VALUES.includes(
    tabQueryParam as ActiveTabType
  );

  const activeTab =
    TABS.find((item) => item.value === tabQueryParam) || TABS[0];

  useEffect(() => {
    if (!userRoles.isAdmin) {
      router.back();
    }
  }, [router, userRoles]);

  return (
    <div className="routeWrapper">
      <div className="pageHeaderWrapper">
        <div className={`${s['profile-details']} textPageIndicator`}>
          <div className={s.profileNameAndAffiliation}>
            <Text transKey="admin" type="h3" lineClamp={2} />

            {user?.profile.affiliation && (
              <Text
                untranslatedText={`${user?.profile.affiliation}`}
                type="body1"
                color="darkerBlue"
                lineClamp={2}
              />
            )}
          </div>
        </div>

        <SegmentedControl
          value={tabQueryParam}
          onChange={(v) => {
            router.push(`${FULL_DASHBOARD_ROUTES.ADMIN}?tab=${v}`);
          }}
          withItemsBorders={false}
          data={TABS.map((item) => ({
            value: item.value,
            label: t(item.label),
          }))}
        />

        <AdminAddButtonVariants
          activeTab={
            activeTab?.value === ADMIN_TAB_OPTIONS.TESTS
              ? null
              : activeTab?.value
          }
          isValidToggleModal={isValidQueryParam}
        />
      </div>

      {activeTab?.value === ADMIN_TAB_OPTIONS.USERS && <UsersTab />}

      {activeTab?.value === ADMIN_TAB_OPTIONS.STUDIES && <StudiesTab />}

      {activeTab?.value === ADMIN_TAB_OPTIONS.SCHOOLS && <SchoolsTab />}

      {activeTab?.value === ADMIN_TAB_OPTIONS.TESTS && <TestsTab />}
    </div>
  );
};

export default Admin;
