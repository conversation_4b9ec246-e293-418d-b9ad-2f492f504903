import { useDebouncedValue } from '@mantine/hooks';
import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useState } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_USERS from '@/services/admin/users';
import { UnassignedResearchersPaginatedResponseType } from '@/types/common';

const FETCH_DATA_LIMIT = 7;

const useUnassignedResearchers = (studyId?: string) => {
  const queryClient = useQueryClient();

  const [activePage, setActivePage] = useState(1);
  const [searchResearcherQuery, setSearchResearcherQuery] = useState('');

  const [debouncedSearchStudentQuery] = useDebouncedValue(
    searchResearcherQuery,
    500
  );

  const {
    data,
    isFetching: areResearchersFetching,
    isLoading: areResearchersLoading,
  } = useQuery<UnassignedResearchersPaginatedResponseType | null>({
    queryFn: () =>
      ADMIN_USERS.GET_UNASSIGNED_RESEARCHERS({
        limit: FETCH_DATA_LIMIT,
        page: activePage,
        query: debouncedSearchStudentQuery,
        studyId,
      }),
    queryKey: [
      QUERY_KEYS.ADMIN_UNASSIGNED_RESEARCHERS,
      activePage,
      debouncedSearchStudentQuery,
    ],
    placeholderData: keepPreviousData,
  });

  const researchers = data?.results || [];

  // Function to revalidate unassigned study admins
  const revalidateUnassignedResearchers = async () => {
    if (studyId) {
      await queryClient.invalidateQueries({
        queryKey: ['unassigned-study-admins', studyId],
      });
    }
  };

  return {
    researchers,
    onResearcherSearch: (value: string) => {
      setSearchResearcherQuery(value);
      setActivePage(1);
    },
    onPageChange: (page: number) => setActivePage(page),
    revalidateUnassignedResearchers,
    activePage,
    areResearchersLoading,
    areResearchersFetching,
    totalNumberOfPages: Math.ceil((data?.count || 0) / FETCH_DATA_LIMIT),
    totalNumberOfResearchers: data?.count || 0,
    searchResearcherQuery,
  };
};

export default useUnassignedResearchers;
