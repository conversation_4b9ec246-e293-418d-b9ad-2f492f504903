import { useQueries, useQueryClient } from '@tanstack/react-query';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_STUDIES from '@/services/admin/studies';
import ADMIN_USERS from '@/services/admin/users';
import {
  AdminStudiesTestsType,
  UnassignedStudyAdminsPaginatedResponseType,
} from '@/types/common';

export const useUnassignedStudyAdminsAndTests = (studyId?: string) => {
  const queryClient = useQueryClient();

  const results = useQueries({
    queries: [
      {
        queryKey: [QUERY_KEYS.UNASSIGNED_STUDY_ADMINS, studyId],
        queryFn: () => ADMIN_USERS.GET_UNASSIGNED_STUDY_ADMINS(studyId),
      },
      {
        queryKey: [QUERY_KEYS.ADMIN_STUDY_TESTS],
        queryFn: () => ADMIN_STUDIES.GET_AVAILABLE_STUDY_TESTS(),
        staleTime: 1000 * 60 * 10,
      },
    ],
  });

  // Destructure the results with proper typing
  const [
    { data: unassignedStudyAdmins, isLoading: areUnassignedStudyAdminsLoading },
    { data: studyTests, isLoading: areStudyTestsLoading },
  ] = results as [
    {
      data: UnassignedStudyAdminsPaginatedResponseType | null;
      isLoading: boolean;
    },
    { data: AdminStudiesTestsType | null; isLoading: boolean },
  ];

  const TEST_TYPES =
    studyTests?.map((item) => ({
      label: item.name,
      value: item.type,
    })) || [];

  // Function to revalidate unassigned study admins
  const revalidateUnassignedStudyAdmins = async () => {
    if (studyId) {
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.UNASSIGNED_STUDY_ADMINS, studyId],
      });
    }
  };

  return {
    areStudyAdminsAndTestsLoading:
      areUnassignedStudyAdminsLoading || areStudyTestsLoading,
    areStudyTestsLoading,
    unassignedStudyAdmins,
    TEST_TYPES,
    revalidateUnassignedStudyAdmins,
  };
};
