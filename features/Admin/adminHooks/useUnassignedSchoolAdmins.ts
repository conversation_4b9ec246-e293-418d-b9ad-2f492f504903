import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useState } from 'react';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_USERS from '@/services/admin/users';
import { UnassignedStudyAdminsPaginatedResponseType } from '@/types/common';

const useUnassignedSchoolAdmins = () => {
  const queryClient = useQueryClient();

  const [activePage, setActivePage] = useState(1);

  const {
    data,
    isFetching: areUnassignedSchoolAdminsFetching,
    isLoading: areUnassignedSchoolAdminsLoading,
  } = useQuery<UnassignedStudyAdminsPaginatedResponseType | null>({
    queryFn: () => ADMIN_USERS.GET_UNASSIGNED_SCHOOL_ADMINS(),
    queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
    placeholderData: keepPreviousData,
    staleTime: 1000 * 60 * 10,
  });

  // Function to revalidate unassigned study admins
  const revalidateUnassignedStudyAdmins = async () => {
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
    });
  };

  return {
    schoolAdmins: data?.results || [],
    onPageChange: (page: number) => setActivePage(page),
    revalidateUnassignedStudyAdmins,
    activePage,
    areUnassignedSchoolAdminsFetching,
    areUnassignedSchoolAdminsLoading,
    totalNumberOfPages: Math.ceil(
      (data?.count || 0) / GLOBAL_PAGINATION_FETCH_LIMIT
    ),
    totalNumberOfResearchers: data?.count || 0,
  };
};

export default useUnassignedSchoolAdmins;
