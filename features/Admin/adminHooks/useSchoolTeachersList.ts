import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useState } from 'react';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import SCHOOL_TEACHERS from '@/services/schools/teachers';
import { SchoolTeachersListType } from '@/types/common';

const useSchoolTeachersList = () => {
  const queryClient = useQueryClient();

  const [activePage, setActivePage] = useState(1);

  const { data, isFetching, isLoading } =
    useQuery<SchoolTeachersListType | null>({
      queryFn: () => SCHOOL_TEACHERS.GET_SCHOOL_TEACHERS_LIST(),
      queryKey: [QUERY_KEYS.ADMIN_SCHOOL_TEACHERS_LIST],
      placeholderData: keepPreviousData,
      staleTime: 1000 * 60 * 10,
    });

  // Function to revalidate unassigned study admins
  const revalidateSchoolTeachersList = async () => {
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ADMIN_SCHOOL_TEACHERS_LIST],
    });
  };

  return {
    schoolTeachersList: data?.results || [],
    onPageChange: (page: number) => setActivePage(page),
    revalidateSchoolTeachersList,
    activePage,
    isSchoolTeachersListFetching: isFetching,
    isSchoolTeachersListLoading: isLoading,
    totalNumberOfPages: Math.ceil(
      (data?.count || 0) / GLOBAL_PAGINATION_FETCH_LIMIT
    ),
    totalNumberOfResearchers: data?.count || 0,
  };
};

export default useSchoolTeachersList;
