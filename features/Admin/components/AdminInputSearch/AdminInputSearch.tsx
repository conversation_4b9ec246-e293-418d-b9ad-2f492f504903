import { Skeleton } from '@mantine/core';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Input from '@/components/Inputs/Input/Input';
import Text from '@/components/Text/Text';

type AdminInputSearchPropsType = {
  onSearch: (value: string) => void;
  numberOfSearchResults: number | null;
  isInformationLoading?: boolean;
  inputIcon?: React.ReactNode;
  placeholder: string;
  isDisabled?: boolean;
};

const AdminInputSearch = ({
  inputIcon,
  isDisabled = false,
  isInformationLoading = false,
  numberOfSearchResults,
  onSearch,
  placeholder,
}: AdminInputSearchPropsType): JSX.Element => {
  const [searchWord, setSearchWord] = useState('');
  const { t } = useTranslation();

  return (
    <Input
      value={searchWord}
      variant="filled"
      isDisabled={isDisabled}
      placeholder={t(placeholder)}
      onChange={(value) => {
        onSearch(value);
        setSearchWord(value);
      }}
      rightSectionWidth={80}
      rightSection={
        isInformationLoading ? (
          <Skeleton />
        ) : (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--spacing-sm)',
            }}
          >
            {inputIcon}

            <Text
              untranslatedText={`${numberOfSearchResults || '0'}`}
              type="body2"
            />
          </div>
        )
      }
    />
  );
};

export default AdminInputSearch;
