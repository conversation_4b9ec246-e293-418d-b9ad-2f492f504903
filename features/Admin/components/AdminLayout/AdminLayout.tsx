import { Indicator, ScrollArea } from '@mantine/core';
import React, { useState } from 'react';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import Icon from '@/components/Icon/Icon';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import ActionButton from '../../../../components/ActionButton/ActionButton';
import AdminInputSearch from '../AdminInputSearch/AdminInputSearch';
import styles from './AdminLayout.module.css';

type AdminLayoutProps = {
  searchInputTransKey: TranslationKeysType;
  filters?: React.ReactNode;
  actionsContent?: React.ReactNode;
  listContent?: React.ReactNode;
  numberOFSelectedFilters?: number;
  onClearFilters?: () => void;
  onSearch: (value: string) => void;
  paginationComponent?: React.ReactNode;
  numberOfSearchResults: number | null;
  isInformationLoading: boolean;
  isInformationFetching: boolean;
  totalNumberOfResults: number | null;
  inputIcon?: React.ReactNode;
};

const AdminLayout = ({
  actionsContent,
  filters,
  inputIcon,
  isInformationFetching,
  isInformationLoading,
  listContent,
  numberOfSearchResults,
  numberOFSelectedFilters = 0,
  onClearFilters,
  onSearch,
  paginationComponent,
  searchInputTransKey,
  totalNumberOfResults,
}: AdminLayoutProps) => {
  const [areFiltersVisible, setAreFiltersVisible] = useState(false);

  return (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <div className={styles.searchWrapper}>
          {filters && (
            <Indicator
              variant="filters"
              disabled={numberOFSelectedFilters === 0 || areFiltersVisible}
              inline
              label={numberOFSelectedFilters}
              size={20}
              offset={7}
              withBorder
            >
              <button
                type="button"
                className={`${styles.filterButton} ${areFiltersVisible && styles.activeButton}`}
                onClick={() => setAreFiltersVisible(!areFiltersVisible)}
              >
                <Icon
                  name="FiltersSvg"
                  color={areFiltersVisible ? 'white' : 'black'}
                />
              </button>
            </Indicator>
          )}

          <AdminInputSearch
            onSearch={onSearch}
            numberOfSearchResults={numberOfSearchResults}
            placeholder={searchInputTransKey}
            inputIcon={inputIcon}
            isDisabled={totalNumberOfResults === 0}
          />

          {numberOFSelectedFilters > 0 && (
            <ActionButton
              type="clearFilters"
              onClick={() => {
                setAreFiltersVisible(false);
                setTimeout(() => {
                  onClearFilters?.();
                }, 400);
              }}
            />
          )}
        </div>

        <div className={styles.actionsWrapper}>{actionsContent}</div>
      </div>

      <div className={styles.filtersAndContainerWrapper}>
        {/* SIDE BAR FILTERS */}
        <div
          className={`${styles.filters} ${areFiltersVisible && styles.open}`}
        >
          {filters}
        </div>

        <ScrollArea.Autosize
          type="never"
          scrollbarSize={4}
          className={styles.scrollAreaWrapper}
        >
          {isInformationFetching && isInformationLoading && <TableSkeleton />}

          {!isInformationFetching && totalNumberOfResults === 0 && (
            <div className={styles.noResultsWrapper}>
              <Text
                transKey="no-results-add-new-entry"
                type="h4"
                color="gray"
                fw={400}
              />
            </div>
          )}

          {!isInformationFetching &&
            totalNumberOfResults !== 0 &&
            numberOfSearchResults === 0 && (
              <div className={styles.noResultsWrapper}>
                <Text
                  transKey="no-search-results"
                  type="h4"
                  color="gray"
                  fw={400}
                />
              </div>
            )}

          {totalNumberOfResults !== 0 &&
            numberOfSearchResults !== 0 &&
            listContent}

          <div className={styles.paginationWrapper}>{paginationComponent}</div>
        </ScrollArea.Autosize>
      </div>
    </div>
  );
};

export default AdminLayout;
