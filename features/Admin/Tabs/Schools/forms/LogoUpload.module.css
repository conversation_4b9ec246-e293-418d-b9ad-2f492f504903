.photoWrapper {
  max-width: 300px;
  max-height: 300px;
}

.uploadHeaderWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xs);
  position: relative;
}

.profile-photo-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: none;

  &:hover {
    background-color: transparent;

    & p,
    & svg,
    & img {
      opacity: 0.7;
    }
  }
}

.imageWrapper {
  position: relative;
}

.deleteButton {
  position: absolute;
  right: calc(50% - 70px);
  top: 150px;
  z-index: 1;
  cursor: pointer;
  border-radius: 50%;
  background-color: var(--color-gray50);
  outline: 2px solid var(--color-white);

  &:hover {
    background-color: var(--color-gray200);
  }
}

.placeholderIcon {
  cursor: pointer !important;
  width: 120px;
  height: 120px;
}

.imageAvatar {
  border-radius: var(--radius-sm);
  overflow: hidden;
  object-fit: contain;
}
