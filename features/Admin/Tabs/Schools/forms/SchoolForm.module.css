.wrapper {
  max-width: 1080px;
  height: 100%;
  transition: all 0.4s ease;
}

.infoWrapper {
  max-height: 1000px;
}

.addressWrapper {
  max-height: 760px;
  max-width: 800px;
}
.row {
  display: flex;
  width: 100%;
  gap: var(--spacing-md);
}

.inputWrapper {
  width: 100%;
}

.schoolForm {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-mdl);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2xl);
}

.header-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.body {
  display: flex;
  gap: var(--spacing-xl);
  height: 100%;
}

.actions {
  display: flex;
  justify-self: flex-end;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.arrowAndTextWrapper {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.pointer {
  cursor: pointer;
}

.addressInput {
  user-select: none;
  width: 100%;
}

.addressRow {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.addressRow input {
  cursor: pointer;
}

.addressRow input:focus {
  border-color: var(--color-gray200) !important;
  outline: none;
}

.addressButtonWrapper {
  margin-top: var(--spacing-lg);
}

.errorAddressButtonWrapper {
  margin-top: var(--spacing-xs);
}
