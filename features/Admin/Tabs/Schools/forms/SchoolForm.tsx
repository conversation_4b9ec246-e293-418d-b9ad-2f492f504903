/* eslint-disable no-param-reassign */
import { zodResolver } from '@hookform/resolvers/zod';
import { TextInput } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import useUnassignedSchoolAdmins from '@/features/Admin/adminHooks/useUnassignedSchoolAdmins';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import { SCHOOL_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import { SchoolFormType } from '../types';
import LogoUpload from './LogoUpload';
import s from './SchoolForm.module.css';

type SchoolFormProps = {
  submitCallback: (data: any) => void;
  isLoading: boolean;
  updateIsUploadingImage: (value: boolean) => void;
  defaultValues: SchoolFormType;
  administratorDetails?: {
    id: string;
    email: string | null;
    firstName: string | null;
    lastName: string | null;
  } | null;
};

const SchoolForm = ({
  administratorDetails,
  defaultValues,
  isLoading,
  submitCallback,
  updateIsUploadingImage,
}: SchoolFormProps) => {
  const { t } = useTranslation();

  const defaultImageToUpload = defaultValues.profilePicture || null;

  const [imageToUpload, setImageToUpload] = useState<
    | {
        imageFile: File;
        fileName: string;
      }
    | string
    | null
  >(defaultImageToUpload);

  const [imageImportError, setImageImportError] = useState<
    'file-too-large' | 'file-invalid-type' | null
  >(null);

  const { areUnassignedSchoolAdminsLoading, schoolAdmins } =
    useUnassignedSchoolAdmins();

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<SchoolFormType>({
    resolver: zodResolver(SCHOOL_FORM_SCHEMA),
    defaultValues,
    mode: 'onSubmit',
  });

  const SCHOOL_TYPES = [
    {
      label: t('public'),
      value: 'public',
    },
    {
      label: t('private'),
      value: 'private',
    },
    {
      label: t('church'),
      value: 'church',
    },
  ];

  const SCHOOL_ADMINS_LIST = [
    ...(administratorDetails?.id
      ? [
          {
            label: `${administratorDetails.firstName || ''} ${administratorDetails.lastName || ''}`,
            value: administratorDetails.id,
          },
        ]
      : []),
    ...schoolAdmins.map((admin) => ({
      label: `${admin.firstName} ${admin.lastName}`,
      value: admin.id,
    })),
  ];

  const uploadLogoImage = useMutation({
    mutationFn: ADMIN_SCHOOLS.UPLOAD_LOGO_IMAGE,
    onError: (error) => {
      notifications.show({
        title: t(error.message),
        message: '',
        color: 'red',
      });

      updateIsUploadingImage(false);
    },
    onSuccess: (res) => {
      if (res) {
        submitCallback({
          name: watch('name') || '',
          code: watch('code') || '',
          email: watch('email') || '',
          phoneNumber: watch('phoneNumber') || '',
          url: watch('url') || '',
          administrator: watch('administrator') || '',
          type: watch('type') || '',
          address: {
            addressLine1: watch('address') || '',
            city: watch('city') || '',
            state: watch('state') || '',
            postcode: watch('postcode') || '',
            country: watch('country') || '',
          },
          profilePicture: res,
        });

        setImageImportError(null);
        updateIsUploadingImage(false);
      }
    },
  });

  const onSubmitSchoolForm = async (data: SchoolFormType) => {
    if (imageToUpload && typeof imageToUpload !== 'string') {
      updateIsUploadingImage(true);
      uploadLogoImage.mutate({ profileImage: imageToUpload });
    } else {
      if (imageToUpload === null) {
        data.profilePicture = null;
      }

      submitCallback({
        name: data.name || '',
        code: data.code || '',
        email: data.email || '',
        phoneNumber: data.phoneNumber || '',
        url: data.url || '',
        administrator: data.administrator || '',
        type: data.type || '',
        address: {
          addressLine1: data.address || '',
          city: data.city || '',
          state: data.state || '',
          postcode: data.postcode || '',
          country: data.country || '',
        },
        profilePicture: data.profilePicture || null,
      });
    }
  };

  return (
    <div className={s.body}>
      <LogoUpload
        imageImportError={imageImportError}
        imageToUpload={imageToUpload}
        updateImageImportError={setImageImportError}
        updateImageToUpload={setImageToUpload}
        defaultImageToUpload={defaultImageToUpload || null}
        isLoading={isLoading}
      />

      <Card size="xl">
        <Text transKey="info" type="h4" color="black" fw={300} mb={16} />

        <form
          id="school-form"
          className={s.schoolForm}
          onSubmit={handleSubmit(onSubmitSchoolForm)}
        >
          <div className={s.row}>
            <TextInput
              placeholder={t('name')}
              label={t('name')}
              error={t(errors.name?.message || '')}
              {...register('name')}
              style={{ width: '150%' }}
            />

            <TextInput
              label={t('code')}
              placeholder={t('code')}
              error={t(errors.code?.message || '')}
              {...register('code')}
              className={s.inputWrapper}
            />
          </div>

          <TextInput
            label={t('address')}
            placeholder={t('address')}
            error={t(errors.address?.message || '')}
            {...register('address')}
          />

          <div className={s.row}>
            <TextInput
              label={t('city')}
              placeholder={t('city')}
              {...register('city')}
              error={t(errors.city?.message || '')}
              className={s.inputWrapper}
            />

            <TextInput
              label={t('post-code')}
              placeholder={t('post-code')}
              {...register('postcode')}
              error={t(errors.postcode?.message || '')}
              className={s.inputWrapper}
            />
          </div>

          <div className={s.row}>
            <TextInput
              label={t('state')}
              placeholder={t('state-optional')}
              {...register('state')}
              className={s.inputWrapper}
            />

            <TextInput
              label={t('country')}
              placeholder={t('country')}
              error={t(errors.country?.message || '')}
              {...register('country')}
              className={s.inputWrapper}
            />
          </div>

          <div className={s.row}>
            <TextInput
              label={t('school-email')}
              placeholder={t('school-email')}
              {...register('email')}
              error={t(errors.email?.message || '')}
              className={s.inputWrapper}
            />

            <TextInput
              label={t('phone')}
              placeholder={t('phone')}
              {...register('phoneNumber')}
              className={s.inputWrapper}
            />
          </div>

          <TextInput
            label={t('website')}
            placeholder="http://my-school.edu"
            {...register('url')}
            error={t(errors.url?.message || '')}
            className={s.inputWrapper}
          />

          <div className={s.row}>
            <Controller
              name="administrator"
              control={control}
              render={({ field }) => (
                <SelectDropdown
                  value={field.value || ''}
                  label={t('schoolAdmin')}
                  isDisabled={areUnassignedSchoolAdminsLoading}
                  data={SCHOOL_ADMINS_LIST}
                  onChange={(v) => {
                    field.onChange(v);
                  }}
                  placeholder={t('schoolAdmin')}
                  clearable
                />
              )}
            />

            <Controller
              name="type"
              control={control}
              rules={{
                required: t('required'),
              }}
              render={({ field }) => (
                <SelectDropdown
                  value={field.value || ''}
                  label={t('type')}
                  data={SCHOOL_TYPES}
                  onChange={(v) => {
                    field.onChange(v);
                  }}
                  placeholder={t('type')}
                  error={t(errors.type?.message || '')}
                />
              )}
            />
          </div>
        </form>
      </Card>
    </div>
  );
};

export default SchoolForm;
