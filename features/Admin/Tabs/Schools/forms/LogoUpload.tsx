import { ActionIcon } from '@mantine/core';
import { Dropzone, IMAGE_MIME_TYPE } from '@mantine/dropzone';
import Image from 'next/image';
import React, { useRef } from 'react';
import { RiDeleteBin5Fill } from 'react-icons/ri';

import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import Text from '@/components/Text/Text';

import s from './LogoUpload.module.css';

type LogoUploadProps = {
  imageToUpload: { fileName: string; imageFile: File } | string | null;
  updateImageToUpload: (
    arg: { imageFile: File; fileName: string } | string | null
  ) => void;
  imageImportError: 'file-too-large' | 'file-invalid-type' | null;
  updateImageImportError: (
    error: 'file-too-large' | 'file-invalid-type' | null
  ) => void;
  defaultImageToUpload?: string | null;
  isLoading: boolean;
};

const LogoUpload = ({
  defaultImageToUpload,
  imageImportError,
  imageToUpload,
  isLoading,
  updateImageImportError,
  updateImageToUpload,
}: LogoUploadProps) => {
  const dropZoneRef = useRef<() => void>(null);

  const displayedAvatarImage = () => {
    if (imageToUpload) {
      if (typeof imageToUpload === 'string') {
        return imageToUpload;
      }
      return URL.createObjectURL(imageToUpload.imageFile as File);
    }

    return defaultImageToUpload || null;
  };

  return (
    <Card size="xl" className={s.photoWrapper}>
      <div className={s.uploadHeaderWrapper}>
        <Text transKey="logo" type="h4" color="black" fw={300} />

        {imageToUpload && (
          <ActionIcon
            variant="filled"
            color="transparent"
            size="lg"
            aria-label="Delete"
            className={s.deleteButton}
            onClick={(e) => {
              e.stopPropagation();
              updateImageToUpload(null);
            }}
          >
            <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
          </ActionIcon>
        )}
      </div>

      <Dropzone
        className={s['profile-photo-wrapper']}
        onDrop={(file) => {
          const fileName = file[0].name;

          if (imageImportError) updateImageImportError(null);

          updateImageToUpload({ fileName, imageFile: file[0] });
        }}
        accept={IMAGE_MIME_TYPE}
        openRef={dropZoneRef}
        activateOnClick
        useFsAccessApi
        maxFiles={1}
        maxSize={5 * 1024 * 1024}
        onReject={(file) => {
          updateImageImportError(
            file[0].errors[0].code === 'file-too-large'
              ? 'file-too-large'
              : 'file-invalid-type'
          );
        }}
        disabled={isLoading}
        styles={{
          inner: {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          },
          root: {
            paddingTop: '24px',
            paddingBottom: '24px',
          },
        }}
      >
        {imageToUpload ? (
          <Image
            src={displayedAvatarImage() || ''}
            alt="profile photo"
            width={120}
            height={120}
            className={s.imageAvatar}
          />
        ) : (
          <Icon
            name="ImagePlaceholderSvg"
            color="turquoise"
            className={s.placeholderIcon}
          />
        )}

        {imageImportError && (
          <Text
            transKey={imageImportError}
            type="label"
            color="danger"
            mt={12}
          />
        )}

        <Text transKey="upload-image" type="button" color="blue" mt={42} />
      </Dropzone>
    </Card>
  );
};

export default LogoUpload;
