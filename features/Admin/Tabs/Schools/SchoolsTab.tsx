import { Pagination } from '@mantine/core';
import React, { useMemo } from 'react';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import { getSelectedFiltersLength } from '@/common/helpers';

import AdminLayout from '../../components/AdminLayout/AdminLayout';
import AdminSchoolsFilters from './components/AdminSchoolsFilters/AdminSchoolsFilters';
import SchoolsListTable from './components/SchoolsListTable/SchoolsListTable';
import useAdminSchoolsQuery from './hooks/useAdminSchoolsQuery';
import useSchoolsQueryParams from './hooks/useSchoolsQueryParams';

const SchoolsTab = () => {
  const {
    clearFilters,
    filters,
    onSearchChange,
    queryParams,
    updateFilters,
    updateQueryParams,
  } = useSchoolsQueryParams();

  const {
    areSchoolsFetching,
    areSchoolsLoading,
    schools,
    totalNumberOfReturnedSchools,
    totalNumberOfSchools,
  } = useAdminSchoolsQuery(queryParams);

  const PAGINATION_COMPONENT = areSchoolsLoading ? null : (
    <Pagination
      total={Math.ceil(
        totalNumberOfReturnedSchools / GLOBAL_PAGINATION_FETCH_LIMIT
      )}
      value={queryParams.activePage}
      hideWithOnePage
      withControls={false}
      onChange={(value) =>
        updateQueryParams({
          type: 'activePage',
          payload: value,
        })
      }
      disabled={areSchoolsFetching || areSchoolsLoading}
    />
  );

  const SCHOOLS_TABLE = useMemo(() => {
    return (
      <SchoolsListTable
        areSchoolsLoading={areSchoolsLoading}
        areSchoolsFetching={areSchoolsFetching}
        schools={schools}
      />
    );
  }, [areSchoolsLoading, areSchoolsFetching, schools]);

  return (
    <AdminLayout
      searchInputTransKey="search-schools"
      onSearch={onSearchChange}
      numberOfSearchResults={totalNumberOfReturnedSchools}
      paginationComponent={PAGINATION_COMPONENT}
      isInformationLoading={areSchoolsLoading}
      isInformationFetching={areSchoolsFetching}
      totalNumberOfResults={totalNumberOfSchools}
      listContent={SCHOOLS_TABLE}
      numberOFSelectedFilters={getSelectedFiltersLength(filters)}
      onClearFilters={clearFilters}
      filters={
        <AdminSchoolsFilters
          isDataFetching={areSchoolsFetching || areSchoolsLoading}
          updateFilters={updateFilters}
          country={filters.country}
        />
      }
    />
  );
};

export default SchoolsTab;
