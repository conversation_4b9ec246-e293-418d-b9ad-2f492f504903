/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Menu, Table, TableData } from '@mantine/core';
import Image from 'next/image';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiEditFill } from 'react-icons/ri';

import { isValidImageUrl } from '@/common/helpers';
import Icon from '@/components/Icon/Icon';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import { AdminSchoolsListType } from '@/types/common';

import EditSchoolPanel from '../EditSchoolPanel/EditSchoolPanel';

type StudiesListTableProps = {
  areSchoolsFetching: boolean;
  areSchoolsLoading: boolean;
  schools: AdminSchoolsListType['results'];
};

const SchoolsListTable = ({
  areSchoolsFetching,
  areSchoolsLoading,
  schools,
}: StudiesListTableProps): JSX.Element => {
  const { t } = useTranslation();
  const [schoolToEdit, setSchoolToEdit] = useState<string | null>(null);

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${t(month)} ${day}  ‘${year} ${hoursAndMinutes}`;
  };

  const TABLE_HEAD = useMemo(() => {
    return [
      '',
      t('name-capital'),
      t('administrator-capital'),
      t('registered-capital'),
      t('teachers-capital'),
      '',
    ];
  }, [t]);

  const TABLE_BODY = useMemo(() => {
    return schools.map((school) => [
      <div
        key={school.id}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '35px',
        }}
      >
        {isValidImageUrl(school?.profilePicture || '') ? (
          <Image
            src={school?.profilePicture || ''}
            alt={school.name}
            width={35}
            height={35}
            style={{
              objectFit: 'contain',
            }}
          />
        ) : (
          ''
        )}
      </div>,
      school.name,
      school?.administrator?.firstName
        ? `${school?.administrator?.firstName || ''} ${school?.administrator?.lastName || ''}`
        : '-',
      getListDateTime(school.createdAt),
      school.teachersCount,
      <Menu key={school.id} shadow="md">
        <Menu.Target>
          <Box>
            <Icon
              name="MenuDotsSvg"
              color="turquoise"
              w={18}
              h={18}
              hasCursor
            />
          </Box>
        </Menu.Target>

        <Menu.Dropdown className="menuDropdown">
          <Menu.Item
            onClick={() => setSchoolToEdit(school.id)}
            leftSection={<RiEditFill size={16} />}
          >
            {t('edit')}
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>,
    ]);
  }, [schools]);

  const tableData: TableData = {
    head: TABLE_HEAD,
    body: TABLE_BODY,
  };

  const TABLE_VIEW_COMPONENT =
    areSchoolsFetching && areSchoolsLoading ? (
      <TableSkeleton numberOfColumns={TABLE_HEAD.length} />
    ) : (
      <Table
        data={tableData}
        stickyHeader
        stickyHeaderOffset={-1}
        highlightOnHover
        styles={{
          thead: {
            border: 'none',
          },
          table: {
            opacity: areSchoolsFetching ? 0.5 : 1,
            pointerEvents: areSchoolsFetching ? 'none' : 'auto',
          },
        }}
      />
    );

  return (
    <>
      {TABLE_VIEW_COMPONENT}

      <PrimaryModal
        isOpen={schoolToEdit !== null}
        content={
          <EditSchoolPanel
            onCloseModal={() => setSchoolToEdit(null)}
            schoolId={schoolToEdit}
          />
        }
      />
    </>
  );
};

export default SchoolsListTable;
