import { Box, Flex, Input, Paper, Table, TableData } from '@mantine/core';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PRODUCTS } from '@/common/consts';
import Button from '@/components/Button/Button';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';

import s from './LicensesAndCertificationsEdit.module.css';

type certificatesAndLicenses = {
  type: string;
  licenses?: number;
  certificates?: number;
  status?: string;
}[];

type LicensesAndCertificationsEditProps = {
  // summary: certificatesAndLicenses;
  initialCertificationsAndLicenses: certificatesAndLicenses;
  isFetching: boolean;
  onUpdateClick: (items: certificatesAndLicenses) => void;
  isRevokeLicenseOrCertificationInProgress: boolean;
  isUpdateBundleInProgress: boolean;
};

const defaultValuesForLicensesAndCertificates = {
  status: 'na',
  licenses: 0,
  certificates: 0,
};

const LicensesAndCertificationsEdit = ({
  initialCertificationsAndLicenses,
  isFetching,
  isRevokeLicenseOrCertificationInProgress,
  isUpdateBundleInProgress,
  onUpdateClick,
}: LicensesAndCertificationsEditProps): JSX.Element => {
  const { t } = useTranslation();
  const { allTests } = TestsContext();

  const [certificatesAndLicenses, setCertificatesAndLicenses] =
    useState<certificatesAndLicenses>(initialCertificationsAndLicenses);

  const certificateStateOptions = [
    {
      value: 'na',
      label: t('N/A'),
    },
    {
      value: 'open',
      label: t('opened'),
    },
    {
      value: 'granted',
      label: t('granted'),
    },
  ];

  const getCertificateOptions = (certificateType: string | null) => {
    switch (certificateType) {
      case 'open':
        return certificateStateOptions.map((option) => {
          return {
            ...option,
            disabled: option.value === 'na',
          };
        });

      case 'granted':
        return certificateStateOptions.map((option) => {
          return {
            ...option,
            disabled: option.value !== 'granted',
          };
        });

      default:
        return certificateStateOptions;
    }
  };

  const onUpdateLicensesAndCertificates = (
    data: NonNullable<certificatesAndLicenses>[0]
  ) => {
    setCertificatesAndLicenses((prev) => {
      const exists = prev?.some((item) => item.type === data.type);

      if (exists) {
        return prev?.map((item) =>
          item.type === data.type ? { ...item, ...data } : item
        );
      }

      return [...(prev || []), data];
    });
  };

  const isSomethingLoading =
    isFetching ||
    isRevokeLicenseOrCertificationInProgress ||
    isUpdateBundleInProgress;

  const isEverythingNA = certificatesAndLicenses.every(
    (item) => item.status === 'na'
  );

  const tableData: TableData = {
    head: [
      t('test-capital'),
      t('status-capital'),
      t('certificates-capital'),
      t('licenses-capital'),
    ],
    body: allTests.map((test) => {
      if (test.research) {
        return [];
      }

      const initialCertificatedAndLicenseDetails =
        initialCertificationsAndLicenses.find(
          (item) => item.type === test.type
        );

      const certificateDetails = certificatesAndLicenses?.find(
        (item) => item.type === test.type
      );

      const isDisabled =
        !certificateDetails || certificateDetails?.status === 'na';

      return [
        <Box
          key={test.type}
          style={{
            width: '100%',
            opacity: isDisabled ? 0.5 : 1,
          }}
        >
          <Text untranslatedText={PRODUCTS[test.type].short} isBold />
        </Box>,
        <Box w={120} key={test.type}>
          <SelectDropdown
            value={
              certificateDetails?.status || certificateStateOptions[0].value
            }
            required
            data={getCertificateOptions(
              defaultValuesForLicensesAndCertificates?.status || null
            )}
            onChange={(v) => {
              onUpdateLicensesAndCertificates({
                type: test.type,
                status: v,
                ...(v === defaultValuesForLicensesAndCertificates?.status && {
                  ...initialCertificationsAndLicenses,
                }),
              });
            }}
            placeholder={t('N/A')}
            unstyled
          />
        </Box>,
        <input
          key={test.type}
          type="number"
          className={s.customLicensesInput}
          min={initialCertificatedAndLicenseDetails?.certificates || 0}
          value={certificateDetails?.certificates?.toString() || ''}
          placeholder={
            defaultValuesForLicensesAndCertificates?.certificates?.toString() ??
            '0'
          }
          onChange={(e) => {
            const { value } = e.target;

            if (
              certificatesAndLicenses?.find(
                (item) =>
                  item.type === test.type && item.status && item.status !== 'na'
              )
            ) {
              onUpdateLicensesAndCertificates({
                type: test.type,
                certificates: value === '' ? 0 : Number(value),
              });
            }
          }}
          onBlur={() => {
            if (
              (initialCertificatedAndLicenseDetails?.certificates || 0) >
              (certificateDetails?.certificates || 0)
            ) {
              onUpdateLicensesAndCertificates({
                type: test.type,
                certificates:
                  initialCertificatedAndLicenseDetails?.certificates,
              });
            }
          }}
          style={{
            opacity: isDisabled ? 0.5 : 1,
            pointerEvents: isDisabled ? 'none' : 'auto',
            maxWidth: 80,
          }}
        />,
        <input
          key={test.type}
          type="number"
          className={s.customLicensesInput}
          min={initialCertificatedAndLicenseDetails?.licenses || 0}
          value={certificateDetails?.licenses?.toString() || ''}
          placeholder={
            defaultValuesForLicensesAndCertificates?.licenses?.toString() ?? '0'
          }
          onChange={(e) => {
            const { value } = e.target;

            if (
              certificatesAndLicenses?.find(
                (item) =>
                  item.type === test.type && item.status && item.status !== 'na'
              )
            ) {
              onUpdateLicensesAndCertificates({
                type: test.type,
                licenses: value === '' ? 0 : Number(value),
              });
            }
          }}
          onBlur={() => {
            if (
              (initialCertificatedAndLicenseDetails?.licenses || 0) >
              (certificateDetails?.licenses || 0)
            ) {
              onUpdateLicensesAndCertificates({
                type: test.type,
                licenses: initialCertificatedAndLicenseDetails?.licenses,
              });
            }
          }}
          style={{
            opacity: isDisabled ? 0.5 : 1,
            pointerEvents: isDisabled ? 'none' : 'auto',
            maxWidth: 80,
          }}
        />,
      ];
    }),
  };

  return (
    <Paper
      p="xl"
      shadow="sm"
      radius="lg"
      className={s.licensesAndCertificationsValuesWrapper}
    >
      <Flex direction="column" gap={24} h="100%">
        <Box h="100%">
          <Box className={s.header}>
            <Text transKey="summary" type="h4" fw={300} mb={28} />
          </Box>

          <Table
            data={tableData}
            styles={{
              thead: {
                border: 'none',
              },
              table: {
                opacity: isSomethingLoading ? 0.5 : 1,
                pointerEvents: isSomethingLoading ? 'none' : 'auto',
              },
            }}
          />
        </Box>

        <Box>
          <Button
            transKey="update-capital"
            hasFullWidth
            onClick={() =>
              onUpdateClick(
                certificatesAndLicenses.filter(
                  (item) => item.status !== 'na'
                ) as any
              )
            }
            isDisabled={
              isFetching ||
              isRevokeLicenseOrCertificationInProgress ||
              isEverythingNA
            }
            isLoading={isUpdateBundleInProgress}
          />
        </Box>
      </Flex>
    </Paper>
  );
};

export default LicensesAndCertificationsEdit;
