.container {
  display: flex;
  width: 100%;
  gap: var(--spacing-lg);
}

.formElements {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-smd);
}

.container {
  width: 100%;
}

.customLicensesInput {
  border: none;
  color: var(--color-gray700);
  font-size: 14px;

  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    opacity: 1;
  }

  &:focus {
    outline: none;
  }
}

.row {
  display: flex;
  gap: var(--spacing-md);
}
