/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Box,
  Flex,
  Pagination,
  ScrollArea,
  Table,
  TableData,
} from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GiBackwardTime } from 'react-icons/gi';

import { PRODUCTS } from '@/common/consts';
import { getListDateTime } from '@/common/helpers';
import Card from '@/components/Card/Card';
import Icon from '@/components/Icon/Icon';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import { ProductsType } from '@/types/common';

import s from './LicensesAndCertificationsHistory.module.css';

type LicensesAndCertificationsHistoryProps = {
  schoolId: string;
  isUpdateBundleInProgress: boolean;
  onRevokeLicense: (bundleId: string, clearLoading: () => void) => void;
  onRevokeCertificate: (bundleId: string, clearLoading: () => void) => void;
};

const lIMIT = 8;

const getLicenseOrCertificateTag = (
  type: 'certificates' | 'licenses',
  testType: ProductsType
) => {
  const isLicense = type === 'licenses';

  return (
    <span
      style={{
        textAlign: 'center',
        overflow: 'hidden',
        display: 'inline-block',
        padding: '6px 8px',
        borderRadius: '5px',
        fontSize: '14px',
        fontWeight: 600,
        backgroundColor: isLicense ? 'var(--color-blue)' : 'var(--color-green)',
        color: isLicense ? 'var(--color-white)' : 'var(--color-white)',
      }}
    >
      <Icon
        name={PRODUCTS[testType][isLicense ? 'licenseIcon' : 'certificateIcon']}
        color="white"
        w={21}
        h={26}
      />
    </span>
  );
};

const LicensesAndCertificationsHistory = ({
  isUpdateBundleInProgress,
  onRevokeCertificate,
  onRevokeLicense,
  schoolId,
}: LicensesAndCertificationsHistoryProps) => {
  const { t } = useTranslation();
  const [revokingIndex, setRevokingIndex] = useState<number | null>(null);
  const [activePage, setActivePage] = useState(1);

  const { data, isFetching, isLoading } = useQuery<any | null>({
    queryFn: () =>
      ADMIN_SCHOOLS.GET_SCHOOL_LICENSES_AND_CERTIFICATES_HISTORY(
        schoolId,
        activePage,
        lIMIT
      ),
    queryKey: ['admin-schools-bundles-history', schoolId, activePage],
    enabled: Boolean(schoolId),
  });

  const LICENSES_HISTORY = data?.results || [];

  const totalNumberOfPages = Math.ceil((data?.totalCount || 1) / 8);

  const historyTable: TableData = {
    head: [
      t('type-capital'),
      t('kind-capital'),
      t('created-capital'),
      t('given-capital'),
      t('available-capital'),
      t('revoke-capital'),
    ],
    body: LICENSES_HISTORY.map((item: any, index: number) => {
      return [
        <Text
          key={`type-${item.id}`}
          untranslatedText={PRODUCTS[item.type as ProductsType].testName}
          noWrap
          fw={600}
        />,
        getLicenseOrCertificateTag(
          item.kind as 'certificates' | 'licenses',
          item.type as ProductsType
        ),
        getListDateTime(item.createdAt, t),
        item.quantity,
        item.remaining,
        <Box
          key={`revoke-${item.id}`}
          className={
            item.remaining > 0
              ? `${s.iconButton} ${revokingIndex === index && s.loading} ${
                  revokingIndex === index && s.disabled
                }`
              : `${s.unableToRevokeButton}`
          }
          onClick={() => {
            if (item.remaining > 0) {
              setRevokingIndex(index);

              if (item.kind === 'licenses') {
                onRevokeLicense(item.id, () => setRevokingIndex(null));
                return;
              }

              onRevokeCertificate(item.id, () => setRevokingIndex(null));
            }
          }}
        >
          <GiBackwardTime
            fontSize={20}
            color={
              item.remaining > 0 ? 'var(--color-blue)' : 'var(--color-white)'
            }
          />
        </Box>,
      ];
    }),
  };

  if (isLoading && isFetching) {
    return (
      <Card size="xl" className={s.licensesHistoryCard}>
        <br />
        <br />
        <TableSkeleton numberOfColumns={5} numberOfRows={8} />
      </Card>
    );
  }

  if (!isFetching && LICENSES_HISTORY.length === 0) {
    return (
      <Card size="xl" className={s.licensesHistoryCard}>
        <Flex justify="center" align="center">
          <Text
            untranslatedText="No licenses or certificates history found "
            type="h4"
            fw={300}
          />
        </Flex>
      </Card>
    );
  }

  return (
    <Card size="xl" className={s.licensesHistoryCard}>
      <Box className={s.header}>
        <Text transKey="history" type="h4" fw={300} mb={28} />
      </Box>

      <ScrollArea
        h={340}
        scrollbarSize={4}
        scrollbars="y"
        offsetScrollbars
        classNames={{ thumb: 'thumb' }}
      >
        <Table
          data={historyTable}
          stickyHeader
          stickyHeaderOffset={-1}
          styles={{
            thead: {
              border: 'none',
            },
            table: {
              opacity: isFetching || isUpdateBundleInProgress ? 0.5 : 1,
              pointerEvents:
                isFetching || isUpdateBundleInProgress ? 'none' : 'auto',
            },
          }}
        />
      </ScrollArea>

      <Pagination
        total={totalNumberOfPages}
        value={activePage}
        hideWithOnePage
        withControls={false}
        style={{
          display: 'flex',
          justifyContent: 'center',
          marginTop: 32,
        }}
        onChange={(value) => setActivePage(value)}
      />
    </Card>
  );
};

export default LicensesAndCertificationsHistory;
