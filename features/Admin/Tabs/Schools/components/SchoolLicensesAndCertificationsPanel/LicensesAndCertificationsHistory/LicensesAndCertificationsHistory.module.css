.licensesHistoryCard {
  min-height: 600px;
  max-height: 600px;
  min-width: 600px;
}

.iconButton {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  background-color: var(--color-gray100);
  transition:
    background-color 0.2s ease,
    transform 0.15s ease;
}

.iconButton:hover {
  background-color: var(--mantine-color-blue-light);
  transform: scale(1.05);
}

.iconButton:active {
  transform: scale(0.95);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.iconButton.loading {
  cursor: wait;
  pointer-events: none;
  animation: rotate 1s linear infinite;
}

.iconButton.disabled {
  cursor: not-allowed;
  opacity: 0.6;
  pointer-events: none;
  transform: none;
}

.unableToRevokeButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  background-color: var(--color-gray300);
  cursor: not-allowed;
}

.tag {
  text-align: center;
  overflow: hidden;
  display: inline-block;
  padding: 6px 12px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-white);
}

/* License tag */
.tag.license {
  background-color: var(--color-blue);
}

/* Certificate tag */
.tag.certificate {
  background-color: var(--color-green);
}
