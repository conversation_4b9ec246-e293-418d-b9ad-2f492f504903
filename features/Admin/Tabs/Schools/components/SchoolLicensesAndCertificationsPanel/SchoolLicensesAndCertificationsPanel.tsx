import { Flex } from '@mantine/core';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { JSX } from 'react';

import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';
import ADMIN_SCHOOLS from '@/services/admin/schools';

import LicensesAndCertificationsEdit from './LicensesAndCertificationsEdit/LicensesAndCertificationsEdit';
import LicensesAndCertificationsHistory from './LicensesAndCertificationsHistory/LicensesAndCertificationsHistory';
import s from './SchoolLicensesAndCertificationsPanel.module.css';

type SchoolLicensesAndCertificationsPanelProps = {
  onCloseModal: () => void;
  schoolId: string | null;
};

const SchoolLicensesAndCertificationsPanel = ({
  onCloseModal,
  schoolId,
}: SchoolLicensesAndCertificationsPanelProps): JSX.Element => {
  const queryClient = useQueryClient();
  const { allTests } = TestsContext();

  const { data, isFetching: isSummaryFetching } = useQuery<any | null>({
    queryFn: () =>
      ADMIN_SCHOOLS.GET_SCHOOL_LICENSES_AND_CERTIFICATES_SUMMARY(
        schoolId || ''
      ),
    queryKey: ['admin-schools-bundles-summary', schoolId],
    enabled: Boolean(schoolId),
  });

  const revokeLicenseMutation = useMutation({
    mutationFn: ADMIN_SCHOOLS.REVOKE_REMAINING_LICENSES_OR_CERTIFICATES,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['admin-schools-bundles-history', schoolId],
      });

      queryClient.invalidateQueries({
        queryKey: ['admin-schools-bundles-summary', schoolId],
      });
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  const revokeCertificateMutation = useMutation({
    mutationFn: ADMIN_SCHOOLS.REVOKE_REMAINING_LICENSES_OR_CERTIFICATES,
    onSuccess: () => {
      // setRevokingIndex(null);

      queryClient.invalidateQueries({
        queryKey: ['admin-schools-bundles-history', schoolId],
      });

      queryClient.invalidateQueries({
        queryKey: ['admin-schools-bundles-summary', schoolId],
      });
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  const updateLicensesAndCertificatesMutation = useMutation({
    mutationFn: ADMIN_SCHOOLS.UPDATE_SCHOOL_LICENSES_AND_CERTIFICATES,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['admin-schools-bundles-history', schoolId],
      });
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  const initialCertificationsAndLicenses = allTests
    .filter((test) => !test.research)
    .map((test) => {
      return {
        type: test.type,
        status: 'na',
        licenses: 0,
        certificates: 0,
      };
    });

  const prefilledCertificationsAndLicensesSummary =
    initialCertificationsAndLicenses.map((initialItem) => {
      const summaryItem = data?.items.find(
        (item: any) => item.type === initialItem.type
      );

      if (summaryItem) {
        return {
          ...initialItem,
          status: summaryItem.status,
          licenses: summaryItem.licenses,
          certificates: summaryItem.certificates,
        };
      }

      return initialItem;
    });

  return (
    <Card size="xl" bg="gray50" className={`${s.wrapper}`}>
      <div className={s.header}>
        <Text transKey="licenses-and-certifications" type="h3" />

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <Flex justify="space-between" gap={24} w="100%">
        {schoolId && (
          <LicensesAndCertificationsHistory
            schoolId={schoolId}
            isUpdateBundleInProgress={
              updateLicensesAndCertificatesMutation.isPending ||
              revokeLicenseMutation.isPending ||
              revokeCertificateMutation.isPending
            }
            onRevokeCertificate={async (bundleId, clearLoading) => {
              await revokeCertificateMutation.mutateAsync({
                bundleId,
                schoolId: schoolId || '',
              });

              clearLoading();
            }}
            onRevokeLicense={async (bundleId, clearLoading) => {
              await revokeLicenseMutation.mutateAsync({
                bundleId,
                schoolId: schoolId || '',
              });

              clearLoading();
            }}
          />
        )}

        {schoolId && (
          <LicensesAndCertificationsEdit
            key={JSON.stringify(prefilledCertificationsAndLicensesSummary)}
            initialCertificationsAndLicenses={
              prefilledCertificationsAndLicensesSummary
            }
            isRevokeLicenseOrCertificationInProgress={
              revokeLicenseMutation.isPending ||
              revokeCertificateMutation.isPending
            }
            isUpdateBundleInProgress={
              updateLicensesAndCertificatesMutation.isPending
            }
            onUpdateClick={(items) => {
              updateLicensesAndCertificatesMutation.mutate({
                items,
                schoolId,
              } as any);
            }}
            isFetching={isSummaryFetching}
          />
        )}
      </Flex>
    </Card>
  );
};

export default SchoolLicensesAndCertificationsPanel;
