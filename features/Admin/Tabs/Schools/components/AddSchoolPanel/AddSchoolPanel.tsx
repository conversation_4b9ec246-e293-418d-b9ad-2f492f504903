/* eslint-disable no-param-reassign */
import { Box } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import ADMIN_SCHOOLS from '@/services/admin/schools';

import SchoolForm from '../../forms/SchoolForm';
import s from './AddSchoolPanel.module.css';

type AddSchoolPanelProps = {
  onCloseModal: () => void;
};

const AddSchoolPanel = ({ onCloseModal }: AddSchoolPanelProps): JSX.Element => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const addSchoolMutation = useMutation({
    mutationFn: ADMIN_SCHOOLS.ADD_SCHOOL,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_SCHOOLS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
      });

      onCloseModal();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  return (
    <Card
      size="xl"
      bg="gray50"
      isLoading={addSchoolMutation.isPending}
      className={s.wrapper}
      hasDynamicHeight
    >
      <div className={s.header}>
        <Text transKey="new-school" type="h3" />

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <SchoolForm
        defaultValues={{
          name: '',
          code: '',
          email: '',
          phoneNumber: '',
          url: '',
          administrator: null,
          type: '',
          address: '',
          city: '',
          state: '',
          postcode: '',
          country: '',
        }}
        submitCallback={addSchoolMutation.mutate}
        isLoading={addSchoolMutation.isPending}
        updateIsUploadingImage={setIsUploadingImage}
      />

      <Box className={s.actions}>
        <Button
          variant="primaryOutlined"
          transKey="cancel-capital"
          onClick={onCloseModal}
        />

        <Button
          transKey="create-capital"
          onClick={() => submitFormById('school-form')}
          isLoading={addSchoolMutation.isPending || isUploadingImage}
        />
      </Box>
    </Card>
  );
};

export default AddSchoolPanel;
