/* eslint-disable no-param-reassign */
import { Box, LoadingOverlay } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import { AdminSchoolsListType } from '@/types/common';

import SchoolForm from '../../forms/SchoolForm';
import s from '../AddSchoolPanel/AddSchoolPanel.module.css';

type EditSchoolPanelProps = {
  onCloseModal: () => void;
  schoolId: string | null;
};

const EditSchoolPanel = ({
  onCloseModal,
  schoolId,
}: EditSchoolPanelProps): JSX.Element => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const { data: schoolData, isLoading } = useQuery<
    AdminSchoolsListType['results'][0] | null
  >({
    enabled: !!schoolId,
    queryFn: () => ADMIN_SCHOOLS.GET_ADMIN_SCHOOL(schoolId || ''),
    queryKey: [QUERY_KEYS.ADMIN_SCHOOLS, schoolId],
    staleTime: 1000 * 60 * 60,
  });

  const editSchoolMutation = useMutation({
    mutationFn: (formValues: any) =>
      ADMIN_SCHOOLS.EDIT_SCHOOL(formValues, schoolId || ''),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_SCHOOLS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_SCHOOLS, schoolId],
      });

      onCloseModal();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const updateIsUploadingImage = (value: boolean) => {
    setIsUploadingImage(value);
  };

  return isLoading ? (
    <LoadingOverlay visible />
  ) : (
    <Card
      size="xl"
      bg="gray50"
      isLoading={isLoading}
      className={s.wrapper}
      hasDynamicHeight
    >
      <div className={s.header}>
        <Text transKey="edit-school" type="h3" />

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <SchoolForm
        submitCallback={editSchoolMutation.mutate}
        defaultValues={{
          name: schoolData?.name || '',
          code: schoolData?.code || '',
          email: schoolData?.email || '',
          phoneNumber: schoolData?.phoneNumber || '',
          url: schoolData?.url || '',
          administrator: schoolData?.administrator?.id || '',
          type: schoolData?.type || '',
          address: schoolData?.address?.addressLine1 || '',
          city: schoolData?.address?.city || '',
          state: schoolData?.address?.state || '',
          postcode: schoolData?.address?.postcode || '',
          country: schoolData?.address?.country || '',
          profilePicture: schoolData?.profilePicture || '',
        }}
        administratorDetails={schoolData?.administrator}
        updateIsUploadingImage={updateIsUploadingImage}
        isLoading={isUploadingImage || editSchoolMutation.isPending}
      />

      <Box className={s.actions}>
        <Button
          variant="primaryOutlined"
          transKey="cancel-capital"
          onClick={onCloseModal}
        />

        <Button
          transKey="save-capital"
          onClick={() => submitFormById('school-form')}
          isLoading={editSchoolMutation.isPending || isUploadingImage}
        />
      </Box>
    </Card>
  );
};

export default EditSchoolPanel;
