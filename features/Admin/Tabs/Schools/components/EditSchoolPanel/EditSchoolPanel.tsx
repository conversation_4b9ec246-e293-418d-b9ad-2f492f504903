import { LoadingOverlay } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { JSX, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { SCHOOLS_ERRORS } from '@/common/errors';
import { isValidImageUrl } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import { AdminSchoolsListType } from '@/types/common';

import SchoolForm from '../../forms/SchoolForm';

type EditSchoolPanelProps = {
  onCloseModal: () => void;
  schoolId: string | null;
};

const EditSchoolPanel = ({
  onCloseModal,
  schoolId,
}: EditSchoolPanelProps): JSX.Element => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [isInvalidVat, setIsInvalidVat] = useState(false);
  const [isCodeNotUnique, setIsCodeNotUnique] = useState(false);

  const { data: schoolData, isLoading } = useQuery<
    AdminSchoolsListType['results'][0] | null
  >({
    enabled: !!schoolId,
    queryFn: () => ADMIN_SCHOOLS.GET_ADMIN_SCHOOL(schoolId || ''),
    queryKey: [QUERY_KEYS.ADMIN_SCHOOLS, schoolId],
    staleTime: 1000 * 60 * 60,
  });

  const editSchoolMutation = useMutation({
    mutationFn: (formValues: any) =>
      ADMIN_SCHOOLS.EDIT_SCHOOL(formValues, schoolId || ''),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_SCHOOLS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_SCHOOLS, schoolId],
      });

      onCloseModal();
    },
    onError: (error) => {
      if (error.message === SCHOOLS_ERRORS.INVALID_TAX_ID) {
        setIsInvalidVat(true);
      } else if (error.message === SCHOOLS_ERRORS.SCHOOL_CODE_ALREADY_EXISTS) {
        setIsCodeNotUnique(true);
      } else {
        notifications.show({
          title: t('error'),
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  return isLoading ? (
    <LoadingOverlay visible />
  ) : (
    <SchoolForm
      submitCallback={editSchoolMutation.mutate}
      defaultValues={{
        name: schoolData?.name || '',
        code: schoolData?.code || '',
        email: schoolData?.email || '',
        phoneNumber: schoolData?.phoneNumber || '',
        url: schoolData?.url || '',
        administrator: schoolData?.administrator?.id || '',
        type: schoolData?.type || '',
        city: schoolData?.address?.city || '',
        postcode: schoolData?.address?.postcode || '',
        country: schoolData?.address?.country.toLowerCase() || '',
        profilePicture:
          schoolData?.profilePicture &&
          isValidImageUrl(schoolData?.profilePicture)
            ? schoolData?.profilePicture
            : '',
        vatNumber: schoolData?.vatNumber || '',
        addressLine1: schoolData?.address?.addressLine1 || '',
      }}
      administratorDetails={schoolData?.administrator}
      isLoading={editSchoolMutation.isPending}
      type="edit"
      onCloseModal={onCloseModal}
      isInvalidVat={isInvalidVat}
      updateInvalidVat={setIsInvalidVat}
      isCodeNotUnique={isCodeNotUnique}
      clearCodeNotUniqueError={() => setIsCodeNotUnique(false)}
    />
  );
};

export default EditSchoolPanel;
