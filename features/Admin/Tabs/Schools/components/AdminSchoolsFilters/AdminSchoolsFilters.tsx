import { useQuery } from '@tanstack/react-query';
import countries from 'i18n-iso-countries';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { COUNTRIES_ISO_CODES } from '@/common/consts';
import QUERY_KEYS from '@/common/queryKeys';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import ADMIN_SCHOOLS from '@/services/admin/schools';

import styles from './AdminSchoolsFilters.module.css';

type AdminUsersFiltersProps = {
  country: string;
  isDataFetching: boolean;
  updateFilters: ({ key, value }: any) => void;
};

const AdminSchoolsFilters = ({
  country,
  isDataFetching,
  updateFilters,
}: AdminUsersFiltersProps) => {
  const { i18n, t } = useTranslation();

  const { data: countriesList, isLoading: isCountryListLoading } = useQuery<
    string[] | null
  >({
    queryFn: ADMIN_SCHOOLS.GET_ADMIN_SCHOOLS_LIST_COUNTRIES,
    queryKey: [`${QUERY_KEYS.COUNTRIES_LIST}-all`],
  });

  const countriesData = useMemo(() => {
    return (
      countriesList
        ?.map((item) => ({
          label: countries.getName(item, i18n.language) || '',
          value: item,
        }))
        .filter((item) => COUNTRIES_ISO_CODES.includes(item.value)) || []
    );
  }, [countriesList, i18n.language]);

  return (
    <div className={styles.labelAndFieldWrapper}>
      <Text transKey="country-capital" type="body2" isBold fw={600} />

      <SelectDropdown
        value={country || ''}
        clearable
        data={countriesData}
        onChange={(value) => {
          updateFilters({
            key: 'country',
            value,
          });
        }}
        isDisabled={isDataFetching || isCountryListLoading}
        placeholder={t('select-country')}
      />
    </div>
  );
};

export default AdminSchoolsFilters;
