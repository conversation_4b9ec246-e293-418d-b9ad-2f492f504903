import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import { AdminSchoolsListType } from '@/types/common';

// import { AdminStudiesPaginatedResponseType } from '@/types/common';
import { QueryParamsStateType } from './useSchoolsQueryParams';

const useAdminSchoolsQuery = (queryParams?: QueryParamsStateType) => {
  const queryClient = useQueryClient();

  const queryKey = useMemo(() => ['admin-schools', queryParams], [queryParams]);
  // AdminStudiesPaginatedResponseType
  const { data, isFetching, isLoading } = useQuery<AdminSchoolsListType | null>(
    {
      queryKey,
      queryFn: () =>
        queryParams ? ADMIN_SCHOOLS.GET_ADMIN_SCHOOLS(queryParams) : null,
      placeholderData: keepPreviousData,
      staleTime: 1000 * 60 * 60,
    }
  );

  const invalidateSchools = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ADMIN_SCHOOLS],
    });
  }, [queryClient]);

  return {
    schools: data?.results || [],
    areSchoolsLoading: isLoading,
    areSchoolsFetching: isFetching,
    invalidateSchools,
    totalNumberOfSchools: data?.totalCount || 0,
    totalNumberOfReturnedSchools: data?.count || 0,
  };
};

export default useAdminSchoolsQuery;
