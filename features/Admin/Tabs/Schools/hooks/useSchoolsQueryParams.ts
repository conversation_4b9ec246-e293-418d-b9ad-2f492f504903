import { useDebouncedCallback } from '@mantine/hooks';
import { useCallback, useReducer } from 'react';

// Define all query parameters in one place
const QUERY_PARAMS_CONFIG = {
  searchWord: {
    actionType: 'SET_SEARCH_WORD' as const,
    initialState: '',
    resetPage: true,
    isFilter: false,
  },
  activePage: {
    actionType: 'SET_ACTIVE_PAGE' as const,
    initialState: 1,
    resetPage: false,
    isFilter: false,
  },
};

type QueryParamKeys = keyof typeof QUERY_PARAMS_CONFIG;

type SearchWordKey = 'searchWord';

type NonSearchParamKeys = Exclude<QueryParamKeys, SearchWordKey>;

export type QueryParamsStateType = {
  [K in QueryParamKeys]: (typeof QUERY_PARAMS_CONFIG)[K]['initialState'];
};

type ActionMap = {
  [K in QueryParamKeys]: {
    type: (typeof QUERY_PARAMS_CONFIG)[K]['actionType'];
    payload: QueryParamsStateType[K];
  };
};

type QueryParamsAction = ActionMap[QueryParamKeys];

type UpdateQueryParamsAction = {
  [K in NonSearchParamKeys]: {
    type: K;
    payload: QueryParamsStateType[K];
  };
}[NonSearchParamKeys];

const initialState: QueryParamsStateType = Object.keys(
  QUERY_PARAMS_CONFIG
).reduce(
  (acc, key) => ({
    ...acc,
    [key]: QUERY_PARAMS_CONFIG[key as QueryParamKeys].initialState,
  }),
  {} as QueryParamsStateType
);

const reducer = (
  state: QueryParamsStateType,
  action: QueryParamsAction
): QueryParamsStateType => {
  const matchedEntry = Object.entries(QUERY_PARAMS_CONFIG).find(
    ([, config]) => config.actionType === action.type
  );

  if (!matchedEntry) {
    throw new Error(`Unhandled action: ${action.type}`);
  }

  const [key] = matchedEntry as [
    QueryParamKeys,
    (typeof QUERY_PARAMS_CONFIG)[QueryParamKeys],
  ];

  return {
    ...state,
    [key]: action.payload,
  };
};

const useSchoolsQueryParams = () => {
  const [queryParams, dispatchQueryParams] = useReducer(reducer, initialState);

  // Debounced search handler
  const handleSearch = useDebouncedCallback((searchTerm: string) => {
    dispatchQueryParams({
      type: QUERY_PARAMS_CONFIG.searchWord.actionType,
      payload: searchTerm,
    });
  }, 500);

  // Update search input value and trigger debounced search
  const onSearchChange = useCallback(
    (value: string) => {
      // reset to page 1
      dispatchQueryParams({
        type: QUERY_PARAMS_CONFIG.activePage.actionType,
        payload: QUERY_PARAMS_CONFIG.activePage.initialState,
      });
      handleSearch(value);
    },
    [handleSearch]
  );

  // Update other query parameters (excluding searchWord)
  const updateQueryParams = useCallback(
    (action: UpdateQueryParamsAction): void => {
      const config = QUERY_PARAMS_CONFIG[action.type];

      if (config.resetPage && action.type !== 'activePage') {
        dispatchQueryParams({
          type: QUERY_PARAMS_CONFIG.activePage.actionType,
          payload: QUERY_PARAMS_CONFIG.activePage.initialState,
        });
      }

      dispatchQueryParams({
        type: config.actionType,
        payload: action.payload,
      } as QueryParamsAction);
    },
    []
  );

  const resetSearchParamsExceptSearchWord = useCallback(() => {
    Object.entries(QUERY_PARAMS_CONFIG).forEach(([key, config]) => {
      if (key !== 'searchWord') {
        dispatchQueryParams({
          type: config.actionType,
          payload: config.initialState,
        } as QueryParamsAction);
      }
    });
  }, []);

  return {
    queryParams,
    updateQueryParams,
    onSearchChange,
    resetSearchParamsExceptSearchWord,
  };
};

export default useSchoolsQueryParams;
