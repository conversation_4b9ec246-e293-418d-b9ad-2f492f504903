import { useDebouncedCallback } from '@mantine/hooks';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useReducer } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_USERS from '@/services/admin/users';
import { UsersAdmin } from '@/types/common';

type QueryParamsStateType = {
  query: string;
  page: number;
  school: string;
  study: string;
  from: Date | null;
  to: Date | null;
};

type FilteredKeys = Exclude<keyof QueryParamsStateType, 'query' | 'page'>;

export type UpdateFiltersType = {
  key: FilteredKeys;
  value: QueryParamsStateType[FilteredKeys];
};

const AVAILABLE_QUERY_SETTERS = {
  SET_SEARCH_WORD: 'SET_SEARCH_WORD',
  SET_ACTIVE_PAGE: 'SET_ACTIVE_PAGE',
  SET_SCHOOL: 'SET_SCHOOL',
  SET_STUDY: 'SET_STUDY',
  SET_FROM: 'SET_FROM',
  SET_TO: 'SET_TO',
} as const;

type QueryParamsActionsStateType =
  | {
      type: typeof AVAILABLE_QUERY_SETTERS.SET_SEARCH_WORD;
      payload: QueryParamsStateType['query'];
    }
  | {
      type: typeof AVAILABLE_QUERY_SETTERS.SET_ACTIVE_PAGE;
      payload: QueryParamsStateType['page'];
    }
  | {
      type: typeof AVAILABLE_QUERY_SETTERS.SET_SCHOOL;
      payload: QueryParamsStateType['school'];
    }
  | {
      type: typeof AVAILABLE_QUERY_SETTERS.SET_STUDY;
      payload: QueryParamsStateType['study'];
    }
  | {
      type: typeof AVAILABLE_QUERY_SETTERS.SET_FROM;
      payload: QueryParamsStateType['from'];
    }
  | {
      type: typeof AVAILABLE_QUERY_SETTERS.SET_TO;
      payload: QueryParamsStateType['to'];
    };

const initialState: QueryParamsStateType = {
  query: '',
  page: 1,
  school: '',
  study: '',
  from: null,
  to: null,
};

const reducer = (
  state: QueryParamsStateType,
  action: QueryParamsActionsStateType
): QueryParamsStateType => {
  switch (action.type) {
    case AVAILABLE_QUERY_SETTERS.SET_SEARCH_WORD:
      return { ...state, query: action.payload };
    case AVAILABLE_QUERY_SETTERS.SET_ACTIVE_PAGE:
      return { ...state, page: action.payload };
    case AVAILABLE_QUERY_SETTERS.SET_SCHOOL:
      return { ...state, school: action.payload };
    case AVAILABLE_QUERY_SETTERS.SET_STUDY:
      return { ...state, study: action.payload };
    case AVAILABLE_QUERY_SETTERS.SET_FROM:
      return { ...state, from: action.payload };
    case AVAILABLE_QUERY_SETTERS.SET_TO:
      return { ...state, to: action.payload };
    default:
      return state;
  }
};

const useAdminUsers = () => {
  const [queryParams, dispatchQueryParams] = useReducer(reducer, initialState);

  const { data, isFetching, isLoading } = useQuery<UsersAdmin | null>({
    queryFn: () => ADMIN_USERS.GET_USERS(queryParams),
    queryKey: [QUERY_KEYS.ADMIN_USERS, queryParams],
    placeholderData: keepPreviousData,
  });

  const updateActivePage = (page: number) => {
    dispatchQueryParams({
      type: AVAILABLE_QUERY_SETTERS.SET_ACTIVE_PAGE,
      payload: page,
    });
  };

  const updateSearchWord = useDebouncedCallback((newSearchWord: string) => {
    updateActivePage(1);

    dispatchQueryParams({
      type: AVAILABLE_QUERY_SETTERS.SET_SEARCH_WORD,
      payload: newSearchWord,
    });
  }, 500);

  const updateFilters = ({ key, value }: UpdateFiltersType) => {
    dispatchQueryParams({
      type: `SET_${key.toUpperCase()}`,
      payload: value,
    } as QueryParamsActionsStateType);
  };

  const clearFilters = () => {
    dispatchQueryParams({
      type: AVAILABLE_QUERY_SETTERS.SET_SCHOOL,
      payload: '',
    });
    dispatchQueryParams({
      type: AVAILABLE_QUERY_SETTERS.SET_STUDY,
      payload: '',
    });
    dispatchQueryParams({
      type: AVAILABLE_QUERY_SETTERS.SET_FROM,
      payload: null,
    });
    dispatchQueryParams({
      type: AVAILABLE_QUERY_SETTERS.SET_TO,
      payload: null,
    });
  };

  return {
    usersList: data?.results || null,
    count: data?.count || 0,
    totalNumberOfUsers: data?.totalCount || 0,
    searchWord: queryParams.query,
    activePage: queryParams.page,
    updateSearchWord,
    updateActivePage,
    areUsersFetching: isFetching,
    areUsersLoading: isLoading,
    updateFilters,
    clearFilters,
    filters: {
      school: queryParams.school,
      study: queryParams.study,
      from: queryParams.from,
      to: queryParams.to,
    },
  };
};

export default useAdminUsers;
