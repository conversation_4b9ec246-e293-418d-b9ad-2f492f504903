import { DateInput } from '@mantine/dates';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { mantineDateParser } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import ADMIN_SCHOOLS from '@/services/admin/schools';
import ADMIN_STUDIES from '@/services/admin/studies';
import { AdminStudiesListType, SchoolsListType } from '@/types/common';

import { UpdateFiltersType } from '../../hooks/useAdminUsers';
import styles from './AdminUsersFilters.module.css';

type AdminUsersFiltersProps = {
  school: string;
  study: string;
  from: Date | null;
  to: Date | null;
  isDataFetching: boolean;
  updateFilters: ({ key, value }: UpdateFiltersType) => void;
};

const AdminUsersFilters = ({
  from,
  isDataFetching,
  school,
  study,
  to,
  updateFilters,
}: AdminUsersFiltersProps) => {
  const { t } = useTranslation();

  const { data: schoolsList, isLoading: isSchoolsListLoading } =
    useQuery<SchoolsListType | null>({
      queryFn: ADMIN_SCHOOLS.GET_ADMIN_SCHOOLS_LIST,
      queryKey: [`${QUERY_KEYS.ADMIN_SCHOOLS}-all`],
    });

  const { data: studyList, isLoading: isStudyListLoading } =
    useQuery<AdminStudiesListType | null>({
      queryFn: ADMIN_STUDIES.GET_STUDIES_LIST_ALL,
      queryKey: [QUERY_KEYS.ADMIN_STUDIES],
    });

  return (
    <>
      <div className={styles.labelAndFieldWrapper}>
        <Text transKey="school-capital" type="body2" isBold fw={600} />

        <SelectDropdown
          value={school || ''}
          clearable
          data={
            schoolsList?.map((item) => ({
              label: item.name,
              value: item.id,
            })) || []
          }
          onChange={(value) => {
            updateFilters({
              key: 'school',
              value,
            });
          }}
          isDisabled={isDataFetching || isSchoolsListLoading || Boolean(study)}
          placeholder={t('select-school')}
        />
      </div>

      <div className={styles.labelAndFieldWrapper}>
        <Text transKey="study-capital" type="body2" isBold fw={600} />

        <SelectDropdown
          value={study || ''}
          clearable
          data={
            studyList?.map((item) => ({
              label: item.name,
              value: item.id,
            })) || []
          }
          onChange={(value) => {
            updateFilters({
              key: 'study',
              value,
            });
          }}
          isDisabled={isDataFetching || isStudyListLoading || Boolean(school)}
          placeholder={t('select-study')}
        />
      </div>

      <div className={styles.labelAndFieldWrapper}>
        <Text
          transKey="registration-date-capital"
          type="body2"
          isBold
          fw={600}
          className={styles.filterLabel}
        />

        <DateInput
          placeholder={t('from')}
          required
          highlightToday
          disabled={isDataFetching}
          allowDeselect
          valueFormat="DD/MM/YYYY"
          value={from}
          clearable
          dateParser={mantineDateParser}
          onChange={(v) =>
            updateFilters({
              key: 'from',
              value: v,
            })
          }
          maxDate={to ? new Date(to) : undefined}
        />

        <DateInput
          placeholder={t('to')}
          required
          clearable
          highlightToday
          allowDeselect
          disabled={isDataFetching}
          valueFormat="DD/MM/YYYY"
          value={to}
          dateParser={mantineDateParser}
          onChange={(v) =>
            updateFilters({
              key: 'to',
              value: v,
            })
          }
          minDate={from ? new Date(from) : undefined}
        />
      </div>
    </>
  );
};

export default AdminUsersFilters;
