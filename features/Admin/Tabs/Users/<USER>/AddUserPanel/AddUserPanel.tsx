import { Box } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserFormType } from '@/features/Admin/types/formsTypes';
import ADMIN_USERS from '@/services/admin/users';

import UserForm from '../../forms/UserForm/UserForm';
import s from './AddUserPanel.module.css';

type AddUserPanelProps = {
  onClose: () => void;
};

const AddUserPanel = ({ onClose }: AddUserPanelProps): JSX.Element => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const [certificatesAndLicenses, setCertificatesAndLicenses] = useState<
    UserFormType['certificatesAndLicenses']
  >([]);

  const addUserMutation = useMutation({
    mutationFn: ADMIN_USERS.ADD_USER,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_USERS],
      });

      onClose();
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const onSubmitUserForm = (data: UserFormType) => {
    const formattedLicenses =
      certificatesAndLicenses
        ?.map((item) => {
          return {
            type: item.type,
            progress: item.progress,
            licenses: item?.licenses || 0,
          };
        })
        ?.filter((item) => item.progress !== 'na') || [];

    addUserMutation.mutate({
      ...data,
      certificatesAndLicenses: formattedLicenses,
    });
  };

  const onUpdateLicensesAndCertificates = (
    data: NonNullable<UserFormType['certificatesAndLicenses']>[0]
  ) => {
    setCertificatesAndLicenses((prev) => {
      const exists = prev?.some((item) => item.type === data.type);

      if (exists) {
        return prev?.map((item) =>
          item.type === data.type ? { ...item, ...data } : item
        );
      }

      return [...(prev || []), data];
    });
  };

  const clearLicensesAndCertificates = () => {
    setCertificatesAndLicenses([]);
  };

  return (
    <Card size="xl" bg="gray50" isLoading={false} className={s.modalWrapper}>
      <div className={s.header}>
        <Text transKey="add-user" type="h3" />

        <CloseButton onClick={onClose} variant="outlined" />
      </div>

      <UserForm
        defaultValues={{
          firstName: '',
          lastName: '',
          email: '',
          role: '',
        }}
        onSubmitUserForm={(values) => onSubmitUserForm(values)}
        onUpdateLicensesAndCertificates={onUpdateLicensesAndCertificates}
        certificatesAndLicenses={certificatesAndLicenses}
        clearLicensesAndCertificates={clearLicensesAndCertificates}
      />

      <Box className={s.actions}>
        <Button
          variant="primaryOutlined"
          transKey="cancel-capital"
          onClick={onClose}
          isDisabled={addUserMutation.isPending}
        />

        <Button
          transKey="create-capital"
          onClick={() => submitFormById('users-form')}
          isLoading={addUserMutation.isPending}
          isDisabled={addUserMutation.isPending}
        />
      </Box>
    </Card>
  );
};

export default AddUserPanel;
