import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Table, TableData, TextInput } from '@mantine/core';
import { JSX } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { PRODUCTS, SUPPORTED_LANGUAGES } from '@/common/consts';
import Card from '@/components/Card/Card';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { TestsContext } from '@/context/TestsProvider';
import { USER_ROLES } from '@/context/UserProvider';
import { UserFormType } from '@/features/Admin/types/formsTypes';
import { USER_FORM_SCHEMA } from '@/zod/zodFormValidationSchemas';

import s from './UserForm.module.css';

type UserFormProps = {
  defaultValues: UserFormType;
  certificatesAndLicenses: UserFormType['certificatesAndLicenses'];
  onSubmitUserForm: (data: UserFormType) => void;
  onUpdateLicensesAndCertificates: (data: {
    type: string;
    progress?: string;
    licenses?: number;
  }) => void;
  clearLicensesAndCertificates: () => void;
  isEditingUser?: boolean;
  emailAlreadyExists: boolean;
  clearEmailAlreadyExists: () => void;
};

const UserForm = ({
  certificatesAndLicenses,
  clearEmailAlreadyExists,
  clearLicensesAndCertificates,
  defaultValues,
  emailAlreadyExists,
  isEditingUser,
  onSubmitUserForm,
  onUpdateLicensesAndCertificates,
}: UserFormProps): JSX.Element => {
  const { t } = useTranslation();
  const { allTests } = TestsContext();

  const resolver: any = zodResolver(
    isEditingUser ? USER_FORM_SCHEMA.omit({ language: true }) : USER_FORM_SCHEMA
  );

  const {
    control,
    formState: { errors },
    handleSubmit,
    register,
    watch,
  } = useForm<UserFormType>({
    resolver,
    defaultValues,
    mode: 'onSubmit',
  });

  const isAdminRole = defaultValues.role === USER_ROLES.ADMIN;

  const userRolesOptions = Object.values(USER_ROLES)
    .filter(
      (role) =>
        role === USER_ROLES.TEACHER ||
        role === USER_ROLES.RESEARCHER ||
        role === USER_ROLES.STUDY_ADMIN ||
        role === USER_ROLES.SCHOOL_ADMIN
    )
    .map((role) => ({
      value: role,
      label: t(role),
    }));

  const certificateStateOptions = [
    {
      value: 'na',
      label: t('N/A'),
    },
    {
      value: 'open',
      label: t('opened'),
    },
    {
      value: 'granted',
      label: t('granted'),
    },
  ];

  const getCertificateOptions = (certificateType: string | null) => {
    switch (certificateType) {
      case 'open':
        return certificateStateOptions.map((option) => {
          return {
            ...option,
            disabled: option.value === 'na',
          };
        });

      case 'granted':
        return certificateStateOptions.map((option) => {
          return {
            ...option,
            disabled: option.value !== 'granted',
          };
        });

      default:
        return certificateStateOptions;
    }
  };

  const selectedUserRole = watch('role');

  const tableData: TableData = {
    head: [t('test-capital'), t('certificate-capital'), t('licenses-capital')],
    body: allTests.map((test) => {
      const certificateDetails = certificatesAndLicenses?.find(
        (item) => item.type === test.type
      );

      const initialCertificationsAndLicenses =
        defaultValues?.certificatesAndLicenses?.find(
          (item) => item.type === test.type
        );

      const isDisabled =
        !certificateDetails || certificateDetails?.progress === 'na';

      return [
        <div
          key={test.type}
          style={{
            width: '100%',
            opacity: isDisabled ? 0.5 : 1,
          }}
        >
          <Text untranslatedText={PRODUCTS[test.type].short} isBold />
        </div>,
        <div
          key={test.type}
          style={{
            width: '100%',
            minWidth: 150,
          }}
        >
          <SelectDropdown
            value={
              certificateDetails?.progress || certificateStateOptions[0].value
            }
            required
            data={getCertificateOptions(
              initialCertificationsAndLicenses?.progress || null
            )}
            onChange={(v) => {
              onUpdateLicensesAndCertificates({
                type: test.type,
                progress: v,
                ...(v === 'na' && {
                  licenses: 0,
                }),

                ...(v === initialCertificationsAndLicenses?.progress &&
                  initialCertificationsAndLicenses?.type === test.type && {
                    licenses: initialCertificationsAndLicenses?.licenses,
                  }),
              });
            }}
            placeholder={t('N/A')}
            unstyled
          />
        </div>,
        <input
          key={test.type}
          type="number"
          className={s.customLicensesInput}
          min={initialCertificationsAndLicenses?.licenses || 0}
          value={certificateDetails?.licenses?.toString() || ''}
          placeholder={
            initialCertificationsAndLicenses?.licenses?.toString() ?? '0'
          }
          onChange={(e) => {
            const { value } = e.target;

            if (
              certificatesAndLicenses?.find(
                (item) =>
                  item.type === test.type &&
                  item.progress &&
                  item.progress !== 'na'
              )
            ) {
              onUpdateLicensesAndCertificates({
                type: test.type,
                licenses: value === '' ? 0 : Number(value),
              });
            }
          }}
          onBlur={() => {
            if (
              initialCertificationsAndLicenses?.licenses &&
              (certificateDetails?.licenses || 0) <
                initialCertificationsAndLicenses?.licenses
            ) {
              onUpdateLicensesAndCertificates({
                type: test.type,
                licenses: initialCertificationsAndLicenses?.licenses,
              });
            }
          }}
          style={{
            opacity: isDisabled ? 0.5 : 1,
            pointerEvents: isDisabled ? 'none' : 'auto',
            maxWidth: 80,
          }}
        />,
      ];
    }),
  };

  return (
    <form
      id="users-form"
      onSubmit={handleSubmit(onSubmitUserForm)}
      className={s.container}
    >
      <div className={s.wrapper}>
        <Card size="xl">
          <Box className={s.header}>
            <Text transKey="info" type="h4" fw={300} mb={28} />
          </Box>

          <Box className={s.formElements}>
            <div className={s.row}>
              <TextInput
                label={t('first-name')}
                placeholder={t('first-name-placeholder')}
                required
                {...register('firstName')}
                error={t(errors.firstName?.message || '')}
                w="100%"
              />

              <TextInput
                label={t('last-name')}
                placeholder={t('last-name-placeholder')}
                {...register('lastName')}
                required
                error={t(errors.lastName?.message || '')}
                w="100%"
              />
            </div>

            <TextInput
              label={t('email')}
              placeholder={t('email-placeholder')}
              {...register('email')}
              required
              error={
                emailAlreadyExists
                  ? t('already-exists')
                  : t(errors.email?.message || '')
              }
              onChange={() => {
                if (emailAlreadyExists) {
                  clearEmailAlreadyExists();
                }
              }}
            />

            <div className={s.row}>
              {isAdminRole ? (
                <TextInput
                  label={t('role')}
                  placeholder={t('administrator')}
                  disabled
                />
              ) : (
                <Controller
                  name="role"
                  control={control}
                  rules={{
                    required: t('field-required'),
                  }}
                  render={({ field }) => (
                    <SelectDropdown
                      value={field.value || ''}
                      label={t('role')}
                      required
                      data={userRolesOptions}
                      onChange={(v) => {
                        if (v !== USER_ROLES.TEACHER) {
                          clearLicensesAndCertificates();
                        }

                        field.onChange(v);
                      }}
                      placeholder={t('user-role')}
                      error={t(errors.role?.message || '')}
                    />
                  )}
                />
              )}

              <Controller
                name="language"
                control={control}
                disabled={isEditingUser}
                render={({ field }) => (
                  <SelectDropdown
                    value={field.value || ''}
                    label={t('language')}
                    isDisabled={isEditingUser}
                    data={Array.from(
                      Object.entries(SUPPORTED_LANGUAGES),
                      ([key, value]) => ({
                        label: t(value.transKey),
                        value: value.shortCode,
                      })
                    )}
                    onChange={(v) => {
                      if (v !== USER_ROLES.TEACHER) {
                        clearLicensesAndCertificates();
                      }

                      field.onChange(v);
                    }}
                    placeholder={t('language')}
                    error={t(errors.language?.message || '')}
                  />
                )}
              />
            </div>
          </Box>
        </Card>
      </div>

      <div className={s.wrapper}>
        <Card size="xl">
          <Box className={s.header}>
            <Text transKey="licenses-certificates" type="h4" fw={300} mb={28} />
          </Box>

          {selectedUserRole === USER_ROLES.STUDY_ADMIN ||
          selectedUserRole === USER_ROLES.RESEARCHER ||
          selectedUserRole === USER_ROLES.SCHOOL_ADMIN ? (
            <Text
              transKey={
                selectedUserRole === USER_ROLES.SCHOOL_ADMIN
                  ? 'no-licenses-school-admin-description'
                  : 'no-licenses-description'
              }
              fw={300}
              mt={128}
              align="center"
              color="gray600"
            />
          ) : (
            <Table data={tableData} />
          )}
        </Card>
      </div>
    </form>
  );
};

export default UserForm;
