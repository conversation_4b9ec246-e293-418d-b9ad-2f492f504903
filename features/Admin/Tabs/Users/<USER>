import { Box, Menu, Pagination, Table, TableData } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RiDeleteBin5Fill, RiEditFill } from 'react-icons/ri';
import { TbArchive, TbArchiveOff } from 'react-icons/tb';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import { getRole, getSelectedFiltersLength } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Icon from '@/components/Icon/Icon';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';
import ADMIN_USERS from '@/services/admin/users';
import { TranslationKeysType } from '@/types/common';

import AdminLayout from '../../components/AdminLayout/AdminLayout';
import AdminUsersFilters from './components/AdminUsersFilters/AdminUsersFilters';
import EditUserPanel from './components/EditUserPanel/EditUserPanel';
import useAdminUsers from './hooks/useAdminUsers';
import s from './UsersTab.module.css';

const UsersTab = () => {
  const { t } = useTranslation();
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [userToEdit, setUserToEdit] = useState<string | null>(null);

  const queryClient = useQueryClient();

  const deleteUser = useMutation({
    mutationFn: () => ADMIN_USERS.DELETE_USER(userToDelete as string),
    onSuccess: () => {
      setUserToDelete(null);

      notifications.show({
        message: t('user-deleted-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_USERS],
      });
    },
    onError: (error) => {
      setUserToDelete(null);

      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const archiveUser = useMutation({
    mutationFn: ADMIN_USERS.ARCHIVE_USER,
    onSuccess: () => {
      notifications.show({
        message: t('user-archived-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_USERS],
      });
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const unArchiveUser = useMutation({
    mutationFn: ADMIN_USERS.UNARCHIVE_USER,
    onSuccess: () => {
      notifications.show({
        message: t('user-un-archived-successfully'),
        color: 'green',
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_USERS],
      });
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${t(month)} ${day}  ‘${year} ${hoursAndMinutes}`;
  };

  const {
    activePage,
    areUsersFetching,
    areUsersLoading,
    clearFilters,
    count,
    filters,
    totalNumberOfUsers,
    updateActivePage,
    updateFilters,
    updateSearchWord,
    usersList,
  } = useAdminUsers();

  const tableData: TableData = {
    head: [
      t('name-capital'),
      t('surname-capital'),
      t('email-capital'),
      t('role-capital'),
      t('membership-capital'),
      t('registered-capital'),
      t('status-capital'),
      '',
    ],
    body: usersList?.map((user) => [
      <Text
        key={user.id}
        untranslatedText={user.profile?.firstName || '-'}
        color={user.isArchived ? 'gray500' : 'gray900'}
      />,
      <Text
        key={user.id}
        untranslatedText={user.profile?.lastName || '-'}
        color={user.isArchived ? 'gray500' : 'gray900'}
      />,
      <Text
        key={user.id}
        untranslatedText={user.profile?.email || '-'}
        color={user.isArchived ? 'gray500' : 'gray900'}
      />,

      <Text
        key={user.id}
        transKey={getRole(user.roles) as TranslationKeysType}
        color={user.isArchived ? 'gray500' : 'gray900'}
      />,
      <Text
        key={user.id}
        untranslatedText={
          getRole(user.roles) === 'schoolTeacher' ||
          getRole(user.roles) === 'schoolAdmin'
            ? user.school?.name || '-'
            : user.study?.name || '-'
        }
        color={user.isArchived ? 'gray500' : 'gray900'}
      />,
      user?.registeredAt ? (
        <Text
          key={user.id}
          untranslatedText={getListDateTime(user?.registeredAt)}
          color={user.isArchived ? 'gray500' : 'gray900'}
        />
      ) : (
        '-'
      ),
      <div
        key={user.id}
        className={`${s.labelWrapper} ${s[user.isArchived ? 'archived' : 'active']}`}
      >
        <Text
          type="label"
          color="white"
          transKey={
            (user?.isArchived ? 'archived' : 'active') as TranslationKeysType
          }
          isBold
        />
      </div>,
      <Menu key={user.id} shadow="md">
        <Menu.Target>
          <Box>
            <Icon
              name="MenuDotsSvg"
              color="turquoise"
              w={18}
              h={18}
              hasCursor
            />
          </Box>
        </Menu.Target>

        <Menu.Dropdown className="menuDropdown">
          <Menu.Item
            onClick={() => {
              setUserToEdit(user.id);
            }}
            leftSection={<RiEditFill size={19} />}
          >
            {t('edit')}
          </Menu.Item>

          <Menu.Item
            onClick={() => {
              if (user?.isArchived) {
                unArchiveUser.mutate(user.id);
              } else {
                archiveUser.mutate(user.id);
              }
            }}
            leftSection={
              user?.isArchived ? (
                <TbArchiveOff size={19} />
              ) : (
                <TbArchive size={19} />
              )
            }
          >
            {t(user?.isArchived ? 'un-archive' : 'archive')}
          </Menu.Item>

          <Menu.Item
            onClick={() => setUserToDelete(user.id)}
            disabled={!user?.isArchived}
            leftSection={
              <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
            }
          >
            {t('delete')}
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>,
    ]),
  };

  const TABLE_VIEW_COMPONENT = (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
        table: {
          opacity: areUsersFetching && activePage !== 1 ? 0.5 : 1,
          pointerEvents: areUsersFetching ? 'none' : 'auto',
        },
      }}
    />
  );

  return (
    <>
      <AdminLayout
        searchInputTransKey="search-users"
        onSearch={(value) => {
          updateSearchWord(value);
        }}
        numberOfSearchResults={count}
        isInformationLoading={areUsersLoading}
        isInformationFetching={areUsersFetching}
        totalNumberOfResults={totalNumberOfUsers}
        numberOFSelectedFilters={getSelectedFiltersLength(filters)}
        filters={
          <AdminUsersFilters
            from={filters.from}
            to={filters.to}
            isDataFetching={areUsersFetching}
            updateFilters={updateFilters}
            school={filters.school}
            study={filters.study}
          />
        }
        listContent={TABLE_VIEW_COMPONENT}
        onClearFilters={clearFilters}
        paginationComponent={
          <Pagination
            total={Math.ceil(count / GLOBAL_PAGINATION_FETCH_LIMIT)}
            value={activePage}
            hideWithOnePage
            withControls={false}
            onChange={(value) => {
              updateActivePage(value);
            }}
            disabled={areUsersFetching}
            m="0 auto"
          />
        }
      />

      <AlertDialog
        isOpen={userToDelete !== null}
        onConfirmAction={deleteUser.mutate}
        title="user-delete-confirmation"
        description="session-delete-confirmation-description"
        onCancel={() => setUserToDelete(null)}
        isActionInProgress={deleteUser.isPending}
        variant="danger"
      />

      <PrimaryModal
        isOpen={userToEdit !== null}
        content={
          <EditUserPanel
            onClose={() => setUserToEdit(null)}
            userId={userToEdit}
          />
        }
      />
    </>
  );
};

export default UsersTab;
