/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Flex, LoadingOverlay, SegmentedControl } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { JSX, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { USERS_ERRORS } from '@/common/errors';
import { getRole, submitFormById } from '@/common/helpers';
import QUERY_KEYS from '@/common/queryKeys';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';
import { UserFormType } from '@/features/Admin/types/formsTypes';
import PurchaseHistory from '@/features/UserProfile/components/PurchaseHitory/PurchaseHistory';
import ADMIN_USERS from '@/services/admin/users';
import { AdminUserType } from '@/types/common';

import UserForm from '../../forms/UserForm/UserForm';
import s from './EditUserPanel.module.css';

const TABS = [
  {
    label: 'account-capital',
    value: 'account',
  },
  {
    label: 'purchases-capital',
    value: 'purchase-history',
  },
] as const;

type EditUserPanelProps = {
  onClose: () => void;
  userId: string | null;
};

type TabValueType = (typeof TABS)[number]['value'];

const EditUserPanel = ({
  onClose,
  userId,
}: EditUserPanelProps): JSX.Element => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const [selectedTab, setSelectedTab] = useState<TabValueType>(TABS[0].value);

  const [certificatesAndLicenses, setCertificatesAndLicenses] = useState<
    UserFormType['certificatesAndLicenses']
  >([]);
  const [emailAlreadyExists, setEmailAlreadyExists] = useState(false);

  const { data: user, isLoading } = useQuery<AdminUserType | null>({
    enabled: Boolean(userId),
    queryFn: () => ADMIN_USERS.GET_USER(userId || ''),
    queryKey: [QUERY_KEYS.ADMIN_USERS, userId],
    placeholderData: keepPreviousData,
  });

  const editUserMutation = useMutation({
    mutationFn: ADMIN_USERS.EDIT_USER,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_USERS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_USERS, userId],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_UNASSIGNED_SCHOOL_ADMINS],
      });

      notifications.show({
        message: t('user-updated-successfully'),
        color: 'green',
      });

      onClose();
    },
    onError: (error) => {
      if (error.message === USERS_ERRORS.USER_ALREADY_EXISTS) {
        setEmailAlreadyExists(true);
      } else {
        notifications.show({
          message: t(error.message),
          color: 'red',
        });
      }
    },
  });

  const initialCertificatesAndLicenses =
    user?.profile?.certificates?.map((certificate) => {
      return {
        type: certificate.type,
        progress: certificate?.progress?.status || 'na',
        licenses:
          user.profile?.licenses?.find((item) => item.type === certificate.type)
            ?.remaining || 0,
      };
    }) || [];

  const onSubmitUserForm = (data: UserFormType) => {
    const formattedLicenses =
      certificatesAndLicenses
        ?.map((item) => {
          return {
            type: item.type,
            progress: item.progress,
            licenses:
              Number(item?.licenses) -
                Number(
                  initialCertificatesAndLicenses?.find(
                    (i) => i.type === item.type
                  )?.licenses
                ) || 0,
          };
        })
        ?.filter((item) => item.progress !== 'na') || [];

    editUserMutation.mutate({
      data: {
        ...data,
        certificatesAndLicenses: formattedLicenses,
      },
      userId: userId || '',
    });
  };

  const onUpdateLicensesAndCertificates = (
    data: NonNullable<UserFormType['certificatesAndLicenses']>[0]
  ) => {
    setCertificatesAndLicenses((prev) => {
      const exists = prev?.some((item) => item.type === data.type);

      if (exists) {
        return prev?.map((item) =>
          item.type === data.type ? { ...item, ...data } : item
        );
      }

      return [...(prev || []), data];
    });
  };

  const clearLicensesAndCertificates = () => {
    setCertificatesAndLicenses(initialCertificatesAndLicenses);
  };

  useEffect(() => {
    setCertificatesAndLicenses(initialCertificatesAndLicenses);
  }, [user]);

  return isLoading ? (
    <LoadingOverlay visible={isLoading} />
  ) : (
    <Card size="xl" bg="gray50" isLoading={false} className={s.modalWrapper}>
      <div className={s.header}>
        <Text transKey="edit-user" type="h3" />

        <Flex pos="absolute" left="37%" right="40%">
          <SegmentedControl
            value={selectedTab}
            onChange={(v) => {
              setSelectedTab(v as TabValueType);
            }}
            withItemsBorders={false}
            data={TABS.map((item) => ({
              value: item.value,
              label: t(item.label),
            }))}
          />
        </Flex>

        <CloseButton onClick={onClose} variant="outlined" />
      </div>

      {selectedTab === 'account' && (
        <>
          <UserForm
            defaultValues={{
              firstName: user?.profile?.firstName || '',
              lastName: user?.profile?.lastName || '',
              email: user?.profile?.email || '',
              role: getRole(user?.roles || []),
              language: user?.profile?.language || '',
              certificatesAndLicenses: initialCertificatesAndLicenses,
            }}
            onSubmitUserForm={(values) => onSubmitUserForm(values)}
            onUpdateLicensesAndCertificates={onUpdateLicensesAndCertificates}
            certificatesAndLicenses={certificatesAndLicenses}
            clearLicensesAndCertificates={clearLicensesAndCertificates}
            isEditingUser
            emailAlreadyExists={emailAlreadyExists}
            clearEmailAlreadyExists={() => setEmailAlreadyExists(false)}
          />

          <Box className={s.actions}>
            <Button
              variant="primaryOutlined"
              transKey="cancel-capital"
              onClick={onClose}
              isDisabled={editUserMutation.isPending}
            />

            <Button
              transKey="save-capital"
              onClick={() => submitFormById('users-form')}
              isLoading={editUserMutation.isPending}
            />
          </Box>
        </>
      )}

      {selectedTab === 'purchase-history' && (
        <Card>
          <PurchaseHistory userId={userId || ''} />
        </Card>
      )}
    </Card>
  );
};

export default EditUserPanel;
