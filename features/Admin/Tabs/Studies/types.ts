import { StudyDetailsType } from '@/types/common';

import {
  A<PERSON><PERSON><PERSON><PERSON>_DISPLAYED_SCREENS,
  INITIAL_SELECTED_STUDY_DETAILS,
} from './consts';

export type StudyPanelDisplayScreenType =
  (typeof AVAILABLE_DISPLAYED_SCREENS)[keyof typeof AVAILABLE_DISPLAYED_SCREENS];

export type StudyDetailsTypeKeyOptions =
  keyof typeof INITIAL_SELECTED_STUDY_DETAILS;

export type AcceptableStudyValueType = StudyDetailsType[keyof StudyDetailsType];
