import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import ADMIN_STUDIES from '@/services/admin/studies';

import useAdminStudiesQuery from '../../hooks/useAdminStudiesQuery';

type TerminateStudyPanelProps = {
  studyId: string | null;
  isAlertDialogOpen: boolean;
  closeAlertDialog: () => void;
};

const TerminateStudyPanel = ({
  closeAlertDialog,
  isAlertDialogOpen,
  studyId,
}: TerminateStudyPanelProps): JSX.Element => {
  const { t } = useTranslation();
  const { invalidateStudies } = useAdminStudiesQuery();

  const terminateStudyByIdMutation = useMutation({
    mutationFn: ADMIN_STUDIES.TERMINATE_STUDY_BY_ID,
    onSuccess: () => {
      closeAlertDialog();

      invalidateStudies();

      notifications.show({
        message: t('study-terminated'),
        color: 'green',
      });
    },
  });

  return (
    <AlertDialog
      isOpen={isAlertDialogOpen}
      onConfirmAction={() => {
        if (studyId) {
          terminateStudyByIdMutation.mutate(studyId);
        }
      }}
      title="confirm-study-termination"
      description="study-terminate-confirmation-description"
      onCancel={closeAlertDialog}
      isActionInProgress={terminateStudyByIdMutation.isPending}
      variant="danger"
    />
  );
};

export default TerminateStudyPanel;
