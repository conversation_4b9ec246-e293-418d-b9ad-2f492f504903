import {
  Box,
  LoadingOverlay,
  Pagination,
  Paper,
  ScrollArea,
  Table,
  TableData,
  Tooltip,
} from '@mantine/core';
import { useTranslation } from 'react-i18next';

import { getFullNameFromFirstAndLastName } from '@/common/helpers';
import Checkbox from '@/components/Checkbox/Checkbox';
import Icon from '@/components/Icon/Icon';
import Input from '@/components/Inputs/Input/Input';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { StudyDetailsType } from '@/types/common';

import s from './ResearchersSelectorCard.module.css';

type ResearchersSelectorCardPropsType = {
  allAvailableResearchersForSelection: StudyDetailsType['researchers'];
  scrollAreaHeight: number;
  selectedResearchers: StudyDetailsType['researchers'];
  isLoading?: boolean;
  isFetching?: boolean;
  isDisabled?: boolean;
  tableProps: {
    paginationData: {
      activePage: number;
      totalNumberOfPages: number;
      totalNumberOfResults: number;
      totalNumberOfSearchResults: number;
      onPageChange: (page: number) => void;
    };
    isGlobalCheckBoxDisabled?: boolean;
    areSelectionCheckBoxesDisabled?: boolean;
    onUpdateSelectedResearchersList: (
      updatedStudentsList: StudyDetailsType['researchers']
    ) => void;
  };
  searchProps: {
    searchResearcherQuery: string;
    onInputChange: (value: string) => void;
  };
};

const ResearchersSelectorCard = ({
  allAvailableResearchersForSelection,
  isDisabled = false,
  isFetching = false,
  isLoading = false,
  scrollAreaHeight,
  searchProps: { onInputChange, searchResearcherQuery },
  selectedResearchers,
  tableProps: {
    areSelectionCheckBoxesDisabled = false,
    isGlobalCheckBoxDisabled = false,
    onUpdateSelectedResearchersList,
    paginationData,
    // totalNumberOfResults,
  },
}: ResearchersSelectorCardPropsType): JSX.Element => {
  const { t } = useTranslation();

  const selectedResearcherIds = selectedResearchers.map(
    (researcher) => researcher.id
  );

  const isGlobalCheckboxChecked = allAvailableResearchersForSelection.every(
    (researcher) => selectedResearcherIds.includes(researcher.id)
  );

  const isGlobalCheckboxIndeterminate =
    allAvailableResearchersForSelection.some((researcher) =>
      selectedResearcherIds.includes(researcher.id)
    );

  const onGlobalCheckBoxSelection = () => {
    if (isGlobalCheckboxIndeterminate) {
      const filteredStudents = selectedResearchers.filter(
        (selectedStudent) =>
          // meaning if it gets included it will return false so filter will remove it
          !allAvailableResearchersForSelection
            .map((student) => student.id)
            .includes(selectedStudent.id)
      );

      onUpdateSelectedResearchersList(filteredStudents);
    } else {
      const updatedList = [
        ...selectedResearchers,
        ...allAvailableResearchersForSelection,
      ];

      onUpdateSelectedResearchersList(updatedList);
    }
  };

  const onIndividualTableSelection = (
    researcher: StudyDetailsType['researchers'][number]
  ) => {
    const isStudentSelected = selectedResearchers.some(
      (selectedResearcher) => selectedResearcher.id === researcher.id
    );

    if (isStudentSelected) {
      const filteredSelectedStudentList = selectedResearchers.filter(
        (selectedResearcher) => selectedResearcher.id !== researcher.id
      );
      onUpdateSelectedResearchersList(filteredSelectedStudentList);
    } else {
      const updatedSelectedStudentList = [...selectedResearchers, researcher];

      onUpdateSelectedResearchersList(updatedSelectedStudentList);
    }
  };

  const tableData: TableData = {
    head: [
      t('name-capital'),
      t('email-capital'),
      <Checkbox
        key="checkbox-icon"
        value=""
        indeterminate={
          isGlobalCheckboxIndeterminate && !isGlobalCheckboxChecked
        }
        isChecked={isGlobalCheckboxChecked}
        onChange={onGlobalCheckBoxSelection}
        isDisabled={isDisabled || isGlobalCheckBoxDisabled}
      />,
    ],
    body: allAvailableResearchersForSelection.map((researcher) => [
      getFullNameFromFirstAndLastName(
        researcher.firstName,
        researcher.lastName
      ),
      researcher.email,
      <Tooltip
        key="checkbox-icon"
        label="You can only select 1 student when this device is selected"
        disabled={!areSelectionCheckBoxesDisabled}
      >
        <Box>
          <Checkbox
            value={researcher.id}
            key={researcher.id}
            isChecked={selectedResearchers.some(
              (selectedResearcher) => selectedResearcher.id === researcher.id
            )}
            onChange={() => onIndividualTableSelection(researcher)}
            isDisabled={isDisabled || areSelectionCheckBoxesDisabled}
          />
        </Box>
      </Tooltip>,
    ]),
  };

  const TABLE_VIEW_COMPONENT = isLoading ? (
    <TableSkeleton numberOfColumns={tableData.head?.length} />
  ) : (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
        table: {
          opacity: isFetching ? 0.5 : 1,
          pointerEvents: isFetching ? 'none' : 'auto',
        },
      }}
    />
  );

  return (
    <Paper className={s.wrapper}>
      <LoadingOverlay
        visible={isLoading}
        zIndex={2}
        overlayProps={{ radius: 'sm', blur: 1 }}
      />

      <div className={s.header}>
        <div className={s.searchInputWrapper}>
          <Input
            value={searchResearcherQuery}
            variant="filled"
            isDisabled={isDisabled || isFetching}
            placeholder={t('search-researchers')}
            onChange={(value) => onInputChange(value)}
            rightSectionWidth={60}
            rightSection={
              <div className={s.inputRightSection}>
                <Icon name="StudentSvg" color="black" size="sm" />

                <Text
                  untranslatedText={`${paginationData.totalNumberOfSearchResults}`}
                  type="body2"
                />
              </div>
            }
          />

          <div className={s.inputRightSection}>
            <Icon name="CheckMarkSvg" color="turquoise" size="md" />

            <Text
              color="turquoise"
              untranslatedText={`${selectedResearchers.length}`}
              type="body1"
            />
          </div>
        </div>
      </div>

      <ScrollArea
        h={scrollAreaHeight}
        scrollbarSize={4}
        scrollbars="y"
        offsetScrollbars
        classNames={{ thumb: 'thumb' }}
      >
        <div className={s.tableWrapper}>
          {allAvailableResearchersForSelection.length > 0 &&
            TABLE_VIEW_COMPONENT}

          {(paginationData.totalNumberOfResults === 0 ||
            allAvailableResearchersForSelection.length === 0) &&
            !isFetching && (
              <Text
                transKey="no-results-found"
                type="h4"
                align="center"
                mt={64}
              />
            )}

          <Pagination
            total={paginationData.totalNumberOfPages}
            value={paginationData.activePage}
            hideWithOnePage
            withControls={false}
            onChange={(value) => paginationData.onPageChange(value)}
            disabled={isFetching}
            m="0 auto"
          />
        </div>
      </ScrollArea>
    </Paper>
  );
};

export default ResearchersSelectorCard;
