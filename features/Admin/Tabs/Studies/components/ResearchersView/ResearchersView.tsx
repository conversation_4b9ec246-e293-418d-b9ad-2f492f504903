import { Box } from '@mantine/core';
import { JSX, useState } from 'react';
import { BiArrowBack } from 'react-icons/bi';

import { getFullNameFromFirstAndLastName } from '@/common/helpers';
import Button from '@/components/Button/Button';
import SelectedStudentsBoard from '@/components/SelectedStudentsBoard/SelectedStudentsBoard';
import Text from '@/components/Text/Text';
import { StudyDetailsType } from '@/types/common';

import useUnassignedResearchers from '../../../../adminHooks/useUnassignedResearchers';
import ResearchersSelectorCard from '../ResearchersSelectorCard/ResearchersSelectorCard';
import s from './ResearchersView.module.css';

type SelectStudentsViewPropsType = {
  onBack: () => void;
  studyId?: string;
  initialResearchers: StudyDetailsType['researchers'];
  isMutationInProgress: boolean;
  onSubmitUpdatedResearchersList: (
    researchers: StudyDetailsType['researchers']
  ) => void;
};

const ResearchersView = ({
  initialResearchers,
  isMutationInProgress,
  onBack,
  onSubmitUpdatedResearchersList,
  studyId,
}: SelectStudentsViewPropsType): JSX.Element => {
  const [selectedResearchers, setSelectedResearchers] =
    useState(initialResearchers);

  const {
    activePage,
    areResearchersFetching,
    areResearchersLoading,
    onPageChange,
    onResearcherSearch,
    researchers,
    searchResearcherQuery,
    totalNumberOfPages,
    totalNumberOfResearchers,
  } = useUnassignedResearchers(studyId);

  return (
    <div className={s.wrapper}>
      <div className={s.header}>
        <Box className={s.arrowAndTextWrapper} onClick={onBack}>
          <BiArrowBack fontSize={24} color="black" className={s.backButton} />

          <Text transKey="add-remove-researchers" type="h3" fw={250} />
        </Box>

        <Button
          transKey="save-capital"
          isDisabled={areResearchersLoading || isMutationInProgress}
          onClick={() => onSubmitUpdatedResearchersList(selectedResearchers)}
        />
      </div>

      <div className={s.cardsWrapper}>
        <ResearchersSelectorCard
          scrollAreaHeight={410}
          allAvailableResearchersForSelection={researchers}
          selectedResearchers={selectedResearchers}
          isLoading={areResearchersLoading}
          isFetching={areResearchersFetching}
          tableProps={{
            paginationData: {
              activePage,
              totalNumberOfPages,
              totalNumberOfResults: totalNumberOfResearchers,
              totalNumberOfSearchResults: totalNumberOfResearchers,
              onPageChange: (page) => onPageChange(page),
            },
            onUpdateSelectedResearchersList: (updatedResearchersList) =>
              setSelectedResearchers(updatedResearchersList),
          }}
          searchProps={{
            searchResearcherQuery,
            onInputChange: onResearcherSearch,
          }}
        />

        {/* Rename it and use as generic */}
        <SelectedStudentsBoard
          scrollAreaHeight={440}
          selectedStudents={selectedResearchers.map((researcher) => ({
            displayedName: getFullNameFromFirstAndLastName(
              researcher.firstName,
              researcher.lastName
            ),
            id: researcher.id,
          }))}
          isLoading={false}
          onDeleteStudent={(studentId) =>
            setSelectedResearchers((prev) =>
              prev.filter((selectedStudent) => selectedStudent.id !== studentId)
            )
          }
        />
      </div>
    </div>
  );
};

export default ResearchersView;
