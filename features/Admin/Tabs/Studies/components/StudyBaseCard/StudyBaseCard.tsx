import { Box, SegmentedControl } from '@mantine/core';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Text from '@/components/Text/Text';

import s from './StudyBaseCard.module.css';

const SEGMENTED_VALUES = {
  STUDY_DETAILS: 'study-details',
  STUDY_STATISTICS: 'study-statistics',
} as const;

type StudyBaseCardTabType =
  (typeof SEGMENTED_VALUES)[keyof typeof SEGMENTED_VALUES];

type TabType = {
  label: string;
  value: (typeof SEGMENTED_VALUES)[keyof typeof SEGMENTED_VALUES];
};

type StudyBaseCardPropsType = {
  title: string;
  mode: 'create' | 'edit';
  onCloseButtonClick: () => void;
};

// THIS COMPONENT SHOULD NOT BE USED ITS JUST A WAY TO ADAPT TO THE DESIGN DEPENDING ON THE CHANGES

const AddStudentPanel = ({
  mode,
  onCloseButtonClick,
  title,
}: StudyBaseCardPropsType): JSX.Element => {
  const { t } = useTranslation();

  const [selectedTab, setSelectedTab] = useState<StudyBaseCardTabType>(
    SEGMENTED_VALUES.STUDY_DETAILS
  );

  const TABS: TabType[] = [
    {
      label: 'individual-capital',
      value: SEGMENTED_VALUES.STUDY_DETAILS,
    },
    {
      label: 'group-capital',
      value: SEGMENTED_VALUES.STUDY_STATISTICS,
    },
  ];

  return (
    <Box
    // className={`
    //   ${s.wrapper}
    //   ${selectedTab === SEGMENTED_VALUES.IMPORT_FROM_FILE && s.importFromFileWrapper}
    //   ${selectedTab === SEGMENTED_VALUES.ADD_NEW_STUDENT && s.addStudentWrapper}`}
    >
      <Card size="xl" bg="gray50" isLoading={false}>
        <div className={s.header}>
          <div className={s.textWrapper}>
            <Text untranslatedText={title} type="h3" />
          </div>

          <div className={s.segmentedWrapper}>
            <SegmentedControl
              w={400}
              fullWidth
              withItemsBorders={false}
              value={selectedTab}
              onChange={(v) => setSelectedTab(v as StudyBaseCardTabType)}
              data={TABS.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
            />
          </div>

          <CloseButton onClick={onCloseButtonClick} variant="outlined" />
        </div>

        {selectedTab === SEGMENTED_VALUES.STUDY_DETAILS &&
          (mode === 'create' ? <div>Create study</div> : <div>Edit study</div>)}

        {selectedTab === SEGMENTED_VALUES.STUDY_STATISTICS && (
          <div> Study statistics</div>
        )}
      </Card>
    </Box>
  );
};

export default AddStudentPanel;
