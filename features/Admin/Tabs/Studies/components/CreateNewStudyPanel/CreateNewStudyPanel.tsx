import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import ADMIN_STUDIES from '@/services/admin/studies';
import { StudyDetailsType } from '@/types/common';

import {
  AVAILABLE_DISPLAYED_SCREENS,
  INITIAL_SELECTED_STUDY_DETAILS,
} from '../../consts';
import useAdminStudiesQuery from '../../hooks/useAdminStudiesQuery';
import commonAddEditStudyCardStyles from '../../styles/AddEditStudyCard.module.css';
import {
  AcceptableStudyValueType,
  StudyDetailsTypeKeyOptions,
  StudyPanelDisplayScreenType,
} from '../../types';
import NewStudyView from '../NewStudyView/NewStudyView';
import ResearchersView from '../ResearchersView/ResearchersView';

type CreateNewStudyPanelProps = {
  onCloseModal: () => void;
};

const CreateNewStudyPanel = ({ onCloseModal }: CreateNewStudyPanelProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [displayedView, setDisplayedView] =
    useState<StudyPanelDisplayScreenType>(
      AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY
    );

  const [selectedStudyDetails, setSelectedStudyDetails] =
    useState<StudyDetailsType>(INITIAL_SELECTED_STUDY_DETAILS);

  const researchers = useMemo(
    () => selectedStudyDetails.researchers,
    [selectedStudyDetails.researchers]
  );

  const { invalidateStudies } = useAdminStudiesQuery();

  const handleSelectedStudyDetailsUpdate = useCallback(
    (key: StudyDetailsTypeKeyOptions, value: AcceptableStudyValueType) => {
      setSelectedStudyDetails((prev) => ({ ...prev, [key]: value }));
    },
    []
  );

  const handleBack = useCallback(
    () => setDisplayedView(AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY),
    []
  );

  const handleSubmitResearchers = useCallback(
    (updatedResearchersList: StudyDetailsType['researchers']) => {
      handleSelectedStudyDetailsUpdate('researchers', updatedResearchersList);
      setDisplayedView(AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY);
    },
    [handleSelectedStudyDetailsUpdate] // Now stable
  );

  const createNewStudyMutation = useMutation({
    mutationFn: ADMIN_STUDIES.CREATE_NEW_STUDY,
    onSuccess: () => {
      onCloseModal();

      invalidateStudies();

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_STUDIES],
      });

      notifications.show({
        message: t('study-created'),
        color: 'green',
      });
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
        autoClose: 20000,
      });
    },
  });

  const VIEWS = {
    [AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY]: (
      <NewStudyView
        title={t('create-new-study')}
        submitButtonLabel={t('create-capital')}
        onCloseModal={onCloseModal}
        selectedStudyDetails={selectedStudyDetails}
        handleSelectedStudyDetailsUpdate={handleSelectedStudyDetailsUpdate}
        onSelectResearchersButtonClick={() => {
          setDisplayedView(AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS);
        }}
        onSubmitButtonClick={() =>
          createNewStudyMutation.mutate(selectedStudyDetails)
        }
        isActionInProgress={createNewStudyMutation.isPending}
      />
    ),
    [AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS]: (
      <ResearchersView
        onBack={handleBack}
        initialResearchers={researchers}
        isMutationInProgress={createNewStudyMutation.isPending}
        onSubmitUpdatedResearchersList={(updatedResearchersList) => {
          handleSubmitResearchers(updatedResearchersList);
        }}
      />
    ),
  };

  return (
    <Card
      size="xl"
      bg="gray50"
      className={`${commonAddEditStudyCardStyles.wrapper} ${displayedView === AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS && commonAddEditStudyCardStyles.addRemoveResearchersWrapper}`}
      hasDynamicHeight={
        displayedView !== AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS
      }
    >
      {VIEWS[displayedView]}
    </Card>
  );
};

export default CreateNewStudyPanel;
