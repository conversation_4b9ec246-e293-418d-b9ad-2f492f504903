import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import ADMIN_STUDIES from '@/services/admin/studies';

import useAdminStudiesQuery from '../../hooks/useAdminStudiesQuery';

type DeleteStudyPanelProps = {
  studyId: string | null;
  isAlertDialogOpen: boolean;
  closeAlertDialog: () => void;
};

const DeleteStudyPanel = ({
  closeAlertDialog,
  isAlertDialogOpen,
  studyId,
}: DeleteStudyPanelProps): JSX.Element => {
  const { t } = useTranslation();
  const { invalidateStudies } = useAdminStudiesQuery();

  const deleteStudyByIdMutation = useMutation({
    mutationFn: ADMIN_STUDIES.DELETE_STUDY_BY_ID,
    onSuccess: () => {
      closeAlertDialog();

      invalidateStudies();

      notifications.show({
        message: t('study-deleted'),
        color: 'green',
      });
    },
  });

  return (
    <AlertDialog
      isOpen={isAlertDialogOpen}
      onConfirmAction={() => {
        if (studyId) {
          deleteStudyByIdMutation.mutate(studyId);
        }
      }}
      title="confirm-study-deletion"
      description="session-delete-confirmation-description"
      onCancel={closeAlertDialog}
      isActionInProgress={deleteStudyByIdMutation.isPending}
      variant="danger"
    />
  );
};

export default DeleteStudyPanel;
