import { notifications } from '@mantine/notifications';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import QUERY_KEYS from '@/common/queryKeys';
import Card from '@/components/Card/Card';
import ADMIN_STUDIES from '@/services/admin/studies';
import { AdminStudyDetailsByIdType, StudyDetailsType } from '@/types/common';

import { AVAILABLE_DISPLAYED_SCREENS } from '../../consts';
import useAdminStudiesQuery from '../../hooks/useAdminStudiesQuery';
import commonAddEditStudyCardStyles from '../../styles/AddEditStudyCard.module.css';
import {
  AcceptableStudyValueType,
  StudyDetailsTypeKeyOptions,
  StudyPanelDisplayScreenType,
} from '../../types';
import NewStudyView from '../NewStudyView/NewStudyView';
import ResearchersView from '../ResearchersView/ResearchersView';

export type InvitedStudentSession = {
  studentId: string;
  studentDisplayedName: string;
  sessionCode: string;
};

type EditStudyPanelProps = {
  studyId: string;
  onCloseModal: () => void;
};

const EditStudyPanel = ({ onCloseModal, studyId }: EditStudyPanelProps) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const { invalidateStudies } = useAdminStudiesQuery();

  const [displayedScreen, setDisplayedScreen] =
    useState<StudyPanelDisplayScreenType>(
      AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY
    );

  const { data, isLoading: areStudyDetailsLoading } =
    useQuery<AdminStudyDetailsByIdType | null>({
      enabled: Boolean(studyId),
      queryKey: [QUERY_KEYS.ADMIN_STUDY_BY_ID, studyId],
      queryFn: () => (studyId ? ADMIN_STUDIES.GET_STUDY_BY_ID(studyId) : null),
    });

  const studyDetails: StudyDetailsType = {
    type: data?.type || null,
    name: data?.name || '',
    country: data?.country || '',
    language: data?.language || '',
    administrator: data?.administrator || null,
    from: (data?.from || null) as any,
    to: (data?.to || null) as any,
    researchers: data?.proctors || [],
  };

  //  create a handleSelectedStudyDetailsUpdate function that used queryClient.setQueryData to update the studyDetails
  const handleSelectedStudyDetailsUpdate = (
    key: StudyDetailsTypeKeyOptions,
    value: AcceptableStudyValueType
  ) => {
    queryClient.setQueryData(
      [QUERY_KEYS.ADMIN_STUDY_BY_ID, studyId],
      (old: AdminStudyDetailsByIdType | null) => {
        if (!old) return null;

        if (key === 'researchers') {
          return {
            ...old,
            proctors: value,
          };
        }

        return {
          ...old,
          [key]: value,
        };
      }
    );
  };

  const onCloseModalAction = () => {
    onCloseModal();
  };

  const updateStudyByIdMutation = useMutation({
    mutationFn: ADMIN_STUDIES.UPDATE_STUDY_BY_ID,
    onSuccess: () => {
      onCloseModal();

      invalidateStudies();

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ADMIN_STUDIES],
      });

      notifications.show({
        message: t('study-updated'),
        color: 'green',
      });
    },
    onError: (error) => {
      notifications.show({
        message: t(error.message),
        color: 'red',
        autoClose: 20000,
      });
    },
  });

  const MODAL_SCREENS = {
    [AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY]: (
      <NewStudyView
        studyId={studyId}
        submitButtonLabel={t('update-capital')}
        title={t('edit-study')}
        onCloseModal={onCloseModalAction}
        selectedStudyDetails={studyDetails}
        handleSelectedStudyDetailsUpdate={handleSelectedStudyDetailsUpdate}
        onSelectResearchersButtonClick={() =>
          setDisplayedScreen(AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS)
        }
        onSubmitButtonClick={() => {
          updateStudyByIdMutation.mutate({
            studyId,
            gatheredStudyDetails: studyDetails,
          });
        }}
        isActionInProgress={updateStudyByIdMutation.isPending}
        areStudyDetailsLoading={areStudyDetailsLoading}
      />
    ),
    [AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS]: (
      <ResearchersView
        studyId={studyId}
        isMutationInProgress={updateStudyByIdMutation.isPending}
        initialResearchers={studyDetails?.researchers || []}
        onSubmitUpdatedResearchersList={(updatedResearchersList) => {
          handleSelectedStudyDetailsUpdate(
            'researchers',
            updatedResearchersList
          );
          setDisplayedScreen(AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY);
        }}
        onBack={() => {
          setDisplayedScreen(AVAILABLE_DISPLAYED_SCREENS.NEW_STUDY);
        }}
      />
    ),
  };

  return (
    // Create a isolated component for the card and using instead of styles folder
    <Card
      size="xl"
      bg="gray50"
      className={`${commonAddEditStudyCardStyles.wrapper} ${displayedScreen === AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS && commonAddEditStudyCardStyles.addRemoveResearchersWrapper}`}
      hasDynamicHeight={
        displayedScreen !== AVAILABLE_DISPLAYED_SCREENS.SELECT_RESEARCHERS
      }
    >
      {MODAL_SCREENS[displayedScreen]}
    </Card>
  );
};

export default EditStudyPanel;
