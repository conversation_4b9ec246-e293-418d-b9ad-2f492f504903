import {
  Box,
  LoadingOverlay,
  ScrollArea,
  TextInput,
  Tooltip,
} from '@mantine/core';
import { DateInput } from '@mantine/dates';
import countries from 'i18n-iso-countries';
import { getLangNameFromCode } from 'language-name-map';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { COUNTRIES_ISO_CODES } from '@/common/consts';
import {
  getFullNameFromFirstAndLastName,
  mantineDateParser,
} from '@/common/helpers';
import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import DisplayCloseTag from '@/components/DisplayCloseTag/DisplayCloseTag';
import Icon from '@/components/Icon/Icon';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { StudyDetailsType } from '@/types/common';

import { useUnassignedStudyAdminsAndTests } from '../../../../adminHooks/useUnassignedStudyAdminsAndTests';
import useStudyLanguages from '../../hooks/useStudyLanguages';
import {
  AcceptableStudyValueType,
  StudyDetailsTypeKeyOptions,
} from '../../types';
import s from './NewStudyView.module.css';

type NewStudyViewProps = {
  studyId?: string;
  selectedStudyDetails: StudyDetailsType;
  title: string;
  submitButtonLabel: string;
  onCloseModal: () => void;
  onSelectResearchersButtonClick: () => void;
  handleSelectedStudyDetailsUpdate: (
    key: StudyDetailsTypeKeyOptions,
    value: AcceptableStudyValueType
  ) => void;
  onSubmitButtonClick: () => void;
  isActionInProgress: boolean;
  areStudyDetailsLoading?: boolean;
};

const NewStudyView = ({
  areStudyDetailsLoading = false,
  handleSelectedStudyDetailsUpdate,
  isActionInProgress,
  onCloseModal,
  onSelectResearchersButtonClick,
  onSubmitButtonClick,
  selectedStudyDetails,
  studyId,
  submitButtonLabel,
  title,
}: NewStudyViewProps) => {
  const { i18n, t } = useTranslation();

  const { areStudyAdminsAndTestsLoading, TEST_TYPES, unassignedStudyAdmins } =
    useUnassignedStudyAdminsAndTests(studyId);

  const { adminStudyLanguages, areStudyLanguagesFetching } = useStudyLanguages(
    selectedStudyDetails.type || '',
    Boolean(selectedStudyDetails.type)
  );

  const isValidToCreateNewStudy = Boolean(
    selectedStudyDetails?.name &&
      selectedStudyDetails.name.trim()?.length > 0 &&
      selectedStudyDetails?.country &&
      selectedStudyDetails?.language &&
      selectedStudyDetails?.from
  );

  const isAnyLoadingInProgress =
    areStudyDetailsLoading ||
    areStudyAdminsAndTestsLoading ||
    isActionInProgress;

  const isSubmitButtonDisabled =
    !isValidToCreateNewStudy || isAnyLoadingInProgress;

  const areResearchersSelected =
    (selectedStudyDetails?.researchers?.length || 0) > 0;

  return (
    <div className={s.container}>
      {/* HEADER */}
      <div className={s.header}>
        <div className={s.textWrapper}>
          <Text untranslatedText={title} type="h3" />
        </div>

        <CloseButton onClick={onCloseModal} variant="outlined" />
      </div>

      <div className={s.content}>
        <Card size="xl" overflow="visible" className={s.formWrapper}>
          <Text transKey="info" type="h3" />

          <div className={s.inputWrapper}>
            {/* STUDY NAME */}
            <TextInput
              placeholder={t('name')}
              disabled={false}
              required
              w="100%"
              value={selectedStudyDetails?.name || ''}
              onChange={(e) =>
                handleSelectedStudyDetailsUpdate('name', e.target.value)
              }
            />

            {/* STUDY COUNTRY = MANDATORY */}
            <SelectDropdown
              value={selectedStudyDetails?.country || ''}
              onChange={(value) => {
                handleSelectedStudyDetailsUpdate('country', value);
              }}
              data={COUNTRIES_ISO_CODES.map((item) => ({
                label: countries.getName(item, i18n.language) || '',
                value: item,
              }))}
              placeholder={t('country')}
              isDisabled={false}
            />
          </div>

          <div className={s.inputWrapper}>
            {/* TEST TYPE */}
            <SelectDropdown
              value={selectedStudyDetails.type || ''}
              data={TEST_TYPES}
              placeholder={t('test-type')}
              onChange={(value) => {
                handleSelectedStudyDetailsUpdate('language', null);
                handleSelectedStudyDetailsUpdate('type', value);
              }}
            />

            {/* STUDY LANGUAGE  = MANDATORY */}
            <SelectDropdown
              value={selectedStudyDetails?.language || ''}
              onChange={(value) => {
                handleSelectedStudyDetailsUpdate('language', value);
              }}
              data={
                adminStudyLanguages.map((item) => ({
                  label: getLangNameFromCode(item)?.native || '',
                  value: item,
                })) || []
              }
              placeholder={t('language')}
              isDisabled={
                selectedStudyDetails.type === null ||
                areStudyLanguagesFetching ||
                adminStudyLanguages.length === 0
              }
            />
          </div>

          {/* FROM-TO */}
          <div className={s.inputWrapper}>
            <div style={{ display: 'flex', gap: '15px', width: '100%' }}>
              <DateInput
                w="100%"
                placeholder={t('starting-date-of-the-study')}
                required
                valueFormat="DD/MM/YYYY"
                highlightToday
                dateParser={mantineDateParser}
                value={
                  selectedStudyDetails?.from
                    ? new Date(selectedStudyDetails?.from)
                    : null
                }
                onChange={(v) => {
                  handleSelectedStudyDetailsUpdate('from', v);
                  handleSelectedStudyDetailsUpdate('to', null);
                }}
                minDate={new Date()}
              />

              <DateInput
                w="100%"
                placeholder={t('ending-date-of-the-study')}
                required
                valueFormat="DD/MM/YYYY"
                highlightToday
                disabled={selectedStudyDetails?.from === null}
                dateParser={mantineDateParser}
                value={
                  selectedStudyDetails?.to
                    ? new Date(selectedStudyDetails?.to)
                    : null
                }
                onChange={(v) => {
                  handleSelectedStudyDetailsUpdate('to', v);
                }}
                minDate={
                  selectedStudyDetails?.from
                    ? new Date(
                        (selectedStudyDetails?.from
                          ? new Date(selectedStudyDetails.from)
                          : new Date()
                        ).getTime() + 86400000
                      ) // 24h in ms
                    : new Date()
                }
              />
            </div>

            {/* STUDY ADMIN ID = OPTIONAL */}
            <SelectDropdown
              value={selectedStudyDetails?.administrator?.id || ''}
              // Data should be a list of all the unassigned study admins
              data={
                unassignedStudyAdmins?.results.map((item) => ({
                  label: getFullNameFromFirstAndLastName(
                    item.firstName,
                    item.lastName
                  ),
                  value: item.id,
                })) || []
              }
              placeholder={t('study-admin')}
              onChange={(value) =>
                handleSelectedStudyDetailsUpdate(
                  'administrator',
                  unassignedStudyAdmins?.results.find(
                    (item) => item.id === value
                  ) || null
                )
              }
              isDisabled={false}
              clearable
            />
          </div>
        </Card>

        {/* SELECTED PARTICIPANTS DESIGN */}
        <Card size="xl">
          <div className={s.row}>
            <div className={s.row}>
              <Icon
                name="PersonSvg"
                color="turquoise"
                size="lg"
                className={s.icon}
              />

              {/* <Text transKey="selected-researchers" type="h4" /> */}

              <Text transKey="selected-researchers" type="h4" />

              <Icon name="CheckMarkSvg" color="turquoise" size="md" />

              <Text
                untranslatedText={`${selectedStudyDetails?.researchers.length}`}
                color="turquoise"
                type="body1"
              />
            </div>

            <Button
              transKey={
                areResearchersSelected ? 'add-remove-capital' : 'select-capital'
              }
              onClick={onSelectResearchersButtonClick}
              variant="primary"
            />
          </div>

          <ScrollArea.Autosize
            mah={160}
            type="always"
            scrollbarSize={4}
            mt={32}
          >
            {areResearchersSelected ? (
              <div className={s.selectedParticipants}>
                {selectedStudyDetails?.researchers.map((researcher) => (
                  <DisplayCloseTag
                    key={researcher.id}
                    text={getFullNameFromFirstAndLastName(
                      researcher.firstName,
                      researcher.lastName
                    )}
                  />
                ))}
              </div>
            ) : (
              <Text
                transKey="no-researchers-selected"
                type="body2"
                align="center"
                color="blue"
                mt={32}
              />
            )}
          </ScrollArea.Autosize>
        </Card>
      </div>

      <div className={s.footer}>
        <div className={s.buttonsWrapper}>
          {/* // Enable when schedule feature is wanted */}
          <Button
            transKey="cancel-capital"
            variant="primaryOutlined"
            onClick={onCloseModal}
            isDisabled={isAnyLoadingInProgress}
          />

          <Tooltip
            label={t('mandatory-field-message-to-create-update-study')}
            withArrow
            position="top"
            disabled={isValidToCreateNewStudy}
          >
            <Box>
              <Button
                untranslatedText={submitButtonLabel}
                variant="primary"
                onClick={onSubmitButtonClick}
                isDisabled={isSubmitButtonDisabled}
                isLoading={isActionInProgress}
              />
            </Box>
          </Tooltip>
        </div>
      </div>

      <LoadingOverlay
        visible={areStudyAdminsAndTestsLoading || areStudyDetailsLoading}
        overlayProps={{ blur: 1 }}
      />
    </div>
  );
};

export default NewStudyView;
