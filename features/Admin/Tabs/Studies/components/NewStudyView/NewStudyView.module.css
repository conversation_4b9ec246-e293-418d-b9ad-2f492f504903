.container {
  min-height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
}

.textWrapper {
  margin-right: var(--spacing-xl);
}

.content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.dropDownsWrapper {
  display: flex;
  gap: var(--spacing-md);

  & button {
    width: 190px;
  }
}

.icon {
  margin: 0 var(--spacing-sm);
}

.selectedParticipants {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;

  & > div {
    min-width: 190px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--spacing-xl);
}

.buttonsWrapper {
  display: flex;
  gap: var(--spacing-mdl);
}

.formWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.inputWrapper {
  display: flex;
  gap: var(--spacing-md);
}
