/* eslint-disable react-hooks/exhaustive-deps */
import { Table, TableData } from '@mantine/core';
import countries from 'i18n-iso-countries';
import { getLangNameFromCode } from 'language-name-map';
import { JSX, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';
import AlertDialog from '@/components/Modals/AlertDialog/AlertDialog';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import TableSkeleton from '@/components/TableSkeleton/TableSkeleton';
import Text from '@/components/Text/Text';
import { AdminStudyType, TranslationKeysType } from '@/types/common';

import DeleteStudyPanel from '../DeleteStudyPanel/DeleteStudyPanel';
import EditStudyPanel from '../EditStudyPanel/EditStudyPanel';
import StudiesActionsMenu from '../StudiesActionsMenu/StudiesActionsMenu';
import TerminateStudyPanel from '../TerminateStudyPanel/TerminateStudyPanel';
import s from './StudiesListTable.module.css';

type StudyActionsType = 'view' | 'edit' | 'delete' | 'terminate' | null;

type SelectStudyType = {
  studyId: string | null;
  action: StudyActionsType;
};

type StudiesListTableProps = {
  areStudiesLoading: boolean;
  areStudiesFetching: boolean;
  studies: AdminStudyType[];
};

const StudiesListTable = ({
  areStudiesFetching,
  areStudiesLoading,
  studies,
}: StudiesListTableProps): JSX.Element => {
  const { i18n, t } = useTranslation();

  const [selectedStudy, setSelectedStudy] = useState<SelectStudyType>({
    studyId: null,
    action: null,
  });

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${t(month)} ${day}  ‘${year} ${hoursAndMinutes}`;
  };

  const TABLE_HEAD = useMemo(() => {
    return [
      t('name-capital'),
      t('country-capital'),
      t('language-capital'),
      t('start-date-capital'),
      t('end-date-capital'),
      t('researchers-capital'),
      t('completed-tests-capital'),
      t('status-capital'),
      '',
    ];
  }, [t]);

  const TABLE_BODY = useMemo(() => {
    return studies.map((study) => [
      study.name,
      countries.getName(study.country, i18n.language) || '',
      getLangNameFromCode(study.language)?.native || '',
      study.from ? getListDateTime(study.from) : '-',
      study.to ? getListDateTime(study.to) : '-',
      study.proctorsCount,
      study?.sessionsCount || '-',
      study?.status ? (
        <div key={study.id} className={`${s.labelWrapper} ${s[study.status]}`}>
          <Text
            type="label"
            color="white"
            transKey={(study?.status || '') as TranslationKeysType}
            isBold
          />
        </div>
      ) : (
        '-'
      ),
      <StudiesActionsMenu
        key={study.id}
        onEditStudy={() =>
          setSelectedStudy({
            studyId: study.id,
            action: 'edit',
          })
        }
        onDeleteStudy={() =>
          setSelectedStudy({
            studyId: study.id,
            action: 'delete',
          })
        }
        onTerminateStudy={() =>
          setSelectedStudy({
            studyId: study.id,
            action: 'terminate',
          })
        }
        isTerminated={study.status === 'Completed'}
      />,
    ]);
  }, [studies]);

  const tableData: TableData = {
    head: TABLE_HEAD,
    body: TABLE_BODY,
  };

  const TABLE_VIEW_COMPONENT = (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
        table: {
          opacity: areStudiesFetching ? 0.5 : 1,
          pointerEvents: areStudiesFetching ? 'none' : 'auto',
        },
      }}
    />
  );

  return (
    <>
      {TABLE_VIEW_COMPONENT}

      {/* Edit Study - using a study id */}
      <PrimaryModal
        isOpen={
          selectedStudy.action === 'edit' && selectedStudy.studyId !== null
        }
        content={
          selectedStudy.studyId ? (
            <EditStudyPanel
              studyId={selectedStudy.studyId}
              onCloseModal={() =>
                setSelectedStudy({ studyId: null, action: null })
              }
            />
          ) : null
        }
      />

      {/* Delete Study - using a study id */}
      <DeleteStudyPanel
        studyId={selectedStudy.studyId || null}
        isAlertDialogOpen={
          selectedStudy.action === 'delete' && selectedStudy.studyId !== null
        }
        closeAlertDialog={() =>
          setSelectedStudy({ studyId: null, action: null })
        }
      />

      {/* Terminate Study - using a study id */}
      <TerminateStudyPanel
        studyId={selectedStudy.studyId || null}
        isAlertDialogOpen={
          selectedStudy.action === 'terminate' && selectedStudy.studyId !== null
        }
        closeAlertDialog={() =>
          setSelectedStudy({ studyId: null, action: null })
        }
      />
    </>
  );
};

export default StudiesListTable;
