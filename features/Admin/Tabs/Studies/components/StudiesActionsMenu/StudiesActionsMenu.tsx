import { Box, Menu } from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';
import {
  RiDeleteBin5Fill,
  RiEditFill,
  RiIndeterminateCircleLine,
} from 'react-icons/ri';

import Icon from '@/components/Icon/Icon';

type StudiesActionsMenuProps = {
  onEditStudy: () => void;
  onDeleteStudy: () => void;
  onTerminateStudy: () => void;
  isTerminated: boolean;
};

const StudiesActionsMenu = ({
  isTerminated,
  onDeleteStudy,
  onEditStudy,
  onTerminateStudy,
}: StudiesActionsMenuProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Menu shadow="md">
      <Menu.Target>
        <Box>
          <Icon name="MenuDotsSvg" color="turquoise" w={18} h={18} hasCursor />
        </Box>
      </Menu.Target>

      <Menu.Dropdown className="menuDropdown">
        <Menu.Item onClick={onEditStudy} leftSection={<RiEditFill size={16} />}>
          {t('edit')}
        </Menu.Item>

        <Menu.Item
          onClick={onDeleteStudy}
          leftSection={
            <RiDeleteBin5Fill color="var(--color-danger)" size={19} />
          }
        >
          {t('delete')}
        </Menu.Item>

        <Menu.Item
          onClick={onTerminateStudy}
          disabled={isTerminated}
          leftSection={
            <RiIndeterminateCircleLine color="var(--color-danger)" size={19} />
          }
        >
          {t('terminate')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default StudiesActionsMenu;
