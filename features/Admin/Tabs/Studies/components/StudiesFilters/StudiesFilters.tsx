import { DateInput, DateValue } from '@mantine/dates';
import { getLangNameFromCode } from 'language-name-map';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import { mantineDateParser } from '@/common/helpers';
import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';
import { StudiesQueryParamsStateType } from '@/types/queryParams/studies';

import useStudyLanguages from '../../hooks/useStudyLanguages';
import s from './StudiesFilters.module.css';

type FiltersType = {
  language: StudiesQueryParamsStateType['studiesLanguage'];
  from: StudiesQueryParamsStateType['startingDateOfTheStudy'];
  to: StudiesQueryParamsStateType['endingDateOfTheStudy'];
};

type StudiesFiltersProps = {
  selectedFilters: FiltersType;
  areFiltersDisabled: boolean;
  onLanguageChange: (value: FiltersType['language']) => void;
  onStartDateChange: (value: FiltersType['from']) => void;
  onEndDateChange: (value: FiltersType['to']) => void;
};

const StudiesFilters = ({
  areFiltersDisabled = false,
  onEndDateChange,
  onLanguageChange,
  onStartDateChange,
  selectedFilters,
}: StudiesFiltersProps): JSX.Element => {
  const { t } = useTranslation();

  const { adminStudyLanguages, areStudyLanguagesLoading } = useStudyLanguages();

  return (
    <>
      <div className={s.labelAndFieldWrapper}>
        <Text transKey="language-capital" type="body2" isBold fw={600} />

        <SelectDropdown
          key={adminStudyLanguages.join(',')}
          value={selectedFilters.language}
          clearable
          data={adminStudyLanguages.map((item) => ({
            label: getLangNameFromCode(item)?.native || '',
            value: item,
          }))}
          isDisabled={areStudyLanguagesLoading || areFiltersDisabled}
          onChange={(value) => onLanguageChange(value)}
          placeholder={t('study-language')}
        />
      </div>

      <div className={s.labelAndFieldWrapper}>
        <Text
          transKey="period-of-time-capital"
          type="body2"
          isBold
          fw={600}
          className={s.filterLabel}
        />

        <DateInput
          placeholder={t('from')}
          required
          highlightToday
          disabled={areStudyLanguagesLoading || areFiltersDisabled}
          allowDeselect
          valueFormat="DD/MM/YYYY"
          value={selectedFilters.from as DateValue}
          clearable
          dateParser={mantineDateParser}
          onChange={(v) => onStartDateChange(v)}
        />

        <DateInput
          placeholder={t('to')}
          required
          clearable
          highlightToday
          allowDeselect
          disabled={areStudyLanguagesLoading || areFiltersDisabled}
          valueFormat="DD/MM/YYYY"
          value={selectedFilters.to as DateValue}
          dateParser={mantineDateParser}
          onChange={(v) => onEndDateChange(v)}
          minDate={new Date(selectedFilters.from || 0)}
        />
      </div>
    </>
  );
};

export default StudiesFilters;
