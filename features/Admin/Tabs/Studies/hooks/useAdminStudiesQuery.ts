import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_STUDIES from '@/services/admin/studies';
import { AdminStudiesPaginatedResponseType } from '@/types/common';

import { QueryParamsStateType } from './useStudiesQueryParams';

const useAdminStudiesQuery = (queryParams?: QueryParamsStateType) => {
  const queryClient = useQueryClient();

  const queryKey = useMemo(
    () => [QUERY_KEYS.ADMIN_STUDIES_PAGINATED_LIST, queryParams],
    [queryParams]
  );

  const { data, isFetching, isLoading } =
    useQuery<AdminStudiesPaginatedResponseType | null>({
      queryKey,
      queryFn: () =>
        queryParams ? ADMIN_STUDIES.GET_STUDIES(queryParams) : null,
      placeholderData: keepPreviousData,
      staleTime: 1000 * 60 * 60,
    });

  const invalidateStudies = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ADMIN_STUDIES_PAGINATED_LIST],
    });
  }, [queryClient]);

  const studies = Array.isArray(data?.results) ? data.results : [];

  return {
    studies,
    areStudiesLoading: isLoading,
    areStudiesFetching: isFetching,
    invalidateStudies,
    totalNumberOfStudies: data?.totalCount || 0,
    totalNumberOfReturnedStudies: data?.count || 0,
  };
};

export default useAdminStudiesQuery;
