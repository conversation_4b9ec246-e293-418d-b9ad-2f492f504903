import { useQuery } from '@tanstack/react-query';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_STUDIES from '@/services/admin/studies';

const useStudyLanguages = (testType?: string, enabled = true) => {
  const { data, isFetching, isLoading } = useQuery<string[] | null>({
    queryKey: [QUERY_KEYS.ADMIN_STUDIES_LANGUAGES, testType],
    queryFn: () => ADMIN_STUDIES.GET_STUDIES_LIST_LANGUAGES(testType || ''),
    enabled,
    // staleTime: 1000 * 60 * 10, // 10 minutes
  });

  return {
    // adminStudyLanguages: ['en', 'el'],
    adminStudyLanguages: data || [],
    areStudyLanguagesLoading: isLoading,
    areStudyLanguagesFetching: isFetching,
  };
};

export default useStudyLanguages;
