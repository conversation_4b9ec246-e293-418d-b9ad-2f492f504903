import { useState } from 'react';

import { StudiesQueryParamsStateType } from '@/types/queryParams/studies';

type FiltersType = {
  language: StudiesQueryParamsStateType['studiesLanguage'];
  from: StudiesQueryParamsStateType['startingDateOfTheStudy'];
  to: StudiesQueryParamsStateType['endingDateOfTheStudy'];
};

const INITIAL_FILTERS: FiltersType = {
  language: '',
  from: null,
  to: null,
};

const useAdminStudiesFilters = () => {
  const [selectedFilters, setSelectedFilters] =
    useState<FiltersType>(INITIAL_FILTERS);

  const totalNumberOfSelectedFilters =
    Object.values(selectedFilters).filter(Boolean).length;

  const handleLanguageChange = (value: FiltersType['language']) => {
    setSelectedFilters((prev) => ({
      ...prev,
      language: value || '',
    }));
  };

  const handleStartDateChange = (value: FiltersType['from']) => {
    setSelectedFilters((prev) => ({
      ...prev,
      from: value || null,
    }));
  };

  const handleEndDateChange = (value: FiltersType['to']) => {
    setSelectedFilters((prev) => ({
      ...prev,
      to: value || null,
    }));
  };

  return {
    selectedFilters,
    numberOfSelectedFilters: totalNumberOfSelectedFilters,
    handleLanguageChange,
    handleStartDateChange,
    handleEndDateChange,
    resetFilters: () => {
      setSelectedFilters(INITIAL_FILTERS);
    },
  };
};

export default useAdminStudiesFilters;
