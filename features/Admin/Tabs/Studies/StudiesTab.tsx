import { Pagination } from '@mantine/core';
import React, { useMemo } from 'react';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';

import AdminLayout from '../../components/AdminLayout/AdminLayout';
import StudiesFilters from './components/StudiesFilters/StudiesFilters';
import StudiesListTable from './components/StudiesListTable/StudiesListTable';
import useAdminStudiesFilters from './hooks/useAdminStudiesFilters';
import useAdminStudiesQuery from './hooks/useAdminStudiesQuery';
import useStudiesQueryParams from './hooks/useStudiesQueryParams';

const StudiesTab = () => {
  const {
    onSearchChange,
    queryParams,
    resetSearchParamsExceptSearchWord,
    updateQueryParams,
  } = useStudiesQueryParams();

  const {
    areStudiesFetching,
    areStudiesLoading,
    studies,
    totalNumberOfReturnedStudies,
    totalNumberOfStudies,
  } = useAdminStudiesQuery(queryParams);

  const {
    handleEndDateChange,
    handleLanguageChange,
    handleStartDateChange,
    numberOfSelectedFilters,
    resetFilters,
    selectedFilters,
  } = useAdminStudiesFilters();

  const PAGINATION_COMPONENT = areStudiesLoading ? null : (
    <Pagination
      total={Math.ceil(
        totalNumberOfReturnedStudies / GLOBAL_PAGINATION_FETCH_LIMIT
      )}
      value={queryParams.activePage}
      hideWithOnePage
      withControls={false}
      onChange={(value) =>
        updateQueryParams({
          type: 'activePage',
          payload: value,
        })
      }
      disabled={areStudiesFetching || areStudiesLoading}
    />
  );

  const FILTERS = (
    <StudiesFilters
      areFiltersDisabled={
        areStudiesFetching || areStudiesLoading || totalNumberOfStudies === 0
      }
      selectedFilters={selectedFilters}
      onEndDateChange={(value) => {
        updateQueryParams({
          type: 'endingDateOfTheStudy',
          payload: value,
        });

        handleEndDateChange(value);
      }}
      onStartDateChange={(value) => {
        updateQueryParams({
          type: 'startingDateOfTheStudy',
          payload: value,
        });
        handleStartDateChange(value);
      }}
      onLanguageChange={(value) => {
        updateQueryParams({
          type: 'studiesLanguage',
          payload: value,
        });
        handleLanguageChange(value);
      }}
    />
  );

  const STUDIES_TABLE = useMemo(() => {
    return (
      <StudiesListTable
        areStudiesLoading={areStudiesLoading}
        areStudiesFetching={areStudiesFetching}
        studies={studies}
      />
    );
  }, [areStudiesLoading, areStudiesFetching, studies]);

  return (
    <AdminLayout
      searchInputTransKey="search-studies"
      onSearch={onSearchChange}
      numberOfSearchResults={totalNumberOfReturnedStudies}
      isInformationLoading={areStudiesLoading}
      isInformationFetching={areStudiesFetching}
      totalNumberOfResults={totalNumberOfStudies}
      filters={FILTERS}
      paginationComponent={PAGINATION_COMPONENT}
      listContent={STUDIES_TABLE}
      numberOFSelectedFilters={numberOfSelectedFilters}
      onClearFilters={() => {
        resetFilters();
        resetSearchParamsExceptSearchWord();
      }}
    />
  );
};

export default StudiesTab;
