import { Pagination } from '@mantine/core';
import React, { useMemo } from 'react';

import { GLOBAL_PAGINATION_FETCH_LIMIT } from '@/common/consts';

import AdminLayout from '../../components/AdminLayout/AdminLayout';
import TestsFilters from './components/TestsFilters/TestsFilters';
import TestsListTable from './components/TestsListTable/TestsListTable';
import useAdminTestsFilters from './hooks/useAdminTestsFilters';
import useAdminTestsQuery from './hooks/useAdminTestsQuery';
import useAdminTestsQueryParams from './hooks/useAdminTestsQueryParams';

const TestsTab = () => {
  const {
    onSearchChange,
    queryParams,
    resetSearchParamsExceptSearchWord,
    updateQueryParams,
  } = useAdminTestsQueryParams();

  const {
    areTestsFetching,
    areTestsLoading,
    tests,
    totalNumberOfReturnedTests,
    totalNumberOfTests,
  } = useAdminTestsQuery(queryParams);

  const {
    handleCountryChange,
    numberOfSelectedFilters,
    resetFilters,
    selectedFilters,
  } = useAdminTestsFilters();

  const PAGINATION_COMPONENT = areTestsLoading ? null : (
    <Pagination
      total={Math.ceil(
        totalNumberOfReturnedTests / GLOBAL_PAGINATION_FETCH_LIMIT
      )}
      value={queryParams.activePage}
      hideWithOnePage
      withControls={false}
      onChange={(value) =>
        updateQueryParams({
          type: 'activePage',
          payload: value,
        })
      }
      disabled={areTestsFetching || areTestsLoading}
    />
  );

  const FILTERS = (
    <TestsFilters
      areFiltersDisabled={
        areTestsFetching || areTestsLoading || totalNumberOfTests === 0
      }
      selectedFilters={selectedFilters}
      onCountryChange={(value) => {
        updateQueryParams({
          type: 'testsCountry',
          payload: value,
        });
        handleCountryChange(value);
      }}
    />
  );

  return (
    <AdminLayout
      searchInputTransKey="search-tests"
      onSearch={onSearchChange}
      numberOfSearchResults={totalNumberOfReturnedTests}
      isInformationLoading={areTestsLoading}
      isInformationFetching={areTestsFetching}
      totalNumberOfResults={totalNumberOfTests}
      filters={FILTERS}
      paginationComponent={PAGINATION_COMPONENT}
      listContent={
        <TestsListTable areTestsFetching={areTestsFetching} tests={tests} />
      }
      numberOFSelectedFilters={numberOfSelectedFilters}
      onClearFilters={() => {
        resetFilters();
        resetSearchParamsExceptSearchWord();
      }}
    />
  );
};

export default TestsTab;
