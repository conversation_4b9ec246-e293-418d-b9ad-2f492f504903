import countries from 'i18n-iso-countries';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';

import SelectDropdown from '@/components/SelectDropdown/SelectDropdown';
import Text from '@/components/Text/Text';

import useAdminTestsCounties from '../../hooks/useAdminTestsCounties';
import s from './TestsFilters.module.css';

type FiltersType = {
  country: string;
};

type TestsFiltersProps = {
  selectedFilters: FiltersType;
  areFiltersDisabled: boolean;
  onCountryChange: (value: FiltersType['country']) => void;
};

const TestsFilters = ({
  areFiltersDisabled = false,
  onCountryChange,
  selectedFilters,
}: TestsFiltersProps): JSX.Element => {
  const { i18n, t } = useTranslation();

  const { adminTestsCountries, areTestsCountriesLoading } =
    useAdminTestsCounties();

  return (
    <div className={s.labelAndFieldWrapper}>
      <Text transKey="countries" type="body2" isBold fw={600} />

      <SelectDropdown
        key={adminTestsCountries.join(',')}
        value={selectedFilters.country}
        clearable
        data={adminTestsCountries.map((lang) => ({
          label: countries.getName(lang, i18n.language) || '',
          value: lang,
        }))}
        isDisabled={areTestsCountriesLoading || areFiltersDisabled}
        onChange={(value) => onCountryChange(value)}
        placeholder={t('country')}
      />
    </div>
  );
};

export default TestsFilters;
