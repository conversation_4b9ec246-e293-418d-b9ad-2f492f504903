import { Box, Menu } from '@mantine/core';
import { JSX } from 'react';
import { useTranslation } from 'react-i18next';
import { BsEyeFill } from 'react-icons/bs';

import Icon from '@/components/Icon/Icon';

type TestsActionsMenuProps = {
  onPercentilesView: () => void;
};

const TestsActionsMenu = ({
  onPercentilesView,
}: TestsActionsMenuProps): JSX.Element => {
  const { t } = useTranslation();

  return (
    <Menu shadow="md">
      <Menu.Target>
        <Box>
          <Icon name="MenuDotsSvg" color="turquoise" w={18} h={18} hasCursor />
        </Box>
      </Menu.Target>

      <Menu.Dropdown className="menuDropdown">
        <Menu.Item
          onClick={onPercentilesView}
          leftSection={<BsEyeFill size={19} />}
        >
          {t('percentiles')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default TestsActionsMenu;
