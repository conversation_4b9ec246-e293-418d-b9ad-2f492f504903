import { Divider, Flex } from '@mantine/core';
import { Dropzone, MIME_TYPES } from '@mantine/dropzone';
import { getName } from 'i18n-iso-countries';
import React, { JSX, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BiArrowBack } from 'react-icons/bi';
import { FaFileAlt } from 'react-icons/fa';
import { VscCloudDownload } from 'react-icons/vsc';

import Button from '@/components/Button/Button';
import Card from '@/components/Card/Card';
import CloseButton from '@/components/CloseButton/CloseButton';
import Icon from '@/components/Icon/Icon';
import PrimaryModal from '@/components/Modals/PrimaryModal/PrimaryModal';
import Text from '@/components/Text/Text';
import { TranslationKeysType } from '@/types/common';

import useAdminTestsQuery from '../../hooks/useAdminTestsQuery';
import s from './ShowPercentilesTestPanel.module.css';
import useDownloadPercentile from './useDownloadPercentile';
import useUploadPercentileFile from './useUploadPercentileFile';

type ShowPercentilesTestPanelProps = {
  data: {
    type: string;
    uploadedAt: string;
  }[];
  isOpen: boolean;
  onClose: () => void;
  testId: string;
  country: string;
  testName: string;
};

export type PercentilesType = 'domains' | 'percentiles' | 'tolerance';

const DISPLAYED_ENTRIES = ['domains', 'percentiles', 'tolerance'];

const ShowPercentilesTestPanel = ({
  country,
  data,
  isOpen,
  onClose,
  testId,
  testName,
}: ShowPercentilesTestPanelProps): JSX.Element => {
  const { i18n, t } = useTranslation();

  const [uploadType, setUploadType] = useState<PercentilesType | null>(null);

  const dropZoneRef = useRef<() => void>(null);

  const { invalidateTests } = useAdminTestsQuery();

  const {
    fileImportError,
    filesToUpload,
    handleFileImportError,
    handleFilesImport,
    handleFileUpload,
    isLoading,
    resetFile,
  } = useUploadPercentileFile({
    onFinishUpload: () => {
      setUploadType(null);

      invalidateTests();
    },
    type: uploadType || null,
    testId,
  });

  const { isDownloading, onDownloadPercentile } = useDownloadPercentile({
    testId,
  });

  const onBackButtonClick = () => {
    setUploadType(null);
    // clear file
    resetFile();
  };

  return (
    <PrimaryModal
      isOpen={isOpen}
      content={
        <Card
          size="xl"
          bg="gray50"
          className={`${s.wrapper} ${uploadType === null ? s.list : s.upload}`}
        >
          {uploadType === null ? (
            <>
              {/* HEADER */}
              <div className={s.header}>
                <div className={s.textWrapper}>
                  <Text transKey="percentiles" type="h3" />

                  <Flex gap="var(--spacing-sm)" mt="var(--spacing-sm)">
                    <Text untranslatedText={testName} />

                    <Text transKey="for" />

                    <Text untranslatedText={getName(country, i18n.language)} />
                  </Flex>
                </div>

                <CloseButton onClick={onClose} variant="outlined" />
              </div>

              <Card bg="white" radius="xs" size="3xl">
                {DISPLAYED_ENTRIES.map((item) => {
                  const uploadedAt = data.find(
                    (test) => test.type === item
                  )?.uploadedAt;

                  return (
                    <React.Fragment key={item}>
                      <Flex justify="space-between">
                        <div>
                          <Flex align="center" mb={16}>
                            <Text
                              transKey={item as TranslationKeysType}
                              type="h4"
                              fw={400}
                              mr={16}
                            />

                            {uploadedAt && (
                              <Icon
                                name="CheckMarkSvg"
                                color="green"
                                size="md"
                              />
                            )}
                          </Flex>

                          <Flex>
                            <Text transKey="uploaded-at" type="body1" mr={16} />

                            <Text untranslatedText=":" type="body1" mr={16} />

                            <Text
                              untranslatedText={uploadedAt || '-'}
                              type="body1"
                              fw={300}
                            />

                            {uploadedAt && (
                              <VscCloudDownload
                                fontSize={24}
                                className={s.downloadButton}
                                style={{
                                  cursor: isDownloading
                                    ? 'not-allowed'
                                    : 'pointer',
                                  opacity: isDownloading ? 0.6 : 1,
                                }}
                                onClick={
                                  isDownloading
                                    ? undefined
                                    : () =>
                                        onDownloadPercentile({
                                          type: item as PercentilesType,
                                        })
                                }
                              />
                            )}
                          </Flex>
                        </div>

                        <Flex align="center">
                          <Button
                            transKey="upload-capital"
                            onClick={() =>
                              setUploadType(item as PercentilesType)
                            }
                          />
                        </Flex>
                      </Flex>

                      <Divider my="md" m="40px 0px" />
                    </React.Fragment>
                  );
                })}
              </Card>
            </>
          ) : (
            <>
              {/* HEADER */}
              <div className={s.header}>
                <Flex align="center">
                  <BiArrowBack
                    fontSize={24}
                    color="black"
                    className={s.backButton}
                    onClick={onBackButtonClick}
                  />

                  <Text
                    transKey="upload-percentiles"
                    transVariables={{ percentileType: t(uploadType) }}
                    type="h3"
                    fw={250}
                  />
                </Flex>

                <Flex align="center">
                  <Button
                    transKey="upload-capital"
                    isDisabled={!filesToUpload}
                    onClick={handleFileUpload}
                    isLoading={isLoading}
                  />
                </Flex>
              </div>

              <Card bg="white" radius="xs" size="3xl">
                <Dropzone
                  onDrop={(files) => {
                    handleFilesImport(files);
                  }}
                  multiple={uploadType === 'percentiles'}
                  onReject={(file) => {
                    handleFileImportError(
                      file[0].errors[0].code === 'file-too-large'
                        ? 'file-too-large'
                        : 'file-invalid-type'
                    );
                  }}
                  // max size 25 mb
                  maxSize={25 * 1024 ** 2}
                  className={s.dropzone}
                  accept={[MIME_TYPES.csv]}
                  disabled={isLoading}
                  openRef={dropZoneRef}
                  onChange={() => {}}
                  activateOnClick
                  useFsAccessApi
                  h={200}
                  classNames={{
                    root: s.dropzone,
                  }}
                >
                  {fileImportError === 'file-invalid-type' && (
                    <>
                      <Text
                        transKey="unsupported-file-type"
                        align="center"
                        color="gray"
                        type="body1"
                        mb={12}
                      />

                      <Text
                        transKey="supported-file-types"
                        align="center"
                        color="gray"
                        type="body2"
                      />
                    </>
                  )}

                  {fileImportError === 'file-too-large' && (
                    <>
                      <Text
                        transKey="file-too-large"
                        align="center"
                        color="gray"
                        type="body1"
                        mb={12}
                      />

                      <Text
                        transKey="max-file-size-25mb"
                        align="center"
                        color="gray"
                        type="body2"
                      />
                    </>
                  )}

                  {filesToUpload && filesToUpload.length > 0 && (
                    <Flex gap="md" wrap="wrap">
                      {filesToUpload.map((fileToUpload, index) => (
                        <div
                          className={s.iconWrapper}
                          key={`file-${fileToUpload}-${index}`}
                        >
                          <FaFileAlt size={50} color="var(--color-blue)" />

                          <Text
                            untranslatedText={fileToUpload.name}
                            type="label"
                            fw={500}
                            mt={6}
                          />
                        </div>
                      ))}
                    </Flex>
                  )}

                  {!filesToUpload && !fileImportError && (
                    <Flex
                      align="center"
                      justify="center"
                      direction="column"
                      h="100%"
                      mih="165"
                    >
                      <Text
                        transKey="click-capital"
                        type="button"
                        color="blue"
                        align="center"
                      />
                      <Text
                        transKey="or-capital"
                        type="button"
                        color="blue"
                        align="center"
                      />
                      <Text
                        transKey="drag-file-here-capital"
                        type="button"
                        color="blue"
                        align="center"
                      />
                    </Flex>
                  )}
                </Dropzone>
              </Card>
            </>
          )}
        </Card>
      }
    />
  );
};

export default ShowPercentilesTestPanel;
