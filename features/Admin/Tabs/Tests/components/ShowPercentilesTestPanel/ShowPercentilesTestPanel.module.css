.wrapper {
  /* max-width: 1200px; */
  max-width: 1000px;
  width: 100%;
  height: 100%;
  transition: all 0.4s ease-in-out;
}

.list {
  min-height: 610px;
  max-height: 610px;
}

.upload {
  min-height: 440px;
  max-height: 580px;
  max-width: 800px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2xl);
}

.textWrapper {
  margin-right: var(--spacing-xl);
}

.backButton {
  cursor: pointer;
  margin-right: var(--spacing-md);
}

.dropzone {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.downloadButton {
  cursor: pointer;
  margin-left: var(--spacing-md);
}

.downloadButton:hover {
  color: var(--color-blue);
}
