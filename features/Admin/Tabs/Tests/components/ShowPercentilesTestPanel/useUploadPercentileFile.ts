/* eslint-disable react-hooks/exhaustive-deps */
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { GLOBAL_ERRORS } from '@/common/errors';
import RESULTS from '@/services/results';

const useUploadPercentileFile = ({
  onFinishUpload,
  testId,
  type,
}: {
  onFinishUpload: () => void;
  testId: string;
  type: 'domains' | 'percentiles' | 'tolerance' | null;
}) => {
  const { t } = useTranslation();

  const [filesToUpload, setFilesToUpload] = useState<File[] | null>(null);
  const [fileImportError, setFileImportError] = useState<
    'file-invalid-type' | 'file-too-large' | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);

  const resetFile = () => {
    setFilesToUpload(null);
    setFileImportError(null);
  };

  const onFinishUploadFiles = () => {
    onFinishUpload();
    resetFile();
    setIsLoading(false);
  };

  const verifyUploadWithBEMutation = useMutation({
    mutationFn: RESULTS.UPDATE_BE_ON_FILE_UPLOAD,
    onError: (error) => {
      resetFile();

      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },

    onSuccess: () => {
      onFinishUploadFiles();
    },
  });

  // Get presigned URL mutation
  const getPresignedUrlMutation = useMutation({
    mutationFn: RESULTS.GET_PRELOAD_URL,
    onError: (error) => {
      resetFile();

      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  // Upload file to presigned URL
  const uploadFileMutation = useMutation({
    mutationFn: RESULTS.UPLOAD_PERCENTILES,
    onError: (error) => {
      resetFile();

      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    },
  });

  const handleFileUpload = async () => {
    if (!filesToUpload || filesToUpload.length === 0) return;

    setIsLoading(true);

    try {
      await Promise.all(
        Array.from(filesToUpload).map(async (file) => {
          const gradeName = file.name.includes('_')
            ? file.name
                .split('_')[1]
                .replace(/\.csv$/i, '')
                .toLowerCase()
            : file.name.replace(/\.csv$/i, '').toLowerCase();

          const presignedResult = await getPresignedUrlMutation.mutateAsync({
            testId,
            type,
            ...(type === 'percentiles' && { grade: gradeName }),
          });

          if (!presignedResult?.url) {
            throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
          }

          await uploadFileMutation.mutateAsync({
            preSignedUrl: presignedResult.url,
            file,
          });
        })
      );

      await verifyUploadWithBEMutation.mutate({
        testId,
        type,
      });
    } catch (error: any) {
      notifications.show({
        message: t(error.message),
        color: 'red',
      });
    }
  };

  const handleFilesImport = (files: File[]) => {
    if (fileImportError) {
      setFileImportError(null);
    }

    setFilesToUpload(files);
  };

  const handleFileImportError = (
    errorType: 'file-too-large' | 'file-invalid-type'
  ) => {
    setFileImportError(errorType);
  };

  return {
    filesToUpload,
    isLoading,
    handleFilesImport,
    fileImportError,
    handleFileUpload,
    handleFileImportError,
    resetFile,
  };
};

export default useUploadPercentileFile;
