/* eslint-disable no-promise-executor-return */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

import RESULTS from '@/services/results';

import { PercentilesType } from './ShowPercentilesTestPanel';

type UseDownloadPercentileProps = {
  testId: string;
};

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Renamed the function cause i create a new helper function in common/helpers.ts called downloadFile that can download pdf and excel files
const downloadFileFromUrl = (url: string) => {
  const link = document.createElement('a');
  link.href = url;
  link.download = url.split('/').pop()?.split('?')[0] || '';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const useDownloadPercentile = ({ testId }: UseDownloadPercentileProps) => {
  const { t } = useTranslation();

  const getDownloadUrl = useMutation({
    mutationFn: RESULTS.GET_DOWNLOAD_URL,
    onError: (error: any) => {
      notifications.show({
        message: t(error?.message),
        color: 'red',
      });
    },

    onSuccess: async (data: { url: string }[] | undefined) => {
      if (!data || data.length === 0) {
        notifications.show({
          message: t('no-files-to-download'),
          color: 'yellow',
        });
        return;
      }

      for (const file of data) {
        downloadFileFromUrl(file.url);
        await delay(500); // delay between downloads
      }
    },
  });

  const onDownloadPercentile = ({ type }: { type: PercentilesType }) => {
    getDownloadUrl.mutate({
      testId,
      type,
    });
  };

  return {
    onDownloadPercentile,
    isDownloading: getDownloadUrl.isPending,
  };
};

export default useDownloadPercentile;
