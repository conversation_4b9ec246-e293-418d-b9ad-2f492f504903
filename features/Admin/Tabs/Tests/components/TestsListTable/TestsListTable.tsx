/* eslint-disable react-hooks/exhaustive-deps */
import { Flex, RingProgress, Table, TableData } from '@mantine/core';
import countries from 'i18n-iso-countries';
import { getLangNameFromCode } from 'language-name-map';
import { JSX, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IoMdCheckmark, IoMdCheckmarkCircleOutline } from 'react-icons/io';

import Text from '@/components/Text/Text';
import { AdminTestsPaginatedResponseType } from '@/types/common';

import ShowPercentilesTestPanel from '../ShowPercentilesTestPanel/ShowPercentilesTestPanel';
import TestsActionsMenu from '../TestsActionsMenu/TestsActionsMenu';

type TestActionsType = 'percentiles_view' | null;

type SelectedOptionType = {
  test: AdminTestsPaginatedResponseType['results'][number] | null;
  action: TestActionsType;
};

type TestsListTableProps = {
  areTestsFetching: boolean;
  tests: AdminTestsPaginatedResponseType['results'];
};

const TestsListTable = ({
  areTestsFetching,

  tests,
}: TestsListTableProps): JSX.Element => {
  const { i18n, t } = useTranslation();

  const [selectedOption, setSelectedOption] = useState<SelectedOptionType>({
    test: null,
    action: null,
  });

  const getListDateTime = (timestamp: string) => {
    // Create a Date object from the timestamp
    const date = new Date(timestamp);

    // Extract the components of the date
    const month = date
      .toLocaleString('default', { month: 'long' })
      .slice(0, 3)
      .toLowerCase();
    const day = date.getDate();
    const year = String(date.getFullYear()).slice(2); // Get the last two digits of the year
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0'); // Pad minutes with leading zero if needed

    const hoursAndMinutes = hours && minutes ? `@ ${hours}:${minutes}` : '';

    // Format the result
    return `${t(month)} ${day}  ‘${year} ${hoursAndMinutes}`;
  };

  const TABLE_HEAD = useMemo(() => {
    return [
      t('name-capital'),
      t('type-capital'),
      t('country-capital'),
      t('language-capital'),
      t('uploaded-capital'),
      t('data-capital'),
      t('store-capital'),
      '',
    ];
  }, [t]);

  const TABLE_BODY = useMemo(() => {
    return tests.map((test) => [
      test.name,
      test.type,
      countries.getName(test.country, i18n.language) || '',
      getLangNameFromCode(test.language)?.native || '',
      test.createdAt ? getListDateTime(test.createdAt) : '-',
      test?.readiness !== null ? (
        <RingProgress
          id={test.id}
          size={40}
          thickness={3}
          label={
            test.readiness === 1 ? (
              <Flex justify="center" align="center">
                <IoMdCheckmark size={20} color="var(--color-green)" />
              </Flex>
            ) : (
              <Flex align="center" justify="center">
                <Text
                  untranslatedText={`${test.readiness * 100}`}
                  type="label"
                  align="center"
                  isBold
                />
                <span
                  style={{
                    fontSize: '8px',
                    fontWeight: 700,
                  }}
                >
                  %
                </span>
              </Flex>
            )
          }
          sections={[
            {
              value: test.readiness * 100,
              color:
                test.readiness === 1
                  ? 'var(--color-green)'
                  : `var(--color-yellow)`,
            },
          ]}
        />
      ) : (
        '-'
      ),
      test.available ? (
        <Flex align="center">
          <IoMdCheckmarkCircleOutline size={20} color="var(--color-green)" />
        </Flex>
      ) : (
        '-'
      ),
      <TestsActionsMenu
        key={test.id}
        onPercentilesView={() =>
          setSelectedOption({
            test,
            action: 'percentiles_view',
          })
        }
      />,
    ]);
  }, [tests]);

  const tableData: TableData = {
    head: TABLE_HEAD,
    body: TABLE_BODY,
  };

  const TABLE_VIEW_COMPONENT = (
    <Table
      data={tableData}
      stickyHeader
      stickyHeaderOffset={-1}
      highlightOnHover
      styles={{
        thead: {
          border: 'none',
        },
        table: {
          opacity: areTestsFetching ? 0.5 : 1,
          pointerEvents: areTestsFetching ? 'none' : 'auto',
        },
      }}
    />
  );

  // update selected option on tests data invalidation
  useEffect(() => {
    setSelectedOption((prev) => ({
      test: tests.find((test) => test.id === prev.test?.id) || null,
      action: prev.action,
    }));
  }, [tests]);

  return (
    <>
      {TABLE_VIEW_COMPONENT}

      {/* Show Percentiles - using data from the list - no call */}
      <ShowPercentilesTestPanel
        data={
          selectedOption.test?.data?.map((test) => ({
            type: test.type,
            uploadedAt: test.uploadedAt ? getListDateTime(test.uploadedAt) : '',
          })) || []
        }
        isOpen={
          selectedOption.action === 'percentiles_view' &&
          selectedOption.test !== null
        }
        onClose={() =>
          setSelectedOption({
            test: null,
            action: null,
          })
        }
        testId={selectedOption.test?.id || ''}
        country={selectedOption.test?.country || ''}
        testName={selectedOption.test?.name || ''}
      />
    </>
  );
};

export default TestsListTable;
