import { useQuery } from '@tanstack/react-query';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_TESTS from '@/services/admin/tests';

const useAdminTestsCounties = (testType?: string, enabled = true) => {
  const { data, isFetching, isLoading } = useQuery<string[] | null>({
    queryKey: [QUERY_KEYS.ADMIN_TESTS_COUNTRIES, testType],
    queryFn: () => ADMIN_TESTS.GET_TESTS_COUNTRIES(),
    enabled,
    staleTime: 1000 * 60 * 60, // 60 minutes
  });

  return {
    adminTestsCountries: data || [],
    areTestsCountriesLoading: isLoading,
    areTestsCountriesFetching: isFetching,
  };
};

export default useAdminTestsCounties;
