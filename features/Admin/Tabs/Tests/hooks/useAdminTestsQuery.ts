import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

import QUERY_KEYS from '@/common/queryKeys';
import ADMIN_TESTS from '@/services/admin/tests';
import { AdminTestsPaginatedResponseType } from '@/types/common';

import { QueryParamsStateType } from './useAdminTestsQueryParams';

const useAdminTestsQuery = (queryParams?: QueryParamsStateType) => {
  const queryClient = useQueryClient();

  const queryKey = useMemo(
    () => [QUERY_KEYS.ADMIN_TESTS_PAGINATED_LIST, queryParams],
    [queryParams]
  );

  const { data, isFetching, isLoading } =
    useQuery<AdminTestsPaginatedResponseType | null>({
      queryKey,
      queryFn: () => (queryParams ? ADMIN_TESTS.GET_TESTS(queryParams) : null),
      placeholderData: keepPreviousData,
      staleTime: 1000 * 60 * 10, // 10 minutes
    });

  const invalidateTests = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ADMIN_TESTS_PAGINATED_LIST],
    });
  }, [queryClient]);

  const tests = Array.isArray(data?.results) ? data.results : [];

  return {
    tests,
    areTestsLoading: isLoading,
    areTestsFetching: isFetching,
    invalidateTests,
    totalNumberOfTests: data?.totalCount || 0,
    totalNumberOfReturnedTests: data?.count || 0,
  };
};

export default useAdminTestsQuery;
