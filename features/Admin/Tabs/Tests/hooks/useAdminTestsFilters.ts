import { useState } from 'react';

// import { TestsQueryParamsStateType } from '@/types/queryParams/tests';

type FiltersType = {
  country: string;
};

const INITIAL_FILTERS: FiltersType = {
  country: '',
};

const useAdminTestsFilters = () => {
  const [selectedFilters, setSelectedFilters] =
    useState<FiltersType>(INITIAL_FILTERS);

  const totalNumberOfSelectedFilters =
    Object.values(selectedFilters).filter(Boolean).length;

  const handleCountryChange = (value: FiltersType['country']) => {
    setSelectedFilters((prev) => ({
      ...prev,
      country: value || '',
    }));
  };

  return {
    selectedFilters,
    numberOfSelectedFilters: totalNumberOfSelectedFilters,
    handleCountryChange,
    resetFilters: () => {
      setSelectedFilters(INITIAL_FILTERS);
    },
  };
};

export default useAdminTestsFilters;
