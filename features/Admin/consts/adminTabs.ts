// ADD A NEW TAB HERE NY ONLY MODIFYING THE TABS OBJECT
export const TABS = [
  {
    label: 'users-capital',
    value: 'users',
  },
  {
    label: 'studies-capital',
    value: 'studies',
  },
  {
    label: 'schools-capital',
    value: 'schools',
  },
  {
    label: 'tests-capital',
    value: 'tests',
  },
] as const;

export const ADMIN_TAB_OPTIONS = TABS.reduce(
  (acc, tab) => {
    acc[tab.value.toUpperCase()] = tab.value;
    return acc;
  },
  {} as { [key in (typeof TABS)[number]['value'] as string]: string }
);
