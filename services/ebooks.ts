import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import {
  GLOBAL_ERRORS,
  PURCHASES_ERRORS,
  STORE_BOOK_ERRORS,
} from '@/common/errors';
import request from '@/modules/api';
import {
  CertificationsProductsType,
  LicensesProductsType,
} from '@/types/common';
import {
  CERTIFICATIONS_PRODUCTS_SCHEMA,
  LICENSES_PRODUCT_SCHEMA,
  PRODUCT_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/products';

const getEbooksID = async (bookId: string): Promise<any | null> => {
  const res = await request(
    `ebooks/${bookId}`
    // 'GET',
    // {},
    // LICENSES_PRODUCT_SCHEMA
  );

  // TODO - Remind BE to explain the error codes - Not in swagger
  if (res.hasError) {
    throw new Error('unexpected-error');
  }

  return res.data;
};

const getEbooksIsbnIsbnGuides = async (isbn: string): Promise<any | null> => {
  const res = await request(
    `ebooks/isbn/${isbn}/guides`
    // 'GET',
    // {},
    // LICENSES_PRODUCT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(STORE_BOOK_ERRORS.GUIDES_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getEbooksIsbnIsbn = async (isbn: string): Promise<any | null> => {
  const res = await request(
    `ebooks/isbn/${isbn}`
    // 'GET',
    // {},
    // LICENSES_PRODUCT_SCHEMA
  );

  if (res.hasError) {
    throw new Error('unexpected-error');
  }

  return res.data;
};

// /ebooks/isbn/{isbn}/content/download-url

const getEbooksIsbnIsbnContentDownloadUrl = async (
  isbn: string
): Promise<any | null> => {
  const res = await request(
    `ebooks/isbn/${isbn}/content/download-url`
    // 'GET',
    // {},
    // LICENSES_PRODUCT_SCHEMA
  );

  if (res.hasError) {
    throw new Error('unexpected-error');
  }

  return res.data;
};

const EBOOKS = {
  GET_EBOOKS_ID: getEbooksID,
  GET_EBOOKS_ISBN_GUIDES: getEbooksIsbnIsbnGuides,
  GET_EBOOK_DETAILS_BY_ISBN: getEbooksIsbnIsbn,
  GET_EBOOK_DOWNLOAD_URL_BY_ISBN: getEbooksIsbnIsbnContentDownloadUrl,
};

export default EBOOKS;
