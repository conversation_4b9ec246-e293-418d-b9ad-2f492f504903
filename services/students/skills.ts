import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { GET_DEFAULT_STUDENT_SKILLS_SCHEMA } from '@/zod/responseSchemas/students/skills';

const getStudentsSkillsDefaults = async (): Promise<any | null> => {
  const res = await request(
    BASE_ENDPOINT_PATHS.STUDENTS_SKILLS_DEFAULTS,
    'GET',
    {},
    GET_DEFAULT_STUDENT_SKILLS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const STUDENTS_SKILLS = {
  GET_DEFAULT_STUDENT_SKILLS: getStudentsSkillsDefaults,
};

export default STUDENTS_SKILLS;
