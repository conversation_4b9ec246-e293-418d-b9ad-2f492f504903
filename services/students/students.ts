import { z } from 'zod';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, STUDENTS_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { BasePaginationType, SupportedSchoolGradesType } from '@/types/common';

const postStudentsStudent = async (student: any) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_STUDENT}`,
    'POST',
    student,
    z.object({
      id: z.string(),
    })
  );

  if (res.hasError) {
    if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
      switch (res.cause) {
        case 'StudentCodeAlreadyExists':
          throw new Error(STUDENTS_ERRORS.STUDENT_CODE_ALREADY_EXISTS);
        default:
          throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getStudentsStudent = async ({
  grades,
  groupName,
  limit,
  page,
  query,
}: BasePaginationType & {
  groupName?: string;
  grades: SupportedSchoolGradesType[];
}): Promise<any | null> => {
  const studentsList = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_STUDENT}?limit=${limit}&page=${page}${query ? `&query=${query}` : ''}${groupName ? `&group=${groupName}` : ''}${grades.length ? `&grades=${grades.join(',')}` : ''}`,
    'GET',
    {}
    // GET_SCHOOL_STUDENTS_SCHEMA
  );

  if (studentsList.hasError) {
    switch (studentsList.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return studentsList.data;
};

const getStudentsStudentId = async (studentId: string): Promise<any | null> => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_STUDENT}/${studentId}`,
    'GET'
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const deleteStudentsStudent = async (studentsToDelete: string[]) => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_STUDENT}`,
    'DELETE',
    { studentIds: studentsToDelete }
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const putStudentsStudentStudentId = async (payload: any, studentId: string) => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_STUDENT}/${studentId}`,
    'PUT',
    payload
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(STUDENTS_ERRORS.STUDENT_CODE_ALREADY_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const INDEPENDENT_TEACHER_STUDENTS = {
  CREATE_NEW_STUDENT: postStudentsStudent,
  GET_STUDENTS: getStudentsStudent,
  GET_SCHOOL_STUDENT_DETAILS_BY_ID: getStudentsStudentId,
  DELETE_STUDENTS: deleteStudentsStudent,
  UPDATE_STUDENT: putStudentsStudentStudentId,
};

export default INDEPENDENT_TEACHER_STUDENTS;
