import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, GROUPS_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  AddGroupFormType,
  AddGroupResponseType,
  GroupDetailsType,
} from '@/types/common';
import {
  ADD_GROUP_RESPONSE_SCHEMA,
  GET_GROUPS_SCHEMA,
  GROUP_DETAILS_SCHEMA,
} from '@/zod/responseSchemas/students/groups';

const getStudentsGroups = async () => {
  const groupsList = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}?limit=100`,
    'GET',
    {},
    GET_GROUPS_SCHEMA
  );

  if (groupsList.hasError) {
    switch (groupsList.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GROUPS_ERRORS.GROUP_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GROUPS_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return groupsList.data;
};

const postStudentsGroups = async (
  data: AddGroupFormType
): Promise<AddGroupResponseType | null> => {
  const addGroupRes = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}`,
    'POST',
    data,
    ADD_GROUP_RESPONSE_SCHEMA
  );

  if (addGroupRes.hasError) {
    switch (addGroupRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GROUPS_ERRORS.GROUP_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GROUPS_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GROUPS_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GROUPS_ERRORS.GROUP_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return addGroupRes.data;
};

const putStudentsGroupsGroupId = async ({
  description,
  groupId,
  name,
}: {
  groupId: string;
  name: AddGroupFormType['name'];
  description?: AddGroupFormType['description'];
}) => {
  const editGroupRes = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}/${groupId}`,
    'PUT',
    {
      name,
      description,
    }
  );

  if (editGroupRes.hasError) {
    switch (editGroupRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GROUPS_ERRORS.GROUP_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GROUPS_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GROUPS_ERRORS.GROUP_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const deleteStudentsGroupsGroupId = async ({
  groupId,
}: {
  groupId: string;
}) => {
  const deleteGroupRes = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}/${groupId}`,
    'DELETE',
    {}
  );

  if (deleteGroupRes.hasError) {
    switch (deleteGroupRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GROUPS_ERRORS.GROUP_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GROUPS_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const getStudentsGroupsGroupId = async (
  groupId: string
): Promise<GroupDetailsType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}/${groupId}`,
    'GET',
    {},
    GROUP_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GROUPS_ERRORS.GROUP_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GROUPS_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const patchStudentsGroupsGroupIdRemove = async ({
  groupId,
  studentsList,
}: {
  studentsList: string[];
  groupId: string;
}) => {
  const deleteClassRes = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}/${groupId}/remove`,
    'PATCH',
    { studentIds: studentsList }
  );

  if (deleteClassRes.hasError) {
    switch (deleteClassRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GROUPS_ERRORS.GROUP_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GROUPS_ERRORS.BAD_REQUEST);

      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const patchStudentsGroupsGroupIdAssign = async ({
  groupId,
  studentIds,
}: {
  groupId: string;
  studentIds: string[];
}) => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.STUDENTS_GROUPS}/${groupId}/assign`,
    'PATCH',
    {
      studentIds,
    }
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const STUDENTS_GROUPS = {
  GET_GROUPS: getStudentsGroups,
  ADD_GROUP: postStudentsGroups,
  UPDATE_GROUP: putStudentsGroupsGroupId,
  DELETE_GROUP: deleteStudentsGroupsGroupId,
  GET_GROUP_DETAILS: getStudentsGroupsGroupId,
  REMOVE_STUDENTS_FROM_GROUP: patchStudentsGroupsGroupIdRemove,
  ASSIGN_STUDENTS_TO_GROUP: patchStudentsGroupsGroupIdAssign,
};

export default STUDENTS_GROUPS;
