import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, TEST_SESSION_REPORT_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  PresignedUrlResponseType,
  TestScoresResultsType,
} from '@/types/common';
import { GET_UPLOAD_URL_SCHEMA } from '@/zod/responseSchemas/data';
import {
  TEST_ERROR_RESULTS_SCHEMA,
  TEST_SCORES_RESULT_SCHEMA,
} from '@/zod/responseSchemas/results';

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const getResultsSessionSessionIdScores = async ({
  refresh,
  testId,
}: {
  testId: string;
  refresh?: boolean;
}): Promise<TestScoresResultsType | null> => {
  if (!testId) return null;

  const response = await request(
    `${BASE_ENDPOINT_PATHS.RESULTS}/${testId}/scores${refresh ? '?refresh=true' : ''}`,
    'GET',
    {},
    TEST_SCORES_RESULT_SCHEMA
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.PRECONDITION_FAILED:
        throw new Error(TEST_SESSION_REPORT_ERRORS.TEST_REPORT_IS_NOT_READY);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const getResultsSessionsSessionIdErrors = async ({
  limit,
  page,
  testId,
}: {
  testId: string;
  limit: number;
  page: number;
}) => {
  if (!testId) return null;

  const response = await request(
    `${BASE_ENDPOINT_PATHS.RESULTS}/${testId}/errors?limit=${limit}&page=${page}`,
    'GET',
    {},
    TEST_ERROR_RESULTS_SCHEMA
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const downloadScoresPdf = async ({
  sessionId,
  summary,
}: {
  sessionId: string;
  summary: string;
}): Promise<BlobPart | null> => {
  const fileResponse = await axios.post(
    `${BASE_API_URL}/${BASE_ENDPOINT_PATHS.RESULTS}/${sessionId}/scores/download`,
    { summary },
    {
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
      responseType: 'blob',
    }
  );

  if (!fileResponse.data) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return fileResponse.data;
};

const getResultsSessionSessionIdScoresDownloadOpen = async (
  testId: string
): Promise<{ summary?: string } | null> => {
  if (!testId) return null;

  const response = await request(
    `${BASE_ENDPOINT_PATHS.RESULTS}/${testId}/scores/download/open`
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_SESSION_REPORT_ERRORS.NO_SESSION_ID_FOUND);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data as any;
};

const getResultsPreloadUrl = async ({
  grade,
  testId,
  type,
}: {
  testId: string;
  type: 'domains' | 'percentiles' | 'tolerance' | null;
  grade?: string;
}): Promise<PresignedUrlResponseType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PERCENTILES}/${testId}/data/${type}/upload-url${
      grade ? `?grade=${grade}` : ''
    }`,
    'GET',
    {},
    GET_UPLOAD_URL_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const postPercentilesParse = async ({
  grade,
  testId,
  type,
}: {
  testId: string;
  type: 'domains' | 'percentiles' | 'tolerance' | null;
  grade?: string;
}): Promise<PresignedUrlResponseType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PERCENTILES}/${testId}/data/${type}/parse${
      grade ? `?grade=${grade}` : ''
    }`,
    'POST'
  );

  if (res.hasError) {
    switch (res.status) {
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data as any;
};

const uploadFile = async ({
  file,
  preSignedUrl,
}: {
  preSignedUrl: string;
  file: File;
}): Promise<null> => {
  const res = await axios.put(preSignedUrl || '  ', file, {
    headers: {
      'Content-Type': 'text/csv',
    },
    withCredentials: false,
    maxBodyLength: Infinity,
  });

  if (res.status !== HTTP_STATUS_CODES.SUCCESS) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const getDownloadUrl = async ({
  testId,
  type,
}: {
  testId: string;
  type: 'domains' | 'percentiles' | 'tolerance' | null;
}): Promise<{ url: string }[] | undefined> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PERCENTILES}/${testId}/data/${type}/download-url`,
    'GET'
  );

  if (res.hasError) {
    switch (res.status) {
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return (res.data as any) || undefined;
};

const RESULTS = {
  GET_TEST_SCORES_REPORT: getResultsSessionSessionIdScores,
  GET_TEST_REPORT_ERRORS: getResultsSessionsSessionIdErrors,
  GET_SCORES_PDF: downloadScoresPdf,
  GET_TEST_SUMMARY: getResultsSessionSessionIdScoresDownloadOpen,
  GET_PRELOAD_URL: getResultsPreloadUrl,
  UPDATE_BE_ON_FILE_UPLOAD: postPercentilesParse,
  UPLOAD_PERCENTILES: uploadFile,
  GET_DOWNLOAD_URL: getDownloadUrl,
};

export default RESULTS;
