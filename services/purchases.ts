import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, PURCHASES_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  PAYMENT_INTENT_SCHEMA,
  PURCHASES_HISTORY_SCHEMA,
  VAT_COUNTRIES_LIST_SCHEMA,
} from '@/zod/responseSchemas/purchases';

// * all error Handling is done here from the FE side - its according to swagger
const getPurchases = async ({
  limit,
  page,
  userId,
}: {
  limit: number;
  page: number;
  userId?: string;
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PURCHASES_HISTORY}?limit=${limit}&page=${page}${userId ? `&userId=${userId}` : ''}`,
    'GET',
    {},
    PURCHASES_HISTORY_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const postPurchasesCreateInvoice = async ({
  products,
}: {
  products: string[];
  vatNumber?: string | null;
  vatCountryCode?: string | null;
}) => {
  const res = await request(
    BASE_ENDPOINT_PATHS.CREATE_INVOICE,
    'POST',
    {
      products,
    },
    PAYMENT_INTENT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST: {
        throw new Error(PURCHASES_ERRORS.INVALID_VAT_NUMBER);
      }
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(PURCHASES_ERRORS.PRODUCT_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data?.clientSecret;
};

// * all error Handling is done here from the FE side - its according to swagger
const postPurchasesCreatePaymentIntent = async ({
  productId,
}: {
  productId: string;
}) => {
  const res = await request(
    BASE_ENDPOINT_PATHS.CREATE_PAYMENT_INTENT,
    'POST',
    {
      productId,
    },
    PAYMENT_INTENT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(PURCHASES_ERRORS.PRODUCT_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data?.clientSecret;
};

const getVatCountriesList = async () => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PURCHASES_HISTORY}/vat-countries`,
    'GET',
    {},
    VAT_COUNTRIES_LIST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const PURCHASES = {
  GET_PURCHASES_HISTORY: getPurchases,
  CREATE_INVOICE: postPurchasesCreateInvoice,
  CREATE_PAYMENT_INTENT: postPurchasesCreatePaymentIntent,
  GET_VAT_COUNTRIES_LIST: getVatCountriesList,
};

export default PURCHASES;
