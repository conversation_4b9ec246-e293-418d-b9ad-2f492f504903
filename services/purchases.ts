import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, PURCHASES_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  PAYMENT_INTENT_SCHEMA,
  PURCHASES_HISTORY_SCHEMA,
} from '@/zod/responseSchemas/purchases';

// * all error Handling is done here from the FE side - its according to swagger
const getPurchases = async ({
  limit,
  page,
}: {
  limit: number;
  page: number;
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.PURCHASES_HISTORY}?limit=${limit}&page=${page}`,
    'GET',
    {},
    PURCHASES_HISTORY_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const postPurchasesCreateInvoice = async ({
  products,
}: {
  products: string[];
}) => {
  const res = await request(
    BASE_ENDPOINT_PATHS.CREATE_INVOICE,
    'POST',
    {
      products,
    },
    PAYMENT_INTENT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(PURCHASES_ERRORS.PRODUCT_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data?.clientSecret;
};

// * all error Handling is done here from the FE side - its according to swagger
const postPurchasesCreatePaymentIntent = async ({
  productId,
}: {
  productId: string;
}) => {
  const res = await request(
    BASE_ENDPOINT_PATHS.CREATE_PAYMENT_INTENT,
    'POST',
    {
      productId,
    },
    PAYMENT_INTENT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(PURCHASES_ERRORS.PRODUCT_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data?.clientSecret;
};

const PURCHASES = {
  GET_PURCHASES_HISTORY: getPurchases,
  CREATE_INVOICE: postPurchasesCreateInvoice,
  CREATE_PAYMENT_INTENT: postPurchasesCreatePaymentIntent,
};

export default PURCHASES;
