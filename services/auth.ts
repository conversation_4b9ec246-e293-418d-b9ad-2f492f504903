import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { AUTH_ERRORS, GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  CompleteUserRegistrationFormValuesType,
  LoginUserFormValuesType,
  SignUpFormSchema,
  StudentWelcomeLoginDetailsType,
} from '@/types/common';
import { GET_STUDENT_LOGIN_WELCOME_DETAILS_SCHEMA } from '@/zod/responseSchemas/auth';

// * all error Handling is done here from the FE side - its according to swagger
const postAuthLogin = async ({
  email,
  password,
}: LoginUserFormValuesType): Promise<null> => {
  const requestBody = {
    username: email,
    password,
  };

  const res = await request(BASE_ENDPOINT_PATHS.LOGIN, 'POST', requestBody);

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(AUTH_ERRORS.SEND_BAD_DATA);
      case HTTP_STATUS_CODES.FORBIDDEN:
        if (res.cause === 'AuthError.StudyNotFound') {
          throw new Error(AUTH_ERRORS.LOGIN_STUDY_NOT_FOUND);
        } else if (res.cause === 'AuthError.UserNotComplete') {
          throw new Error(AUTH_ERRORS.NOT_COMPLETE_REGISTRATION);
        } else if (res.cause === 'AuthError.UserNotFound') {
          throw new Error(AUTH_ERRORS.LOGIN_SCHOOL_NOT_FOUND);
        } else if (res.cause === 'AuthError.NotVerifiedEmail') {
          throw new Error(AUTH_ERRORS.NOT_VERIFIED_EMAIL);
        } else if (res.cause === 'AuthError.SchoolNotFound') {
          throw new Error(AUTH_ERRORS.NOT_ASSIGNED_TO_SCHOOL);
        }

        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);

      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(AUTH_ERRORS.INVALID_USERNAME_OR_PASSWORD);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

type PostAuthLoginOtpType = {
  action: 'testing' | 'learning';
  session?: {
    // refers to the test-session-id
    id: string;
    state: 'active' | 'in-progress' | 'scheduled' | 'expired' | 'completed';
  };
};

const postAuthLoginOtp = async (
  conductTestCode: string
): Promise<PostAuthLoginOtpType | null> => {
  const res = await request(BASE_ENDPOINT_PATHS.LOGIN_OTP, 'POST', {
    code: conductTestCode,
  });

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(AUTH_ERRORS.SEND_BAD_DATA);
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(AUTH_ERRORS.INVALID_SIX_DIGIT_CODE);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(AUTH_ERRORS.INVALID_SIX_DIGIT_CODE);
      case HTTP_STATUS_CODES.NOT_ACCEPTABLE:
        throw new Error(AUTH_ERRORS.TEST_SESSION_NOT_ACTIVE);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data as any;
};

// * all error Handling is done here from the FE side - its according to swagger
const patchAuthCompleteRegistration = async (
  data: CompleteUserRegistrationFormValuesType
): Promise<null> => {
  const userDetails = {
    email: data.email,
    password: data.newPassword,
    currentPassword: data.temporaryPassword,
  };

  const res = await request(BASE_ENDPOINT_PATHS.SIGNUP, 'PATCH', userDetails);

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(AUTH_ERRORS.REGISTRATION_ALREADY_COMPLETED);
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(AUTH_ERRORS.INVALID_USERNAME_OR_PASSWORD);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

// * all error Handling is done here from the FE side - its according to swagger
const patchAuthAcceptInvitation = async (data: {
  invitationId: string;
  password: string;
}): Promise<null> => {
  const invitedUserDetails = {
    password: data.password,
    invitationId: data.invitationId,
  };

  const res = await request(
    BASE_ENDPOINT_PATHS.ACCEPT_INVITATION,
    'PATCH',
    invitedUserDetails
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(AUTH_ERRORS.INVITATION_ALREADY_USED);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(AUTH_ERRORS.INVITATION_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

// * all error Handling is done here from the FE side - its according to swagger
const getAuthLogout = async (): Promise<null> => {
  const res = await request(BASE_ENDPOINT_PATHS.LOGOUT);

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const postForgotPassword = async (data: { email: string }): Promise<null> => {
  const res = await request(BASE_ENDPOINT_PATHS.FORGOT_PASSWORD, 'POST', {
    username: data.email,
  });

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(AUTH_ERRORS.INVITATION_ALREADY_USED);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(AUTH_ERRORS.INVITATION_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const postResetPassword = async (data: {
  password: string;
  token: string;
}): Promise<null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.RESET_PASSWORD}?token=${data.token}`,
    'PUT',
    {
      password: data.password,
    }
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(AUTH_ERRORS.INVITATION_ALREADY_USED);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const patchAuthChangePassword = async (data: {
  currentPassword: string;
  newPassword: string;
}): Promise<null> => {
  const res = await request(BASE_ENDPOINT_PATHS.CHANGE_PASSWORD, 'PATCH', data);

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(AUTH_ERRORS.SEND_BAD_DATA);
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(AUTH_ERRORS.INVALID_PROVIDED_PASSWORD);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const postRegister = async (data: SignUpFormSchema): Promise<null> => {
  const res = await request(BASE_ENDPOINT_PATHS.REGISTER, 'POST', {
    firstName: data.firstName,
    lastName: data.lastName,
    email: data.email,
    password: data.password,
    language: data.language,
  });

  if (res.hasError) {
    if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
      throw new Error(res.cause);
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const postVerify = async (data: { code: string }): Promise<null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.VERIFY_EMAIL}?code=${data.code}`,
    'PATCH'
  );

  if (res.hasError) {
    if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
      throw new Error(res.cause);
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const getResendVerificationCode = async (userEmail: string): Promise<null> => {
  const res = await request(
    BASE_ENDPOINT_PATHS.RESEND_EMAIL_VERIFICATION_CODE,
    'POST',
    { email: userEmail }
  );

  if (res.hasError) {
    if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
      throw new Error(res.cause);
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const getAuthLearningWelcome =
  async (): Promise<StudentWelcomeLoginDetailsType | null> => {
    const res = await request(
      BASE_ENDPOINT_PATHS.STUDENT_LOGIN_WELCOME_DETAILS,
      'GET',
      {},
      GET_STUDENT_LOGIN_WELCOME_DETAILS_SCHEMA
    );

    if (res.hasError) {
      if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
        throw new Error(res.cause);
      }

      throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }

    return res.data;
  };

const AUTH = {
  LOGIN_USER: postAuthLogin,
  LOGIN_STUDENT: postAuthLoginOtp,
  LOGOUT_USER: getAuthLogout,
  COMPLETE_USER_REGISTRATION: patchAuthCompleteRegistration,
  ACCEPT_USER_INVITATION: patchAuthAcceptInvitation,
  FORGOT_PASSWORD: postForgotPassword,
  RESET_PASSWORD: postResetPassword,
  CHANGE_USER_PASSWORD: patchAuthChangePassword,
  REGISTER: postRegister,
  VERIFY_EMAIL: postVerify,
  RESEND_EMAIL_VERIFICATION_CODE: getResendVerificationCode,
  GET_STUDENT_LOGIN_WELCOME_DETAILS: getAuthLearningWelcome,
};

export default AUTH;
