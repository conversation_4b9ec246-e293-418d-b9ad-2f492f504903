import { z } from 'zod';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, INVITATIONS_ERRORS } from '@/common/errors';
import { extractErrorEmailFromBeError } from '@/common/helpers';
import request from '@/modules/api';
import { TEACHER_INVITE_DATA_SCHEMA } from '@/zod/zodFormValidationSchemas';

const handleError = (status: number) => {
  switch (status) {
    case HTTP_STATUS_CODES.FORBIDDEN:
      throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
    case HTTP_STATUS_CODES.NOT_FOUND:
      throw new Error(INVITATIONS_ERRORS.INVITATION_NOT_FOUND);
    default:
      throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }
};

// * all error Handling is done here from the FE side - its according to swagger
const postInvitations = async (
  teachersDataList: z.infer<typeof TEACHER_INVITE_DATA_SCHEMA>[]
) => {
  const trimmedTeachersDataList = teachersDataList.map((teacher) => ({
    ...teacher,
    email: teacher.email.trim(),
  }));

  const res = await request(
    `${BASE_ENDPOINT_PATHS.INVITATIONS}`,
    'POST',
    trimmedTeachersDataList
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(INVITATIONS_ERRORS.TEACHER_ALREADY_INVITED, {
          cause: extractErrorEmailFromBeError(`${res.errors}`),
        });
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(INVITATIONS_ERRORS.USERS_SCHOOL_NOT_FOUND);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(INVITATIONS_ERRORS.INVALID_TEACHER_INVITATION_DETAILS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

// * all error Handling is done here from the FE side - its according to swagger
const patchInvitationsInvitationIdRevoke = async ({
  invitationId,
}: {
  invitationId: string;
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.INVITATIONS}/${invitationId}/revoke`,
    'PATCH',
    {}
  );

  if (res.hasError) {
    handleError(res.status);
  }

  return null;
};

// * all error Handling is done here from the FE side - its according to swagger
const patchInvitationsInvitationIdResend = async ({
  invitationId,
}: {
  invitationId: string;
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.INVITATIONS}/${invitationId}/resend`,
    'PATCH',
    {}
  );

  if (res.hasError) {
    handleError(res.status);
  }

  return null;
};

const INVITATIONS = {
  INVITE_TEACHERS: postInvitations,
  REVOKE_TEACHER_INVITATION: patchInvitationsInvitationIdRevoke,
  RESEND_TEACHER_INVITATION: patchInvitationsInvitationIdResend,
};

export default INVITATIONS;
