import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { SchoolInterestFormType } from '@/types/common';

const postSchoolInterest = async (
  data: SchoolInterestFormType
): Promise<null> => {
  const res = await request(BASE_ENDPOINT_PATHS.LANDING, 'POST', data);

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

export const WEBSITE = {
  SEND_SCHOOL_INTEREST: postSchoolInterest,
};
