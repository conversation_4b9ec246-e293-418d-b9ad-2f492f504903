import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { StudiesListType } from '@/types/common';
import { STUDIES_LIST_SCHEMA } from '@/zod/responseSchemas/studies';

const getStudiesList = async (): Promise<StudiesListType | null> => {
  const res = await request(
    BASE_ENDPOINT_PATHS.STUDIES_LIST,
    'GET',
    {},
    STUDIES_LIST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      //   case HTTP_STATUS_CODES.NOT_FOUND:
      //     throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const STUDIES = {
  GET_STUDIES: getStudiesList,
};

export default STUDIES;
