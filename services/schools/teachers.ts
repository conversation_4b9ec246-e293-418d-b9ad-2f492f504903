import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, SCHOOLS_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { BasePaginationType } from '@/types/common';
import {
  GET_TEACHERS_SCHEMA,
  TEACHER_SCHEMA,
} from '@/zod/responseSchemas/schools/teachers';

const getSchoolsTeachers = async ({
  limit,
  page,
  query,
}: BasePaginationType) => {
  const teachersList = await request(
    `${BASE_ENDPOINT_PATHS.ALL_SCHOOL_TEACHERS}?limit=${limit}&page=${page}&query=${query.toLowerCase().trim()}`,
    'GET',
    {},
    GET_TEACHERS_SCHEMA
  );

  if (teachersList.hasError) {
    switch (teachersList.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return teachersList.data;
};

const deleteSchoolsTeachers = async ({ teacherId }: { teacherId: string }) => {
  const deleteTeacherRes = await request(
    `${BASE_ENDPOINT_PATHS.ALL_SCHOOL_TEACHERS}/${teacherId}`,
    'DELETE',
    {}
  );

  if (deleteTeacherRes.hasError) {
    switch (deleteTeacherRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(SCHOOLS_ERRORS.TEACHER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const getSchoolsTeachersTeacherId = async ({
  teacherId,
}: {
  teacherId: string;
}) => {
  const teacherDetails = await request(
    `${BASE_ENDPOINT_PATHS.ALL_SCHOOL_TEACHERS}/${teacherId}`,
    'GET',
    {},
    TEACHER_SCHEMA
  );

  if (teacherDetails.hasError) {
    switch (teacherDetails.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(SCHOOLS_ERRORS.TEACHER_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return teacherDetails.data;
};

const SCHOOL_TEACHERS = {
  GET_TEACHERS: getSchoolsTeachers,
  DELETE_TEACHER: deleteSchoolsTeachers,
  GET_TEACHER_DETAILS: getSchoolsTeachersTeacherId,
};

export default SCHOOL_TEACHERS;
