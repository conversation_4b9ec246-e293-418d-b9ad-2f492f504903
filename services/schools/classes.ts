import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { CLASSES_ERRORS, GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  AddClassFormType,
  AddClassResponseType,
  ClassDetailsType,
} from '@/types/common';
import {
  ADD_CLASS_RESPONSE_SCHEMA,
  CLASS_DETAILS_SCHEMA,
  GET_CLASSES_SCHEMA,
} from '@/zod/responseSchemas/schools/classes';

const postSchoolsClasses = async (
  data: AddClassFormType
): Promise<AddClassResponseType | null> => {
  const addClassRes = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}`,
    'POST',
    data,
    ADD_CLASS_RESPONSE_SCHEMA
  );

  if (addClassRes.hasError) {
    switch (addClassRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(CLASSES_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(CLASSES_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(CLASSES_ERRORS.CLASS_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return addClassRes.data;
};

const getClasses = async () => {
  // limit is 96 so it can be divided by 2 and 3 to get the correct number of columns

  const classesList = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}?limit=96`,
    'GET',
    {},
    GET_CLASSES_SCHEMA
  );

  if (classesList.hasError) {
    switch (classesList.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(CLASSES_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return classesList.data;
};

const deleteClassesClassId = async ({ classId }: { classId: string }) => {
  const deleteClassRes = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}/${classId}`,
    'DELETE'
  );

  if (deleteClassRes.hasError) {
    switch (deleteClassRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(CLASSES_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const patchSchoolsClassesClassIdAssign = async ({
  classId,
  studentIds,
}: {
  classId: string;
  studentIds: string[];
}) => {
  const assignStudentsToClassRes = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}/${classId}/assign`,
    'PATCH',
    {
      studentIds,
    }
  );

  if (assignStudentsToClassRes.hasError) {
    switch (assignStudentsToClassRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(CLASSES_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(CLASSES_ERRORS.CLASS_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const putSchoolsClassesClassId = async ({
  classId,
  grade,
  name,
}: {
  classId: string;
  name: AddClassFormType['name'];
  grade: AddClassFormType['grade'];
}) => {
  const deleteClassRes = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}/${classId}`,
    'PUT',
    {
      name,
      grade,
    }
  );

  if (deleteClassRes.hasError) {
    switch (deleteClassRes.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(CLASSES_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const patchClassesClassIdRemove = async ({
  classId,
  studentsList,
}: {
  studentsList: string[];
  classId: string;
}) => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}/${classId}/remove`,
    'PATCH',
    { studentIds: studentsList }
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(CLASSES_ERRORS.BAD_REQUEST);

      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const getSchoolsClassesClassId = async (
  classId: string
): Promise<ClassDetailsType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOL_CLASSES}/${classId}`,
    'GET',
    {},
    CLASS_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(CLASSES_ERRORS.CLASS_NOT_FOUND);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const SCHOOL_CLASSES = {
  ADD_CLASS: postSchoolsClasses,
  GET_CLASSES: getClasses,
  DELETE_CLASS: deleteClassesClassId,
  ASSIGN_SCHOOL_STUDENTS_TO_CLASS: patchSchoolsClassesClassIdAssign,
  UPDATE_CLASS: putSchoolsClassesClassId,
  REMOVE_STUDENTS_FROM_CLASS: patchClassesClassIdRemove,
  GET_CLASS_DETAILS: getSchoolsClassesClassId,
};

export default SCHOOL_CLASSES;
