import { z } from 'zod';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, SCHOOLS_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  BasePaginationType,
  PaginatedStudentList,
  StudentType,
  SupportedSchoolGradesType,
} from '@/types/common';
import { GET_SCHOOL_STUDENTS_SCHEMA } from '@/zod/responseSchemas/schools/students';

const postSchoolsStudents = async (student: StudentType) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOLS_STUDENTS}`,
    'POST',
    student,
    z.object({
      id: z.string(),
    })
  );

  if (res.hasError) {
    if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
      switch (res.cause) {
        case 'StudentCodeAlreadyExists':
          throw new Error(SCHOOLS_ERRORS.SCHOOL_STUDENT_CODE_ALREADY_EXISTS);
        default:
          throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getSchoolsStudents = async ({
  className,
  grades,
  limit,
  page,
  query,
}: BasePaginationType & {
  className?: string;
  grades: SupportedSchoolGradesType[];
}): Promise<PaginatedStudentList | null> => {
  const studentsList = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOLS_STUDENTS}?limit=${limit}&page=${page}${query ? `&query=${query}` : ''}${className ? `&class=${className}` : ''}${grades.length ? `&grades=${grades.join(',')}` : ''}`,
    'GET',
    {},
    GET_SCHOOL_STUDENTS_SCHEMA
  );

  if (studentsList.hasError) {
    switch (studentsList.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return studentsList.data;
};

const getSchoolsStudentsStudentId = async (
  studentId: string
): Promise<any | null> => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOLS_STUDENTS}/${studentId}`,
    'GET'
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const deleteSchoolsStudents = async (schoolStudentsToDelete: string[]) => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOLS_STUDENTS}`,
    'DELETE',
    { studentIds: schoolStudentsToDelete }
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const putSchoolsStudentsStudentId = async (payload: any, studentId: string) => {
  const response = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOLS_STUDENTS}/${studentId}`,
    'PUT',
    payload
  );

  if (response.hasError) {
    switch (response.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(SCHOOLS_ERRORS.SCHOOL_STUDENT_CODE_ALREADY_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return response.data;
};

const getSchoolsStudentsUnclaimed = async ({
  className,
  limit,
  page,
  query,
}: BasePaginationType & {
  className?: string;
}): Promise<PaginatedStudentList | null> => {
  const studentsList = await request(
    `${BASE_ENDPOINT_PATHS.SCHOOLS_STUDENTS_UNCLAIMED}?limit=${limit}&page=${page}${query ? `&query=${query.toLowerCase().trim()}` : ''}${className ? `&class=${className}` : ''}`,

    'GET',
    {},
    GET_SCHOOL_STUDENTS_SCHEMA
  );

  if (studentsList.hasError) {
    switch (studentsList.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return studentsList.data;
};

const patchSchoolsStudentsAssignClaim = async (studentIds: string[]) => {
  const assignClaimRes = await request(
    `${BASE_ENDPOINT_PATHS.CLAIM_SCHOOL_STUDENTS}`,
    'PATCH',
    {
      studentIds,
    }
  );

  if (assignClaimRes.hasError) {
    switch (assignClaimRes.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const patchSchoolsStudentsAssignUnclaim = async (studentIds: string[]) => {
  const assignClaimRes = await request(
    `${BASE_ENDPOINT_PATHS.UNCLAIM_SCHOOL_STUDENTS}`,
    'PATCH',
    {
      studentIds,
    }
  );

  if (assignClaimRes.hasError) {
    switch (assignClaimRes.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const SCHOOL_STUDENTS = {
  CREATE_NEW_SCHOOL_STUDENT: postSchoolsStudents,
  GET_SCHOOL_STUDENTS: getSchoolsStudents,
  GET_SCHOOL_UNCLAIMED_STUDENTS: getSchoolsStudentsUnclaimed,
  GET_SCHOOL_STUDENT_DETAILS_BY_ID: getSchoolsStudentsStudentId,
  DELETE_SCHOOL_STUDENTS: deleteSchoolsStudents,
  CLAIM_SCHOOL_STUDENTS_AS_SCHOOL_TEACHER: patchSchoolsStudentsAssignClaim,
  UNCLAIM_SCHOOL_STUDENTS_AS_SCHOOL_TEACHER: patchSchoolsStudentsAssignUnclaim,
  UPDATE_SCHOOL_STUDENT: putSchoolsStudentsStudentId,
};

export default SCHOOL_STUDENTS;
