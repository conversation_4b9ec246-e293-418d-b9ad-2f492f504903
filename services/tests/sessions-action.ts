import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, TEST_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  AdjustAudioDetailsType,
  ConductedTestSessionStatsType,
  StudentWelcomeDetailsType,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  SubTestQuestionType,
  TestLocalesType,
} from '@/types/common';
import { NarrowedSubTestQuestionType } from '@/types/currentSubTestExerciseResponse';
import {
  GET_ADJUST_AUDIO_DETAILS_SCHEMA,
  GET_INTRO_SCHEMA_FOR_CURRENT_SUB_TEST_SCHEMA,
  GET_PROGRESS_FROM_TEST_SESSION_BY_ID_SCHEMA,
  GET_STUDENT_WELCOME_DETAILS_SCHEMA,
  GET_TEST_LOCALES_SCHEMA,
} from '@/zod/responseSchemas/tests/sessions-action';

const handleExerciseCurrentErrors = (status: number) => {
  switch (status) {
    case HTTP_STATUS_CODES.NOT_FOUND:
      throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
    case HTTP_STATUS_CODES.METHOD_NOT_ALLOWED:
      throw new Error(TEST_ERRORS.SESSION_IS_EITHER_NOT_STARTED_OR_COMPLETED);
    case HTTP_STATUS_CODES.CONFLICT:
      throw new Error(
        TEST_ERRORS.SESSION_ANSWERED_QUESTION_IS_NOT_IN_SYNC_WITH_CURRENT
      );
    // case HTTP_STATUS_CODES.BAD_FORM_DATA:
    //   throw new Error(
    //     TEST_ERRORS.SESSION_ANSWERED_QUESTION_IS_NOT_IN_SYNC_WITH_CURRENT
    //   );
    default:
      throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }
};

const getTestsSessionsSessionIdExercisesCurrent = async (
  sessionId: string
): Promise<NarrowedSubTestQuestionType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/exercises/current`,
    'GET',
    {}
  );

  if (res.hasError) {
    handleExerciseCurrentErrors(res.status);
  }

  return res.data;
};

type AnswerCurrentQuestionResponseType = {
  nextCode: number;
  nextMode: 'practice' | 'exercise';
  audio: string;
  completed: 'test' | 'subtest' | 'exercise';
  failure: string;
  retake?: boolean;
  result: {
    correct: boolean;
    answer: string;
    type: 'index' | 'value' | 'array';
    audio: string;
  };
};

type PatchCurrentExerciseWholeResponse = {
  data: AnswerCurrentQuestionResponseType | null;
  status: number;
  hasError: boolean;
  cause?: string;
  errors?: string[];
};

const patchTestsSessionsSessionIdExercisesCurrent = async ({
  code,
  questionAnswer,
  sessionId,
  time,
}: {
  code: string;
  questionAnswer: string;
  sessionId: string;
  time: number;
}): Promise<AnswerCurrentQuestionResponseType | null> => {
  const res = (await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/exercises/current`,
    'PATCH',
    {
      code,
      time,
      answer: questionAnswer,
    }
  )) as PatchCurrentExerciseWholeResponse;

  if (res.hasError) {
    handleExerciseCurrentErrors(res.status);
  }

  return res.data;
};

const getTestsSessionsSessionIdWelcome = async (
  sessionId: string
): Promise<StudentWelcomeDetailsType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/welcome`,
    'GET',
    {},
    GET_STUDENT_WELCOME_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      case HTTP_STATUS_CODES.METHOD_NOT_ALLOWED:
        throw new Error(TEST_ERRORS.SESSION_IS_EITHER_NOT_STARTED_OR_COMPLETED);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const patchTestsSessionsSessionIdStart = async (sessionId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/start`,
    'PATCH',
    {}
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(
          TEST_ERRORS.SESSION_IS_ALREADY_STARTED_OR_SCHEDULED_FOR_LATER
        );
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const patchTestsSessionsSessionIdContinue = async (sessionId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/continue`,
    'PATCH',
    {}
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      case HTTP_STATUS_CODES.BAD_REQUEST:
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestsSessionsSessionIdProgress = async (
  sessionId: string
): Promise<ConductedTestSessionStatsType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/progress`,
    'GET',
    {},
    GET_PROGRESS_FROM_TEST_SESSION_BY_ID_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      case HTTP_STATUS_CODES.METHOD_NOT_ALLOWED:
        throw new Error(TEST_ERRORS.STUDENT_TEST_SESSION_IS_NOT_RUNNING);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getAdjustAudio = async (
  sessionId: string
): Promise<AdjustAudioDetailsType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/adjust-audio`,
    'GET',
    {},
    GET_ADJUST_AUDIO_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestLocales = async (
  sessionId: string
): Promise<TestLocalesType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/locals`,
    'GET',
    {},
    GET_TEST_LOCALES_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestsSessionsSessionIdIntro = async (sessionId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/intro`,
    'GET',
    {},
    GET_INTRO_SCHEMA_FOR_CURRENT_SUB_TEST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const patchTestsSessionsSessionIdIntroWatched = async (sessionId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/intro/watched`,
    'PATCH',
    {}
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const deleteTestsSessionsSessionId = async (sessionId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}`,
    'DELETE',
    {}
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(TEST_ERRORS.SESSION_ID_DOES_NOT_EXIST_FOR_USER);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const SESSIONS_ACTIONS = {
  START_TEST_SESSION: patchTestsSessionsSessionIdStart,
  CONTINUE_TEST_SESSION: patchTestsSessionsSessionIdContinue,
  GET_TEST_SESSION_DETAILS: getTestsSessionsSessionIdProgress,
  GET_TEST_SESSION_INTRO: getTestsSessionsSessionIdIntro,
  INTRO_WATCHED: patchTestsSessionsSessionIdIntroWatched,
  GET_CURRENT_STATE_OF_SESSION: getTestsSessionsSessionIdExercisesCurrent,
  ANSWER_TEST_QUESTION: patchTestsSessionsSessionIdExercisesCurrent,
  ADJUST_AUDIO_DETAILS: getAdjustAudio,
  GET_TEST_LOCALS: getTestLocales,
  GET_STUDENT_WELCOME_DETAILS: getTestsSessionsSessionIdWelcome,
  DELETE_SESSION: deleteTestsSessionsSessionId,
};

export default SESSIONS_ACTIONS;
