import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { TestAdminActionType } from '@/types/common';
import { SKIP_TEST_SCHEMA } from '@/zod/responseSchemas/tests/sessions-admin';

const postTestSessionsSessionSessionIdSkipNext = async (
  sessionId: string
): Promise<TestAdminActionType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/skip/next`,
    'POST',
    {},
    SKIP_TEST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const postTestSessionsSessionSessionIdSkipPrevious = async (
  sessionId: string
): Promise<TestAdminActionType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_SESSION}/${sessionId}/skip/previous`,
    'POST',
    {},
    SKIP_TEST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const SESSIONS_ADMIN_ACTIONS = {
  NEXT_SESSION: postTestSessionsSessionSessionIdSkipNext,
  PREVIOUS_SESSION: postTestSessionsSessionSessionIdSkipPrevious,
};

export default SESSIONS_ADMIN_ACTIONS;
