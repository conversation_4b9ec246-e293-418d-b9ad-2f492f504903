import * as Sentry from '@sentry/nextjs';
import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, TEST_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { CanCreateSessionTestType } from '@/types/common';
import { CAN_CREATE_TEST_SESSION_SCHEMA } from '@/zod/responseSchemas/tests/sessions-create';

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const postTestsSessionsCreateNew = async (conductTestDetails: any) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_CREATE}/new`,
    'POST',
    conductTestDetails
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.PAYMENT_REQUIRED:
        throw new Error(TEST_ERRORS.NOT_ENOUGH_LICENSES);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const postTestsSessionsCreateShare = async (
  sharedCodes: { studentId: string; code: string; email?: string }[]
) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_CREATE}/share`,
    'POST',
    sharedCodes
    // TEST_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.PAYMENT_REQUIRED:
        throw new Error(TEST_ERRORS.NOT_ENOUGH_LICENSES);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const postTestsSessionsCreatePrint = async (
  test: { studentName: string; code: string; email?: string }[]
) => {
  try {
    const res = await axios({
      method: 'post',
      url: `${BASE_API_URL}/tests/sessions/create/print`,
      responseType: 'blob',
      headers: {
        Accept: 'application/pdf',
      },
      data: test,
      withCredentials: true,
    });

    return res;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      Sentry.captureException(error);
    }
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }
};

const getTestsSessionsCreateCan =
  async (): Promise<CanCreateSessionTestType | null> => {
    const res = await request(
      `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_CREATE}/can`,
      'GET',
      {},
      CAN_CREATE_TEST_SESSION_SCHEMA
    );

    if (res.hasError) {
      switch (res.status) {
        case HTTP_STATUS_CODES.UNAUTHORIZED:
          throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
        default:
          throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }
    }

    return res.data;
  };

const TESTS_SESSIONS_CREATE = {
  NEW_TEST_SESSION: postTestsSessionsCreateNew,
  SHARE_CREATED_TEST_SESSION_CODES: postTestsSessionsCreateShare,
  PRINT_CREATED_TEST_SESSION_CODES: postTestsSessionsCreatePrint,
  CAN_CREATE_TEST_SESSION: getTestsSessionsCreateCan,
};

export default TESTS_SESSIONS_CREATE;
