import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { DOWNLOAD_SESSIONS, GLOBAL_ERRORS, TEST_ERRORS } from '@/common/errors';
import { buildQueryParams } from '@/common/helpers';
import { ContentFilterType } from '@/features/CertificationTests/types';
import request from '@/modules/api';
import {
  BasePaginationType,
  ProductsType,
  TestsResultsMathproScoreAveragesType,
} from '@/types/common';
import { ExportTestDataQueryParameters } from '@/types/tests/session-listing';
import {
  TEST_SESSION_EXPORT_SCHEMA,
  TEST_SESSIONS_LIST_SCHEMA,
  TESTS_RESULTS_MATHPRO_SCORE_AVERAGES_SCHEMA,
} from '@/zod/responseSchemas/tests/sessions-listing.';

type SessionsParamsType = BasePaginationType & {
  className?: string | null;
  groupName?: string | null;
  from?: string | null;
  to?: string | null;
  query?: string;
  testType: ProductsType;
  study?: string | null;
  grades: string[];
  report?: boolean;
  displayedFilterType?: ContentFilterType | null;
  sort?: string | null;
};

const getFormattedQueryParams = (queryParams: SessionsParamsType) => {
  const queryParameters = {
    limit: queryParams.limit,
    page: queryParams.page,
    ...(queryParams.query && { query: queryParams.query }),
    ...(queryParams.groupName && { group: queryParams.groupName }),
    ...(queryParams.className && { class: queryParams.className }),
    ...(queryParams.testType && { type: queryParams.testType }),
    ...(queryParams.from && { from: queryParams.from }),
    ...(queryParams.to && { to: queryParams.to }),
    ...(queryParams.displayedFilterType && {
      status: queryParams.displayedFilterType,
    }),
    ...(queryParams.study && { study: queryParams.study }),
    ...(queryParams.grades?.length && { grades: queryParams.grades }),
    ...(queryParams.sort && { sort: queryParams.sort }),
    ...(queryParams.report && { report: queryParams.report }),
  };

  return queryParameters;
};

const getTestsSessionsPagingTypeType = async (
  queryParams: SessionsParamsType
) => {
  const queryParameters = getFormattedQueryParams(queryParams);

  const queryString = buildQueryParams(queryParameters);

  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_PAGING_TYPE}/${queryParams.testType}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    TEST_SESSIONS_LIST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestsSessionsPagingStudy = async (queryParams: SessionsParamsType) => {
  const queryParameters = getFormattedQueryParams(queryParams);

  const queryString = buildQueryParams(queryParameters);

  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_STUDY}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    TEST_SESSIONS_LIST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED_ACCESS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// Getting conducted test sessionId from the unique generated code
const getTestsSessionsGetCodeCodeId = async (sessionTestCode: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_GET_CODE}/${sessionTestCode}`,
    'GET',
    {}
    // TEST_SESSIONS_LIST_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      // case HTTP_STATUS_CODES.UNAUTHORIZED:
      //   console.log('is unothorized');
      //   throw new Error(TEST_ERRORS.INVALID_SIX_DIGIT_CODE);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const postTestsSessionsExportQueueTypeTypeResults = async (
  exportDataParams: ExportTestDataQueryParameters
) => {
  const { classOrGroupId, from, grades, isStudy, selectedStudy, status, to } =
    exportDataParams || {};

  const queryParams = {
    ...(isStudy && { mode: 'study' }),
    ...(selectedStudy && { study: selectedStudy }),
    ...(from && { from }),
    ...(to && { to }),
    ...(grades?.length && { grades }),
    ...(classOrGroupId?.length && { group: classOrGroupId }),
    ...(status && { status }),
  };

  const queryString = buildQueryParams(queryParams);

  const url = `${BASE_ENDPOINT_PATHS.TESTS_SESSIONS_EXPORT}${
    exportDataParams?.testType
  }/results${queryString ? `?${queryString}` : ''}`;

  const res = (await request(url, 'POST')) as any;

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(DOWNLOAD_SESSIONS.NO_SESSIONS_TO_DOWNLOAD_FOUND);
      case HTTP_STATUS_CODES.CONTENT_TOO_LARGE:
        throw new Error(DOWNLOAD_SESSIONS.SESSION_COUNT_TOO_LARGE, {
          cause: {
            count: (res.cause as any)?.count || 0,
            limit: (res.cause as any)?.limit || 0,
          },
        });
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data as any;
};

const getTestsSessionsExportQueueProgress = async () => {
  const res = await request(
    `/tests/sessions/export/queue/progress`,
    'GET',
    {},
    TEST_SESSION_EXPORT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(DOWNLOAD_SESSIONS.NO_SESSIONS_TO_DOWNLOAD_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getTestsSessionsExportQueueDownload = async () => {
  const res = await request(`/tests/sessions/export/queue/download`);

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

const getTestsSessionsPagingTypeTypeReportsDownload =
  async (exportReportDataParams: {
    testType: ProductsType;
    grades: string[];
    classId?: string;
    from?: string;
    to?: string;
    status?: ContentFilterType;
  }) => {
    const { classId, from, grades, status, testType, to } =
      exportReportDataParams || {};

    const queryParams = {
      ...(from && { from }),
      ...(to && { to }),
      ...(grades?.length && { grades }),
      ...(classId?.length && { class: classId }),
      ...(status && { status }),
    };

    const queryString = buildQueryParams(queryParams);

    const res = await axios({
      method: 'get',
      url: `${BASE_API_URL}/tests/sessions/paging/type/${testType}/reports/download${queryString ? `?${queryString}` : ''}`,
      responseType: 'blob',
      headers: {
        Accept: 'application/pdf',
      },
      withCredentials: true,
    });

    if (axios.isAxiosError(res)) {
      if (
        res.response?.status === 404 &&
        res.cause?.toString() === 'ExportDataExceptions.NoSessionsFound'
      ) {
        throw new Error(DOWNLOAD_SESSIONS.NO_SESSIONS_TO_DOWNLOAD_FOUND);
      }

      throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }

    return res as any;
  };

const getTestsSessionsPagingTypeTypeAverages = async (averageScoreDataParams: {
  isStudy: boolean;
  testType: ProductsType;
  grades?: string[];
  classId?: string;
  groupId?: string;
  from?: string;
  to?: string;
  status?: ContentFilterType;
  studyId?: string;
}): Promise<TestsResultsMathproScoreAveragesType | null> => {
  const {
    classId,
    from,
    grades,
    groupId,
    isStudy,
    status,
    studyId,
    testType,
    to,
  } = averageScoreDataParams || {};

  const queryParams = {
    ...(isStudy && { mode: 'study' }),
    ...(from && { from }),
    ...(to && { to }),
    ...(grades?.length && { grades }),
    ...(status && status !== 'all' && { status }),
    ...(studyId && { study: studyId }),
    ...(classId?.length && { class: classId }),
    ...(groupId?.length && { group: groupId }),
  };

  const queryString = buildQueryParams(queryParams);

  const res = await request(
    `/tests/sessions/paging/type/${testType}/averages${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    TESTS_RESULTS_MATHPRO_SCORE_AVERAGES_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(DOWNLOAD_SESSIONS.NO_SESSIONS_TO_DOWNLOAD_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const TESTS_SESSIONS_LISTING = {
  GET_TEST_SESSIONS: getTestsSessionsPagingTypeType,
  GET_TEST_SESSIONS_STUDY: getTestsSessionsPagingStudy,
  GET_SESSION_ID_FROM_SESSION_CODE: getTestsSessionsGetCodeCodeId,
  START_EXPORT_TEST_DATA_BULL_JOB: postTestsSessionsExportQueueTypeTypeResults,
  GET_SESSIONS_EXPORT_PROGRESS: getTestsSessionsExportQueueProgress,
  DOWNLOAD_TESTS_DATA_EXCEL: getTestsSessionsExportQueueDownload,
  DOWNLOAD_TESTS_REPORT: getTestsSessionsPagingTypeTypeReportsDownload,
  GET_RESULTS_MATHPRO_SCORE: getTestsSessionsPagingTypeTypeAverages,
};

export default TESTS_SESSIONS_LISTING;
