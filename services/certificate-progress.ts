import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { CERTIFICATE_PROGRESS_ERRORS, GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  CertificateProgressModuleAnswerType,
  CertificateProgressType,
  ModuleProgressSchemaByCertificateAndOrderType,
  ProductsType,
} from '@/types/common';
import {
  CERTIFICATE_PROGRESS_MODULE_ANSWER_SCHEMA,
  CERTIFICATE_PROGRESS_SCHEMA_BY_TYPE,
  MODULE_PROGRESS_SCHEMA_BY_CERTIFICATE_TYPE_AND_ORDER,
} from '@/zod/responseSchemas/certificate-progress';

// * all error Handling is done here from the FE side - its according to swagger
const getCertificateProgressByType = async (
  type: ProductsType
): Promise<CertificateProgressType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.CERTIFICATES_PROGRESS}/type/${type}`,
    'GET',
    {},
    CERTIFICATE_PROGRESS_SCHEMA_BY_TYPE
  );

  if (res.hasError) {
    switch (res.status) {
      // The test type you are trying to access is not supported. Its a bad request.
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(CERTIFICATE_PROGRESS_ERRORS.NOT_SUPPORTED_TEST_TYPE);
      // A progress report with this type for this user does not exist.
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(
          CERTIFICATE_PROGRESS_ERRORS.PROGRESS_REPORT_FOR_USER_NOT_FOUND
        );
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const getModuleByCertificationAndOrder = async (
  certificationType: ProductsType,
  module: number
): Promise<ModuleProgressSchemaByCertificateAndOrderType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.CERTIFICATES_PROGRESS}/type/${certificationType}/module/${module}`,
    'GET',
    {},
    MODULE_PROGRESS_SCHEMA_BY_CERTIFICATE_TYPE_AND_ORDER
  );

  if (res.hasError) {
    switch (res.status) {
      // The test type you are trying to access is not supported. Its a bad request.
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(CERTIFICATE_PROGRESS_ERRORS.NOT_SUPPORTED_TEST_TYPE);
      // A progress report with this type for this user does not exist.
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(
          CERTIFICATE_PROGRESS_ERRORS.PROGRESS_REPORT_FOR_USER_NOT_FOUND
        );
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const postCertificateProgressByTypeByModuleAndAnswer = async ({
  certificationType,
  module,
  question,
  selectedAnswers,
}: {
  certificationType: ProductsType;
  module: number;
  question: number;
  selectedAnswers: number[];
}): Promise<CertificateProgressModuleAnswerType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.CERTIFICATES_PROGRESS}/type/${certificationType}/module/${module}/answer`,
    'POST',
    {
      selected: selectedAnswers,
      question,
    },
    CERTIFICATE_PROGRESS_MODULE_ANSWER_SCHEMA
  );

  if (res.hasError) {
    if (res.status === HTTP_STATUS_CODES.BAD_REQUEST) {
      switch (res.cause) {
        case 'CertificateProgressExceptions.CertificateComplete':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.CERTIFICATE_COMPLETE);
        case 'CertificateProgressExceptions.ModuleNotCurrent':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.MODULE_NOT_CURRENT);
        case 'CertificateProgressExceptions.CertificateNotFound':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.CERTIFICATE_NOT_FOUND);
        case 'CertificateProgressExceptions.ModuleDoesNotExist':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.MODULE_NOT_EXIST);
        case 'CertificateProgressExceptions.QuestionDoesNotExist':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.QUESTION_NOT_EXIST);
        case 'CertificateProgressExceptions.AnswersHaveBadFormat':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.BAD_ANSWERS_FORMAT);
        case 'CertificateProgressExceptions.AnsweredAlready':
          throw new Error(CERTIFICATE_PROGRESS_ERRORS.ANSWERED_ALREADY);
        default:
          throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      }
    }

    if (res.status === HTTP_STATUS_CODES.NOT_FOUND) {
      throw new Error(
        CERTIFICATE_PROGRESS_ERRORS.PROGRESS_REPORT_FOR_USER_NOT_FOUND
      );
    }

    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const CERTIFICATES_PROGRESS = {
  GET_CERTIFICATE_PROGRESS_BY_TYPE: getCertificateProgressByType,
  GET_MODULE_BY_CERTIFICATION_AND_ORDER: getModuleByCertificationAndOrder,
  ANSWER_QUESTION_FROM_CERTIFICATE_MODULE:
    postCertificateProgressByTypeByModuleAndAnswer,
};

export default CERTIFICATES_PROGRESS;
