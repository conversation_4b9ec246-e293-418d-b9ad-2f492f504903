import qs from 'qs';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS, USERS_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import { UserDetailsType } from '@/types/common';
import {
  USER_DETAILS_SCHEMA,
  USER_OWNS_PRODUCT_SCHEMA,
} from '@/zod/responseSchemas/users';

// * all error Handling is done here from the FE side - its according to swagger
const getUsersDetails = async (): Promise<UserDetailsType | null> => {
  const res = await request(
    BASE_ENDPOINT_PATHS.USER_DETAILS,
    'GET',
    {},
    USER_DETAILS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

// * all error Handling is done here from the FE side - its according to swagger
const getUsersOwnsProducts = async (data: {
  email: string;
  productId: string;
}): Promise<boolean> => {
  const userDetails = {
    email: data.email.toLowerCase().trim(),
    productId: data.productId,
  };

  const res = await request(
    `${BASE_ENDPOINT_PATHS.USER_OWNS_PRODUCT}?${qs.stringify({ email: userDetails.email })}&${qs.stringify({ productId: userDetails.productId })}`,
    'GET',
    {},
    USER_OWNS_PRODUCT_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data?.ownsProduct || false;
};

const USERS = {
  GET_USER_DETAILS: getUsersDetails,
  CHECK_PRODUCT_OWNERSHIP: getUsersOwnsProducts,
};

export default USERS;
