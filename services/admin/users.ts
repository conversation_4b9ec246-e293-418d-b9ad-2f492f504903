import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import {
  GL<PERSON><PERSON>L_PAGINATION_FETCH_LIMIT,
  HTTP_STATUS_CODES,
} from '@/common/consts';
import { GLOBAL_ERRORS, USERS_ERRORS } from '@/common/errors';
import { buildQueryParams } from '@/common/helpers';
import { UserFormType } from '@/features/Admin/types/formsTypes';
import request from '@/modules/api';
import {
  AdminUserType,
  UnassignedResearchersPaginatedResponseType,
  UnassignedStudyAdminsPaginatedResponseType,
  UsersAdmin,
} from '@/types/common';
import {
  ADMIN_USER_SCHEMA,
  UNASSIGNED_RESEARCHERS_SCHEMA,
  UNASSIGNED_STUDY_ADMINS_SCHEMA,
  USERS_SCHEMA_ADMIN,
} from '@/zod/responseSchemas/admin/users';

const getAdminUsersResearchersUnassigned = async ({
  limit,
  page,
  query,
  studyId,
}: {
  limit: number;
  page: number;
  query: string;
  studyId?: string;
}): Promise<UnassignedResearchersPaginatedResponseType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/researchers/unassigned?limit=${limit}${page ? `&page=${page}` : ''}${query ? `&query=${query}` : ''}${studyId ? `&studyId=${studyId}` : ''}`,
    'GET',
    {},
    UNASSIGNED_RESEARCHERS_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminUsersStudyAdminsUnassigned = async (
  studyId?: string
): Promise<UnassignedStudyAdminsPaginatedResponseType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/study-admins/unassigned${studyId ? `?studyId=${studyId}` : ''}`,
    'GET',
    {},
    UNASSIGNED_STUDY_ADMINS_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminUsersSchoolAdminsUnassigned =
  async (): Promise<UnassignedStudyAdminsPaginatedResponseType | null> => {
    const res = await request(
      `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/school-admins/unassigned`,
      'GET',
      {},
      UNASSIGNED_STUDY_ADMINS_SCHEMA
    );

    if (res.hasError) {
      throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }

    return res.data;
  };

const getAdminUsers = async (queryParams: {
  page: number;
  query: string;
  school: string;
  study: string;
  from: Date | null;
  to: Date | null;
}): Promise<UsersAdmin | null> => {
  const queryString = buildQueryParams({
    ...queryParams,
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
  });

  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    USERS_SCHEMA_ADMIN
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const deleteUser = async (userId: string): Promise<UsersAdmin | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/${userId}`,
    'DELETE'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const archiveUser = async (userId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/${userId}/archive`,
    'PATCH'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const unArchiveUser = async (userId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/${userId}/unarchive`,
    'PATCH'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return null;
};

const postAdminUsers = async (data: UserFormType) => {
  const res = await request(`${BASE_ENDPOINT_PATHS.ADMIN_USERS}`, 'POST', data);

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(USERS_ERRORS.USER_ALREADY_EXISTS);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const putAdminUsers = async ({
  data,
  userId,
}: {
  data: UserFormType;
  userId: string;
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/${userId}`,
    'PUT',
    data
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        throw new Error(USERS_ERRORS.USER_ALREADY_EXISTS);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(USERS_ERRORS.USER_NOT_FOUND);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const getAdminUser = async (userId: string): Promise<AdminUserType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_USERS}/${userId}`,
    'GET',
    {},
    ADMIN_USER_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const ADMIN_USERS = {
  GET_UNASSIGNED_RESEARCHERS: getAdminUsersResearchersUnassigned,
  GET_UNASSIGNED_STUDY_ADMINS: getAdminUsersStudyAdminsUnassigned,
  GET_UNASSIGNED_SCHOOL_ADMINS: getAdminUsersSchoolAdminsUnassigned,
  GET_USERS: getAdminUsers,
  DELETE_USER: deleteUser,
  ARCHIVE_USER: archiveUser,
  UNARCHIVE_USER: unArchiveUser,
  ADD_USER: postAdminUsers,
  EDIT_USER: putAdminUsers,
  GET_USER: getAdminUser,
};

export default ADMIN_USERS;
