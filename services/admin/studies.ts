/* eslint-disable @typescript-eslint/no-unused-vars */
import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import {
  GLOBAL_PAGINATION_FETCH_LIMIT,
  HTTP_STATUS_CODES,
} from '@/common/consts';
import { ADMIN_STUDIES_ERRORS, GLO<PERSON>L_ERRORS } from '@/common/errors';
import { buildQueryParams, getPayloadDate } from '@/common/helpers';
import request from '@/modules/api';
import {
  AdminStudiesPaginatedResponseType,
  AdminStudyDetailsByIdType,
  StudyDetailsType,
  UnassignedResearchersPaginatedResponseType,
} from '@/types/common';
import { StudiesQueryParamsStateType } from '@/types/queryParams/studies';
import {
  ADMIN_STUDIES_LIST_SCHEMA,
  ADMIN_STUDY_SCHEMA__DETAILS_BY_ID,
  STUDIES_SCHEMA,
} from '@/zod/responseSchemas/admin/studies';

const createStudyPayload = (gatheredStudyDetails: StudyDetailsType) => {
  const newStudyPayload = {
    name: gatheredStudyDetails.name.trim(),
    type: gatheredStudyDetails.type,
    country: gatheredStudyDetails.country,
    language: gatheredStudyDetails.language,
    administrator: gatheredStudyDetails.administrator?.id || null,
    from: gatheredStudyDetails.from,
    to: gatheredStudyDetails.to,
    researchers: gatheredStudyDetails.researchers.map(
      (researcher) => researcher.id
    ),
  };

  return newStudyPayload;
};

const postAdminStudies = async (
  gatheredStudyDetails: StudyDetailsType
): Promise<void> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}`,
    'POST',
    createStudyPayload(gatheredStudyDetails)
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.CONFLICT:
        throw new Error(ADMIN_STUDIES_ERRORS.STUDY_NAME_EXISTS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(ADMIN_STUDIES_ERRORS.INVALID_PROVIDED_STUDY_DATA);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }
};

const getAdminStudiesListAll = async () => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/list/all`,
    'GET',
    {},
    STUDIES_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminStudies = async (
  queryParams: StudiesQueryParamsStateType
): Promise<AdminStudiesPaginatedResponseType | null> => {
  const {
    activePage,
    endingDateOfTheStudy,
    searchWord,
    startingDateOfTheStudy,
    studiesLanguage,
  } = queryParams || {};

  const queryParameters = {
    ...(searchWord && { query: searchWord }),
    ...(activePage && { page: activePage }),
    ...(studiesLanguage && { language: studiesLanguage }),
    ...(startingDateOfTheStudy && { from: startingDateOfTheStudy }),
    ...(endingDateOfTheStudy && { to: endingDateOfTheStudy }),
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
  };

  const queryString = buildQueryParams(queryParameters);

  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    ADMIN_STUDIES_LIST_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminStudiesListLanguages = async (
  testType: string
): Promise<string[] | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/list/languages${testType ? `?type=${testType}` : ''}`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminStudiesId = async (
  studyId: string
): Promise<AdminStudyDetailsByIdType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/${studyId}`,
    'GET',
    {},
    ADMIN_STUDY_SCHEMA__DETAILS_BY_ID
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const putAdminStudiesId = async ({
  gatheredStudyDetails,
  studyId,
}: {
  studyId: string;
  gatheredStudyDetails: StudyDetailsType;
}): Promise<void> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/${studyId}`,
    'PUT',
    createStudyPayload(gatheredStudyDetails)
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.CONFLICT:
        throw new Error(ADMIN_STUDIES_ERRORS.STUDY_NAME_EXISTS);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(ADMIN_STUDIES_ERRORS.INVALID_PROVIDED_STUDY_DATA);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }
};

const deleteAdminStudiesId = async (studyId: string): Promise<void> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/${studyId}`,
    'DELETE'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }
};

const patchAdminStudiesIdTerminate = async (studyId: string): Promise<void> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/${studyId}/terminate`,
    'PATCH'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }
};

const getAdminStudiesTestsAll = async () => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_STUDIES}/tests/all`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const ADMIN_STUDIES = {
  CREATE_NEW_STUDY: postAdminStudies,
  GET_STUDIES: getAdminStudies,
  GET_STUDIES_LIST_ALL: getAdminStudiesListAll,
  GET_STUDIES_LIST_LANGUAGES: getAdminStudiesListLanguages,
  GET_STUDY_BY_ID: getAdminStudiesId,
  UPDATE_STUDY_BY_ID: putAdminStudiesId,
  DELETE_STUDY_BY_ID: deleteAdminStudiesId,
  GET_AVAILABLE_STUDY_TESTS: getAdminStudiesTestsAll,
  TERMINATE_STUDY_BY_ID: patchAdminStudiesIdTerminate,
};

export default ADMIN_STUDIES;
