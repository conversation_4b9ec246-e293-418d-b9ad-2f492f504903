import * as Sentry from '@sentry/nextjs';
import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import {
  GLOBAL_PAGINATION_FETCH_LIMIT,
  HTTP_STATUS_CODES,
} from '@/common/consts';
import { GLOBAL_ERRORS, SCHOOLS_ERRORS } from '@/common/errors';
import { buildQueryParams } from '@/common/helpers';
import request from '@/modules/api';
import {
  AdminSchoolsListType,
  AdminUserType,
  SchoolsListType,
} from '@/types/common';
import { ADMIN_SCHOOLS_LIST_SCHEMA } from '@/zod/responseSchemas/admin/schools';

const getAdminSchools = async (
  queryParams: any
): Promise<AdminSchoolsListType | null> => {
  const { activePage, country, searchWord } = queryParams || {};

  const queryParameters = {
    ...(searchWord && { query: searchWord }),
    ...(activePage && { page: activePage }),
    ...(country && { country }),
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
  };

  const queryString = buildQueryParams(queryParameters);

  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    ADMIN_SCHOOLS_LIST_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminSchoolsListAll = async (): Promise<SchoolsListType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/list/all`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const postAdminSchools = async (data: any) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}`,
    'POST',
    data
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        if (res.errors?.includes('Tax ID is invalid')) {
          throw new Error(SCHOOLS_ERRORS.INVALID_TAX_ID);
        }

        throw new Error(SCHOOLS_ERRORS.SCHOOL_CODE_ALREADY_EXISTS);

      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data as any;
};

const getAdminSchoolsId = async (id: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${id}`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const putAdminSchools = async (data: any, schoolId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}`,
    'PUT',
    data
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        if (res.errors?.includes('Tax ID is invalid')) {
          throw new Error(SCHOOLS_ERRORS.INVALID_TAX_ID);
        }

        throw new Error(SCHOOLS_ERRORS.SCHOOL_CODE_ALREADY_EXISTS);

      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data as any;
};

const postGetUploadUrl = async ({
  profileImage,
}: {
  profileImage: {
    fileName: string;
    imageFile: File;
  };
}) => {
  const res = (await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/upload-school-pic`,
    'POST',
    {
      filename: profileImage.fileName,
    }
  )) as any;

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  const resUpload = await axios.put(
    res.data.url || '  ',
    profileImage.imageFile,
    {
      headers: {
        'x-amz-acl': 'public-read',
      },
      withCredentials: false,
      maxBodyLength: Infinity,
    }
  );

  if (axios.isAxiosError(resUpload)) {
    Sentry.captureException(resUpload);
  }

  if (resUpload.status !== HTTP_STATUS_CODES.SUCCESS) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  const profileImageUrl = res.data.url.split('?')[0] || '';

  return profileImageUrl;
};

const getAdminSchoolsListCountries = async () => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/list/countries`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const getAdminSchoolsSchoolIdBundlesHistory = async (
  schoolId: string,
  activePage: number,
  limit: number
) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}/bundles/history?limit=${limit}&page=${activePage}`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const deleteAdminSchoolsSchoolIdBundlesId = async ({
  bundleId,
  schoolId,
}: {
  bundleId: string;
  schoolId: string;
}) => {
  //  Bundle is corresponding to a license or certificate id
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}/bundles/${bundleId}`,
    'DELETE'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const getAdminSchoolsSchoolIdBundlesSummary = async (schoolId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}/bundles/summary`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const postAdminSchoolsSchoolIdBundlesSummary = async ({
  items,
  schoolId,
}: {
  schoolId: string;
  items: {
    type: string;
    licenses: number;
    certificates: number;
    status: string;
  }[];
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}/bundles/summary`,
    'POST',
    { items }
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const postLoginAsSchoolAdmin = async (
  schoolId: string
): Promise<AdminUserType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}/admin/login`,
    'POST'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data as any;
};

const ADMIN_SCHOOLS = {
  GET_ADMIN_SCHOOLS: getAdminSchools,
  GET_ADMIN_SCHOOLS_LIST: getAdminSchoolsListAll,
  GET_ADMIN_SCHOOLS_LIST_COUNTRIES: getAdminSchoolsListCountries,
  ADD_SCHOOL: postAdminSchools,
  GET_ADMIN_SCHOOL: getAdminSchoolsId,
  EDIT_SCHOOL: putAdminSchools,
  UPLOAD_LOGO_IMAGE: postGetUploadUrl,
  GET_SCHOOL_LICENSES_AND_CERTIFICATES_HISTORY:
    getAdminSchoolsSchoolIdBundlesHistory,
  REVOKE_REMAINING_LICENSES_OR_CERTIFICATES:
    deleteAdminSchoolsSchoolIdBundlesId,
  GET_SCHOOL_LICENSES_AND_CERTIFICATES_SUMMARY:
    getAdminSchoolsSchoolIdBundlesSummary,
  UPDATE_SCHOOL_LICENSES_AND_CERTIFICATES:
    postAdminSchoolsSchoolIdBundlesSummary,
  LOGIN_AS_SCHOOL_ADMIN: postLoginAsSchoolAdmin,
};

export default ADMIN_SCHOOLS;
