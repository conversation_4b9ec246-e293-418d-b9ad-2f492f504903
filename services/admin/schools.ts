import * as Sentry from '@sentry/nextjs';
import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import {
  GLOBAL_PAGINATION_FETCH_LIMIT,
  HTTP_STATUS_CODES,
} from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import { buildQueryParams } from '@/common/helpers';
import request from '@/modules/api';
import { AdminSchoolsListType, SchoolsListType } from '@/types/common';
import { ADMIN_SCHOOLS_LIST_SCHEMA } from '@/zod/responseSchemas/admin/schools';

const getAdminSchools = async (
  queryParams: any
): Promise<AdminSchoolsListType | null> => {
  const { activePage, searchWord } = queryParams || {};

  const queryParameters = {
    ...(searchWord && { query: searchWord }),
    ...(activePage && { page: activePage }),
    limit: GLOBAL_PAGINATION_FETCH_LIMIT,
  };

  const queryString = buildQueryParams(queryParameters);

  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}${queryString ? `?${queryString}` : ''}`,
    'GET',
    {},
    ADMIN_SCHOOLS_LIST_SCHEMA
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminSchoolsListAll = async (): Promise<SchoolsListType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/list/all`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const postAdminSchools = async (data: any) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}`,
    'POST',
    data
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const getAdminSchoolsId = async (id: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${id}`,
    'GET'
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const putAdminSchools = async (data: any, schoolId: string) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/${schoolId}`,
    'PUT',
    data
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return res.data;
};

const postGetUploadUrl = async ({
  profileImage,
}: {
  profileImage: {
    fileName: string;
    imageFile: File;
  };
}) => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.ADMIN_SCHOOLS}/upload-school-pic`,
    'POST',
    {
      filename: profileImage.fileName,
    }
  );

  if (res.hasError) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  const resUpload = await axios.put(
    res.data.url || '  ',
    profileImage.imageFile,
    {
      headers: {
        'x-amz-acl': 'public-read',
      },
      withCredentials: false,
      maxBodyLength: Infinity,
    }
  );

  if (axios.isAxiosError(resUpload)) {
    Sentry.captureException(resUpload);
  }

  if (resUpload.status !== HTTP_STATUS_CODES.SUCCESS) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  const profileImageUrl = res.data.url.split('?')[0] || '';

  return profileImageUrl;
};

const ADMIN_SCHOOLS = {
  GET_ADMIN_SCHOOLS: getAdminSchools,
  GET_ADMIN_SCHOOLS_LIST: getAdminSchoolsListAll,
  ADD_SCHOOL: postAdminSchools,
  GET_ADMIN_SCHOOL: getAdminSchoolsId,
  EDIT_SCHOOL: putAdminSchools,
  UPLOAD_LOGO_IMAGE: postGetUploadUrl,
};

export default ADMIN_SCHOOLS;
