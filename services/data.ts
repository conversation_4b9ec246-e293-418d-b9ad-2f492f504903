import * as Sentry from '@sentry/nextjs';
import axios from 'axios';

import BASE_ENDPOINT_PATHS from '@/common/baseEndpoints';
import { HTTP_STATUS_CODES } from '@/common/consts';
import { GLOBAL_ERRORS } from '@/common/errors';
import request from '@/modules/api';
import {
  PresignedUrlResponseType,
  UploadFileProgressType,
} from '@/types/common';
import {
  GET_TEMPLATE_URL_SCHEMA,
  GET_UPLOAD_URL_SCHEMA,
} from '@/zod/responseSchemas/data';

const getExcelTemplate = async ({
  type,
}: {
  type: 'students';
}): Promise<BlobPart | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.DATA_IMPORT_TEMPLATE}?type=${type}`,
    'GET',
    {},
    GET_TEMPLATE_URL_SCHEMA
  );

  if (res.hasError || !res?.data?.url) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.BAD_FORM_DATA:
        throw new Error(GLOBAL_ERRORS.BAD_REQUEST);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  const fileResponse = await axios.get(res.data.url, {
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: false,
    responseType: 'blob',
  });

  if (axios.isAxiosError(fileResponse)) {
    Sentry.captureException(fileResponse);
  }

  if (!fileResponse.data) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  return fileResponse.data;
};

const getPresignedUrl = async (): Promise<PresignedUrlResponseType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.DATA_IMPORT_UPLOAD_URL}`,
    'GET',
    {},
    GET_UPLOAD_URL_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const uploadFile = async ({
  file,
  fileNameFromBe,
  preSignedUrl,
}: {
  preSignedUrl: string;
  file: File;
  fileNameFromBe: string;
}): Promise<null> => {
  const res = await axios.put(preSignedUrl || '  ', file, {
    headers: {
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    withCredentials: false,
    maxBodyLength: Infinity,
  });

  if (axios.isAxiosError(res)) {
    Sentry.captureException(res);
  }

  if (res.status !== HTTP_STATUS_CODES.SUCCESS) {
    throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
  }

  if (res.status === HTTP_STATUS_CODES.SUCCESS) {
    const triggerBeToProcessFile = await request(
      `${BASE_ENDPOINT_PATHS.DATA_IMPORT}/${fileNameFromBe}/start`,
      'POST',
      {}
    );

    if (triggerBeToProcessFile.hasError) {
      throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return null;
};

const getUploadProgress = async ({
  fileName,
}: {
  fileName: string;
}): Promise<UploadFileProgressType | null> => {
  const res = await request(
    `${BASE_ENDPOINT_PATHS.DATA_IMPORT}/${fileName}/progress`,
    'GET',
    {}
    // GET_UPLOAD_PROGRESS_SCHEMA
  );

  if (res.hasError) {
    switch (res.status) {
      case HTTP_STATUS_CODES.FORBIDDEN:
        throw new Error(GLOBAL_ERRORS.UNAUTHORIZED);
      case HTTP_STATUS_CODES.NOT_FOUND:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
      default:
        throw new Error(GLOBAL_ERRORS.UNEXPECTED_ERROR);
    }
  }

  return res.data;
};

const DATA = {
  GET_DOWNLOAD_TEMPLATE: getExcelTemplate,
  GET_PRESIGNED_URL: getPresignedUrl,
  UPLOAD_FILE: uploadFile,
  GET_UPLOAD_PROGRESS: getUploadProgress,
};

export default DATA;
