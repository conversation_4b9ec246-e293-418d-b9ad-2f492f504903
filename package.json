{"name": "mathpro-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typeCheck": "tsc -p tsconfig.json --noEmit", "test": "npm run lint && npm run typeCheck", "full-test": "npm run test && npm run build", "check-translation-keys": "node ./scripts/translation-key-checker.js", "pre-commit-test": "npm run test && npm run check-translation-keys", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@mantine/carousel": "^7.14.0", "@mantine/charts": "^7.14.0", "@mantine/core": "^7.14.0", "@mantine/dates": "^7.14.0", "@mantine/dropzone": "^7.14.3", "@mantine/hooks": "^7.14.0", "@mantine/modals": "^7.14.0", "@mantine/notifications": "^7.14.0", "@mantine/nprogress": "^7.14.0", "@sentry/nextjs": "^9.8.0", "@stripe/react-stripe-js": "^2.8.0", "@stripe/stripe-js": "^4.5.0", "@tanstack/react-query": "^5.56.2", "@tanstack/react-query-devtools": "^5.58.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "embla-carousel-react": "^8.2.1", "framer-motion": "^12.5.0", "i18n-iso-countries": "^7.14.0", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "language-name-map": "^0.3.0", "lottie-react": "^2.4.0", "next": "^14.2.13", "qs": "^6.13.0", "react": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.1", "react-icons": "^5.3.0", "recharts": "^2.12.7", "sharp": "^0.33.5", "validator": "^13.12.0", "zod": "^3.23.8"}, "devDependencies": {"@types/language-name-map": "^0.3.4", "@types/node": "^20", "@types/qs": "^6.9.16", "@types/react": "^18", "@types/react-dom": "^18", "@types/validator": "^13.12.2", "eslint": "^8", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "14.2.9", "eslint-config-prettier": "^9.1.0", "eslint-plugin-check-file": "^2.8.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sort-destructure-keys": "^2.0.0", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-typescript-sort-keys": "^3.2.0", "husky": "^9.1.6", "postcss": "^8.4.45", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.3.3", "typescript": "^5"}}