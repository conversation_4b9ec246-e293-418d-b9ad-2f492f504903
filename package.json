{"name": "mathpro-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typeCheck": "tsc -p tsconfig.json --noEmit", "test": "npm run lint && npm run typeCheck", "full-test": "npm run test && npm run build", "check-translation-keys": "node ./scripts/translation-key-checker.js", "pre-commit-test": "npm run test && npm run check-translation-keys", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@mantine/carousel": "^8.3.2", "@mantine/charts": "^8.3.2", "@mantine/core": "^8.3.2", "@mantine/dates": "^8.3.2", "@mantine/dropzone": "^8.3.2", "@mantine/hooks": "^8.3.2", "@mantine/modals": "^8.3.2", "@mantine/notifications": "^8.3.2", "@mantine/nprogress": "^8.3.2", "@mantine/tiptap": "^8.3.2", "@sentry/nextjs": "^10.15.0", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^7.9.0", "@tanstack/react-query": "^5.90.2", "@tanstack/react-query-devtools": "^5.90.2", "@tiptap/extension-link": "^3.6.1", "@tiptap/extension-placeholder": "^3.6.1", "@tiptap/extension-underline": "^3.6.1", "@tiptap/pm": "^3.6.1", "@tiptap/react": "^3.6.1", "@tiptap/starter-kit": "^3.6.1", "axios": "^1.12.2", "dayjs": "^1.11.18", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.22", "highlight.js": "^11.11.1", "i18n-iso-countries": "^7.14.0", "i18next": "^25.5.2", "i18next-browser-languagedetector": "^8.2.0", "language-name-map": "^0.3.0", "lottie-react": "^2.4.1", "next": "^15.5.4", "qs": "^6.14.0", "react": "^19", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19", "react-hook-form": "^7.63.0", "react-i18next": "^16.0.0", "react-icons": "^5.5.0", "recharts": "^3.2.1", "sharp": "^0.34.4", "validator": "^13.15.15", "zod": "^4.1.11", "react-markdown": "^10.1.0", "react-slick": "^0.31.0", "rehype-highlight": "^7.0.2", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "slick-carousel": "^1.8.1"}, "devDependencies": {"@eslint/js": "^9.36.0", "@next/eslint-plugin-next": "^15.5.4", "@types/language-name-map": "^0.3.4", "@types/node": "^24", "@types/qs": "^6.14.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/validator": "^13.15.3", "@typescript-eslint/eslint-plugin": "^8.44.1", "@typescript-eslint/parser": "^8.44.1", "eslint": "^9.36.0", "eslint-config-next": "^15.5.4", "eslint-config-prettier": "^10.1.8", "eslint-plugin-check-file": "^3.3.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "@types/react-slick": "^0.23.13", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sort-destructure-keys": "^2.0.0", "eslint-plugin-sort-keys-fix": "^1.1.2", "husky": "^9.1.7", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.18.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.6.2", "typescript": "^5"}}